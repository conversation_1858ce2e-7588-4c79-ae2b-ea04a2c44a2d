"""
演示配置驱动的问题片段过滤机制的灵活性
"""

import json
import os
import requests

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heibailan5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def get_fragments_count(token, task_id):
    """获取问题片段数量"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(url, headers=headers, params={"page": 1, "limit": 100})
        
        if response.status_code == 200:
            data = response.json()
            return data['data']['total_count'], data['data']['fragments']
        else:
            print(f"❌ 获取问题片段失败: {response.status_code}")
            return 0, []
            
    except Exception as e:
        print(f"❌ 获取问题片段失败: {str(e)}")
        return 0, []

def modify_rule_config(rule_category, rule_name, exclude_flag):
    """修改规则配置"""
    config_path = "backend/config/rules/hbkj_bachelor_2024.json"
    
    try:
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 修改配置
        if rule_category in config['rules'] and rule_name in config['rules'][rule_category]:
            if exclude_flag:
                config['rules'][rule_category][rule_name]['exclude_from_fragments'] = True
                print(f"✅ 设置规则 {rule_category}.{rule_name} 排除问题片段")
            else:
                if 'exclude_from_fragments' in config['rules'][rule_category][rule_name]:
                    del config['rules'][rule_category][rule_name]['exclude_from_fragments']
                    print(f"✅ 移除规则 {rule_category}.{rule_name} 的排除设置")
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        print(f"❌ 修改规则配置失败: {str(e)}")
        return False

def show_current_config():
    """显示当前配置中的排除设置"""
    config_path = "backend/config/rules/hbkj_bachelor_2024.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("📋 当前规则排除配置:")
        
        excluded_rules = []
        for category, rules in config.get('rules', {}).items():
            for rule_name, rule_config in rules.items():
                if rule_config.get('exclude_from_fragments', False):
                    excluded_rules.append(f"{category}.{rule_name}")
        
        if excluded_rules:
            for rule in excluded_rules:
                print(f"   ❌ {rule} - 排除问题片段")
        else:
            print("   ✅ 没有规则被排除")
        
        return excluded_rules
        
    except Exception as e:
        print(f"❌ 读取配置失败: {str(e)}")
        return []

def demo_config_flexibility():
    """演示配置灵活性"""
    print("🚀 演示配置驱动的问题片段过滤机制")
    print("=" * 60)
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌")
        return
    
    # 使用已有的任务ID
    task_id = "task_72f94cbac2374d63b2cf7a5d2d97d7e4"
    
    print(f"🔍 使用任务: {task_id}")
    
    # 1. 显示当前配置
    print(f"\n📊 步骤1: 显示当前配置")
    excluded_rules = show_current_config()
    
    # 2. 获取当前问题片段数量
    print(f"\n📊 步骤2: 获取当前问题片段数量")
    count1, fragments1 = get_fragments_count(token, task_id)
    print(f"   当前问题片段数量: {count1}")
    
    # 显示规则分布
    rule_stats = {}
    for fragment in fragments1:
        rule_id = fragment.get('rule_id', '未知')
        rule_stats[rule_id] = rule_stats.get(rule_id, 0) + 1
    
    print(f"   规则分布:")
    for rule_id, count in sorted(rule_stats.items()):
        print(f"     {rule_id}: {count} 个")
    
    # 3. 演示添加排除规则
    print(f"\n🔧 步骤3: 演示添加排除规则")
    print(f"   假设我们想排除所有content类别的规则...")
    
    # 备份当前配置
    config_backup_path = "backend/config/rules/hbkj_bachelor_2024.json.backup"
    import shutil
    shutil.copy("backend/config/rules/hbkj_bachelor_2024.json", config_backup_path)
    print(f"   ✅ 已备份配置文件")
    
    # 添加排除设置
    content_rules = [
        ("content", "chinese_abstract_word_count"),
        ("content", "english_abstract_word_count"),
        ("content", "chinese_keywords_count"),
        ("content", "english_keywords_count"),
        ("content", "references_item_count")
    ]
    
    for category, rule_name in content_rules:
        modify_rule_config(category, rule_name, True)
    
    # 4. 获取修改后的问题片段数量
    print(f"\n📊 步骤4: 获取修改后的问题片段数量")
    count2, fragments2 = get_fragments_count(token, task_id)
    print(f"   修改后问题片段数量: {count2}")
    print(f"   减少了: {count1 - count2} 个问题片段")
    
    # 显示修改后的规则分布
    rule_stats2 = {}
    for fragment in fragments2:
        rule_id = fragment.get('rule_id', '未知')
        rule_stats2[rule_id] = rule_stats2.get(rule_id, 0) + 1
    
    print(f"   修改后规则分布:")
    for rule_id, count in sorted(rule_stats2.items()):
        print(f"     {rule_id}: {count} 个")
    
    # 5. 恢复配置
    print(f"\n🔄 步骤5: 恢复原始配置")
    shutil.copy(config_backup_path, "backend/config/rules/hbkj_bachelor_2024.json")
    os.remove(config_backup_path)
    print(f"   ✅ 已恢复原始配置")
    
    # 6. 验证恢复
    print(f"\n📊 步骤6: 验证配置恢复")
    count3, fragments3 = get_fragments_count(token, task_id)
    print(f"   恢复后问题片段数量: {count3}")
    
    if count3 == count1:
        print(f"   ✅ 配置恢复成功，问题片段数量一致")
    else:
        print(f"   ⚠️ 配置恢复后数量不一致，可能需要重启服务")
    
    # 7. 总结
    print(f"\n📋 总结:")
    print(f"   ✅ 通过修改配置文件可以灵活控制问题片段的生成")
    print(f"   ✅ 无需修改代码，只需配置 'exclude_from_fragments': true")
    print(f"   ✅ 支持细粒度控制，可以针对特定规则进行排除")
    print(f"   ✅ 配置变更可以立即生效（可能需要重新生成问题片段）")
    
    print(f"\n🌐 前端查看: http://localhost:3000/document/{task_id}/statistics")

if __name__ == "__main__":
    demo_config_flexibility()
    print("\n" + "=" * 60)
    print("🏁 演示完成")
