<template>
  <teleport to="body">
    <transition name="modal-backdrop" appear>
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
        @click="handleCancel"
        role="dialog"
        aria-modal="true"
      >
        <transition name="modal-content" appear>
          <div
            @click.stop
            class="relative w-full max-w-lg bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto"
          >
            <!-- 图标区域 -->
            <div class="px-6 pt-6">
              <div class="mx-auto flex items-center justify-center w-12 h-12 rounded-full mb-4 bg-blue-100 dark:bg-blue-900/30">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
              </div>
            </div>
            
            <!-- 标题和描述 -->
            <div class="px-6 pb-4 text-center">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                重新分析选项
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                选择不同的检测标准可以获得不同的分析结果
              </p>
            </div>

            <!-- 表单内容 -->
            <div class="px-6 pb-6">
              <!-- 检测标准选择 -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                  📚 检测标准
                </label>
                <div class="grid grid-cols-1 gap-4">
                  <!-- GB/T 7713.1-2006 -->
                  <label class="cursor-pointer">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="gbt_7713_1_2006" 
                      v-model="options.standard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mt-1">
                          <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                          </svg>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-semibold text-gray-900 dark:text-white mb-1">GB/T 7713.1-2006</h4>
                          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">学位论文编写规则</p>
                          <p class="text-xs text-blue-600 dark:text-blue-400">结构检查 + 格式检查</p>
                        </div>
                      </div>
                    </div>
                  </label>
                  
                  <!-- GB/T 7714-2015 -->
                  <label class="cursor-pointer">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="gbt_7714_2015" 
                      v-model="options.standard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 h-10 w-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-1">
                          <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                          </svg>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-semibold text-gray-900 dark:text-white mb-1">GB/T 7714-2015</h4>
                          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">参考文献著录规则</p>
                          <p class="text-xs text-green-600 dark:text-green-400">引用格式检查</p>
                        </div>
                      </div>
                    </div>
                  </label>
                  
                  <!-- 河北科技学院本科论文检查 -->
                  <label class="cursor-pointer">
                    <input 
                      type="radio" 
                      name="detection-standard" 
                      value="hbkj_bachelor_2024" 
                      v-model="options.standard"
                      class="sr-only peer"
                    >
                    <div class="border-2 border-gray-200 dark:border-gray-600 rounded-lg p-4 transition-all peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mt-1">
                          <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                          </svg>
                        </div>
                        <div class="flex-1">
                          <h4 class="font-semibold text-gray-900 dark:text-white mb-1">河北科技学院</h4>
                          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">本科论文检查</p>
                          <p class="text-xs text-purple-600 dark:text-purple-400">全面检查 + 任务书</p>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <!-- 费用提醒 -->
              <div class="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                  </svg>
                  <div class="text-sm">
                    <p class="font-medium text-yellow-800 dark:text-yellow-200">费用说明</p>
                    <p class="text-yellow-700 dark:text-yellow-300 mt-1">
                      重新分析将消耗 <strong>1次检测次数</strong>
                    </p>
                  </div>
                </div>
              </div>

              <!-- 选择说明 -->
              <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <div class="text-sm">
                    <p class="font-medium text-blue-800 dark:text-blue-200">选择建议</p>
                    <p class="text-blue-700 dark:text-blue-300 mt-1">
                      • <strong>GB/T 7713.1-2006</strong>：适用于学位论文的结构和格式检查<br>
                      • <strong>GB/T 7714-2015</strong>：适用于参考文献格式的专项检查<br>
                      • <strong>河北科技学院</strong>：适用于河北科技学院本科论文的全面检查
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 按钮区域 -->
            <div class="flex space-x-3 px-6 pb-6">
              <button
                @click="handleCancel"
                class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors"
              >
                取消
              </button>
              <button
                @click="handleConfirm"
                :disabled="!isOptionsValid"
                class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                确认重新分析
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  isVisible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  confirm: [options: ReanalyzeOptions]
  cancel: []
}>()

// 重新分析选项类型
interface ReanalyzeOptions {
  analysisType: string
  standard: string
}

// 默认选项
const options = ref<ReanalyzeOptions>({
  analysisType: 'paper_check', // 固定为论文检测
  standard: 'gbt_7713_1_2006' // 默认选择GB/T 7713.1-2006
})

// 验证选项是否有效
const isOptionsValid = computed(() => {
  return options.value.standard && options.value.standard.length > 0
})

// 处理确认
const handleConfirm = () => {
  if (isOptionsValid.value) {
    emit('confirm', { ...options.value })
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleCancel()
  } else if (event.key === 'Enter' && isOptionsValid.value) {
    handleConfirm()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
/* 过渡动画 */
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}
</style> 