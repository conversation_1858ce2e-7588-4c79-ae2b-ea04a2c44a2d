<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端英文摘要标准要求测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端英文摘要标准要求测试</h1>
        
        <div class="test-section">
            <div class="test-title">📋 测试前端getWordRequirementsFromStandard方法</div>
            <div id="requirements-test"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 字数分析数据生成测试</div>
            <div id="analysis-test"></div>
        </div>
    </div>

    <script>
        // 模拟前端的getWordRequirementsFromStandard方法
        const getWordRequirementsFromStandard = () => {
            const requirements = {}

            // 从规则文件中解析字数要求
            // chinese_abstract_word_count: min: 300, max: 500, unit: "字"
            requirements['中文摘要'] = { min: 300, max: 500, unit: '字' }

            // english_abstract_word_count: min: 300, max: 500, unit: "词"
            requirements['英文摘要'] = { min: 300, max: 500, unit: '词' }

            // references_item_count: min: 10, unit: "条"
            requirements['参考文献'] = { min: 10, unit: '条' }

            return requirements
        }

        // 模拟文档结构数据
        const mockDocumentStructures = [
            {
                name: '中文摘要',
                content: { word_count: 355 },
                count: '355字'
            },
            {
                name: '英文摘要',
                content: { word_count: 194 },
                count: '194词'
            },
            {
                name: '参考文献',
                content: { word_count: 15 },
                count: '中文11条外文3条'
            }
        ]

        // 模拟generateWordAnalysisData方法
        const generateWordAnalysisData = (structures) => {
            const requirements = getWordRequirementsFromStandard()
            const data = []

            structures.forEach(structure => {
                const name = structure.name
                const currentCount = structure.content.word_count || 0
                const countDisplay = structure.count || `${currentCount}字`
                
                // 获取标准要求
                const requirement = requirements[name]
                let standardRequirement = '-'
                let analysisResult = '无要求'
                
                if (requirement) {
                    if (requirement.min && requirement.max) {
                        standardRequirement = `${requirement.min}-${requirement.max}${requirement.unit}`
                    } else if (requirement.min) {
                        standardRequirement = `≥${requirement.min}${requirement.unit}`
                    }
                    
                    // 分析是否符合要求
                    if (requirement.min && requirement.max) {
                        if (currentCount >= requirement.min && currentCount <= requirement.max) {
                            analysisResult = '✅ 达标'
                        } else if (currentCount < requirement.min) {
                            analysisResult = '⚠️ 不足'
                        } else {
                            analysisResult = '⚠️ 过多'
                        }
                    } else if (requirement.min) {
                        analysisResult = currentCount >= requirement.min ? '✅ 达标' : '⚠️ 不足'
                    }
                }

                data.push({
                    structure: name,
                    standardRequirement,
                    currentSituation: countDisplay,
                    analysisResult
                })
            })

            return data
        }

        // 执行测试
        function runTests() {
            // 测试1: 检查标准要求
            const requirements = getWordRequirementsFromStandard()
            let requirementsHtml = '<table><tr><th>结构</th><th>最小值</th><th>最大值</th><th>单位</th></tr>'
            
            Object.entries(requirements).forEach(([name, req]) => {
                requirementsHtml += `<tr><td>${name}</td><td>${req.min || '-'}</td><td>${req.max || '-'}</td><td>${req.unit}</td></tr>`
            })
            requirementsHtml += '</table>'
            
            // 检查英文摘要是否包含在要求中
            const hasEnglishAbstract = requirements['英文摘要']
            if (hasEnglishAbstract) {
                requirementsHtml += '<div class="result success">✅ 英文摘要标准要求已正确添加！</div>'
                requirementsHtml += `<div class="result info">📋 英文摘要要求: ${hasEnglishAbstract.min}-${hasEnglishAbstract.max}${hasEnglishAbstract.unit}</div>`
            } else {
                requirementsHtml += '<div class="result error">❌ 英文摘要标准要求缺失！</div>'
            }
            
            document.getElementById('requirements-test').innerHTML = requirementsHtml

            // 测试2: 生成字数分析数据
            const analysisData = generateWordAnalysisData(mockDocumentStructures)
            let analysisHtml = '<table><tr><th>结构名称</th><th>标准要求</th><th>当前情况</th><th>分析结果</th></tr>'
            
            analysisData.forEach(item => {
                analysisHtml += `<tr><td>${item.structure}</td><td>${item.standardRequirement}</td><td>${item.currentSituation}</td><td>${item.analysisResult}</td></tr>`
            })
            analysisHtml += '</table>'
            
            // 检查英文摘要的分析结果
            const englishAbstractData = analysisData.find(item => item.structure === '英文摘要')
            if (englishAbstractData && englishAbstractData.standardRequirement !== '-') {
                analysisHtml += '<div class="result success">✅ 英文摘要字数分析功能正常！</div>'
                analysisHtml += `<div class="result info">📊 英文摘要分析: 标准要求${englishAbstractData.standardRequirement}，当前${englishAbstractData.currentSituation}，结果${englishAbstractData.analysisResult}</div>`
            } else {
                analysisHtml += '<div class="result error">❌ 英文摘要字数分析功能异常！</div>'
            }
            
            document.getElementById('analysis-test').innerHTML = analysisHtml
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', runTests)
    </script>
</body>
</html>
