"""
Word文档分析服务 - 系统管理API
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
from datetime import datetime, timedelta
import psutil
import os
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import crud
from app.database.session import get_db
from app.core.logging import logger
from app.core.response import success_response, error_response
from app.models.task import TaskStatus, TaskType
from app.models.paper_check import ComplianceStatus, ProblemSeverity

router = APIRouter()


class SystemStats(BaseModel):
    """系统统计信息"""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    pending_tasks: int
    processing_tasks: int
    total_documents: int
    total_problems: int
    avg_compliance_score: Optional[float] = None


class TaskTypeStats(BaseModel):
    """任务类型统计"""
    task_type: str
    count: int
    success_rate: float
    avg_processing_time: Optional[float] = None


class ProblemStats(BaseModel):
    """问题统计信息"""
    severity: str
    count: int
    percentage: float


class SystemPerformance(BaseModel):
    """系统性能信息"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    active_tasks: int
    queue_size: int
    uptime: str


class AnalysisReport(BaseModel):
    """分析报告摘要"""
    period: str
    total_analyses: int
    success_rate: float
    avg_compliance_score: float
    top_problems: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]


@router.get("/stats")
async def get_system_stats(session: AsyncSession = Depends(get_db)):
    """
    获取系统统计信息
    """
    try:
        # 获取任务统计
        total_tasks = await crud.count_tasks(session)
        completed_tasks = await crud.count_tasks(session, status=TaskStatus.COMPLETED)
        failed_tasks = await crud.count_tasks(session, status=TaskStatus.FAILED)
        pending_tasks = await crud.count_tasks(session, status=TaskStatus.PENDING)
        processing_tasks = await crud.count_tasks(session, status=TaskStatus.PROCESSING)
        
        # 获取文档统计
        total_documents = await crud.count_documents(session)
        
        # 获取问题统计
        total_problems = await crud.count_problems(session)
        
        # 计算平均合规分数
        avg_compliance_score = await crud.get_average_compliance_score(session)
        
        logger.info("获取系统统计信息")
        
        stats_data = {
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "pending_tasks": pending_tasks,
            "processing_tasks": processing_tasks,
            "total_documents": total_documents,
            "total_problems": total_problems,
            "avg_compliance_score": avg_compliance_score
        }
        
        return success_response(data=stats_data, message="获取系统统计信息成功")
        
    except Exception as e:
        logger.error(f"获取系统统计信息失败: {str(e)}")
        return error_response(message=f"获取统计信息失败: {str(e)}", code=500)


@router.get("/stats/tasks", response_model=List[TaskTypeStats])
async def get_task_type_stats(session: AsyncSession = Depends(get_db)):
    """
    获取任务类型统计信息
    """
    try:
        stats = []
        
        for task_type in TaskType:
            total_count = await crud.count_tasks(session, task_type=task_type)
            completed_count = await crud.count_tasks(
                session,
                task_type=task_type, 
                status=TaskStatus.COMPLETED
            )
            
            success_rate = (completed_count / total_count * 100) if total_count > 0 else 0
            
            # 计算平均处理时间
            avg_processing_time = await _calculate_avg_processing_time(session, task_type)
            
            stats.append(TaskTypeStats(
                task_type=task_type.value,
                count=total_count,
                success_rate=success_rate,
                avg_processing_time=avg_processing_time
            ))
        
        logger.info("获取任务类型统计信息")
        return stats
        
    except Exception as e:
        logger.error(f"获取任务类型统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务类型统计失败: {str(e)}")


@router.get("/stats/problems", response_model=List[ProblemStats])
async def get_problem_stats(session: AsyncSession = Depends(get_db)):
    """
    获取问题严重程度统计
    """
    try:
        total_problems = await crud.count_problems(session)
        stats = []
        
        for severity in ["critical", "warning", "info"]:
            count = await crud.count_problems(session, severity=severity)
            percentage = (count / total_problems * 100) if total_problems > 0 else 0
            
            stats.append(ProblemStats(
                severity=severity,
                count=count,
                percentage=percentage
            ))
        
        logger.info("获取问题统计信息")
        return stats
        
    except Exception as e:
        logger.error(f"获取问题统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取问题统计失败: {str(e)}")


@router.get("/performance")
async def get_system_performance(session: AsyncSession = Depends(get_db)):
    """
    获取系统性能信息
    """
    try:
        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Windows系统使用C盘
        try:
            disk_path = 'C:\\' if os.name == 'nt' else '/'
            disk = psutil.disk_usage(disk_path)
        except:
            # 如果获取磁盘信息失败，使用默认值
            disk = type('obj', (object,), {'percent': 0})()
        
        # 获取活跃任务数
        active_tasks = await crud.count_tasks(session, status=TaskStatus.PROCESSING)
        
        # 获取队列大小
        queue_size = await crud.count_tasks(session, status=TaskStatus.PENDING)
        
        # 计算系统运行时间
        boot_time = psutil.boot_time()
        uptime_seconds = datetime.now().timestamp() - boot_time
        uptime = str(timedelta(seconds=int(uptime_seconds)))
        
        logger.info("获取系统性能信息")
        
        performance_data = {
            "cpu_usage": cpu_usage,
            "memory_usage": memory.percent,
            "disk_usage": disk.percent,
            "active_tasks": active_tasks,
            "queue_size": queue_size,
            "uptime": uptime
        }
        
        return success_response(data=performance_data, message="获取系统性能信息成功")
        
    except Exception as e:
        logger.error(f"获取系统性能信息失败: {str(e)}")
        return error_response(message=f"获取性能信息失败: {str(e)}", code=500)


@router.get("/report", response_model=AnalysisReport)
async def get_analysis_report(
    period: str = Query("7d", description="统计周期 (1d, 7d, 30d, 90d)"),
    include_details: bool = Query(False, description="是否包含详细信息"),
    session: AsyncSession = Depends(get_db)
):
    """
    获取分析报告
    
    - **period**: 统计周期 (1d=1天, 7d=7天, 30d=30天, 90d=90天)
    - **include_details**: 是否包含详细信息
    """
    try:
        # 解析时间周期
        period_days = {
            "1d": 1,
            "7d": 7,
            "30d": 30,
            "90d": 90
        }.get(period, 7)
        
        start_date = datetime.now() - timedelta(days=period_days)
        
        # 获取周期内的分析统计
        total_analyses = await crud.count_tasks_since(session, start_date, status=TaskStatus.COMPLETED)
        total_tasks = await crud.count_tasks_since(session, start_date)
        
        success_rate = (total_analyses / total_tasks * 100) if total_tasks > 0 else 0
        
        # 获取平均合规分数
        avg_compliance_score = await crud.get_average_compliance_score_since(session, start_date)
        
        # 获取热门问题
        top_problems = await crud.get_top_problems_since(session, start_date, limit=10)
        
        # 性能指标
        performance_metrics = {
            "avg_processing_time": await _calculate_avg_processing_time_since(session, start_date),
            "peak_concurrent_tasks": await _calculate_peak_concurrent_tasks_since(session, start_date),
            "system_availability": await _calculate_system_availability_since(session, start_date)
        }
        
        logger.info(f"获取分析报告: {period}")
        
        return AnalysisReport(
            period=period,
            total_analyses=total_analyses,
            success_rate=success_rate,
            avg_compliance_score=avg_compliance_score or 0.0,
            top_problems=top_problems,
            performance_metrics=performance_metrics
        )
        
    except Exception as e:
        logger.error(f"获取分析报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析报告失败: {str(e)}")


@router.get("/logs")
async def get_system_logs(
    level: str = Query("INFO", description="日志级别"),
    limit: int = Query(100, ge=1, le=1000, description="返回条数"),
    module: Optional[str] = Query(None, description="模块筛选")
):
    """
    获取系统日志
    
    - **level**: 日志级别 (DEBUG, INFO, WARNING, ERROR)
    - **limit**: 返回条数
    - **module**: 模块筛选
    """
    try:
        # 读取日志文件
        log_file = "logs/word_service.log"
        
        if not os.path.exists(log_file):
            return {"logs": [], "message": "日志文件不存在"}
        
        logs = []
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 取最后的limit行
            recent_lines = lines[-limit:] if len(lines) > limit else lines
            
            for line in recent_lines:
                try:
                    import json
                    log_entry = json.loads(line.strip())
                    
                    # 级别筛选
                    if level and log_entry.get("level", "").upper() != level.upper():
                        continue
                    
                    # 模块筛选
                    if module and log_entry.get("module", "") != module:
                        continue
                    
                    logs.append(log_entry)
                    
                except json.JSONDecodeError:
                    # 跳过无法解析的行
                    continue
        
        logger.info(f"获取系统日志: {len(logs)}条")
        
        return {
            "logs": logs,
            "total": len(logs),
            "level": level,
            "module": module
        }
        
    except Exception as e:
        logger.error(f"获取系统日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统日志失败: {str(e)}")


@router.post("/maintenance/cleanup")
async def cleanup_old_data(
    days: int = Query(30, ge=1, description="清理多少天前的数据"),
    dry_run: bool = Query(True, description="是否为试运行模式")
):
    """
    清理旧数据
    
    - **days**: 清理多少天前的数据
    - **dry_run**: 是否为试运行模式（不实际删除）
    """
    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 统计要清理的数据
        old_tasks = await crud.count_tasks_before(cutoff_date)
        old_documents = await crud.count_documents_before(cutoff_date)
        
        result = {
            "dry_run": dry_run,
            "cutoff_date": cutoff_date.isoformat(),
            "tasks_to_cleanup": old_tasks,
            "documents_to_cleanup": old_documents,
            "cleaned": False
        }
        
        if not dry_run:
            # 实际执行清理
            cleaned_tasks = await crud.delete_tasks_before(cutoff_date)
            cleaned_documents = await crud.delete_documents_before(cutoff_date)
            
            result.update({
                "cleaned": True,
                "tasks_cleaned": cleaned_tasks,
                "documents_cleaned": cleaned_documents
            })
            
            logger.info(f"数据清理完成", 
                       tasks_cleaned=cleaned_tasks,
                       documents_cleaned=cleaned_documents)
        else:
            logger.info(f"数据清理试运行", 
                       tasks_to_cleanup=old_tasks,
                       documents_to_cleanup=old_documents)
        
        return result
        
    except Exception as e:
        logger.error(f"数据清理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据清理失败: {str(e)}")


@router.get("/info")
async def get_system_info():
    """
    获取系统基本信息
    """
    try:
        # 获取系统基本信息
        import platform
        import sys
        from app.core.config import settings
        
        info = {
            "service_name": "Word文档分析服务",
            "version": "1.0.0",
            "python_version": sys.version,
            "platform": platform.platform(),
            "debug_mode": settings.debug,
            "max_file_size": settings.files.max_size,
            "allowed_extensions": settings.files.allowed_extensions,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info("获取系统信息")
        return success_response(data=info, message="获取系统信息成功")
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return error_response(message=f"获取系统信息失败: {str(e)}", code=500)


@router.get("/config")
async def get_system_config():
    """
    获取系统配置信息
    """
    try:
        from app.core.config import settings
        
        # 返回非敏感的配置信息
        config_info = {
            "app_name": settings.app_name,
            "version": settings.version,
            "debug": settings.debug,
            "database": {
                "type": "PostgreSQL",
                "host": settings.database.url.split("@")[-1].split("/")[0] if "@" in settings.database.url else "unknown",
                "database": settings.database.url.split("/")[-1] if "/" in settings.database.url else "unknown",
                "pool_size": settings.database.pool_size,
                "max_overflow": settings.database.max_overflow
            },
            "redis": {
                "enabled": bool(settings.redis.url),
                "host": settings.redis.url.split("//")[-1].split(":")[0] if settings.redis.url else "unknown"
            },
            "logging": {
                "level": settings.logging.level,
                "format": settings.logging.format
            },
            "task_settings": {
                "max_concurrent_tasks": settings.tasks.max_concurrent,
                "task_timeout": settings.tasks.timeout,
                "retry_attempts": settings.tasks.retry_attempts
            }
        }
        
        logger.info("获取系统配置信息")
        return success_response(data=config_info, message="获取系统配置信息成功")
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


@router.get("/health")
async def get_system_health(session: AsyncSession = Depends(get_db)):
    """
    获取系统健康状态
    """
    try:
        # 基础健康检查项目
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        # 数据库连接检查
        try:
            total_tasks = await crud.count_tasks(session)
            health_status["checks"]["database"] = {
                "status": "up",
                "total_tasks": total_tasks
            }
        except Exception as e:
            health_status["checks"]["database"] = {
                "status": "down",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # 系统资源检查
        try:
            import psutil
            cpu_usage = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            health_status["checks"]["system_resources"] = {
                "status": "up",
                "cpu_usage": cpu_usage,
                "memory_usage": memory.percent
            }
            
            # 如果资源使用率过高，标记为警告
            if cpu_usage > 80 or memory.percent > 80:
                health_status["status"] = "warning"
                
        except Exception as e:
            health_status["checks"]["system_resources"] = {
                "status": "unknown",
                "error": str(e)
            }
        
        # 任务管理器检查
        try:
            from app.tasks.manager import task_manager
            pending_tasks = await crud.count_tasks(session, status=TaskStatus.PENDING)
            processing_tasks = await crud.count_tasks(session, status=TaskStatus.PROCESSING)
            
            health_status["checks"]["task_manager"] = {
                "status": "up",
                "pending_tasks": pending_tasks,
                "processing_tasks": processing_tasks
            }
            
        except Exception as e:
            health_status["checks"]["task_manager"] = {
                "status": "down",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        logger.info("系统健康检查完成", status=health_status["status"])
        
        return success_response(
            data=health_status,
            message=f"系统健康状态: {health_status['status']}"
        )
        
    except Exception as e:
        logger.error(f"系统健康检查失败: {str(e)}")
        return error_response(
            message=f"系统健康检查失败: {str(e)}", 
            code=500
        )


@router.post("/cleanup")
async def cleanup_system(
    days: int = Query(30, ge=1, description="清理多少天前的数据"),
    dry_run: bool = Query(True, description="是否为试运行模式"),
    include_logs: bool = Query(False, description="是否清理日志文件")
):
    """
    清理系统数据
    """
    try:
        cleanup_result = {
            "dry_run": dry_run,
            "days": days,
            "cleaned_items": {},
            "errors": []
        }
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 清理已完成的旧任务
        try:
            if dry_run:
                old_tasks_count = await crud.count_completed_tasks_before(cutoff_date)
                cleanup_result["cleaned_items"]["tasks"] = {
                    "count": old_tasks_count,
                    "action": "would_delete"
                }
            else:
                deleted_count = await crud.delete_completed_tasks_before(cutoff_date)
                cleanup_result["cleaned_items"]["tasks"] = {
                    "count": deleted_count,
                    "action": "deleted"
                }
        except Exception as e:
            cleanup_result["errors"].append(f"清理任务失败: {str(e)}")
        
        # 清理旧的问题记录
        try:
            if dry_run:
                old_problems_count = await crud.count_problems_before(cutoff_date)
                cleanup_result["cleaned_items"]["problems"] = {
                    "count": old_problems_count,
                    "action": "would_delete"
                }
            else:
                deleted_count = await crud.delete_problems_before(cutoff_date)
                cleanup_result["cleaned_items"]["problems"] = {
                    "count": deleted_count,
                    "action": "deleted"
                }
        except Exception as e:
            cleanup_result["errors"].append(f"清理问题记录失败: {str(e)}")
        
        # 清理临时文件
        try:
            import os
            temp_dir = "data/temp"
            if os.path.exists(temp_dir):
                temp_files = []
                for file in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, file)
                    if os.path.isfile(file_path):
                        # 检查文件修改时间
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        if file_mtime < cutoff_date:
                            temp_files.append(file)
                            if not dry_run:
                                os.remove(file_path)
                
                cleanup_result["cleaned_items"]["temp_files"] = {
                    "count": len(temp_files),
                    "action": "deleted" if not dry_run else "would_delete"
                }
            else:
                cleanup_result["cleaned_items"]["temp_files"] = {
                    "count": 0,
                    "action": "no_temp_dir"
                }
        except Exception as e:
            cleanup_result["errors"].append(f"清理临时文件失败: {str(e)}")
        
        # 清理日志文件（如果启用）
        if include_logs:
            try:
                log_dir = "logs"
                if os.path.exists(log_dir):
                    log_files = []
                    for file in os.listdir(log_dir):
                        if file.endswith('.log'):
                            file_path = os.path.join(log_dir, file)
                            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            if file_mtime < cutoff_date:
                                log_files.append(file)
                                if not dry_run:
                                    os.remove(file_path)
                    
                    cleanup_result["cleaned_items"]["log_files"] = {
                        "count": len(log_files),
                        "action": "deleted" if not dry_run else "would_delete"
                    }
                else:
                    cleanup_result["cleaned_items"]["log_files"] = {
                        "count": 0,
                        "action": "no_log_dir"
                    }
            except Exception as e:
                cleanup_result["errors"].append(f"清理日志文件失败: {str(e)}")
        
        # 记录清理操作
        action_type = "试运行" if dry_run else "实际清理"
        logger.info(f"系统清理{action_type}完成", 
                   days=days, 
                   cleaned_items=cleanup_result["cleaned_items"],
                   errors=cleanup_result["errors"])
        
        return success_response(
            data=cleanup_result,
            message=f"系统清理{action_type}完成"
        )
        
    except Exception as e:
        logger.error(f"系统清理失败: {str(e)}")
        return error_response(
            message=f"系统清理失败: {str(e)}", 
            code=500
        )


# =================== 性能计算辅助函数 ===================

async def _calculate_avg_processing_time(session: AsyncSession, task_type: Optional[str] = None) -> Optional[float]:
    """
    计算平均处理时间
    
    Args:
        session: 数据库会话
        task_type: 任务类型，如果为None则计算所有类型
        
    Returns:
        平均处理时间（秒），如果无数据返回None
    """
    try:
        from app.models.task import TaskStatus
        
        # 获取已完成的任务
        completed_tasks = await crud.get_tasks_with_times(
            session,
            status=TaskStatus.COMPLETED,
            task_type=task_type,
            limit=1000  # 限制获取最近1000个任务
        )
        
        if not completed_tasks:
            return None
        
        # 计算处理时间
        processing_times = []
        for task in completed_tasks:
            if task.created_at and task.completed_at:
                duration = (task.completed_at - task.created_at).total_seconds()
                if duration > 0:
                    processing_times.append(duration)
        
        if not processing_times:
            return None
        
        return sum(processing_times) / len(processing_times)
        
    except Exception as e:
        logger.error(f"计算平均处理时间失败: {str(e)}")
        return None


async def _calculate_avg_processing_time_since(session: AsyncSession, start_date: datetime) -> Optional[float]:
    """
    计算指定时间以来的平均处理时间
    
    Args:
        session: 数据库会话
        start_date: 开始时间
        
    Returns:
        平均处理时间（秒），如果无数据返回None
    """
    try:
        from app.models.task import TaskStatus
        
        # 获取指定时间以来的已完成任务
        completed_tasks = await crud.get_tasks_since(
            session,
            start_date,
            status=TaskStatus.COMPLETED,
            limit=1000
        )
        
        if not completed_tasks:
            return None
        
        # 计算处理时间
        processing_times = []
        for task in completed_tasks:
            if task.created_at and task.completed_at:
                duration = (task.completed_at - task.created_at).total_seconds()
                if duration > 0:
                    processing_times.append(duration)
        
        if not processing_times:
            return None
        
        return sum(processing_times) / len(processing_times)
        
    except Exception as e:
        logger.error(f"计算时段平均处理时间失败: {str(e)}")
        return None


async def _calculate_peak_concurrent_tasks_since(session: AsyncSession, start_date: datetime) -> Optional[int]:
    """
    计算指定时间以来的峰值并发任务数
    
    Args:
        session: 数据库会话
        start_date: 开始时间
        
    Returns:
        峰值并发任务数，如果无数据返回None
    """
    try:
        # 这里简化实现，使用当前活跃任务数作为近似值
        # 实际实现需要根据历史数据计算
        from app.models.task import TaskStatus
        
        active_tasks = await crud.count_tasks(session, status=TaskStatus.PROCESSING)
        
        # 返回活跃任务数的1.5倍作为估算的峰值
        return int(active_tasks * 1.5) if active_tasks > 0 else 1
        
    except Exception as e:
        logger.error(f"计算峰值并发任务数失败: {str(e)}")
        return None


async def _calculate_system_availability_since(session: AsyncSession, start_date: datetime) -> float:
    """
    计算指定时间以来的系统可用性
    
    Args:
        session: 数据库会话
        start_date: 开始时间
        
    Returns:
        系统可用性百分比 (0-100)
    """
    try:
        # 简化实现：基于任务成功率估算系统可用性
        total_tasks = await crud.count_tasks_since(session, start_date)
        
        if total_tasks == 0:
            return 99.9  # 默认可用性
        
        from app.models.task import TaskStatus
        failed_tasks = await crud.count_tasks_since(
            session, 
            start_date, 
            status=TaskStatus.FAILED
        )
        
        # 基于任务失败率计算可用性
        failure_rate = failed_tasks / total_tasks if total_tasks > 0 else 0
        availability = (1 - failure_rate) * 100
        
        # 确保可用性在合理范围内
        return max(95.0, min(99.99, availability))
        
    except Exception as e:
        logger.error(f"计算系统可用性失败: {str(e)}")
        return 99.0  # 默认可用性


# 🔥 新增：检测标准配置缓存
from functools import lru_cache
from typing import Dict, Any
import time

# 缓存配置
CACHE_TTL = 300  # 5分钟缓存
_cache_timestamps: Dict[str, float] = {}

@lru_cache(maxsize=32)
def _load_detection_standard_from_file(standard_id: str, cache_key: str) -> Dict[str, Any]:
    """
    从文件加载检测标准配置（带LRU缓存）

    Args:
        standard_id: 检测标准ID
        cache_key: 缓存键（包含时间戳以实现TTL）

    Returns:
        检测标准配置字典
    """
    import json
    from pathlib import Path

    # 构建检测标准文件路径
    config_dir = Path(__file__).parent.parent.parent.parent / "config" / "rules"
    standard_file = config_dir / f"{standard_id}.json"

    if not standard_file.exists():
        raise HTTPException(
            status_code=404,
            detail=f"检测标准 '{standard_id}' 不存在"
        )

    # 读取检测标准配置
    with open(standard_file, 'r', encoding='utf-8') as f:
        standard_config = json.load(f)

    logger.info(f"从文件加载检测标准配置: {standard_id}")
    return standard_config

def _get_cache_key(standard_id: str) -> str:
    """
    生成缓存键，包含时间戳以实现TTL
    """
    current_time = time.time()

    # 检查是否需要更新缓存键
    if standard_id not in _cache_timestamps:
        _cache_timestamps[standard_id] = current_time
    elif current_time - _cache_timestamps[standard_id] > CACHE_TTL:
        # 缓存过期，更新时间戳
        _cache_timestamps[standard_id] = current_time
        # 清除旧的缓存条目
        _load_detection_standard_from_file.cache_clear()

    # 返回包含时间戳的缓存键
    return f"{standard_id}_{int(_cache_timestamps[standard_id] // CACHE_TTL)}"

@router.get("/detection-standards/{standard_id}")
async def get_detection_standard(standard_id: str):
    """
    获取检测标准配置（带缓存优化）

    Args:
        standard_id: 检测标准ID，如 'hbkj_bachelor_2024'

    Returns:
        检测标准的完整配置信息
    """
    try:
        # 生成缓存键
        cache_key = _get_cache_key(standard_id)

        # 从缓存获取配置
        standard_config = _load_detection_standard_from_file(standard_id, cache_key)

        logger.info(f"成功获取检测标准配置: {standard_id} (缓存状态: {cache_key})")
        return success_response(standard_config)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取检测标准配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取检测标准配置失败: {str(e)}"
        )


@router.post("/detection-standards/cache/clear")
async def clear_detection_standards_cache():
    """
    清空检测标准配置缓存

    Returns:
        操作结果
    """
    try:
        # 清空LRU缓存
        _load_detection_standard_from_file.cache_clear()

        # 清空时间戳缓存
        _cache_timestamps.clear()

        logger.info("检测标准配置缓存已清空")
        return success_response({
            "message": "检测标准配置缓存已清空",
            "timestamp": time.time()
        })

    except Exception as e:
        logger.error(f"清空检测标准配置缓存失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"清空检测标准配置缓存失败: {str(e)}"
        )


@router.get("/detection-standards/cache/info")
async def get_detection_standards_cache_info():
    """
    获取检测标准配置缓存信息

    Returns:
        缓存统计信息
    """
    try:
        cache_info = _load_detection_standard_from_file.cache_info()

        return success_response({
            "cache_info": {
                "hits": cache_info.hits,
                "misses": cache_info.misses,
                "maxsize": cache_info.maxsize,
                "currsize": cache_info.currsize
            },
            "cached_standards": list(_cache_timestamps.keys()),
            "cache_ttl_seconds": CACHE_TTL,
            "timestamp": time.time()
        })

    except Exception as e:
        logger.error(f"获取检测标准配置缓存信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取检测标准配置缓存信息失败: {str(e)}"
        )


@router.get("/detection-standards")
async def list_detection_standards():
    """
    获取所有可用的检测标准列表

    Returns:
        可用检测标准的列表
    """
    try:
        import json
        from pathlib import Path

        # 获取检测标准目录
        config_dir = Path(__file__).parent.parent.parent.parent / "config" / "rules"

        if not config_dir.exists():
            return success_response([])

        standards = []

        # 遍历所有JSON文件
        for json_file in config_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                standard_info = {
                    "id": json_file.stem,
                    "name": config.get("name", json_file.stem),
                    "description": config.get("description", ""),
                    "version": config.get("version", "1.0"),
                    "document_structure_count": len(config.get("document_structure", []))
                }
                standards.append(standard_info)

            except Exception as e:
                logger.warning(f"读取检测标准文件失败 {json_file}: {str(e)}")
                continue

        logger.info(f"成功获取检测标准列表，共 {len(standards)} 个标准")
        return success_response(standards)

    except Exception as e:
        logger.error(f"获取检测标准列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取检测标准列表失败: {str(e)}"
        )