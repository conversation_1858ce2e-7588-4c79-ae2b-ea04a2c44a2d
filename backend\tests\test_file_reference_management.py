"""
测试文件引用计数管理功能

测试场景：
1. 上传文档，生成task1和document1
2. 重新分析，生成task2和document2（指向同一文件）
3. 删除task1，验证文件是否被保留
4. 删除task2，验证文件是否被删除
"""

import pytest
import os
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, patch

from app.database import crud
from app.api.v1.tasks import cancel_task
from app.api.v1.documents import delete_document, reanalyze_document
from app.models.task import TaskCreate, TaskStatus, TaskType
from app.models.document import DocumentCreate


class TestFileReferenceManagement:
    
    @pytest.fixture
    async def setup_test_data(self, session_mock):
        """设置测试数据"""
        # 创建临时测试文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
        temp_file.write(b'Test document content')
        temp_file.close()
        
        # 模拟用户
        user_id = "test_user_123"
        
        # 任务1数据
        task1_data = {
            "task_id": "task_001",
            "user_id": user_id,
            "filename": "test_doc.docx",
            "file_path": temp_file.name,
            "file_size": 1024,
            "task_type": TaskType.PAPER_CHECK,
            "status": TaskStatus.COMPLETED
        }
        
        # 任务2数据（重新分析，指向同一文件）
        task2_data = {
            "task_id": "reanalyze_002",
            "user_id": user_id,
            "filename": "重新分析_test_doc.docx",
            "file_path": temp_file.name,  # 同一个文件路径
            "file_size": 1024,
            "task_type": TaskType.PAPER_CHECK,
            "status": TaskStatus.COMPLETED
        }
        
        return {
            "temp_file": temp_file.name,
            "user_id": user_id,
            "task1": task1_data,
            "task2": task2_data
        }
    
    async def test_file_preserved_when_other_references_exist(self, setup_test_data, session_mock):
        """测试当存在其他引用时，文件被保留"""
        data = setup_test_data
        
        # 模拟数据库查询，返回有其他引用
        with patch('app.database.crud.get_task') as mock_get_task, \
             patch('app.database.crud.delete_task') as mock_delete_task, \
             patch.object(session_mock, 'execute') as mock_execute:
            
            # 设置返回值
            mock_get_task.return_value = AsyncMock(**data["task1"])
            mock_delete_task.return_value = True
            
            # 模拟查询结果：还有1个其他引用
            mock_result = AsyncMock()
            mock_result.fetchone.return_value.count = 1
            mock_execute.return_value = mock_result
            
            # 执行删除任务1
            with patch('os.path.exists', return_value=True), \
                 patch('os.remove') as mock_remove:
                
                from fastapi import BackgroundTasks
                background_tasks = BackgroundTasks()
                
                result = await cancel_task(
                    task_id="task_001",
                    background_tasks=background_tasks,
                    user_id=data["user_id"],
                    session=session_mock
                )
                
                # 验证文件没有被删除
                mock_remove.assert_not_called()
                
                # 验证返回结果
                assert result["data"]["file_deleted"] is False
                assert "其他任务引用" in result["data"]["message"]
    
    async def test_file_deleted_when_no_other_references(self, setup_test_data, session_mock):
        """测试当没有其他引用时，文件被删除"""
        data = setup_test_data
        
        # 模拟数据库查询，返回没有其他引用
        with patch('app.database.crud.get_task') as mock_get_task, \
             patch('app.database.crud.delete_task') as mock_delete_task, \
             patch.object(session_mock, 'execute') as mock_execute:
            
            # 设置返回值
            mock_get_task.return_value = AsyncMock(**data["task2"])
            mock_delete_task.return_value = True
            
            # 模拟查询结果：没有其他引用
            mock_result = AsyncMock()
            mock_result.fetchone.return_value.count = 0
            mock_execute.return_value = mock_result
            
            # 执行删除任务2
            with patch('os.path.exists', return_value=True), \
                 patch('os.remove') as mock_remove:
                
                from fastapi import BackgroundTasks
                background_tasks = BackgroundTasks()
                
                result = await cancel_task(
                    task_id="reanalyze_002",
                    background_tasks=background_tasks,
                    user_id=data["user_id"],
                    session=session_mock
                )
                
                # 验证文件被删除
                mock_remove.assert_called_once_with(data["temp_file"])
                
                # 验证返回结果
                assert result["data"]["file_deleted"] is True
                assert "文件已安全删除" in result["data"]["message"]
    
    async def test_reanalyze_with_missing_file(self, setup_test_data, session_mock):
        """测试重新分析时原文件不存在的情况"""
        data = setup_test_data
        
        # 模拟原始任务存在但文件不存在
        with patch('app.database.crud.get_task') as mock_get_task, \
             patch('app.database.crud.get_user_by_id') as mock_get_user:
            
            # 模拟任务存在
            mock_get_task.return_value = AsyncMock(**data["task1"])
            
            # 模拟用户有余额
            mock_user = AsyncMock()
            mock_user.check_balance = 5
            mock_get_user.return_value = mock_user
            
            # 模拟文件不存在
            with patch('pathlib.Path.exists', return_value=False):
                
                from app.api.v1.documents import AnalysisRequest
                request = AnalysisRequest(analysis_type="paper_check")
                
                # 期望抛出404异常
                with pytest.raises(Exception) as exc_info:
                    await reanalyze_document(
                        document_id="task_001",
                        request=request,
                        user_id=data["user_id"],
                        session=session_mock
                    )
                
                # 验证错误信息
                assert "原始文档文件不存在" in str(exc_info.value)
    
    async def test_document_delete_with_reference_count(self, setup_test_data, session_mock):
        """测试文档删除时的引用计数检查"""
        data = setup_test_data
        
        # 模拟文档和任务
        with patch('app.database.crud.get_document') as mock_get_doc, \
             patch('app.database.crud.get_task') as mock_get_task, \
             patch('app.database.crud.delete_task') as mock_delete_task, \
             patch('app.database.crud.delete_document') as mock_delete_doc, \
             patch.object(session_mock, 'execute') as mock_execute:
            
            # 设置返回值
            mock_document = AsyncMock()
            mock_document.task_id = "task_001"
            mock_get_doc.return_value = mock_document
            
            mock_get_task.return_value = AsyncMock(**data["task1"])
            mock_delete_task.return_value = True
            mock_delete_doc.return_value = True
            
            # 模拟查询结果：没有其他引用
            mock_result = AsyncMock()
            mock_result.fetchone.return_value.count = 0
            mock_execute.return_value = mock_result
            
            # 执行删除文档
            with patch('os.path.exists', return_value=True), \
                 patch('os.remove') as mock_remove:
                
                result = await delete_document(
                    document_id="doc_001",
                    user_id=data["user_id"],
                    session=session_mock
                )
                
                # 验证文件被删除
                mock_remove.assert_called_once_with(data["temp_file"])
                
                # 验证任务和文档记录都被删除
                mock_delete_task.assert_called_once_with(session_mock, "task_001")
                mock_delete_doc.assert_called_once_with(session_mock, "doc_001")
                
                # 验证返回结果
                assert result["data"]["file_deleted"] is True
                assert result["data"]["task_deleted"] is True
    
    def teardown_method(self):
        """清理测试文件"""
        # 这里可以添加清理临时文件的逻辑
        pass 