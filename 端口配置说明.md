# Word文档分析服务 - 端口配置说明

## 🌐 端口配置总览

### 生产环境端口分配

| 服务类型 | 服务名称 | 端口 | 协议 | 用途说明 |
|---------|---------|------|------|----------|
| 后端API | FastAPI应用 | 8000 | HTTP | 后端API服务 |
| 数据库 | PostgreSQL | 5432 | TCP | 主数据库 |
| 缓存 | Redis | 6379 | TCP | 缓存和会话存储 |
| 前端应用 | frontend-user | 3000 | HTTP | 统一前端Web应用（包含用户端和管理端） |
| 反向代理 | Nginx | 80/443 | HTTP/HTTPS | 反向代理和负载均衡 |

### 开发环境端口分配

| 服务类型 | 服务名称 | 端口 | 协议 | 用途说明 |
|---------|---------|------|------|----------|
| 后端API | FastAPI应用 | 8000 | HTTP | 开发模式热重载 |
| 数据库 | PostgreSQL | 5432 | TCP | 开发数据库 |
| 缓存 | Redis | 6379 | TCP | 开发缓存 |
| 前端应用 | Vite Dev Server | 3000 | HTTP | 统一前端开发服务器（用户端+管理端） |

### 详细端口配置

#### 🎯 后端服务 (Port 8000)
- **FastAPI应用**: `http://localhost:8000`
- **API文档**: `http://localhost:8000/docs`
- **健康检查**: `http://localhost:8000/health`

#### 🎨 前端应用 (Port 3000)
- **开发服务器**: `http://localhost:3000`
- **HMR端口**: 24678 (自动分配)
- **架构模式**: 统一开发环境（共享依赖和配置）
- **用户端功能**: 文档上传、检测结果查看、用户中心 (src/views/*.vue)
- **管理端功能**: 用户管理、订单管理、系统监控 (src/views/admin/*.vue)
- **共享资源**: 组件库、工具函数、API客户端、TypeScript类型

#### 🗄️ 数据服务
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`

### 端口配置文件

#### 前端应用 (frontend-user/vite.config.ts)
```typescript
export default defineConfig({
  server: {
    port: 3000,
    host: '0.0.0.0'
  },
  preview: {
    port: 3000
  }
})
```

### 端口冲突解决

#### Windows环境检查端口占用
```powershell
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :3001
netstat -ano | findstr :8000

# 终止进程
taskkill /PID <进程ID> /F
```

#### 端口配置优先级
1. **生产环境**: 使用环境变量配置
2. **开发环境**: 使用配置文件默认值
3. **端口冲突**: Vite自动递增端口号

### 防火墙配置

#### Windows防火墙规则
```powershell
# 允许FastAPI端口
netsh advfirewall firewall add rule name="FastAPI" dir=in action=allow protocol=TCP localport=8000

# 允许用户端前端端口
netsh advfirewall firewall add rule name="Frontend-User" dir=in action=allow protocol=TCP localport=3000

# 允许管理端前端端口
netsh advfirewall firewall add rule name="Frontend-Admin" dir=in action=allow protocol=TCP localport=3001
```

### 域名和SSL配置

#### 生产环境域名规划
- **用户端**: `https://paper-check.example.com` → Port 3000
- **管理端**: `https://admin.paper-check.example.com` → Port 3001
- **API服务**: `https://api.paper-check.example.com` → Port 8000

#### Nginx反向代理配置
```nginx
# 用户端前端
server {
    listen 80;
    server_name paper-check.example.com;
    location / {
        proxy_pass http://localhost:3000;
    }
}

# 管理端前端
server {
    listen 80;
    server_name admin.paper-check.example.com;
    location / {
        proxy_pass http://localhost:3001;
    }
}

# API服务
server {
    listen 80;
    server_name api.paper-check.example.com;
    location / {
        proxy_pass http://localhost:8000;
    }
}
```

### 开发环境启动顺序

1. **启动后端服务** (Port 8000)
   ```bash
   cd backend
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

2. **启动前端应用** (Port 3000)
   ```bash
   cd frontend/frontend-user
   npm run dev
   ```

### 注意事项

1. **端口独占**: 每个服务使用独立端口，避免冲突
2. **开发热重载**: 所有前端项目支持HMR热更新
3. **跨域配置**: 前端项目需要配置CORS代理到后端API
4. **安全考虑**: 生产环境建议使用HTTPS和防火墙规则
5. **监控告警**: 可以配置端口监控，及时发现服务异常

## 🌐 网络架构图

```
┌─────────────────────────────────────────────────────────┐
│                    网络端口架构                            │
├─────────────────────────────────────────────────────────┤
│  客户端浏览器                                              │
│  ├── http://localhost:5173 (开发环境)                    │
│  └── http://your-domain.com (生产环境)                   │
├─────────────────────────────────────────────────────────┤
│  Nginx 反向代理 (生产环境)                                 │
│  ├── :80 → 前端静态文件                                   │
│  ├── :443 → 前端静态文件 (HTTPS)                         │
│  └── /api/* → backend:8000                              │
├─────────────────────────────────────────────────────────┤
│  前端服务                                                 │
│  ├── Vite Dev Server :5173 (开发环境)                    │
│  └── 静态文件 (生产环境，由Nginx提供)                      │
├─────────────────────────────────────────────────────────┤
│  后端服务                                                 │
│  └── FastAPI :8000 (所有环境)                           │
├─────────────────────────────────────────────────────────┤
│  数据库服务                                               │
│  ├── PostgreSQL :5432                                   │
│  └── Redis :6379                                        │
└─────────────────────────────────────────────────────────┘
```

## 🔄 代理配置详解

### 开发环境代理流程
```
浏览器请求 → Vite DevServer(:5173) → 代理配置 → FastAPI(:8000)
    ↑                                                    ↓
http://localhost:5173/api/auth/login              http://localhost:8000/api/auth/login
```

### 生产环境代理流程
```
浏览器请求 → Nginx(:80/443) → 反向代理 → FastAPI(:8000)
    ↑                                           ↓
https://your-domain.com/api/auth/login    http://backend:8000/api/auth/login
```

## ⚠️ 注意事项

### 1. 端口冲突避免
- **8000端口**: 专门给后端FastAPI使用，不要用于其他服务
- **5173端口**: Vite默认端口，如冲突可在vite.config.ts中修改
- **5432端口**: PostgreSQL标准端口，本地开发确保不冲突
- **6379端口**: Redis标准端口，本地开发确保不冲突

### 2. 防火墙配置
```bash
# Windows防火墙 (生产环境)
netsh advfirewall firewall add rule name="FastAPI" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="HTTP" dir=in action=allow protocol=TCP localport=80
netsh advfirewall firewall add rule name="HTTPS" dir=in action=allow protocol=TCP localport=443

# 开发环境通常不需要配置防火墙
```

### 3. 环境变量配置
```bash
# 前端环境变量 (.env.development)
VITE_API_BASE_URL=http://localhost:8000
VITE_DEV_SERVER_PORT=5173

# 前端环境变量 (.env.production)
VITE_API_BASE_URL=https://your-domain.com

# 后端环境变量
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/word_service
REDIS_URL=redis://localhost:6379/0
```

## 🚀 部署检查清单

### 开发环境启动顺序
1. ✅ PostgreSQL (5432端口)
2. ✅ Redis (6379端口)
3. ✅ FastAPI后端 (8000端口)
4. ✅ Vue3前端 (5173端口)

### 生产环境启动顺序
1. ✅ PostgreSQL (5432端口)
2. ✅ Redis (6379端口)
3. ✅ FastAPI后端 (8000端口)
4. ✅ Nginx (80/443端口)

### 端口验证命令
```bash
# Windows检查端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :5173
netstat -ano | findstr :5432
netstat -ano | findstr :6379

# Linux检查端口占用
ss -tlnp | grep :8000
ss -tlnp | grep :5173
ss -tlnp | grep :5432
ss -tlnp | grep :6379
```

## 📋 常见问题

### Q1: 为什么前端代理配置指向8000端口？
**A**: 8000端口是后端FastAPI服务端口，前端需要通过代理将API请求转发到后端。这不是前端自己的端口，而是代理目标端口。

### Q2: 生产环境需要5173端口吗？
**A**: 不需要。生产环境中前端是静态文件，由Nginx在80/443端口提供服务。5173端口只在开发环境使用。

### Q3: 如何修改默认端口？
**A**: 
- FastAPI: 修改启动命令中的`--port`参数
- Vite: 在`vite.config.ts`中设置`server.port`
- Nginx: 修改配置文件中的`listen`指令

### Q4: Docker环境中端口映射如何配置？
**A**: 使用`ports`配置，格式为`宿主机端口:容器端口`，如`"8000:8000"`

---

## 📞 技术支持

如有端口配置相关问题，请：
1. 检查本文档的配置说明
2. 使用端口验证命令确认占用情况
3. 查看相关服务的启动日志
4. 联系技术支持团队

---

**更新时间**: 2025-01-22  
**文档版本**: v1.0  
**维护者**: Cursor AI Assistant