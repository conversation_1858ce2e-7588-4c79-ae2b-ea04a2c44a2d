<template>
  <!-- 使用BaseLayout组件 -->
  <BaseLayout
    title="任务中心"
    description="监控您的文档分析任务进度和状态"
  >
    <!-- 标题右侧操作按钮 -->
    <template #header-actions>
      <div class="flex space-x-3">
        <BaseButton 
          @click="refreshTasks" 
          variant="secondary"
          :disabled="isRefreshing"
          prepend-icon="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
        >
          <span class="flex items-center">
            <svg v-if="isRefreshing" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isRefreshing ? '刷新中...' : '刷新' }}
          </span>
        </BaseButton>
        <BaseButton 
          to="/upload" 
          variant="primary"
          prepend-icon="M12 6v6m0 0v6m0-6h6m-6 0H6"
        >
          新建任务
        </BaseButton>
      </div>
    </template>

        <!-- 统计卡片 - 移动端优化 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 mb-6 md:mb-8">
          <BaseCard class="text-center p-3 md:p-6">
              <div class="mx-auto h-10 w-10 md:h-12 md:w-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-2 md:mb-3">
                <svg class="h-5 w-5 md:h-6 md:w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <p class="text-xl md:text-2xl font-bold text-blue-600 dark:text-blue-400">{{ statistics.pending }}</p>
              <p class="text-xs md:text-sm text-gray-600 dark:text-gray-300">等待处理</p>
          </BaseCard>
          
          <BaseCard class="text-center p-3 md:p-6">
              <div class="mx-auto h-10 w-10 md:h-12 md:w-12 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center mb-2 md:mb-3">
                <svg class="h-5 w-5 md:h-6 md:w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <p class="text-xl md:text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ statistics.processing }}</p>
              <p class="text-xs md:text-sm text-gray-600 dark:text-gray-300">处理中</p>
          </BaseCard>
          
          <BaseCard class="text-center p-3 md:p-6">
              <div class="mx-auto h-10 w-10 md:h-12 md:w-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-2 md:mb-3">
                <svg class="h-5 w-5 md:h-6 md:w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <p class="text-xl md:text-2xl font-bold text-green-600 dark:text-green-400">{{ statistics.completed }}</p>
              <p class="text-xs md:text-sm text-gray-600 dark:text-gray-300">已完成</p>
          </BaseCard>
          
          <BaseCard class="text-center p-3 md:p-6">
              <div class="mx-auto h-10 w-10 md:h-12 md:w-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mb-2 md:mb-3">
                <svg class="h-5 w-5 md:h-6 md:w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <p class="text-xl md:text-2xl font-bold text-red-600 dark:text-red-400">{{ statistics.failed }}</p>
              <p class="text-xs md:text-sm text-gray-600 dark:text-gray-300">失败</p>
          </BaseCard>
        </div>

        <!-- 筛选和搜索栏 - 移动端优化 -->
        <BaseCard class="mb-6">
            <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-4 md:gap-4">
              <!-- 搜索框 -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 md:hidden">搜索任务</label>
                <BaseInput
                  v-model="searchTerm"
                  @input="filterTasks"
                  placeholder="搜索任务..." 
                  prepend-icon="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </div>
              
              <!-- 筛选器 - 移动端两列布局 -->
              <div class="grid grid-cols-2 gap-3 md:col-span-2 md:grid-cols-2 md:gap-4">
                <!-- 状态筛选 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 md:hidden">状态</label>
                  <BaseInput
                    v-model="statusFilter"
                    @change="filterTasks"
                    type="select"
                    :options="[
                      { value: '', label: '所有状态' },
                      { value: 'pending', label: '等待处理' },
                      { value: 'processing', label: '处理中' },
                      { value: 'completed', label: '已完成' },
                      { value: 'failed', label: '失败' }
                    ]"
                  />
                </div>
                
                <!-- 类型筛选 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 md:hidden">类型</label>
                  <BaseInput
                    v-model="typeFilter"
                    @change="filterTasks"
                    type="select"
                    :options="[
                      { value: '', label: '所有类型' },
                      { value: 'paper_check', label: '论文检测' },
                      { value: 'format_check', label: '格式检查' },
                      { value: 'structure_check', label: '结构分析' }
                    ]"
                  />
                </div>
              </div>
            </div>
        </BaseCard>

        <!-- 任务列表 -->
        <div class="space-y-3 md:space-y-4">
          <BaseCard 
            v-for="task in filteredTasks" 
            :key="task.task_id"
            :class="['task-item', task.status]"
          >
              <!-- 移动端和桌面端适配的任务卡片头部 -->
              <div class="flex items-start justify-between mb-2 md:mb-4 space-x-2 md:space-x-3">
                <div class="flex items-start space-x-2 md:space-x-3 min-w-0 flex-1">
                  <div :class="['file-icon flex-shrink-0', getFileExtension(task.filename)]">
                    {{ getFileExtension(task.filename).toUpperCase() }}
                  </div>
                  <div class="min-w-0 flex-1">
                    <h3 class="text-sm md:text-lg font-semibold text-gray-900 dark:text-white truncate leading-tight">{{ task.filename }}</h3>
                    <div class="text-xs md:text-sm text-gray-600 dark:text-gray-300 mt-0.5 md:mt-1">
                      <div class="flex items-center flex-wrap gap-x-1.5 md:gap-x-2 gap-y-1">
                        <span>{{ getTaskTypeLabel(task.task_type) }}</span>
                        <!-- 桌面端显示完整任务ID -->
                        <span class="hidden md:inline">•</span>
                        <span class="hidden md:inline">任务ID: {{ task.task_id }}</span>
                        <!-- 移动端显示简化任务ID -->
                        <span class="md:hidden text-gray-400">•</span>
                        <span class="md:hidden font-mono text-xs text-gray-500 dark:text-gray-400" :title="task.task_id">
                          {{ formatTaskId(task.task_id) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-1 md:space-x-2 flex-shrink-0">
                  <span :class="getStatusBadgeClass(task.status)">{{ getStatusLabel(task.status) }}</span>
                  <div class="relative">
                    <button 
                      @click="toggleTaskMenu(task.task_id)"
                      class="flex items-center justify-center w-6 h-6 md:w-8 md:h-8 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <svg class="h-4 w-4 md:h-5 md:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                      </svg>
                    </button>
                    <div 
                      v-show="activeTaskMenu === task.task_id" 
                      class="absolute right-0 mt-1 w-44 md:w-48 bg-white dark:bg-gray-800 rounded-md md:rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10"
                    >
                      <div class="py-1">
                        <button 
                          v-if="task.status === 'completed'"
                          @click="viewReport(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >查看报告</button>
                        <button 
                          v-if="task.status === 'completed'"
                          @click="downloadReport(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >下载报告</button>
                        <button 
                          v-if="task.status !== 'processing'"
                          @click="viewTaskDetail(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >{{ task.status === 'failed' ? '查看错误' : '查看详情' }}</button>
                        <button 
                          v-if="task.status === 'processing' || task.status === 'pending'"
                          @click="cancelTask(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >取消任务</button>
                        <button 
                          v-if="task.status === 'failed' || task.status === 'completed'"
                          @click="retryTask(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >{{ task.status === 'failed' ? '重试任务' : '重新分析' }}</button>
                        <button 
                          v-if="task.status !== 'processing'"
                          @click="deleteTask(task.task_id)" 
                          class="block w-full text-left px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >删除任务</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 进度条或状态信息 -->
              <div class="mb-2 md:mb-4">
                <template v-if="task.status === 'processing'">
                  <div class="flex justify-between text-xs md:text-sm text-gray-600 dark:text-gray-300 mb-1 md:mb-2">
                    <span>分析进度</span>
                    <span class="font-medium">{{ Math.round(task.progress || 0) }}%</span>
                  </div>
                  <div class="progress">
                    <div class="progress-bar" :style="{ width: (task.progress || 0) + '%' }"></div>
                  </div>
                </template>
                
                <template v-else-if="task.status === 'pending'">
                  <div class="flex justify-between text-xs md:text-sm text-gray-600 dark:text-gray-300 mb-1 md:mb-2">
                    <span>任务状态</span>
                    <span class="text-orange-600 dark:text-orange-400 font-medium">等待处理中</span>
                  </div>
                  <div class="progress">
                    <div class="progress-bar bg-orange-400" style="width: 10%"></div>
                  </div>
                </template>
                
                <template v-else-if="task.status === 'completed'">
                  <div class="flex justify-between text-xs md:text-sm text-gray-600 dark:text-gray-300 mb-1 md:mb-2">
                    <span>分析结果</span>
                    <span class="text-green-600 dark:text-green-400 font-medium">分析完成</span>
                  </div>
                  <div class="progress">
                    <div class="progress-bar bg-green-500" style="width: 100%"></div>
                  </div>
                </template>
                
                <template v-else-if="task.status === 'failed'">
                  <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-md p-2 md:p-3">
                    <div class="flex items-start space-x-2">
                      <svg class="h-3.5 w-3.5 md:h-5 md:w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <span class="text-xs md:text-sm text-red-800 dark:text-red-200 min-w-0 flex-1 break-words leading-tight">{{ task.error_message || '处理失败' }}</span>
                    </div>
                  </div>
                </template>
              </div>
              
              <!-- 任务信息 - 移动端紧凑布局 -->
              <div class="grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-3 text-xs md:text-sm text-gray-600 dark:text-gray-300">
                <div class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">创建时间</span>
                  <p class="truncate text-gray-800 dark:text-gray-200 mt-1 leading-tight font-medium">{{ formatDateTime(task.created_at) }}</p>
                </div>
                <div class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">文件大小</span>
                  <p class="truncate text-gray-800 dark:text-gray-200 mt-1 leading-tight font-medium">{{ formatFileSize(task.file_size) }}</p>
                </div>
                <div v-if="task.status === 'processing'" class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">当前进度</span>
                  <p class="text-blue-600 dark:text-blue-400 truncate mt-1 leading-tight font-medium">{{ Math.round(task.progress || 0) }}%</p>
                </div>
                <div v-else-if="task.status === 'pending'" class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">当前状态</span>
                  <p class="text-orange-600 dark:text-orange-400 truncate mt-1 leading-tight font-medium">等待处理</p>
                </div>
                <div v-else-if="task.status === 'completed'" class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">完成时间</span>
                  <p class="truncate text-gray-800 dark:text-gray-200 mt-1 leading-tight font-medium">{{ task.completed_at ? formatDateTime(task.completed_at) : '已完成' }}</p>
                </div>
                <div v-else-if="task.status === 'failed'" class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">更新时间</span>
                  <p class="truncate text-gray-800 dark:text-gray-200 mt-1 leading-tight font-medium">{{ formatDateTime(task.updated_at) }}</p>
                </div>
                <div v-if="task.status === 'completed'" class="min-w-0">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">报告状态</span>
                  <p class="text-green-600 dark:text-green-400 truncate mt-1 leading-tight font-medium">可下载</p>
                </div>
                <div v-else-if="task.status === 'failed' && task.error_message" class="min-w-0 col-span-2">
                  <span class="font-medium block leading-none text-gray-500 dark:text-gray-400">错误信息</span>
                  <p class="text-red-600 dark:text-red-400 truncate mt-1 leading-tight" :title="task.error_message">{{ task.error_message }}</p>
                </div>
              </div>
          </BaseCard>
        </div>

        <!-- 加载更多 - 移动端优化 -->
        <div class="text-center mt-6 md:mt-8">
          <BaseButton 
            @click="loadMoreTasks" 
            variant="secondary"
            class="w-full sm:w-auto"
          >
            加载更多任务
          </BaseButton>
        </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import { $confirm } from '@/utils/useConfirm'
import { TaskApi } from '@/services/taskApi'
import type { Task, TaskProgress } from '@/types'
import { $notify } from '@/utils/useNotifications'

// 状态管理
const themeStore = useThemeStore()
const userStore = useUserStore()
const router = useRouter()
const taskApi = new TaskApi()

// 响应式数据
const showUserMenu = ref(false)
const activeTaskMenu = ref<string | null>(null)
const searchTerm = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const updateInterval = ref<number | null>(null)
const isLoading = ref(true)
const isRefreshing = ref(false)
const error = ref<string | null>(null)

// 真实任务数据
const tasks = ref<Task[]>([])
const taskStatistics = ref({
  pending: 0,
  processing: 0,
  completed: 0,
  failed: 0
})
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)

// 计算属性
const statistics = computed(() => {
  return taskStatistics.value
})

const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    const matchesSearch = task.filename.toLowerCase().includes(searchTerm.value.toLowerCase())
    const matchesStatus = !statusFilter.value || task.status === statusFilter.value
    const matchesType = !typeFilter.value || task.task_type === typeFilter.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

// 方法
const toggleTheme = () => {
  themeStore.toggleTheme()
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const logout = () => {
  userStore.logout()
  router.push('/auth')
}

// 数据加载方法
const loadTasks = async (isRefresh = false) => {
  try {
    // 只在非刷新模式下设置loading状态
    if (!isRefresh) {
      isLoading.value = true
    }
    error.value = null
    
    // 获取任务列表
    const response = await taskApi.getTasks({
      page: currentPage.value,
      limit: pageSize.value,
      sort_by: 'created_at',
      sort_order: 'desc'
    })
    
    tasks.value = response.tasks || []
    totalTasks.value = response.pagination?.total || 0
    
    // 从任务列表计算统计信息
    calculateStatistics()
    
  } catch (err: any) {
    error.value = err.message || '加载任务失败'
    console.error('加载任务失败:', err)
    if (!isRefresh) {
      $notify.error('加载任务失败')
    }
    throw err // 重新抛出错误，让上层处理
  } finally {
    // 只在非刷新模式下重置loading状态
    if (!isRefresh) {
      isLoading.value = false
    }
  }
}

// 计算任务统计信息
const calculateStatistics = () => {
  const stats = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0
  }
  
  tasks.value.forEach(task => {
    switch (task.status) {
      case 'pending':
        stats.pending++
        break
      case 'processing':
        stats.processing++
        break
      case 'completed':
        stats.completed++
        break
      case 'failed':
      case 'cancelled':
        stats.failed++
        break
    }
  })
  
  taskStatistics.value = stats
}

const toggleTaskMenu = (taskId: string) => {
  activeTaskMenu.value = activeTaskMenu.value === taskId ? null : taskId
}

const filterTasks = () => {
  // 重置页码并重新加载第一页数据
  currentPage.value = 1
  loadTasks(false)
}

const getFileExtension = (filename: string) => {
  return filename.split('.').pop()?.toLowerCase() || 'file'
}

const getTaskTypeLabel = (type: string) => {
  const labels = {
    'paper_check': '论文检测',
    'format_check': '格式检查',
    'content_analysis': '结构分析'
  }
  return labels[type as keyof typeof labels] || type
}

const getStatusLabel = (status: string) => {
  const labels = {
    'pending': '队列中',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return labels[status as keyof typeof labels] || status
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    'pending': 'status-badge status-pending',
    'processing': 'status-badge status-processing',
    'completed': 'status-badge status-completed',
    'failed': 'status-badge status-failed',
    'cancelled': 'status-badge status-failed'
  }
  return classes[status as keyof typeof classes] || 'status-badge'
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', { 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatTaskId = (taskId: string) => {
  // 移动端显示简化的任务ID：保留前4位和后4位，中间用...连接
  if (taskId.length <= 12) return taskId
  return `${taskId.substring(0, 6)}...${taskId.substring(taskId.length - 4)}`
}

const refreshTasks = async () => {
  if (isRefreshing.value) return // 防止重复点击
  
  try {
    isRefreshing.value = true
    
    // 重置页码到第一页
    currentPage.value = 1
    
    // 重新加载任务数据
    await loadTasks(true)
    
    // 显示刷新成功提示
    $notify.success('任务列表已刷新')
    
  } catch (err: any) {
    console.error('刷新任务失败:', err)
    $notify.error('刷新失败，请重试')
  } finally {
    isRefreshing.value = false
  }
}

const viewReport = (taskId: string) => {
  router.push(`/document-detail/${taskId}`)
}

const downloadReport = async (taskId: string) => {
  try {
    // TODO: 实现下载报告功能
    $notify.success('报告下载功能开发中...')
  } catch (error) {
    console.error('下载报告失败:', error)
    $notify.error('下载报告失败')
  }
}

const viewTaskDetail = (taskId: string) => {
  // TODO: 实现查看任务详情功能
  console.log('查看任务详情', taskId)
  $notify.info('查看详情功能开发中...')
}

const cancelTask = async (taskId: string) => {
  const result = await $confirm.warning('确定要取消这个任务吗？取消后无法恢复。', {
    title: '取消任务',
    confirmText: '确定取消',
    cancelText: '保留任务'
  })
  if (result) {
    try {
      await taskApi.cancelTask(taskId)
      $notify.success('任务已取消')
      loadTasks(false) // 重新加载任务列表
    } catch (error) {
      console.error('取消任务失败:', error)
      $notify.error('取消任务失败')
    }
  }
}

const retryTask = async (taskId: string) => {
  try {
    const response = await taskApi.retryTask(taskId)
    $notify.success('任务已重新启动')
    loadTasks(false) // 重新加载任务列表
  } catch (error) {
    console.error('重试任务失败:', error)
    $notify.error('重试任务失败')
  }
}

const deleteTask = async (taskId: string) => {
  const result = await $confirm.danger('确定要删除这个任务吗？删除后无法恢复。', {
    title: '删除任务',
    confirmText: '确定删除',
    cancelText: '取消'
  })
  if (result) {
    try {
      await taskApi.deleteTask(taskId)
      $notify.success('任务已删除')
      loadTasks(false) // 重新加载任务列表
    } catch (error) {
      console.error('删除任务失败:', error)
      $notify.error('删除任务失败')
    }
  }
}

const loadMoreTasks = async () => {
  try {
    const nextPage = currentPage.value + 1
    
    // 获取下一页任务列表
    const response = await taskApi.getTasks({
      page: nextPage,
      limit: pageSize.value,
      sort_by: 'created_at',
      sort_order: 'desc'
    })
    
    // 如果有新数据，追加到现有列表
    if (response.tasks && response.tasks.length > 0) {
      tasks.value.push(...response.tasks)
      currentPage.value = nextPage
      // 重新计算统计信息
      calculateStatistics()
    } else {
      $notify.info('没有更多任务了')
    }
    
  } catch (error) {
    console.error('加载更多任务失败:', error)
    $notify.error('加载更多任务失败')
  }
}

const startRealtimeUpdates = () => {
  updateInterval.value = setInterval(() => {
    // 仅当有处理中的任务时才刷新
    if (tasks.value.some(task => task.status === 'processing')) {
      loadTasks(true) // 实时更新使用刷新模式，不显示loading
    }
  }, 3000)
}

const stopRealtimeUpdates = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (showUserMenu.value && !target.closest('.relative')) {
    showUserMenu.value = false
  }
  if (activeTaskMenu.value && !target.closest('.relative')) {
    activeTaskMenu.value = null
  }
}

// 生命周期
onMounted(() => {
  // 检查登录状态
  if (!userStore.isAuthenticated) {
    router.push('/auth')
    return
  }
  
  // 初始化数据
  loadTasks(false)
  
  document.addEventListener('click', handleClickOutside)
  startRealtimeUpdates()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  stopRealtimeUpdates()
})
</script>

<style scoped>
/* 文件图标样式 */
.file-icon {
  @apply w-9 h-9 md:w-12 md:h-12 flex items-center justify-center rounded-md text-xs md:text-sm font-medium text-white p-1 md:p-1.5;
  line-height: 1;
  font-size: 9px;
}

@media (min-width: 768px) {
  .file-icon {
    font-size: 11px;
  }
}

:deep(.form-group) {
  @apply mb-0;
}

/* 文件类型特定颜色 */
.file-icon.docx, .file-icon.doc {
  @apply bg-blue-500;
}

.file-icon.pdf {
  @apply bg-red-500;
}

.file-icon.txt {
  @apply bg-gray-500;
}

.file-icon.file {
  @apply bg-gray-400;
}

/* 状态徽章样式 */
.status-badge {
  @apply px-1.5 py-0.5 md:px-2 md:py-1 text-xs font-medium rounded-full;
}

.status-pending {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200;
}

.status-processing {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200;
}

.status-completed {
  @apply bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200;
}

.status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200;
}

/* 进度条样式 */
.progress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 md:h-2;
}

.progress-bar {
  @apply bg-blue-500 h-1.5 md:h-2 rounded-full transition-all duration-300;
}

/* 任务卡片状态指示 */
.task-item.completed {
  @apply border-l-4 border-green-500;
}

.task-item.processing {
  @apply border-l-4 border-blue-500;
}

.task-item.pending {
  @apply border-l-4 border-orange-500;
}

.task-item.failed {
  @apply border-l-4 border-red-500;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .task-item {
    @apply p-3 py-2.5;
  }
  
  /* 确保长文本正确换行 */
  .break-words {
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  /* 移动端按钮更小 */
  .task-item button {
    @apply text-xs;
  }
}


</style> 