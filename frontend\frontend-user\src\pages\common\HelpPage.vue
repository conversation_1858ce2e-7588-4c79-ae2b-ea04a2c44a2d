<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
            </div>
          </router-link>
          
          <!-- 返回按钮 -->
          <div class="flex items-center space-x-4">
            <router-link to="/" class="btn btn-secondary">
              返回首页
            </router-link>
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center mb-12">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          帮助中心
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-300">
          查找问题答案，了解如何使用我们的服务
        </p>
      </div>

      <!-- 搜索框 -->
      <div class="mb-12">
        <div class="relative max-w-md mx-auto">
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="搜索帮助内容..." 
            class="form-input pl-10 w-full"
          >
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </div>
        </div>
      </div>

      <!-- 帮助分类 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <div v-for="category in helpCategories" :key="category.id" class="card hover:shadow-lg transition-shadow cursor-pointer" @click="selectCategory(category)">
          <div class="card-body text-center">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
              <component :is="category.icon" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ category.name }}</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ category.description }}</p>
          </div>
        </div>
      </div>

      <!-- 常见问题 -->
      <div class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">常见问题</h2>
        <div class="space-y-4">
          <div v-for="faq in filteredFAQs" :key="faq.id" class="card">
            <div class="card-body">
              <button 
                @click="faq.expanded = !faq.expanded"
                class="w-full text-left flex items-center justify-between"
              >
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ faq.question }}</h3>
                <svg 
                  :class="['h-5 w-5 text-gray-500 transition-transform', { 'rotate-180': faq.expanded }]"
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              <div v-if="faq.expanded" class="mt-4 text-gray-600 dark:text-gray-300">
                {{ faq.answer }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系支持 -->
      <div class="card">
        <div class="card-body text-center">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">没有找到答案？</h2>
          <p class="text-gray-600 dark:text-gray-300 mb-6">我们的支持团队随时为您提供帮助</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="btn btn-primary">
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              邮件支持
            </a>
            <a href="tel:************" class="btn btn-secondary">
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              电话支持
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

// 搜索查询
const searchQuery = ref('')

// 帮助分类
const helpCategories = [
  {
    id: 1,
    name: '文档上传',
    description: '了解如何上传和管理文档',
    icon: 'svg'
  },
  {
    id: 2,
    name: '格式检测',
    description: '论文格式检测功能说明',
    icon: 'svg'
  },
  {
    id: 3,
    name: '账户管理',
    description: '管理您的账户和订阅',
    icon: 'svg'
  },
  {
    id: 4,
    name: '技术支持',
    description: '解决技术问题和故障',
    icon: 'svg'
  },
  {
    id: 5,
    name: '计费相关',
    description: '订阅、付费和发票问题',
    icon: 'svg'
  },
  {
    id: 6,
    name: 'API文档',
    description: '开发者接口文档',
    icon: 'svg'
  }
]

// 常见问题
const faqs = ref([
  {
    id: 1,
    question: '支持哪些文档格式？',
    answer: '我们支持DOC、DOCX、PDF和RTF格式的文档。建议使用DOCX格式以获得最佳的分析效果。',
    category: 'upload',
    expanded: false
  },
  {
    id: 2,
    question: '单个文档的最大大小限制是多少？',
    answer: '免费用户单个文档限制为10MB，付费用户可上传最大50MB的文档。',
    category: 'upload',
    expanded: false
  },
  {
    id: 3,
    question: '格式检测包含哪些内容？',
    answer: '格式检测包括：标题层级、段落格式、字体规范、行距设置、页面布局、图表格式、参考文献格式等多个方面的检查。',
    category: 'format',
    expanded: false
  },
  {
    id: 4,
    question: '如何修改个人信息？',
    answer: '登录后进入"个人中心"页面，您可以修改用户名、邮箱、电话等个人信息。邮箱修改需要验证新邮箱。',
    category: 'account',
    expanded: false
  },
  {
    id: 5,
    question: '如何取消订阅？',
    answer: '在"个人中心"的"订阅管理"中可以取消自动续费，订阅期到期后将自动降级为免费版本。',
    category: 'billing',
    expanded: false
  },
  {
    id: 6,
    question: '处理一个文档需要多长时间？',
    answer: '通常情况下，文档分析在1-3分钟内完成。复杂文档或高峰期可能需要更长时间。',
    category: 'technical',
    expanded: false
  }
])

// 筛选FAQ
const filteredFAQs = computed(() => {
  if (!searchQuery.value) return faqs.value
  
  return faqs.value.filter(faq => 
    faq.question.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 选择分类
const selectCategory = (category: any) => {
  console.log('选择分类:', category)
  // 这里可以实现分类筛选逻辑
}
</script>

<style scoped>
/* 帮助页面特有样式 */
</style> 