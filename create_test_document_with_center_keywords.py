"""
创建一个测试文档，其中中文关键词是居中对齐的，用于测试规则
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

def create_test_document():
    """创建测试文档"""
    
    print("📝 创建测试文档...")
    
    # 创建新文档
    doc = Document()
    
    # 添加标题
    title = doc.add_paragraph("新媒体技术对舞蹈编导创作手法的影响研究")
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.runs[0]
    title_run.font.size = Pt(16)
    title_run.font.bold = True
    
    # 添加作者
    author = doc.add_paragraph("李岩")
    author.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加空行
    doc.add_paragraph("")
    
    # 添加中文摘要
    abstract_title = doc.add_paragraph("摘要")
    abstract_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    abstract_title.runs[0].font.bold = True
    
    abstract_content = doc.add_paragraph(
        "随着新媒体技术的快速发展，虚拟现实（VR）、增强现实（AR）等技术在舞蹈编导创作中的应用越来越广泛。"
        "本文通过分析新媒体技术对舞蹈编导创作手法的影响，探讨了技术与艺术融合的新路径。"
        "研究发现，新媒体技术不仅丰富了舞蹈的表现形式，还为编导提供了更多创作可能性。"
    )
    
    # 🔥 关键：添加居中对齐的中文关键词（这应该被检测为错误）
    keywords = doc.add_paragraph("关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法")
    keywords.alignment = WD_ALIGN_PARAGRAPH.CENTER  # 设置为居中对齐，这应该被检测为错误
    
    # 添加空行
    doc.add_paragraph("")
    
    # 添加英文摘要
    english_abstract_title = doc.add_paragraph("ABSTRACT")
    english_abstract_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    english_abstract_title.runs[0].font.bold = True
    
    english_abstract_content = doc.add_paragraph(
        "With the rapid development of new media technology, virtual reality (VR), augmented reality (AR) "
        "and other technologies are increasingly used in dance choreography creation. This paper analyzes "
        "the impact of new media technology on dance choreography creation methods and explores new paths "
        "for the integration of technology and art."
    )
    
    # 添加英文关键词（也设置为居中，用于对比）
    english_keywords = doc.add_paragraph("Key Words: dance creation; virtual reality; augmented reality; short video communication; choreographic techniques")
    english_keywords.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加一些正文内容
    doc.add_paragraph("")
    
    chapter1 = doc.add_paragraph("第一章 绪论")
    chapter1.runs[0].font.bold = True
    chapter1.runs[0].font.size = Pt(14)
    
    section1_1 = doc.add_paragraph("1.1 研究背景")
    section1_1.runs[0].font.bold = True
    
    content1 = doc.add_paragraph(
        "近年来，随着科技的不断进步，新媒体技术在各个领域都得到了广泛应用。"
        "在舞蹈艺术领域，虚拟现实、增强现实等技术的引入，为传统的舞蹈编导创作带来了新的机遇和挑战。"
    )
    
    # 保存文档
    doc_path = "docs/test_center_keywords.docx"
    doc.save(doc_path)
    
    print(f"✅ 测试文档已创建: {doc_path}")
    print("📋 文档特点:")
    print("   - 中文关键词：居中对齐（应被检测为错误）")
    print("   - 英文关键词：居中对齐（用于对比）")
    print("   - 其他内容：正常格式")
    
    return doc_path

if __name__ == "__main__":
    create_test_document()
