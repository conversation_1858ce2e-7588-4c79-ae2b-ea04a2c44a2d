"""
论文检测报告生成器

该模块实现了检测结果报告生成功能，包括：
- 检测结果汇总
- 问题分类和统计
- 修复建议生成
- 多种格式输出（JSON、HTML、文本等）
"""

import asyncio
import json
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
import structlog

from .rule_engine import CheckResult, CheckSeverity
from .format_checker import FormatCheckResult, FormatIssue
from .structure_checker import StructureCheckResult, StructureIssue
from ..core.exceptions import DocumentAnalysisException


logger = structlog.get_logger(__name__)


class IssueSeverity(Enum):
    """问题严重程度（与CheckSeverity保持一致）"""
    ERROR = "error"         # 错误 - 必须修复
    WARNING = "warning"     # 警告 - 建议修复
    INFO = "info"          # 信息 - 提示性
    SUGGESTION = "suggestion"  # 建议 - 优化建议


class ReportFormat(Enum):
    """报告格式"""
    JSON = "json"
    HTML = "html"
    TEXT = "text"
    MARKDOWN = "markdown"


@dataclass
class IssueSummary:
    """问题摘要数据类"""
    issue_type: str                     # 问题类型
    severity: IssueSeverity             # 严重程度
    count: int                          # 问题数量
    messages: List[str] = field(default_factory=list)  # 问题消息列表
    suggestions: List[str] = field(default_factory=list)  # 修复建议列表
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "issue_type": self.issue_type,
            "severity": self.severity.value,
            "count": self.count,
            "messages": self.messages,
            "suggestions": self.suggestions
        }


@dataclass
class CheckReport:
    """检测报告数据类"""
    document_name: str                  # 文档名称
    check_time: datetime                # 检测时间
    total_checks: int                   # 总检查数
    passed_checks: int                  # 通过检查数
    failed_checks: int                  # 失败检查数
    
    # 问题统计
    total_issues: int = 0               # 总问题数
    error_count: int = 0                # 错误数
    warning_count: int = 0              # 警告数
    info_count: int = 0                 # 信息数
    suggestion_count: int = 0           # 建议数
    
    # 详细结果
    format_results: List[FormatCheckResult] = field(default_factory=list)
    structure_results: List[StructureCheckResult] = field(default_factory=list)
    rule_results: List[CheckResult] = field(default_factory=list)
    
    # 问题摘要
    issue_summaries: List[IssueSummary] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "document_name": self.document_name,
            "check_time": self.check_time.isoformat(),
            "total_checks": self.total_checks,
            "passed_checks": self.passed_checks,
            "failed_checks": self.failed_checks,
            "total_issues": self.total_issues,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "info_count": self.info_count,
            "suggestion_count": self.suggestion_count,
            "format_results": [result.to_dict() for result in self.format_results],
            "structure_results": [result.to_dict() for result in self.structure_results],
            "rule_results": [result.to_dict() for result in self.rule_results],
            "issue_summaries": [summary.to_dict() for summary in self.issue_summaries],
            "metadata": self.metadata
        }


class ReportGenerator:
    """检测报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.logger = structlog.get_logger(__name__)
    
    async def generate_report(self,
                             document_name: str,
                             check_results: List[CheckResult],
                             metadata: Optional[Dict[str, Any]] = None) -> CheckReport:
        """
        生成检测报告
        
        Args:
            document_name: 文档名称
            check_results: 检测结果列表
            metadata: 元数据
            
        Returns:
            检测报告
        """
        try:
            metadata = metadata or {}
            
            # 计算统计信息
            total_checks = len(check_results)
            passed_checks = sum(1 for r in check_results if r.passed)
            failed_checks = total_checks - passed_checks
            
            # 统计问题数量
            total_issues = sum(1 for r in check_results if not r.passed)
            error_count = sum(1 for r in check_results if not r.passed and r.severity == CheckSeverity.ERROR)
            warning_count = sum(1 for r in check_results if not r.passed and r.severity == CheckSeverity.WARNING)
            info_count = sum(1 for r in check_results if not r.passed and r.severity == CheckSeverity.INFO)
            
            # 创建报告
            report = CheckReport(
                document_name=document_name,
                check_time=datetime.now(),
                total_checks=total_checks,
                passed_checks=passed_checks,
                failed_checks=failed_checks,
                total_issues=total_issues,
                error_count=error_count,
                warning_count=warning_count,
                info_count=info_count,
                metadata=metadata
            )
            
            self.logger.info("检测报告生成完成",
                           document_name=document_name,
                           total_checks=total_checks,
                           total_issues=total_issues)
            
            return report
            
        except Exception as e:
            self.logger.error("报告生成失败", error=str(e))
            raise DocumentAnalysisException(f"报告生成失败: {str(e)}") from e
    
    async def export_report(self,
                           report: CheckReport,
                           output_path: str,
                           format_type: ReportFormat = ReportFormat.JSON) -> None:
        """
        导出报告到文件
        
        Args:
            report: 检测报告
            output_path: 输出文件路径
            format_type: 报告格式
        """
        try:
            if format_type == ReportFormat.JSON:
                await self._export_json_report(report, output_path)
            elif format_type == ReportFormat.HTML:
                await self._export_html_report(report, output_path)
            elif format_type == ReportFormat.TEXT:
                await self._export_text_report(report, output_path)
            elif format_type == ReportFormat.MARKDOWN:
                await self._export_markdown_report(report, output_path)
            else:
                raise DocumentAnalysisException(f"不支持的报告格式: {format_type.value}")
            
            self.logger.info("报告导出完成",
                           output_path=output_path,
                           format_type=format_type.value)
            
        except Exception as e:
            self.logger.error("报告导出失败",
                            output_path=output_path,
                            error=str(e))
            raise DocumentAnalysisException(f"报告导出失败: {str(e)}") from e
    
    async def _export_json_report(self, report: CheckReport, output_path: str) -> None:
        """导出JSON格式报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report.to_dict(), f, ensure_ascii=False, indent=2)
    
    async def _export_text_report(self, report: CheckReport, output_path: str) -> None:
        """导出文本格式报告"""
        content = self._generate_text_content(report)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _export_html_report(self, report: CheckReport, output_path: str) -> None:
        """导出HTML格式报告"""
        content = self._generate_html_content(report)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    async def _export_markdown_report(self, report: CheckReport, output_path: str) -> None:
        """导出Markdown格式报告"""
        content = self._generate_markdown_content(report)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_text_content(self, report: CheckReport) -> str:
        """生成文本格式内容"""
        lines = []
        lines.append("=" * 60)
        lines.append(f"论文检测报告 - {report.document_name}")
        lines.append("=" * 60)
        lines.append(f"检测时间: {report.check_time.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # 总体统计
        lines.append("总体统计:")
        lines.append(f"  总检查项: {report.total_checks}")
        lines.append(f"  通过检查: {report.passed_checks}")
        lines.append(f"  失败检查: {report.failed_checks}")
        lines.append(f"  通过率: {report.passed_checks/report.total_checks*100:.1f}%" if report.total_checks > 0 else "  通过率: 0%")
        lines.append("")
        
        # 问题统计
        lines.append("问题统计:")
        lines.append(f"  总问题数: {report.total_issues}")
        lines.append(f"  错误: {report.error_count}")
        lines.append(f"  警告: {report.warning_count}")
        lines.append(f"  信息: {report.info_count}")
        lines.append(f"  建议: {report.suggestion_count}")
        lines.append("")
        
        # 问题详情
        if report.issue_summaries:
            lines.append("问题详情:")
            for summary in report.issue_summaries:
                lines.append(f"  [{summary.severity.value.upper()}] {summary.issue_type} ({summary.count}个)")
                for message in summary.messages[:3]:  # 只显示前3个消息
                    lines.append(f"    - {message}")
                if len(summary.messages) > 3:
                    lines.append(f"    ... 还有{len(summary.messages) - 3}个问题")
                lines.append("")
        
        return "\n".join(lines)
    
    def _generate_html_content(self, report: CheckReport) -> str:
        """生成HTML格式内容"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文检测报告 - {report.document_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat-box {{ background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }}
        .issue {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
        .error {{ border-left-color: #dc3545; }}
        .warning {{ border-left-color: #ffc107; }}
        .info {{ border-left-color: #17a2b8; }}
        .suggestion {{ border-left-color: #28a745; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>论文检测报告</h1>
        <p><strong>文档名称:</strong> {report.document_name}</p>
        <p><strong>检测时间:</strong> {report.check_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>检查统计</h3>
            <p>总检查项: {report.total_checks}</p>
            <p>通过检查: {report.passed_checks}</p>
            <p>失败检查: {report.failed_checks}</p>
        </div>
        <div class="stat-box">
            <h3>问题统计</h3>
            <p>总问题数: {report.total_issues}</p>
            <p>错误: {report.error_count}</p>
            <p>警告: {report.warning_count}</p>
            <p>信息: {report.info_count}</p>
        </div>
    </div>
    
    <h2>问题详情</h2>
"""
        
        for summary in report.issue_summaries:
            severity_class = summary.severity.value
            html += f"""
    <div class="issue {severity_class}">
        <h4>[{summary.severity.value.upper()}] {summary.issue_type} ({summary.count}个)</h4>
        <ul>
"""
            for message in summary.messages:
                html += f"            <li>{message}</li>\n"
            
            html += "        </ul>\n    </div>\n"
        
        html += """
</body>
</html>
"""
        return html
    
    def _generate_markdown_content(self, report: CheckReport) -> str:
        """生成Markdown格式内容"""
        lines = []
        lines.append(f"# 论文检测报告 - {report.document_name}")
        lines.append("")
        lines.append(f"**检测时间:** {report.check_time.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # 总体统计
        lines.append("## 总体统计")
        lines.append("")
        lines.append(f"- **总检查项:** {report.total_checks}")
        lines.append(f"- **通过检查:** {report.passed_checks}")
        lines.append(f"- **失败检查:** {report.failed_checks}")
        pass_rate = report.passed_checks/report.total_checks*100 if report.total_checks > 0 else 0
        lines.append(f"- **通过率:** {pass_rate:.1f}%")
        lines.append("")
        
        # 问题统计
        lines.append("## 问题统计")
        lines.append("")
        lines.append(f"- **总问题数:** {report.total_issues}")
        lines.append(f"- **错误:** {report.error_count}")
        lines.append(f"- **警告:** {report.warning_count}")
        lines.append(f"- **信息:** {report.info_count}")
        lines.append(f"- **建议:** {report.suggestion_count}")
        lines.append("")
        
        # 问题详情
        if report.issue_summaries:
            lines.append("## 问题详情")
            lines.append("")
            for summary in report.issue_summaries:
                lines.append(f"### [{summary.severity.value.upper()}] {summary.issue_type} ({summary.count}个)")
                lines.append("")
                for message in summary.messages:
                    lines.append(f"- {message}")
                lines.append("")
        
        return "\n".join(lines) 