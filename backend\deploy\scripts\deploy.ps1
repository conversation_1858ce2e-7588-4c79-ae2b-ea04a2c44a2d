# ==================================================
# Word文档分析服务 - 一键部署脚本
# ==================================================

param(
    [string]$Environment = "production",
    [string]$Version = "latest",
    [switch]$Build = $false,
    [switch]$Monitoring = $false,
    [switch]$Force = $false,
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
Word文档分析服务 - 一键部署脚本

用法:
    .\deploy.ps1 [参数]

参数:
    -Environment    部署环境 (production/development/testing) [默认: production]
    -Version        镜像版本标签 [默认: latest]
    -Build          是否重新构建镜像
    -Monitoring     是否启用监控服务 (Prometheus + Grafana)
    -Force          强制重新部署 (停止并删除现有容器)
    -Help           显示此帮助信息

示例:
    .\deploy.ps1                                    # 生产环境部署
    .\deploy.ps1 -Environment development -Build    # 开发环境部署并构建
    .\deploy.ps1 -Monitoring -Force                 # 强制部署并启用监控
"@ -ForegroundColor Green
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "========================================" -ForegroundColor Green
Write-Host "Word文档分析服务 - 一键部署" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "版本: $Version" -ForegroundColor Yellow
Write-Host "构建镜像: $Build" -ForegroundColor Yellow
Write-Host "启用监控: $Monitoring" -ForegroundColor Yellow
Write-Host "强制部署: $Force" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

# 检查Docker环境
Write-Host "检查Docker环境..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "Docker版本: $dockerVersion" -ForegroundColor Green
    
    $composeVersion = docker-compose --version
    Write-Host "Docker Compose版本: $composeVersion" -ForegroundColor Green
} catch {
    Write-Error "Docker或Docker Compose未安装或未正确配置"
    exit 1
}

# 检查必要文件
$requiredFiles = @(
    "Dockerfile",
    "docker-compose.yml",
    "requirements.txt",
    "config.yaml"
)

Write-Host "检查必要文件..." -ForegroundColor Yellow
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Error "必要文件不存在: $file"
        exit 1
    }
    Write-Host "✓ $file" -ForegroundColor Green
}

# 创建必要目录
$requiredDirs = @(
    "data",
    "logs",
    "data/uploads",
    "data/temp",
    "data/images",
    "data/reports",
    "data/backups"
)

Write-Host "创建必要目录..." -ForegroundColor Yellow
foreach ($dir in $requiredDirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "创建目录: $dir" -ForegroundColor Green
    }
}

# 设置环境变量
Write-Host "设置环境变量..." -ForegroundColor Yellow
$env:ENVIRONMENT = $Environment
$env:VERSION = $Version

# 如果指定了强制部署，先停止并删除现有容器
if ($Force) {
    Write-Host "强制停止现有服务..." -ForegroundColor Yellow
    try {
        docker-compose down --remove-orphans
        Write-Host "现有服务已停止" -ForegroundColor Green
    } catch {
        Write-Warning "停止现有服务时出现错误: $($_.Exception.Message)"
    }
}

# 构建镜像（如果需要）
if ($Build) {
    Write-Host "构建Docker镜像..." -ForegroundColor Yellow
    try {
        if ($Environment -eq "development") {
            docker-compose build --target development word-service
        } else {
            docker-compose build --target production word-service
        }
        Write-Host "镜像构建完成" -ForegroundColor Green
    } catch {
        Write-Error "镜像构建失败: $($_.Exception.Message)"
        exit 1
    }
}

# 准备Docker Compose命令
$composeCmd = "docker-compose"
$composeArgs = @("up", "-d")

# 根据环境选择服务
if ($Environment -eq "development") {
    $composeArgs += @("word-service", "redis")
} else {
    $composeArgs += @("word-service", "redis", "nginx")
}

# 如果启用监控
if ($Monitoring) {
    $composeArgs += @("--profile", "monitoring")
}

# 启动服务
Write-Host "启动服务..." -ForegroundColor Yellow
try {
    & $composeCmd $composeArgs
    Write-Host "服务启动成功" -ForegroundColor Green
} catch {
    Write-Error "服务启动失败: $($_.Exception.Message)"
    exit 1
}

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 健康检查
Write-Host "执行健康检查..." -ForegroundColor Yellow
$maxRetries = 10
$retryCount = 0

while ($retryCount -lt $maxRetries) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:8000/health" -Method Get -TimeoutSec 5
        if ($response.success -eq $true) {
            Write-Host "✓ 服务健康检查通过" -ForegroundColor Green
            break
        }
    } catch {
        $retryCount++
        Write-Host "健康检查失败，重试 $retryCount/$maxRetries..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
}

if ($retryCount -eq $maxRetries) {
    Write-Warning "健康检查失败，但服务可能仍在启动中"
}

# 显示服务状态
Write-Host "========================================" -ForegroundColor Green
Write-Host "部署完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 显示访问地址
Write-Host "服务访问地址:" -ForegroundColor Cyan
Write-Host "  主服务: http://localhost:8000" -ForegroundColor White
Write-Host "  API文档: http://localhost:8000/docs" -ForegroundColor White
Write-Host "  健康检查: http://localhost:8000/health" -ForegroundColor White

if ($Environment -ne "development") {
    Write-Host "  Nginx代理: http://localhost" -ForegroundColor White
}

if ($Monitoring) {
    Write-Host "  Prometheus: http://localhost:9090" -ForegroundColor White
    Write-Host "  Grafana: http://localhost:3000 (admin/admin)" -ForegroundColor White
}

# 显示容器状态
Write-Host "`n容器状态:" -ForegroundColor Cyan
docker-compose ps

# 显示日志查看命令
Write-Host "`n有用的命令:" -ForegroundColor Cyan
Write-Host "  查看日志: docker-compose logs -f word-service" -ForegroundColor White
Write-Host "  停止服务: docker-compose down" -ForegroundColor White
Write-Host "  重启服务: docker-compose restart word-service" -ForegroundColor White
Write-Host "  进入容器: docker-compose exec word-service powershell" -ForegroundColor White

Write-Host "`n部署完成！" -ForegroundColor Green
