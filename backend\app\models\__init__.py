"""
Word文档分析服务 - 数据模型包
"""

# 任务相关模型
from .task import (
    TaskStatus,
    TaskType,
    TaskBase,
    TaskCreate,
    TaskUpdate,
    TaskInDB,
    Task
)

# 文档相关模型
from .document import (
    ContentElementType,
    DocumentBase,
    DocumentCreate,
    DocumentInDB,
    Document,
    ContentElementBase,
    ContentElementCreate,
    ContentElementInDB,
    ContentElement,
    ImageBase,
    ImageCreate,
    ImageInDB,
    Image
)

# 论文检测相关模型
from .paper_check import (
    ComplianceStatus,
    ProblemSeverity,
    ProblemCategory,
    PaperCheckResultBase,
    PaperCheckResultCreate,
    PaperCheckResultInDB,
    PaperCheckResult,
    ProblemBase,
    ProblemCreate,
    ProblemInDB,
    Problem,
    ProblemFragment,
    PaperStandard,
    CheckOptions
)

# 检测结果相关模型
from .check_result import (
    CheckSeverity,
    IssueType,
    CheckIssue,
    CheckResult,
    CheckIssueModel,
    CheckResultModel,
    PaperCheckReport,
    create_check_issue,
    create_check_result
)

# 规则相关模型
from .rule import (
    RuleType,
    RuleStatus,
    CheckRule,
    CheckRuleModel,
    RuleSetModel,
    create_check_rule,
    load_rule_from_dict
)

__all__ = [
    # 任务模型
    "TaskStatus",
    "TaskType",
    "TaskBase", 
    "TaskCreate",
    "TaskUpdate",
    "TaskInDB",
    "Task",
    
    # 文档模型
    "ContentElementType",
    "DocumentBase",
    "DocumentCreate", 
    "DocumentInDB",
    "Document",
    "ContentElementBase",
    "ContentElementCreate",
    "ContentElementInDB", 
    "ContentElement",
    "ImageBase",
    "ImageCreate",
    "ImageInDB",
    "Image",
    
    # 论文检测模型
    "ComplianceStatus",
    "ProblemSeverity",
    "ProblemCategory", 
    "PaperCheckResultBase",
    "PaperCheckResultCreate",
    "PaperCheckResultInDB",
    "PaperCheckResult",
    "ProblemBase",
    "ProblemCreate",
    "ProblemInDB",
    "Problem",
    "ProblemFragment",
    "PaperStandard",
    "CheckOptions",
    
    # 检测结果模型
    "CheckSeverity",
    "IssueType",
    "CheckIssue",
    "CheckResult",
    "CheckIssueModel",
    "CheckResultModel",
    "PaperCheckReport",
    "create_check_issue",
    "create_check_result",
    
    # 规则模型
    "RuleType",
    "RuleStatus",
    "CheckRule",
    "CheckRuleModel",
    "RuleSetModel",
    "create_check_rule",
    "load_rule_from_dict"
] 