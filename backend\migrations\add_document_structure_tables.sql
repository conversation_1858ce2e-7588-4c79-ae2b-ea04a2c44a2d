-- 添加文档结构统计表
-- 执行时间: 2024-12-19

-- 1. 创建文档结构统计表
CREATE TABLE IF NOT EXISTS document_structures (
    structure_id VARCHAR(50) PRIMARY KEY,
    document_id VARCHAR(50) NOT NULL,
    structure_name VARCHAR(200) NOT NULL,
    structure_type VARCHAR(20) NOT NULL CHECK (structure_type IN ('standard', 'non-standard', 'actual')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'missing', 'extra')),
    word_count INTEGER DEFAULT 0 CHECK (word_count >= 0),
    reference_count INTEGER DEFAULT 0 CHECK (reference_count >= 0),
    page_number INTEGER CHECK (page_number > 0),
    paragraph_index INTEGER CHECK (paragraph_index >= 0),
    style_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY(document_id) REFERENCES documents(document_id) ON DELETE CASCADE
);

-- 2. 创建文档内容缓存表
CREATE TABLE IF NOT EXISTS document_content_cache (
    cache_id VARCHAR(50) PRIMARY KEY,
    document_id VARCHAR(50) NOT NULL,
    content_type VARCHAR(50) NOT NULL DEFAULT 'full_content',
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT DEFAULT 0 CHECK (file_size >= 0),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    FOREIGN KEY(document_id) REFERENCES documents(document_id) ON DELETE CASCADE,
    UNIQUE(document_id, content_type)
);

-- 3. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_document_structures_document_id ON document_structures(document_id);
CREATE INDEX IF NOT EXISTS idx_document_structures_type ON document_structures(structure_type);
CREATE INDEX IF NOT EXISTS idx_document_structures_status ON document_structures(status);
CREATE INDEX IF NOT EXISTS idx_document_content_cache_document_id ON document_content_cache(document_id);

-- 4. 添加注释
COMMENT ON TABLE document_structures IS '文档结构统计表，存储每个结构的详细统计信息';
COMMENT ON COLUMN document_structures.structure_name IS '结构名称，如"封面"、"中文摘要"等';
COMMENT ON COLUMN document_structures.word_count IS '该结构的字数统计';
COMMENT ON COLUMN document_structures.reference_count IS '参考文献条数（仅对参考文献结构有效）';

COMMENT ON TABLE document_content_cache IS '文档内容缓存表，存储文档的原始内容文件路径';
COMMENT ON COLUMN document_content_cache.file_path IS '缓存文件的完整路径';
COMMENT ON COLUMN document_content_cache.content_type IS '内容类型，如full_content、formatted_content等';
