/**
 * 统一存储工具类
 * 提供类型安全的本地存储操作
 */

import { logger } from './logger'

interface StorageItem<T = any> {
  value: T
  timestamp: number
  expires?: number // 过期时间（毫秒）
}

type StorageType = 'local' | 'session'

class Storage {
  private getStorage(type: StorageType): globalThis.Storage {
    return type === 'local' ? localStorage : sessionStorage
  }

  private createKey(key: string): string {
    return `wms_${key}` // 添加应用前缀
  }

  /**
   * 设置存储项
   */
  set<T>(key: string, value: T, options?: {
    type?: StorageType
    expires?: number // 过期时间（毫秒）
  }): void {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)
    const finalKey = this.createKey(key)

    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        expires: options?.expires ? Date.now() + options.expires : undefined
      }

      storage.setItem(finalKey, JSON.stringify(item))
      logger.debug(`Storage set: ${key}`, { type: storageType, hasExpires: !!options?.expires })
    } catch (error) {
      logger.error(`Failed to set storage item: ${key}`, error)
    }
  }

  /**
   * 获取存储项
   */
  get<T>(key: string, options?: {
    type?: StorageType
    defaultValue?: T
  }): T | null {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)
    const finalKey = this.createKey(key)

    try {
      const raw = storage.getItem(finalKey)
      if (!raw) {
        return options?.defaultValue ?? null
      }

      const item: StorageItem<T> = JSON.parse(raw)
      
      // 检查是否过期
      if (item.expires && Date.now() > item.expires) {
        this.remove(key, { type: storageType })
        logger.debug(`Storage item expired: ${key}`)
        return options?.defaultValue ?? null
      }

      return item.value
    } catch (error) {
      logger.error(`Failed to get storage item: ${key}`, error)
      return options?.defaultValue ?? null
    }
  }

  /**
   * 删除存储项
   */
  remove(key: string, options?: { type?: StorageType }): void {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)
    const finalKey = this.createKey(key)

    try {
      storage.removeItem(finalKey)
      logger.debug(`Storage removed: ${key}`)
    } catch (error) {
      logger.error(`Failed to remove storage item: ${key}`, error)
    }
  }

  /**
   * 检查存储项是否存在
   */
  has(key: string, options?: { type?: StorageType }): boolean {
    return this.get(key, options) !== null
  }

  /**
   * 清除所有应用相关的存储项
   */
  clear(options?: { type?: StorageType }): void {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)

    try {
      const keys = Object.keys(storage).filter(key => key.startsWith('wms_'))
      keys.forEach(key => storage.removeItem(key))
      logger.info(`Storage cleared: ${keys.length} items removed`)
    } catch (error) {
      logger.error('Failed to clear storage', error)
    }
  }

  /**
   * 获取所有应用相关的存储项
   */
  getAll(options?: { type?: StorageType }): Record<string, any> {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)
    const result: Record<string, any> = {}

    try {
      Object.keys(storage).forEach(key => {
        if (key.startsWith('wms_')) {
          const originalKey = key.replace('wms_', '')
          result[originalKey] = this.get(originalKey, { type: storageType })
        }
      })
    } catch (error) {
      logger.error('Failed to get all storage items', error)
    }

    return result
  }

  /**
   * 获取存储使用情况
   */
  getUsage(options?: { type?: StorageType }): {
    used: number
    total: number
    percentage: number
  } {
    const storageType = options?.type || 'local'
    const storage = this.getStorage(storageType)

    try {
      let used = 0
      Object.keys(storage).forEach(key => {
        if (key.startsWith('wms_')) {
          used += storage.getItem(key)?.length || 0
        }
      })

      // 估算总容量（通常为5MB）
      const total = 5 * 1024 * 1024
      const percentage = Math.round((used / total) * 100)

      return { used, total, percentage }
    } catch (error) {
      logger.error('Failed to get storage usage', error)
      return { used: 0, total: 0, percentage: 0 }
    }
  }
}

// 创建全局存储实例
export const storage = new Storage()

// 便捷方法
export const setItem = storage.set.bind(storage)
export const getItem = storage.get.bind(storage)
export const removeItem = storage.remove.bind(storage)
export const hasItem = storage.has.bind(storage)
export const clearStorage = storage.clear.bind(storage)

// 特定类型的存储方法
export const userStorage = {
  setToken: (token: string) => storage.set('access_token', token),
  getToken: () => storage.get<string>('access_token'),
  removeToken: () => storage.remove('access_token'),
  
  setUser: (user: any) => storage.set('user', user),
  getUser: () => storage.get('user'),
  removeUser: () => storage.remove('user'),

  setTheme: (theme: string) => storage.set('theme', theme),
  getTheme: () => storage.get<string>('theme', { defaultValue: 'light' }),

  setPreferences: (preferences: any) => storage.set('userPreferences', preferences),
  getPreferences: () => storage.get('userPreferences', { defaultValue: {} }),

  setRememberMe: (remember: boolean) => storage.set('rememberMe', remember),
  getRememberMe: () => storage.get<boolean>('rememberMe', { defaultValue: false })
}

export const adminStorage = {
  setToken: (token: string) => storage.set('adminToken', token),
  getToken: () => storage.get<string>('adminToken'),
  removeToken: () => storage.remove('adminToken'),

  setRememberMe: (remember: boolean) => storage.set('adminRememberMe', remember),
  getRememberMe: () => storage.get<boolean>('adminRememberMe', { defaultValue: false })
}

export default storage 