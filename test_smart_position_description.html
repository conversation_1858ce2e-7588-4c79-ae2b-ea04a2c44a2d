<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能位置描述解决方案</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .problem-analysis {
            background: #fef2f2;
            border: 1px solid #ef4444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .problem-analysis h3 {
            color: #ef4444;
            margin-top: 0;
        }
        .solution {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution h3 {
            color: #10b981;
            margin-top: 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .before, .after {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .before { border-color: #ef4444; }
        .after { border-color: #10b981; }
        .header {
            padding: 15px;
            font-weight: bold;
            color: white;
        }
        .before .header { background: #ef4444; }
        .after .header { background: #10b981; }
        .content {
            padding: 20px;
        }
        .fragment-group {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
        }
        .group-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .fragment-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: grid;
            grid-template-columns: 40px 1fr 120px;
            gap: 15px;
            align-items: start;
        }
        .fragment-item:last-child {
            border-bottom: none;
        }
        .original-text {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 8px;
        }
        .position-info {
            font-size: 12px;
            color: #6b7280;
        }
        .position-bad {
            color: #ef4444;
            background: #fef2f2;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .position-good {
            color: #10b981;
            background: #ecfdf5;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .category-badge {
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .strategy-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .strategy-table th, .strategy-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .strategy-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .priority-badge {
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 智能位置描述解决方案</h1>
        <p>针对Word COM接口页码获取不准确的问题，提供智能的位置描述方案。</p>

        <div class="problem-analysis">
            <h3>🔍 问题分析</h3>
            <ul class="feature-list">
                <li><strong>页码获取不准确</strong>：Word COM接口的Range.Information(3)方法返回错误页码</li>
                <li><strong>位置信息缺失</strong>：段落的page_number字段为None，position为0</li>
                <li><strong>用户体验差</strong>：显示"第1页"但实际在第11页，误导用户</li>
                <li><strong>定位困难</strong>：用户无法快速找到问题在文档中的真实位置</li>
            </ul>
        </div>

        <div class="solution">
            <h3>💡 解决方案</h3>
            <ul class="feature-list">
                <li><strong>智能内容识别</strong>：根据文本内容特征判断位置类型</li>
                <li><strong>相对位置描述</strong>：使用"文档前部"、"摘要部分"等描述</li>
                <li><strong>多层级策略</strong>：按优先级尝试不同的位置描述方法</li>
                <li><strong>用户友好</strong>：提供有意义的位置信息，便于理解和定位</li>
            </ul>
        </div>

        <h2>📊 解决方案对比</h2>
        <div class="comparison">
            <div class="before">
                <div class="header">❌ 问题：不准确的页码</div>
                <div class="content">
                    <div class="fragment-group">
                        <div class="group-header">📋 中文关键词 (1个问题)</div>
                        <div class="fragment-item">
                            <div>1</div>
                            <div>
                                <div class="original-text">关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）</div>
                                <div class="position-info">
                                    <span class="position-bad">位置: 第1页</span>
                                </div>
                            </div>
                            <div>
                                <div class="category-badge">对齐方式问题</div>
                            </div>
                        </div>
                    </div>
                    <div style="color: #ef4444; margin-top: 10px;">
                        ❌ 问题：显示"第1页"但实际在第11页，误导用户
                    </div>
                </div>
            </div>

            <div class="after">
                <div class="header">✅ 解决：智能位置描述</div>
                <div class="content">
                    <div class="fragment-group">
                        <div class="group-header">📋 中文关键词 (1个问题)</div>
                        <div class="fragment-item">
                            <div>1</div>
                            <div>
                                <div class="original-text">关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）</div>
                                <div class="position-info">
                                    <span class="position-good">位置: 摘要部分</span>
                                </div>
                            </div>
                            <div>
                                <div class="category-badge">对齐方式问题</div>
                            </div>
                        </div>
                    </div>
                    <div style="color: #10b981; margin-top: 10px;">
                        ✅ 改进：显示"摘要部分"，用户知道在文档前部查找
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 智能位置识别策略</h2>
        <table class="strategy-table">
            <thead>
                <tr>
                    <th>优先级</th>
                    <th>策略</th>
                    <th>内容特征</th>
                    <th>位置描述</th>
                    <th>示例</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="priority-badge">1</span></td>
                    <td>内容特征识别</td>
                    <td>关键词段落</td>
                    <td>摘要部分</td>
                    <td>关键词：人工智能；机器学习</td>
                </tr>
                <tr>
                    <td><span class="priority-badge">2</span></td>
                    <td>内容特征识别</td>
                    <td>摘要段落</td>
                    <td>文档前部</td>
                    <td>摘要：本文研究了...</td>
                </tr>
                <tr>
                    <td><span class="priority-badge">3</span></td>
                    <td>内容特征识别</td>
                    <td>参考文献</td>
                    <td>文档末尾</td>
                    <td>[1] 张三. 研究报告[J]...</td>
                </tr>
                <tr>
                    <td><span class="priority-badge">4</span></td>
                    <td>相对位置估算</td>
                    <td>文档长度比例</td>
                    <td>文档前部/中部/后部</td>
                    <td>根据段落位置计算</td>
                </tr>
                <tr>
                    <td><span class="priority-badge">5</span></td>
                    <td>段落序号</td>
                    <td>段落计数</td>
                    <td>第N段</td>
                    <td>第15段</td>
                </tr>
            </tbody>
        </table>

        <h2>📋 技术实现</h2>
        <div class="code-block">
            <h3>1. 智能内容位置识别</h3>
            <pre>
def _get_smart_content_location(self, original_text, rule_id, document_data):
    """根据内容类型和文档结构智能判断位置"""
    text = original_text.strip()
    
    # 根据内容特征判断文档位置
    if text.startswith("关键词：") or text.startswith("关键词:"):
        return "摘要部分"  # 中文关键词通常在摘要附近
    elif text.startswith("Keywords:") or text.startswith("Key words:"):
        return "摘要部分"  # 英文关键词通常在摘要附近
    elif "摘要" in text[:10]:
        return "文档前部"  # 摘要在文档前部
    elif text.startswith("[") and "]" in text[:10]:
        return "文档末尾"  # 参考文献在文档末尾
    elif "参考文献" in text:
        return "文档末尾"
    elif "结论" in text or "Conclusion" in text:
        return "文档后部"
    
    return None
            </pre>
        </div>

        <div class="code-block">
            <h3>2. 相对位置估算</h3>
            <pre>
def _get_relative_position_in_document(self, position, document_data):
    """根据位置在文档中的相对位置返回描述"""
    total_paragraphs = document_data.content_stats.get('total_paragraphs', 0)
    
    # 根据段落位置判断相对位置
    if paragraph_positions:
        relative_pos = current_index / total_paragraphs
        
        if relative_pos < 0.33:
            return "文档前部"
        elif relative_pos < 0.67:
            return "文档中部"
        else:
            return "文档后部"
    
    return None
            </pre>
        </div>

        <h2>🎯 实际效果展示</h2>
        <div id="effect-demo"></div>

        <div class="solution">
            <h3>🚀 用户体验提升</h3>
            <ul class="feature-list">
                <li><strong>准确定位</strong>：用户知道问题在"摘要部分"而不是错误的"第1页"</li>
                <li><strong>直观理解</strong>：使用自然语言描述，无需解释</li>
                <li><strong>快速查找</strong>：根据位置描述快速定位到文档相应部分</li>
                <li><strong>容错性强</strong>：即使页码信息缺失也能提供有意义的位置信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 展示实际效果
        function showEffectDemo() {
            const effects = [
                {
                    content: "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法",
                    before: "第1页",
                    after: "摘要部分",
                    explanation: "根据'关键词：'开头识别为关键词段落，通常位于摘要附近"
                },
                {
                    content: "Key words:dance creation;virtual reality (VR); augmented reality (AR)",
                    before: "第1页",
                    after: "摘要部分", 
                    explanation: "根据'Key words:'开头识别为英文关键词段落"
                },
                {
                    content: "摘要：本文研究了虚拟现实技术在舞蹈创作中的应用...",
                    before: "第2页",
                    after: "文档前部",
                    explanation: "根据'摘要：'开头识别为摘要段落，位于文档前部"
                },
                {
                    content: "[1] 张三. 虚拟现实技术在舞蹈创作中的应用研究[J]. 计算机学报, 2023.",
                    before: "第35页",
                    after: "文档末尾",
                    explanation: "根据'[1]'开头识别为参考文献，位于文档末尾"
                }
            ]

            let html = `
            <table class="strategy-table">
                <thead>
                    <tr>
                        <th>原文内容</th>
                        <th>修复前</th>
                        <th>修复后</th>
                        <th>识别逻辑</th>
                    </tr>
                </thead>
                <tbody>
            `

            effects.forEach((item) => {
                html += `
                <tr>
                    <td style="font-family: monospace; background: #f8f9fa; max-width: 200px; word-break: break-all;">
                        ${item.content.substring(0, 50)}...
                    </td>
                    <td>
                        <span class="position-bad">${item.before}</span>
                    </td>
                    <td>
                        <span class="position-good">${item.after}</span>
                    </td>
                    <td style="font-size: 12px; color: #666;">
                        ${item.explanation}
                    </td>
                </tr>
                `
            })

            html += `
                </tbody>
            </table>
            <div style="color: #10b981; margin-top: 15px;">
                ✅ 现在用户可以根据有意义的位置描述快速定位问题！
            </div>
            `

            document.getElementById('effect-demo').innerHTML = html
        }

        // 页面加载时显示效果
        document.addEventListener('DOMContentLoaded', showEffectDemo)
    </script>
</body>
</html>
