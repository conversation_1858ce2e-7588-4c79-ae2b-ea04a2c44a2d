"""
Word文档分析服务 - 任务进度跟踪

实现实时任务进度跟踪系统，支持：
- 实时进度更新
- 进度状态管理
- WebSocket进度推送
- 进度历史记录
- 进度统计和分析
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
import uuid

from app.core.logging import logger
from app.core.config import get_settings


class ProgressStatus(str, Enum):
    """进度状态"""
    PENDING = "pending"       # 等待中
    RUNNING = "running"       # 运行中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"         # 失败
    CANCELLED = "cancelled"   # 已取消


@dataclass
class ProgressStep:
    """进度步骤"""
    step_id: str
    name: str
    description: str
    progress: float = 0.0  # 0-100
    status: ProgressStatus = ProgressStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理datetime序列化
        if data['start_time']:
            data['start_time'] = data['start_time'].isoformat()
        if data['end_time']:
            data['end_time'] = data['end_time'].isoformat()
        return data


@dataclass
class TaskProgress:
    """任务进度"""
    task_id: str
    task_type: str
    overall_progress: float = 0.0  # 0-100
    status: ProgressStatus = ProgressStatus.PENDING
    current_step: Optional[str] = None
    steps: Dict[str, ProgressStep] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.steps is None:
            self.steps = {}
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'overall_progress': self.overall_progress,
            'status': self.status.value,
            'current_step': self.current_step,
            'steps': {step_id: step.to_dict() for step_id, step in self.steps.items()},
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'estimated_completion': self.estimated_completion.isoformat() if self.estimated_completion else None,
            'error_message': self.error_message,
            'metadata': self.metadata
        }
        return data


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self.active_tasks: Dict[str, TaskProgress] = {}
        self.progress_history: Dict[str, List[Dict[str, Any]]] = {}
        self.subscribers: Set[str] = set()
        self.settings = get_settings()
        
        # 进度更新回调
        self.progress_callbacks: List[callable] = []
        
        # 统计信息
        self.stats = {
            'total_tasks_tracked': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0,
            'average_completion_time': 0.0
        }
    
    async def start_task(self, task_id: str, task_type: str, steps: Optional[List[str]] = None) -> bool:
        """
        开始跟踪任务进度
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            steps: 预定义的步骤列表
            
        Returns:
            bool: 操作是否成功
        """
        try:
            progress = TaskProgress(
                task_id=task_id,
                task_type=task_type,
                status=ProgressStatus.RUNNING,
                start_time=datetime.utcnow()
            )
            
            # 初始化步骤
            if steps:
                for i, step_name in enumerate(steps):
                    step_id = f"step_{i+1}"
                    step = ProgressStep(
                        step_id=step_id,
                        name=step_name,
                        description=f"执行步骤: {step_name}"
                    )
                    progress.steps[step_id] = step
            
            self.active_tasks[task_id] = progress
            self.stats['total_tasks_tracked'] += 1
            
            # 推送初始进度
            await self._notify_progress_update(task_id)
            
            logger.info(f"开始跟踪任务进度: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"开始跟踪任务失败: {task_id} - {str(e)}")
            return False
    
    async def update_progress(
        self,
        task_id: str,
        progress: float,
        step_id: Optional[str] = None,
        message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            progress: 进度值 (0-100)
            step_id: 当前步骤ID
            message: 进度消息
            metadata: 额外元数据
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if task_id not in self.active_tasks:
                logger.warning(f"任务不存在于进度跟踪中: {task_id}")
                return False
            
            task_progress = self.active_tasks[task_id]
            
            # 更新整体进度
            task_progress.overall_progress = min(100.0, max(0.0, progress))
            
            # 更新当前步骤
            if step_id:
                task_progress.current_step = step_id
                
                # 更新步骤进度
                if step_id in task_progress.steps:
                    step = task_progress.steps[step_id]
                    step.progress = progress
                    step.status = ProgressStatus.RUNNING
                    if step.start_time is None:
                        step.start_time = datetime.utcnow()
                    if message:
                        step.description = message
            
            # 更新元数据
            if metadata:
                task_progress.metadata.update(metadata)
            
            # 估算完成时间
            if progress > 0:
                task_progress.estimated_completion = self._estimate_completion_time(task_progress)
            
            # 推送进度更新
            await self._notify_progress_update(task_id)
            
            logger.debug(f"更新任务进度: {task_id} - {progress:.1f}%")
            return True
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {task_id} - {str(e)}")
            return False
    
    async def complete_step(
        self,
        task_id: str,
        step_id: str,
        message: Optional[str] = None
    ) -> bool:
        """
        完成任务步骤
        
        Args:
            task_id: 任务ID
            step_id: 步骤ID
            message: 完成消息
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if task_id not in self.active_tasks:
                return False
            
            task_progress = self.active_tasks[task_id]
            
            if step_id in task_progress.steps:
                step = task_progress.steps[step_id]
                step.status = ProgressStatus.COMPLETED
                step.progress = 100.0
                step.end_time = datetime.utcnow()
                if message:
                    step.description = message
                
                # 计算整体进度
                await self._recalculate_overall_progress(task_id)
                
                # 推送进度更新
                await self._notify_progress_update(task_id)
                
                logger.debug(f"完成任务步骤: {task_id}/{step_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"完成任务步骤失败: {task_id}/{step_id} - {str(e)}")
            return False
    
    async def complete_task(
        self,
        task_id: str,
        message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        完成任务跟踪
        
        Args:
            task_id: 任务ID
            message: 完成消息
            result: 任务结果
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if task_id not in self.active_tasks:
                return False

            # 🔥 调试：记录传入的参数
            logger.info(f"完成任务 {task_id}")
            logger.info(f"消息: {message}")
            logger.info(f"结果存在: {result is not None}")
            if result:
                logger.info(f"结果类型: {type(result)}")
                logger.info(f"结果键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                if isinstance(result, dict) and 'document_info' in result:
                    doc_info = result['document_info']
                    logger.info(f"document_info类型: {type(doc_info)}")
                    logger.info(f"document_info键: {list(doc_info.keys()) if isinstance(doc_info, dict) else 'Not a dict'}")
                    if isinstance(doc_info, dict) and 'cover_page_info' in doc_info:
                        cover_info = doc_info['cover_page_info']
                        logger.info(f"cover_page_info类型: {type(cover_info)}")
                        logger.info(f"cover_page_info键: {list(cover_info.keys()) if isinstance(cover_info, dict) else 'Not a dict'}")

            task_progress = self.active_tasks[task_id]
            task_progress.status = ProgressStatus.COMPLETED
            task_progress.overall_progress = 100.0
            task_progress.end_time = datetime.utcnow()

            if message:
                task_progress.metadata['completion_message'] = message
            if result:
                task_progress.metadata['result'] = result
                logger.info(f"结果已保存到task_progress.metadata['result']")
            
            # 完成所有未完成的步骤
            for step in task_progress.steps.values():
                if step.status == ProgressStatus.RUNNING:
                    step.status = ProgressStatus.COMPLETED
                    step.progress = 100.0
                    step.end_time = datetime.utcnow()
            
            # 更新数据库中的任务状态
            await self._update_database_task_status(task_id, result)

            # 推送最终进度
            await self._notify_progress_update(task_id)

            # 移动到历史记录
            await self._archive_task_progress(task_id)
            
            self.stats['completed_tasks'] += 1
            logger.info(f"任务完成: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"完成任务跟踪失败: {task_id} - {str(e)}")
            return False

    async def _update_database_task_status(self, task_id: str, result: Optional[Dict[str, Any]] = None):
        """更新数据库中的任务状态"""
        try:
            from app.database import crud
            from app.database.session import session_manager
            from app.models import TaskUpdate, TaskStatus
            from datetime import datetime

            # 🔥 调试：记录数据库更新操作
            logger.info(f"开始更新数据库任务状态: {task_id}")
            logger.info(f"传入的结果存在: {result is not None}")
            if result:
                logger.info(f"结果类型: {type(result)}")
                logger.info(f"结果大小: {len(str(result))} 字符")
                # 检查结果是否可序列化
                try:
                    import json
                    from enum import Enum
                    from datetime import datetime

                    def json_serializer(obj):
                        """自定义JSON序列化器，处理枚举和其他特殊对象"""
                        if isinstance(obj, Enum):
                            return obj.value
                        elif isinstance(obj, datetime):
                            return obj.isoformat()
                        elif hasattr(obj, 'to_dict'):
                            return obj.to_dict()
                        elif hasattr(obj, '__dict__'):
                            return obj.__dict__
                        else:
                            return str(obj)

                    json_str = json.dumps(result, ensure_ascii=False, default=json_serializer)
                    logger.info(f"结果可序列化，JSON长度: {len(json_str)}")
                except Exception as json_e:
                    logger.error(f"结果序列化失败: {str(json_e)}")
                    logger.error(f"序列化错误详情: {type(json_e).__name__}: {json_e}")

            task_update = TaskUpdate(
                status=TaskStatus.COMPLETED,
                completed_at=datetime.now(),
                updated_at=datetime.now(),
                result=result
            )

            logger.info(f"TaskUpdate对象已创建，result字段: {task_update.result is not None}")

            await session_manager.execute_crud_operation(crud.update_task, task_id, task_update)
            logger.info(f"数据库任务状态已更新为COMPLETED: {task_id}")

        except Exception as e:
            logger.error(f"更新数据库任务状态失败: {task_id} - {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    async def fail_task(
        self,
        task_id: str,
        error_message: str,
        step_id: Optional[str] = None
    ) -> bool:
        """
        标记任务失败
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            step_id: 失败的步骤ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if task_id not in self.active_tasks:
                return False
            
            task_progress = self.active_tasks[task_id]
            task_progress.status = ProgressStatus.FAILED
            task_progress.error_message = error_message
            task_progress.end_time = datetime.utcnow()
            
            # 标记失败的步骤
            if step_id and step_id in task_progress.steps:
                step = task_progress.steps[step_id]
                step.status = ProgressStatus.FAILED
                step.error_message = error_message
                step.end_time = datetime.utcnow()
            
            # 更新数据库中的任务状态
            await self._update_database_task_status_failed(task_id, error_message)

            # 推送失败状态
            await self._notify_progress_update(task_id)

            # 移动到历史记录
            await self._archive_task_progress(task_id)
            
            self.stats['failed_tasks'] += 1
            logger.error(f"任务失败: {task_id} - {error_message}")
            return True
            
        except Exception as e:
            logger.error(f"标记任务失败失败: {task_id} - {str(e)}")
            return False

    async def _update_database_task_status_failed(self, task_id: str, error_message: str):
        """更新数据库中的任务状态为失败"""
        try:
            from app.database import crud
            from app.database.session import session_manager
            from app.models import TaskUpdate, TaskStatus
            from datetime import datetime

            task_update = TaskUpdate(
                status=TaskStatus.FAILED,
                completed_at=datetime.now(),
                updated_at=datetime.now(),
                error_message=error_message
            )

            await session_manager.execute_crud_operation(crud.update_task, task_id, task_update)
            logger.info(f"数据库任务状态已更新为FAILED: {task_id}")

        except Exception as e:
            logger.error(f"更新数据库任务失败状态失败: {task_id} - {str(e)}")
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务跟踪
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if task_id not in self.active_tasks:
                return False
            
            task_progress = self.active_tasks[task_id]
            task_progress.status = ProgressStatus.CANCELLED
            task_progress.end_time = datetime.utcnow()
            
            # 取消所有运行中的步骤
            for step in task_progress.steps.values():
                if step.status == ProgressStatus.RUNNING:
                    step.status = ProgressStatus.CANCELLED
                    step.end_time = datetime.utcnow()
            
            # 推送取消状态
            await self._notify_progress_update(task_id)
            
            # 移动到历史记录
            await self._archive_task_progress(task_id)
            
            self.stats['cancelled_tasks'] += 1
            logger.info(f"任务取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务跟踪失败: {task_id} - {str(e)}")
            return False
    
    async def get_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务进度"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id].to_dict()
        
        # 检查历史记录
        if task_id in self.progress_history:
            history = self.progress_history[task_id]
            if history:
                return history[-1]  # 返回最新的记录
        
        return None
    
    async def get_all_active_progress(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活跃任务进度"""
        return {
            task_id: progress.to_dict()
            for task_id, progress in self.active_tasks.items()
        }
    
    async def get_progress_stats(self) -> Dict[str, Any]:
        """获取进度统计信息"""
        return {
            'active_tasks': len(self.active_tasks),
            'subscribers': len(self.subscribers),
            **self.stats
        }
    
    def add_progress_callback(self, callback: callable):
        """添加进度更新回调"""
        self.progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: callable):
        """移除进度更新回调"""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    async def _notify_progress_update(self, task_id: str):
        """通知进度更新"""
        try:
            if task_id not in self.active_tasks:
                return
            
            progress_data = self.active_tasks[task_id].to_dict()
            
            # 调用所有注册的回调
            for callback in self.progress_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(task_id, progress_data)
                    else:
                        callback(task_id, progress_data)
                except Exception as e:
                    logger.error(f"进度回调执行失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"通知进度更新失败: {task_id} - {str(e)}")
    
    async def _recalculate_overall_progress(self, task_id: str):
        """重新计算整体进度"""
        try:
            if task_id not in self.active_tasks:
                return
            
            task_progress = self.active_tasks[task_id]
            
            if not task_progress.steps:
                return
            
            # 基于步骤进度计算整体进度
            total_progress = sum(step.progress for step in task_progress.steps.values())
            overall_progress = total_progress / len(task_progress.steps)
            
            task_progress.overall_progress = min(100.0, max(0.0, overall_progress))
            
        except Exception as e:
            logger.error(f"重新计算整体进度失败: {task_id} - {str(e)}")
    
    def _estimate_completion_time(self, task_progress: TaskProgress) -> Optional[datetime]:
        """估算完成时间"""
        try:
            if not task_progress.start_time or task_progress.overall_progress <= 0:
                return None
            
            elapsed = (datetime.utcnow() - task_progress.start_time).total_seconds()
            
            # 估算剩余时间
            remaining_progress = 100.0 - task_progress.overall_progress
            estimated_remaining = (elapsed / task_progress.overall_progress) * remaining_progress
            
            return datetime.utcnow() + timedelta(seconds=estimated_remaining)
            
        except Exception as e:
            logger.warning(f"估算完成时间失败: {str(e)}")
            return None
    
    async def _archive_task_progress(self, task_id: str):
        """归档任务进度"""
        try:
            if task_id not in self.active_tasks:
                return
            
            task_progress = self.active_tasks[task_id]
            
            # 添加到历史记录
            if task_id not in self.progress_history:
                self.progress_history[task_id] = []
            
            self.progress_history[task_id].append(task_progress.to_dict())
            
            # 限制历史记录数量
            max_history = 10
            if len(self.progress_history[task_id]) > max_history:
                self.progress_history[task_id] = self.progress_history[task_id][-max_history:]
            
            # 从活跃任务中移除
            del self.active_tasks[task_id]
            
            logger.debug(f"任务进度已归档: {task_id}")
            
        except Exception as e:
            logger.error(f"归档任务进度失败: {task_id} - {str(e)}")


# 全局进度跟踪器实例
_progress_tracker: Optional[ProgressTracker] = None


async def get_progress_tracker() -> ProgressTracker:
    """获取全局进度跟踪器实例"""
    global _progress_tracker
    
    if _progress_tracker is None:
        _progress_tracker = ProgressTracker()
    
    return _progress_tracker 