# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据处理和验证
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.1

# 异步和并发
asyncio-mqtt==0.11.1
aiofiles==23.2.1

# Windows COM接口
pywin32==306

# 数据库和缓存
redis==5.0.1
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23

# 图片处理
pillow==10.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
bcrypt==4.1.2
passlib==1.7.4
PyJWT==2.8.0

# 日志和监控
structlog==23.2.0
psutil==5.9.6

# 开发工具
python-dotenv==1.0.0
pyyaml==6.0.1

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1 