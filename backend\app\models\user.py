"""
Word文档分析服务 - 用户数据模型（优化版本）
"""

import uuid
from typing import Optional
from datetime import datetime
import re

from pydantic import BaseModel, Field, EmailStr, validator


class UserBase(BaseModel):
    """用户模型的基础部分"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr = Field(..., description="用户邮箱，符合RFC 5321标准")
    full_name: Optional[str] = Field(None, max_length=100)

    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式：只允许字母、数字、下划线和连字符"""
        if not re.match(r'^[a-zA-Z0-9_-]{3,50}$', v):
            raise ValueError('用户名只能包含字母、数字、下划线和连字符，长度3-50字符')
        return v

    @validator('email')
    def validate_email_format(cls, v):
        """验证邮箱格式"""
        email_str = str(v)
        if len(email_str) > 320:  # RFC 5321标准邮箱最大长度
            raise ValueError('邮箱地址过长，最大长度为320字符')
        
        # 基本格式验证
        if not re.match(r'^[^@\s]+@[^@\s]+\.[^@\s]+$', email_str):
            raise ValueError('邮箱格式不正确')
        return v

    @validator('full_name')
    def validate_full_name(cls, v):
        """验证全名"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
            if len(v) > 100:
                raise ValueError('全名长度不能超过100字符')
        return v


class UserCreate(UserBase):
    """创建用户时使用的模型"""
    password: str = Field(..., min_length=8, description="密码，最少8位")
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        
        # 检查密码强度（可选：根据需求调整）
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            # 这里可以根据安全要求调整，当前为警告而非错误
            pass  # 暂时不强制密码复杂度
        
        return v


class UserUpdate(BaseModel):
    """更新用户时使用的模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    check_balance: Optional[int] = Field(None, ge=0, description="剩余检测次数")

    @validator('username')
    def validate_username_update(cls, v):
        """验证更新时的用户名格式"""
        if v is not None:
            if not re.match(r'^[a-zA-Z0-9_-]{3,50}$', v):
                raise ValueError('用户名只能包含字母、数字、下划线和连字符，长度3-50字符')
        return v

    @validator('email')
    def validate_email_update(cls, v):
        """验证更新时的邮箱格式"""
        if v is not None:
            email_str = str(v)
            if len(email_str) > 320:
                raise ValueError('邮箱地址过长，最大长度为320字符')
        return v

    @validator('full_name')
    def validate_full_name_update(cls, v):
        """验证更新时的全名"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
            if len(v) > 100:
                raise ValueError('全名长度不能超过100字符')
        return v

    @validator('check_balance')
    def validate_check_balance(cls, v):
        """验证检测余额"""
        if v is not None and v < 0:
            raise ValueError('检测余额不能为负数')
        return v


class UserInDBBase(UserBase):
    """数据库中存储的用户模型的基础部分"""
    id: str = Field(default_factory=lambda: f"user_{uuid.uuid4().hex}")
    check_balance: int = Field(default=0, ge=0, description="剩余检测次数")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = Field(default=True, description="账户是否激活")

    class Config:
        from_attributes = True


class UserInDB(UserInDBBase):
    """数据库中存储的完整用户模型"""
    hashed_password: str = Field(..., description="哈希后的密码")


class UserResponse(UserInDBBase):
    """返回给客户端的用户模型，不包含密码"""
    pass


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

    @validator('username')
    def validate_login_username(cls, v):
        """验证登录用户名（可以是用户名或邮箱）"""
        v = v.strip()
        if len(v) == 0:
            raise ValueError('用户名或邮箱不能为空')
        return v

    @validator('password')
    def validate_login_password(cls, v):
        """验证登录密码"""
        if len(v) == 0:
            raise ValueError('密码不能为空')
        return v


class UserProfile(BaseModel):
    """用户资料模型"""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    check_balance: int
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = Field(None, description="最后登录时间")
    total_tasks: int = Field(default=0, ge=0, description="总任务数")
    completed_tasks: int = Field(default=0, ge=0, description="已完成任务数")
    
    class Config:
        from_attributes = True


class UserStatistics(BaseModel):
    """用户统计信息模型"""
    total_documents: int = Field(default=0, ge=0, description="总文档数")
    total_tasks: int = Field(default=0, ge=0, description="总任务数")
    completed_tasks: int = Field(default=0, ge=0, description="已完成任务数")
    failed_tasks: int = Field(default=0, ge=0, description="失败任务数")
    total_problems_found: int = Field(default=0, ge=0, description="发现的问题总数")
    average_compliance_score: Optional[float] = Field(None, ge=0, le=100, description="平均合规分数")
    check_balance: int = Field(default=0, ge=0, description="剩余检测次数")
    member_since: datetime = Field(..., description="注册时间")
    last_activity: Optional[datetime] = Field(None, description="最后活动时间")


class PasswordReset(BaseModel):
    """密码重置模型"""
    email: EmailStr = Field(..., description="注册邮箱")


class PasswordResetConfirm(BaseModel):
    """密码重置确认模型"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=8, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")

    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """验证两次密码输入一致"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次密码输入不一致')
        return v

    @validator('new_password')
    def validate_new_password(cls, v):
        """验证新密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        return v


class PasswordChange(BaseModel):
    """密码修改模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")

    @validator('confirm_password')
    def validate_passwords_match(cls, v, values):
        """验证两次密码输入一致"""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次密码输入不一致')
        return v

    @validator('new_password')
    def validate_new_password_strength(cls, v, values):
        """验证新密码与当前密码不同且符合强度要求"""
        if 'current_password' in values and v == values['current_password']:
            raise ValueError('新密码不能与当前密码相同')
        
        if len(v) < 8:
            raise ValueError('新密码长度至少8位')
        
        return v 