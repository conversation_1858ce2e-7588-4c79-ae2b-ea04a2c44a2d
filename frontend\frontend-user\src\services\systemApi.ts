import apiService from './api'
import type { ApiResponse } from '@/types'

export interface SystemStats {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  pending_tasks: number
  processing_tasks: number
  total_documents: number
  total_problems: number
  avg_compliance_score?: number | null
}

export interface ProblemStats {
  severity: string
  count: number
  percentage: number
}

export interface DetectionStandard {
  metadata: {
    standard_id: string
    name: string
    description: string
    version: string
  }
  definitions: {
    document_structure: Array<{
      name: string
      required: boolean
      identifiers: string[]
    }>
  }
  rules: {
    content: {
      [key: string]: {
        name: string
        check_function: string
        severity: string
        parameters: {
          min?: number
          max?: number
          unit: string
          errorMessage: string
        }
      }
    }
  }
}

export interface DetectionStandardInfo {
  id: string
  name: string
  description: string
  version: string
  document_structure_count: number
}

export const systemApi = {
  getStats: async (): Promise<SystemStats> => {
    return apiService.get('/v1/system/stats')
  },

  getProblemStats: async (): Promise<ProblemStats[]> => {
    return apiService.get('/v1/system/stats/problems')
  },

  // 🔥 新增：获取检测标准配置
  getDetectionStandard: async (standardId: string): Promise<DetectionStandard> => {
    return apiService.get(`/v1/system/detection-standards/${standardId}`)
  },

  // 🔥 新增：获取所有检测标准列表
  getDetectionStandards: async (): Promise<DetectionStandardInfo[]> => {
    return apiService.get('/v1/system/detection-standards')
  }
}