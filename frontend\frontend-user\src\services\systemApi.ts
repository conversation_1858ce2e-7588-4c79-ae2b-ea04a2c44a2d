import apiService from './api'
import type { ApiResponse } from '@/types'

export interface SystemStats {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  pending_tasks: number
  processing_tasks: number
  total_documents: number
  total_problems: number
  avg_compliance_score?: number | null
}

export interface ProblemStats {
  severity: string
  count: number
  percentage: number
}

export interface DetectionStandard {
  metadata: {
    standard_id: string
    name: string
    description: string
    version: string
  }
  definitions: {
    document_structure: Array<{
      name: string
      required: boolean
      identifiers: string[]
    }>
  }
  rules: {
    content: {
      [key: string]: {
        name: string
        check_function: string
        severity: string
        parameters: {
          min?: number
          max?: number
          unit: string
          errorMessage: string
        }
      }
    }
  }
}

export interface DetectionStandardInfo {
  id: string
  name: string
  description: string
  version: string
  document_structure_count: number
}

// 🔥 新增：检测标准配置缓存
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // 生存时间（毫秒）
}

class DetectionStandardCache {
  private cache = new Map<string, CacheEntry<DetectionStandard>>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5分钟缓存

  set(key: string, data: DetectionStandard, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key: string): DetectionStandard | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

// 创建全局缓存实例
const detectionStandardCache = new DetectionStandardCache()

export const systemApi = {
  getStats: async (): Promise<SystemStats> => {
    return apiService.get('/v1/system/stats')
  },

  getProblemStats: async (): Promise<ProblemStats[]> => {
    return apiService.get('/v1/system/stats/problems')
  },

  // 🔥 优化：带缓存的检测标准配置获取
  getDetectionStandard: async (standardId: string): Promise<DetectionStandard> => {
    // 先尝试从缓存获取
    const cached = detectionStandardCache.get(standardId)
    if (cached) {
      console.log(`✅ 从缓存获取检测标准配置: ${standardId}`)
      return cached
    }

    // 缓存未命中，从API获取
    console.log(`🌐 从API获取检测标准配置: ${standardId}`)
    const data = await apiService.get(`/v1/system/detection-standards/${standardId}`)

    // 存入缓存
    detectionStandardCache.set(standardId, data)

    return data
  },

  // 🔥 新增：获取所有检测标准列表
  getDetectionStandards: async (): Promise<DetectionStandardInfo[]> => {
    return apiService.get('/v1/system/detection-standards')
  },

  // 🔥 新增：缓存管理方法
  clearDetectionStandardCache: (): void => {
    detectionStandardCache.clear()
    console.log('🗑️ 检测标准配置缓存已清空')
  },

  getCacheInfo: (): { size: number } => {
    return {
      size: detectionStandardCache.size()
    }
  }
}