# ==================================================
# Word文档分析服务 - Docker Compose配置
# ==================================================

version: '3.8'

services:
  # ==================================================
  # 主应用服务
  # ==================================================
  word-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: word-document-service:latest
    container_name: word-service-app
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # 数据持久化
      - word_data:/app/data
      - word_logs:/app/logs
      # 配置文件挂载
      - ./config.yaml:/app/config.yaml:ro
      - ./deploy/config/production.yaml:/app/config/production.yaml:ro
    networks:
      - word-network
    depends_on:
      - redis
      - nginx
    healthcheck:
      test: ["CMD", "powershell", "-File", "C:\\app\\healthcheck.ps1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ==================================================
  # Redis缓存服务
  # ==================================================
  redis:
    image: redis:7-alpine
    container_name: word-service-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis_data:/data
    networks:
      - word-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # ==================================================
  # Nginx反向代理
  # ==================================================
  nginx:
    image: nginx:alpine
    container_name: word-service-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./deploy/nginx/ssl:/etc/nginx/ssl:ro
      - word_logs:/var/log/nginx
    networks:
      - word-network
    depends_on:
      - word-service
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

  # ==================================================
  # 监控服务 (可选)
  # ==================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: word-service-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./deploy/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - word-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: word-service-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deploy/monitoring/grafana:/etc/grafana/provisioning
    networks:
      - word-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

# ==================================================
# 网络配置
# ==================================================
networks:
  word-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ==================================================
# 数据卷配置
# ==================================================
volumes:
  word_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  word_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
