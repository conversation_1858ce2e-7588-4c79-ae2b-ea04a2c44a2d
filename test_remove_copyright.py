#!/usr/bin/env python3
"""
测试移除版权声明规则后的功能
"""

import requests
import json

def test_after_removing_copyright():
    """测试移除版权声明规则后的功能"""
    
    print("🔍 测试移除版权声明规则后的功能")
    print("=" * 50)
    
    try:
        # 获取API数据
        response = requests.get('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024')
        api_data = response.json()['data']
        content_rules = api_data.get('rules', {}).get('content', {})
        
        # 模拟前端解析逻辑
        requirements = {}
        
        for rule_key, rule_config in content_rules.items():
            params = rule_config.get('parameters', {})
            if params and params.get('unit') and params.get('target_structure'):
                structure_name = params['target_structure']
                
                requirements[structure_name] = {
                    'min': params.get('min'),
                    'max': params.get('max'),
                    'unit': params.get('unit'),
                    'rule_name': rule_config.get('name')
                }
        
        print("🔍 前端解析结果:")
        print("📋 字数要求配置:")
        
        for name, req in requirements.items():
            if req['min'] and req['max']:
                requirement_text = f"{req['min']}-{req['max']}{req['unit']}"
            elif req['min']:
                requirement_text = f"≥{req['min']}{req['unit']}"
            else:
                requirement_text = "-"
            print(f"   {name}: {requirement_text}")
        
        # 检查版权声明是否已移除
        if '版权声明' in requirements:
            print("❌ 版权声明规则仍然存在于前端解析结果中！")
            return False
        else:
            print("✅ 版权声明规则已从前端解析结果中正确移除")
        
        # 模拟字数分析数据生成
        mock_structures = [
            {'name': '中文摘要', 'content': {'word_count': 355}, 'count': '355字'},
            {'name': '英文摘要', 'content': {'word_count': 194}, 'count': '194词'},
            {'name': '中文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '英文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '参考文献', 'content': {'word_count': 14}, 'count': '中文11条外文3条'}
        ]
        
        print("\n📊 字数分析结果:")
        print("-" * 60)
        print(f"{'结构名称':<12} {'标准要求':<15} {'当前情况':<15} {'分析结果':<10}")
        print("-" * 60)
        
        for structure in mock_structures:
            name = structure['name']
            current_count = structure['content']['word_count']
            count_display = structure['count']
            
            requirement = requirements.get(name)
            
            if requirement:
                min_val = requirement['min']
                max_val = requirement['max']
                unit = requirement['unit']
                
                if min_val and max_val:
                    standard_req = f"{min_val}-{max_val}{unit}"
                    if min_val <= current_count <= max_val:
                        result = "✅ 达标"
                    elif current_count < min_val:
                        result = "⚠️ 不足"
                    else:
                        result = "⚠️ 过多"
                elif min_val:
                    standard_req = f"≥{min_val}{unit}"
                    result = "✅ 达标" if current_count >= min_val else "⚠️ 不足"
                else:
                    standard_req = "-"
                    result = "无要求"
            else:
                standard_req = "-"
                result = "无要求"
            
            print(f"{name:<12} {standard_req:<15} {count_display:<15} {result:<10}")
        
        print("\n✅ 前端字数分析功能正常，版权声明已正确移除")
        
        # 验证规则数量
        expected_rules = 5  # 中文摘要、英文摘要、中文关键词、英文关键词、参考文献
        actual_rules = len(requirements)
        
        if actual_rules == expected_rules:
            print(f"✅ 规则数量正确: {actual_rules}个")
        else:
            print(f"❌ 规则数量错误: 期望{expected_rules}个，实际{actual_rules}个")
            return False
        
        print("\n📋 最终状态:")
        print("✅ 版权声明规则已完全移除")
        print("✅ 其他5个内容检查规则正常工作")
        print("✅ 前后端配置一致")
        print("✅ 字数分析功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_after_removing_copyright()
    exit(0 if success else 1)
