<template>
  <div class="document-analysis-panel space-y-6">
    <!-- 分析概览 -->
    <BaseCard>
      <template #header>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">分析概览</h2>
        <span v-if="analysis" 
              :class="getComplianceStatusClass(analysis.compliance_status)"
              class="px-3 py-1 rounded-full text-sm font-medium">
          {{ getComplianceStatusText(analysis.compliance_status) }}
        </span>
      </template>
      
      <div v-if="loading" class="text-center py-8">
        <div class="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-gray-600 dark:text-gray-300">正在加载分析结果...</p>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <div class="text-red-500 mb-4">
          <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <p class="text-red-500 mb-4">{{ error }}</p>
        <BaseButton @click="loadAnalysis" variant="secondary" size="sm">重试</BaseButton>
      </div>

      <div v-else-if="analysis" class="space-y-6">
        <!-- 问题统计 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="h-16 w-16 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="h-8 w-8 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ analysis.major_problems || 0 }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">严重问题</p>
          </div>
          
          <div class="text-center">
            <div class="h-16 w-16 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="h-8 w-8 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.502 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ analysis.minor_problems || 0 }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">一般问题</p>
          </div>
          
          <div class="text-center">
            <div class="h-16 w-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ analysis.total_problems || 0 }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300">总问题数</p>
          </div>
        </div>

        <!-- 质量得分 -->
        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6 border border-gray-200 dark:border-gray-600">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">文档质量得分</h3>
            <span class="text-2xl font-bold" :class="getScoreColor(analysis.overall_score)">
              {{ Math.round(analysis.overall_score) }}分
            </span>
          </div>
          <div class="progress mb-3">
            <div class="progress-bar" 
                 :class="getScoreBarColor(analysis.overall_score)"
                 :style="{ width: analysis.overall_score + '%' }"></div>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-300">
            {{ getScoreDescription(analysis.overall_score) }}
          </p>
        </div>

        <!-- 检测标准和时间 -->
        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">检测标准:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ analysis.paper_standard }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">检测时间:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatDateTime(analysis.checked_at) }}</span>
            </div>
          </div>
        </div>

        <!-- 详细分析结果 -->
        <div v-if="analysis.detailed_results && Object.keys(analysis.detailed_results).length > 0"
             class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
          <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">详细分析结果</h4>
          <div class="space-y-2 text-sm">
            <div v-for="(value, key) in analysis.detailed_results" :key="key" class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-300">{{ formatDetailKey(key) }}:</span>
              <span class="font-medium text-gray-900 dark:text-white">{{ formatDetailValue(value) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <div class="text-gray-400 mb-4">
          <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
        </div>
        <p class="text-gray-500 dark:text-gray-400">暂无分析结果</p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import { DocumentApi } from '@/services/documentApi'
import type { PaperCheckResult } from '@/types'

// Props
interface Props {
  documentId: string
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// 状态
const loading = ref(false)
const error = ref<string | null>(null)
const analysis = ref<PaperCheckResult | null>(null)

// 服务
const documentApi = new DocumentApi()

// 方法
const loadAnalysis = async () => {
  if (!props.documentId) return

  loading.value = true
  error.value = null

  try {
    const result = await documentApi.getDocumentAnalysis(props.documentId)
    analysis.value = result
  } catch (err: any) {
    error.value = err.message || '加载分析结果失败'
    console.error('加载分析结果失败:', err)
  } finally {
    loading.value = false
  }
}

const getComplianceStatusClass = (status?: string) => {
  const classes = {
    'compliant': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    'partially_compliant': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    'non_compliant': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    'unknown': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
  return classes[status as keyof typeof classes] || classes.unknown
}

const getComplianceStatusText = (status?: string) => {
  const texts = {
    'compliant': '符合标准',
    'partially_compliant': '部分符合',
    'non_compliant': '不符合标准', 
    'unknown': '未知状态'
  }
  return texts[status as keyof typeof texts] || '未知状态'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-600 dark:text-green-400'
  if (score >= 75) return 'text-blue-600 dark:text-blue-400'
  if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

const getScoreBarColor = (score: number) => {
  if (score >= 90) return 'bg-green-500'
  if (score >= 75) return 'bg-blue-500'
  if (score >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

const getScoreDescription = (score: number) => {
  if (score >= 90) return '您的文档质量优秀，格式规范性很好。'
  if (score >= 75) return '您的文档整体质量良好，但在格式规范性方面还有改进空间。'
  if (score >= 60) return '您的文档存在一些格式问题，建议根据报告进行调整。'
  return '您的文档存在较多格式问题，需要重点关注并修改。'
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatDetailKey = (key: string) => {
  const keyMap: Record<string, string> = {
    'format_score': '格式得分',
    'structure_score': '结构得分',
    'reference_score': '引用得分',
    'citation_score': '引文得分',
    'total_elements': '总元素数',
    'processed_elements': '已处理元素',
    'error_count': '错误数量',
    'warning_count': '警告数量'
  }
  return keyMap[key] || key
}

const formatDetailValue = (value: any) => {
  if (typeof value === 'number') {
    if (value >= 0 && value <= 100 && value % 1 !== 0) {
      return `${Math.round(value * 10) / 10}%`
    }
    return value.toString()
  }
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  return value?.toString() || ''
}

// 监听documentId变化
watch(() => props.documentId, (newId) => {
  if (newId && props.autoLoad) {
    loadAnalysis()
  }
})

// 生命周期
onMounted(() => {
  if (props.documentId && props.autoLoad) {
    loadAnalysis()
  }
})

// 暴露方法
defineExpose({
  loadAnalysis,
  analysis
})
</script>

<style scoped>
.progress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3;
}

.progress-bar {
  @apply h-3 rounded-full transition-all duration-300;
}

.document-analysis-panel :deep(.card-header) {
  @apply flex items-center justify-between;
}
</style> 