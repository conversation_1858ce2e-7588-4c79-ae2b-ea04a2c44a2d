/* 通用组件样式 */

/* 卡片样式 */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700;
}

.card-body {
  @apply px-6 py-4;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
}

.card-hover {
  @apply hover:shadow-lg hover:scale-105 cursor-pointer transition-all duration-200;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600;
}

.btn-secondary {
  @apply text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-blue-500;
}

.btn-success {
  @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500 dark:bg-green-500 dark:hover:bg-green-600;
}

.btn-danger {
  @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600;
}

.btn-warning {
  @apply text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 dark:bg-yellow-500 dark:hover:bg-yellow-600;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 表单样式 */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm 
         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
         dark:focus:ring-blue-400 dark:focus:border-blue-400
         transition-colors duration-200;
}

.form-input:disabled {
  @apply bg-gray-100 dark:bg-gray-800 cursor-not-allowed;
}

.form-help {
  @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
}

.form-error {
  @apply mt-1 text-sm text-red-600 dark:text-red-400;
}

/* 状态徽章 */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300;
}

.status-processing {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300;
}

.status-completed {
  @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300;
}

.status-failed {
  @apply bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300;
}

/* 文件图标 */
.file-icon {
  @apply w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-semibold text-xs flex-shrink-0;
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.file-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.file-icon.docx {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.file-icon.doc {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
}

.file-icon.pdf {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.file-icon-small {
  @apply w-8 h-8 text-xs;
}

/* 暗黑模式文件图标 */
.dark .file-icon {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .file-icon:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* 复选框样式 */
.custom-checkbox {
  @apply relative inline-flex items-center cursor-pointer select-none;
}

.custom-checkbox input[type="checkbox"] {
  @apply absolute opacity-0 w-0 h-0;
}

.checkbox-visual {
  @apply w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 
         flex items-center justify-center transition-all duration-200 relative;
}

.checkbox-visual::after {
  content: '';
  @apply w-1.5 h-2.5 border-white border-solid border-r-2 border-b-2 scale-0 transition-transform duration-200;
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg) scale(0);
}

.custom-checkbox input[type="checkbox"]:checked + .checkbox-visual {
  @apply bg-blue-600 dark:bg-blue-500 border-blue-600 dark:border-blue-500;
}

.custom-checkbox input[type="checkbox"]:checked + .checkbox-visual::after {
  transform: translate(-50%, -50%) rotate(45deg) scale(1);
}

.custom-checkbox:hover .checkbox-visual {
  @apply border-blue-400 dark:border-blue-400;
}

.custom-checkbox input[type="checkbox"]:focus + .checkbox-visual {
  @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* 进度条 */
.progress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-bar {
  @apply h-2 bg-blue-600 dark:bg-blue-400 rounded-full transition-all duration-300;
}

/* 主题切换开关样式 */
.theme-toggle {
  position: relative;
  display: inline-block;
  width: 3.25rem;
  height: 1.75rem;
  background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
  border: none;
  border-radius: 0.875rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  outline: none;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

.theme-toggle:hover {
  /* 移除花俏的hover效果 */
}

.theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}

.theme-toggle:active {
  transform: scale(0.98);
}

/* 开关滑块 */
.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(145deg, #ffffff, #f9fafb);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  z-index: 3;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 滑块内的图标 */
.theme-toggle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0.125rem;
  width: 1rem;
  height: 1rem;
  margin-top: -0.5rem;
  margin-left: 0.25rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23f59e0b' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  z-index: 4;
}

/* 暗黑模式时的开关状态 */
.dark .theme-toggle {
  background: linear-gradient(145deg, #3b82f6, #2563eb);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

.dark .theme-toggle::before {
  transform: translateX(1.5rem);
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.9);
}

.dark .theme-toggle::after {
  transform: translateX(1.5rem);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z' /%3E%3C/svg%3E");
  margin-top: -0.5rem;
}

/* 开关内的图标容器 - 隐藏背景图标 */
.theme-toggle-icons {
  display: none;
}

/* 主题切换容器 */
.theme-toggle-container {
  display: flex;
  align-items: center;
}

/* 选项卡 */
.tab-navigation {
  @apply border-b border-gray-200 dark:border-gray-700;
}

.tab-button {
  @apply px-6 py-3 text-sm font-medium text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 transition-colors duration-200;
}

.tab-button.active {
  @apply text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400;
}

.tab-content {
  @apply pt-6;
}

/* 空状态 */
.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply text-4xl mb-4 opacity-50;
}

.empty-state-title {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-2;
}

.empty-state-description {
  @apply text-gray-500 dark:text-gray-400 max-w-sm mx-auto;
}

/* 文本截断 */
.text-truncate {
  @apply truncate;
}

/* 分页 */
.pagination {
  @apply flex items-center space-x-1;
}

.pagination-button {
  @apply px-3 py-1.5 text-sm font-medium rounded-md border border-gray-300 dark:border-gray-600 
         text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 
         hover:bg-gray-50 dark:hover:bg-gray-600 
         focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 
         transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-button.active {
  @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

/* 模态框 */
.modal-overlay {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50;
}

.modal-container {
  @apply relative top-20 mx-auto p-5 border border-gray-300 dark:border-gray-600 shadow-lg rounded-md bg-white dark:bg-gray-800;
}

.modal-header {
  @apply border-b border-gray-200 dark:border-gray-600 pb-3 mb-4;
}

.modal-footer {
  @apply border-t border-gray-200 dark:border-gray-600 pt-4 mt-4 flex justify-end space-x-2;
}

/* 响应式网格 */
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* 表格圆角增强 */
.card .overflow-x-auto {
  @apply rounded-lg;
}

.card table {
  @apply rounded-lg overflow-hidden;
  border-collapse: separate;
  border-spacing: 0;
}

.card table thead th:first-child {
  @apply rounded-tl-lg;
}

.card table thead th:last-child {
  @apply rounded-tr-lg;
}

.card table tbody tr:last-child td:first-child {
  @apply rounded-bl-lg;
}

.card table tbody tr:last-child td:last-child {
  @apply rounded-br-lg;
} 