<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">仪表盘</router-link>
            <router-link to="/admin/users" class="text-red-600 dark:text-red-400 font-medium">用户管理</router-link>
            <router-link to="/admin/documents" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">任务监控</router-link>
            <router-link to="/admin/system" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">系统设置</router-link>
            <router-link to="/admin/reports" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">报告中心</router-link>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题和操作 -->
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            用户管理
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            管理系统用户账户和权限设置
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button @click="exportUsers" class="btn btn-secondary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            导出用户
          </button>
          <button @click="showAddUserDialog = true" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            添加用户
          </button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ userStats.total.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">总用户数</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ userStats.active.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">活跃用户</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ userStats.premium.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">付费用户</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ userStats.newToday.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">今日新增</p>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="card mb-6">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- 搜索框 -->
            <div>
              <div class="relative">
                <input 
                  v-model="searchQuery" 
                  type="text" 
                  placeholder="搜索用户名、邮箱..." 
                  class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  @input="filterUsers"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                </div>
              </div>
            </div>
            
            <!-- 状态筛选 -->
            <div>
              <select v-model="statusFilter" @change="filterUsers" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">所有状态</option>
                <option value="active">活跃</option>
                <option value="inactive">非活跃</option>
                <option value="suspended">已暂停</option>
              </select>
            </div>
            
            <!-- 套餐筛选 -->
            <div>
              <select v-model="subscriptionFilter" @change="filterUsers" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">所有套餐</option>
                <option value="free">免费版</option>
                <option value="basic">基础版</option>
                <option value="standard">标准版</option>
                <option value="professional">专业版</option>
                <option value="enterprise">企业版</option>
              </select>
            </div>
            
            <!-- 注册时间 -->
            <div>
              <select v-model="dateFilter" @change="filterUsers" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="year">今年</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">用户列表</h2>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              共 {{ filteredUsers.length }} 个用户
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <label class="custom-checkbox">
                      <input type="checkbox" @change="selectAllUsers">
                      <span class="checkbox-visual"></span>
                    </label>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    套餐状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    使用情况
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    注册时间
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    最后活跃
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="user in paginatedUsers" :key="user.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <label class="custom-checkbox">
                      <input type="checkbox" v-model="selectedUsers" :value="user.id">
                      <span class="checkbox-visual"></span>
                    </label>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="h-10 w-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-blue-600 dark:text-blue-400 font-medium text-sm">{{ user.username.charAt(0).toUpperCase() }}</span>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ user.username }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">ID: {{ user.id }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-col">
                      <span :class="[
                        'status-badge text-xs mb-1',
                        user.subscription === 'free' ? 'status-failed' :
                        user.subscription === 'basic' ? 'status-pending' :
                        user.subscription === 'standard' ? 'status-pending' :
                        user.subscription === 'professional' ? 'status-completed' :
                        user.subscription === 'enterprise' ? 'status-completed' :
                        'status-failed'
                      ]">
                        {{ getSubscriptionText(user.subscription) }}
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {{ user.subscription !== 'free' ? '2024-12-15 到期' : '免费用户' }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    <div class="flex flex-col">
                      <span>文档: {{ user.documentCount }}/{{ getDocumentLimit(user.subscription) }}</span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">本月使用</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div class="flex flex-col">
                      <span>{{ formatDate(user.createdAt) }}</span>
                      <span class="text-xs">{{ formatTime(user.createdAt) }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ getRelativeTime(user.lastLogin) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button @click="viewUser(user)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">查看</button>
                      <button @click="editUser(user)" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">编辑</button>
                      <button @click="toggleUserStatus(user)" class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300">
                        {{ user.status === 'suspended' ? '恢复' : '暂停' }}
                      </button>
                      <button @click="deleteUser(user)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- 批量操作和分页 -->
          <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <!-- 批量操作 -->
            <div class="flex items-center justify-center mb-4">
              <button 
                @click="performBatchAction" 
                :disabled="selectedUsers.length === 0"
                class="btn btn-secondary btn-sm"
                :class="{ 'opacity-50 cursor-not-allowed': selectedUsers.length === 0 }"
              >
                {{ selectedUsers.length > 0 ? `批量操作 (${selectedUsers.length})` : '批量操作' }}
              </button>
            </div>
            
            <!-- 分页 -->
                         <BasePagination 
               :current-page="currentPage"
               :page-size="pageSize"
               :total="filteredUsers.length"
               @page-change="(page: number) => currentPage = page"
             />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加用户对话框 -->
    <div v-if="showAddUserDialog" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">添加新用户</h3>
        <form @submit.prevent="addUser" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">用户名</label>
            <input v-model="newUser.username" type="text" required class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">邮箱</label>
            <input v-model="newUser.email" type="email" required class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">密码</label>
            <input v-model="newUser.password" type="password" required class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">订阅类型</label>
            <select v-model="newUser.subscription" class="form-select w-full">
              <option value="free">免费版</option>
              <option value="basic">基础版</option>
              <option value="premium">高级版</option>
              <option value="enterprise">企业版</option>
            </select>
          </div>
          <div class="flex space-x-3 pt-4">
            <button type="submit" class="flex-1 btn btn-primary">添加用户</button>
            <button type="button" @click="showAddUserDialog = false" class="flex-1 btn btn-secondary">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'
import { $notify } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'
import BasePagination from '@/components/BasePagination.vue'

const router = useRouter()
const themeStore = useThemeStore()

// 响应式数据
const showAdminMenu = ref(false)
const showAddUserDialog = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const subscriptionFilter = ref('')
const dateFilter = ref('')
const selectedUsers = ref<number[]>([])
const currentPage = ref(1)
const pageSize = 20

// 新用户表单
const newUser = ref({
  username: '',
  email: '',
  password: '',
  subscription: 'free' as 'free' | 'basic' | 'standard' | 'professional' | 'enterprise'
})

// 用户统计
const userStats = ref({
  total: 1247,
  active: 1089,
  premium: 156,
  newToday: 23
})

// 模拟用户数据
const users = ref([
  {
    id: 1001,
    username: '张三',
    email: '<EMAIL>',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    subscription: 'professional' as 'free' | 'basic' | 'standard' | 'professional' | 'enterprise',
    documentCount: 145,
    createdAt: '2024-01-15T14:30:00Z',
    lastLogin: '2024-01-20T10:30:00Z'
  },
  {
    id: 1002,
    username: '李四',
    email: '<EMAIL>',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    subscription: 'standard' as 'free' | 'basic' | 'standard' | 'professional' | 'enterprise',
    documentCount: 89,
    createdAt: '2024-01-10T09:15:00Z',
    lastLogin: '2024-01-19T16:20:00Z'
  },
  {
    id: 1003,
    username: '王五',
    email: '<EMAIL>',
    status: 'suspended' as 'active' | 'inactive' | 'suspended',
    subscription: 'free' as 'free' | 'basic' | 'standard' | 'professional' | 'enterprise',
    documentCount: 10,
    createdAt: '2023-12-28T16:45:00Z',
    lastLogin: '2024-01-18T08:30:00Z'
  }
])

// 计算属性
const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = !searchQuery.value || 
      user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    
    const matchesStatus = !statusFilter.value || user.status === statusFilter.value
    const matchesSubscription = !subscriptionFilter.value || user.subscription === subscriptionFilter.value
    
    // 日期筛选逻辑
    let matchesDate = true
    if (dateFilter.value) {
      const now = new Date()
      const userDate = new Date(user.createdAt)
      
      switch (dateFilter.value) {
        case 'today':
          matchesDate = userDate.toDateString() === now.toDateString()
          break
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          matchesDate = userDate >= weekAgo
          break
        case 'month':
          matchesDate = userDate.getMonth() === now.getMonth() && userDate.getFullYear() === now.getFullYear()
          break
        case 'year':
          matchesDate = userDate.getFullYear() === now.getFullYear()
          break
      }
    }
    
    return matchesSearch && matchesStatus && matchesSubscription && matchesDate
  })
})

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredUsers.value.slice(start, end)
})



// 工具方法
const getSubscriptionText = (subscription: string) => {
  const map: Record<string, string> = {
    free: '免费版',
    basic: '基础版',
    standard: '标准版',
    professional: '专业版',
    enterprise: '企业版'
  }
  return map[subscription] || subscription
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    suspended: '已暂停'
  }
  return map[status] || status
}

const getDocumentLimit = (subscription: string) => {
  const limits: Record<string, number> = {
    free: 10,
    basic: 50,
    standard: 200,
    professional: 500,
    enterprise: 1000
  }
  return limits[subscription] || 10
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

const getRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}



// 事件处理方法
const selectAllUsers = (event: Event) => {
  const checkbox = event.target as HTMLInputElement
  if (checkbox.checked) {
    selectedUsers.value = paginatedUsers.value.map(user => user.id)
  } else {
    selectedUsers.value = []
  }
}

const filterUsers = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  subscriptionFilter.value = ''
  dateFilter.value = ''
  currentPage.value = 1
}

const viewUser = (user: any) => {
  console.log('查看用户:', user)
  // 实际实现：跳转到用户详情页或打开模态框
}

const editUser = (user: any) => {
  console.log('编辑用户:', user)
  // 实际实现：打开编辑用户的模态框
}

const toggleUserStatus = (user: any) => {
  const newStatus = user.status === 'suspended' ? 'active' : 'suspended'
  user.status = newStatus
  console.log(`用户 ${user.username} 状态已更改为: ${newStatus}`)
}

const deleteUser = async (user: any) => {
  const result = await $confirm.danger(`确定要删除用户 ${user.username} 吗？此操作无法撤销。`, {
    title: '删除用户',
    confirmText: '确定删除',
    cancelText: '取消'
  })
  if (result) {
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
      $notify.success(`用户 ${user.username} 已删除`)
    }
  }
}

const addUser = () => {
  if (!newUser.value.username || !newUser.value.email || !newUser.value.password) {
    $notify.warning('请填写完整信息')
    return
  }
  
  const user = {
    id: Date.now(),
    username: newUser.value.username,
    email: newUser.value.email,
    status: 'active' as const,
    subscription: newUser.value.subscription,
    documentCount: 0,
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  }
  
  users.value.unshift(user)
  showAddUserDialog.value = false
  
  // 重置表单
  newUser.value = {
    username: '',
    email: '',
    password: '',
    subscription: 'free'
  }
  
  $notify.success(`用户 ${user.username} 添加成功`)
}

const exportUsers = () => {
  $notify.info('用户数据导出功能开发中...')
}

const performBatchAction = () => {
  if (selectedUsers.value.length === 0) {
    $notify.warning('请先选择要操作的用户')
    return
  }
  
  // 这里可以考虑实现一个选择对话框组件来替代prompt
  $notify.info(`已选择 ${selectedUsers.value.length} 个用户，请在菜单中选择操作`)
  
  // 模拟批量操作
  setTimeout(() => {
    $notify.success('批量暂停操作已完成')
  }, 1000)
  
  setTimeout(() => {
    $notify.success('批量激活操作已完成')
  }, 2000)
  
  setTimeout(() => {
    $notify.success('批量删除操作已完成')
  }, 3000)
  
  setTimeout(() => {
    $notify.info('正在导出选中用户数据...')
  }, 4000)
}

// 管理员登出
const handleAdminLogout = () => {
  adminLogout(router)
}

// 页面初始化
onMounted(async () => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/users')) {
    return
  }
})
</script>

<style scoped>
/* 用户管理特有样式 */
</style> 