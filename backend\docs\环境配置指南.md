# Word文档分析服务 - 环境配置指南

> **紧急修复通知**: 如果遇到数据库连接错误，配置已紧急恢复。服务现在应该能正常启动。

> **安全警告**: 配置文件中发现硬编码敏感信息，已修复。请按照本指南正确配置环境变量。

## 🚨 紧急修复 - 数据库连接问题

如果您刚才遇到了 `asyncpg.exceptions.ConnectionDoesNotExistError` 错误：

1. **问题原因**: 优化过程中临时更改了数据库密码
2. **已修复**: 配置已恢复，服务现在应该能正常启动
3. **建议**: 为了长期安全，请按下面的指南设置环境变量

## 🔒 安全配置要求

### 1. 创建 .env 文件

在 `backend/` 目录下创建 `.env` 文件：

```bash
# 数据库配置 - 必填
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/word_service

# 安全密钥 - 必填  
SECRET_KEY=你的超级安全密钥-生产环境必须更改

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 任务处理
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT=300

# Word COM
WORD_POOL_SIZE=3
WORD_STARTUP_TIMEOUT=30

# 文件处理
MAX_FILE_SIZE=52428800
UPLOAD_PATH=data/uploads

# 日志
LOG_LEVEL=INFO
LOG_FILE=logs/word_service.log
```

### 2. 关键配置说明

#### 数据库配置
```bash
# 格式: postgresql+asyncpg://用户名:密码@主机:端口/数据库名
DATABASE_URL=postgresql+asyncpg://postgres:your_password@localhost:5432/word_service
```

#### 安全密钥
```bash
# 生成随机密钥（示例）
SECRET_KEY=$(python -c "import secrets; print(secrets.token_urlsafe(32))")
```

#### 环境设置
```bash
# 开发环境
ENVIRONMENT=development
DEBUG=true

# 生产环境  
ENVIRONMENT=production
DEBUG=false
```

## 🛡️ 安全最佳实践

### 1. 密码安全
- ❌ 不要在代码中硬编码密码
- ✅ 使用强密码（包含大小写字母、数字、特殊字符）
- ✅ 定期更换密码

### 2. 密钥管理
- ❌ 不要提交 `.env` 文件到版本控制
- ✅ 使用环境变量或密钥管理服务
- ✅ 为不同环境使用不同的密钥

### 3. 配置文件
```bash
# 添加到 .gitignore
echo ".env" >> .gitignore
echo "*.env" >> .gitignore
echo "secrets/" >> .gitignore
```

## 🔧 开发环境快速配置

### 1. 最小配置（开发用）
```bash
# .env
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/word_service
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=true
ENVIRONMENT=development
```

### 2. 启动服务
```bash
cd backend
python -m uvicorn app.main:app --reload
```

## 🚀 生产环境配置

### 1. 系统环境变量
```bash
# 在系统中设置环境变量
export DATABASE_URL="postgresql+asyncpg://user:password@localhost:5432/word_service"
export SECRET_KEY="your-super-secure-production-key"
export ENVIRONMENT="production"
export DEBUG="false"
```

### 2. Docker配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  word-service:
    build: .
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
```

## ⚠️ 故障排除

### 1. 数据库连接问题
```bash
# 检查数据库连接
psql "postgresql://username:password@localhost:5432/word_service"
```

### 2. 配置验证
```python
# 验证配置加载
from app.core.config import get_settings
settings = get_settings()
print(f"Environment: {settings.environment}")
print(f"Database URL: {settings.database.url}")
```

### 3. 日志查看
```bash
# 查看服务日志
tail -f logs/word_service.log
```

## 📋 配置检查清单

- [ ] 创建 `.env` 文件
- [ ] 配置数据库连接
- [ ] 设置安全密钥
- [ ] 配置文件权限（只读）
- [ ] 添加 `.env` 到 `.gitignore`
- [ ] 测试数据库连接
- [ ] 验证服务启动
- [ ] 检查日志输出

---

**重要提醒**: 
- 绝对不要在代码中硬编码敏感信息
- 定期检查和更新密钥
- 为不同环境使用不同的配置 