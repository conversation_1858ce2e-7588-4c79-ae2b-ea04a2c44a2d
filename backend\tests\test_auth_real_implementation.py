"""真实的认证测试实现"""
import pytest


def test_user_registration_real(client):
    """真实的用户注册测试实现"""
    response = client.post("/api/v1/auth/register", json={
        "username": "realuser",
        "email": "<EMAIL>", 
        "password": "RealPass123!"
    })
    
    # 验证响应状态码
    assert response.status_code in [200, 201], f"期望状态码200或201，实际: {response.status_code}"
    
    # 验证响应数据结构
    data = response.json()
    assert "data" in data, "响应应包含data字段"
    
    # 验证token存在
    user_data = data["data"]
    assert "token" in user_data or "access_token" in user_data, "响应应包含token或access_token"
    
    return response

def test_user_login_real(client):
    """真实的用户登录测试实现"""
    # 先注册用户
    client.post("/api/v1/auth/register", json={
        "username": "loginuser",
        "email": "<EMAIL>",
        "password": "LoginPass123!"
    })
    
    # 然后登录
    response = client.post("/api/v1/auth/login", data={
        "username": "loginuser", 
        "password": "LoginPass123!"
    })
    
    # 验证登录响应
    if response.status_code == 200:
        data = response.json()
        assert "data" in data, "登录响应应包含data字段"
        assert "access_token" in data["data"], "登录响应应包含access_token"
    
    return response
