/* Word文档分析服务 - 原型阶段自定义样式 */

/* 全局变量定义 */
:root {
  /* 明亮模式颜色 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* 明亮模式背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  
  /* 明亮模式文字色 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  
  /* 明亮模式边框色 */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  
  /* 其他样式 */
  --border-radius: 8px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* 暗黑模式变量 */
.dark {
  /* 暗黑模式品牌色调整 */
  --primary-color: #60a5fa;
  --primary-hover: #3b82f6;
  --primary-dark: #2563eb;
  --success-color: #34d399;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  
  /* 暗黑模式背景色 */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  
  /* 暗黑模式文字色 */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  
  /* 暗黑模式边框色 */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  
  /* 暗黑模式阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

/* 主题切换过渡动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* 主题切换开关样式 */
.theme-toggle {
  position: relative;
  display: inline-block;
  width: 3.25rem;
  height: 1.75rem;
  background: linear-gradient(145deg, #f3f4f6, #e5e7eb);
  border: none;
  border-radius: 0.875rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  outline: none;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

.theme-toggle:hover {
  /* 移除花俏的hover效果 */
}

.theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
}

.theme-toggle:active {
  transform: scale(0.98);
}

/* 开关滑块 */
.theme-toggle::before {
  content: '';
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(145deg, #ffffff, #f9fafb);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  z-index: 3;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 滑块内的图标 */
.theme-toggle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0.125rem;
  width: 1rem;
  height: 1rem;
  margin-top: -0.5rem;
  margin-left: 0.25rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23f59e0b' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  z-index: 4;
}

/* 暗黑模式时的开关状态 */
.dark .theme-toggle {
  background: linear-gradient(145deg, #3b82f6, #2563eb);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

.dark .theme-toggle::before {
  transform: translateX(1.5rem);
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.9);
}

.dark .theme-toggle::after {
  transform: translateX(1.5rem);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236366f1' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z' /%3E%3C/svg%3E");
  margin-top: -0.5rem;
}

/* 开关内的图标容器 - 隐藏背景图标 */
.theme-toggle-icons {
  display: none;
}

/* 主题切换容器 */
.theme-toggle-container {
  display: flex;
  align-items: center;
}

/* 基础重置和字体 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* 通用组件样式 */
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-primary);
  transition: box-shadow 0.2s ease-in-out;
}

.dark .card {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.dark .card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.card-header {
  padding: 1.5rem 1.5rem 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: 1.5rem;
}

.dark .card-header {
  border-bottom-color: #374151;
}

.card-body {
  padding: 1.5rem;
}

/* 当卡片同时有header和body时，减少重复的边距 */
.card-header + .card-body {
  padding-top: 0;
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  font-size: 0.875rem;
  line-height: 1.25rem;
  height: auto;
  min-height: 2.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.dark .btn-secondary {
  background-color: #374151;
  color: #f9fafb;
  border-color: #4b5563;
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.dark .btn-secondary:hover {
  background-color: #4b5563;
  color: #f9fafb;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--error-color);
  color: white;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

/* 表单组件 */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-secondary);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease-in-out;
  height: auto;
  min-height: 2.5rem;
  box-sizing: border-box;
}

.dark .form-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .form-input:hover {
  border-color: #6b7280;
  background-color: #4b5563;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.dark .form-input:focus {
  border-color: #3b82f6;
  background-color: #4b5563;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.2);
}

.form-input.error {
  border-color: var(--error-color);
}

.dark .form-input.error {
  background-color: #3f1f1f;
  border-color: #dc2626;
}

/* 表单输入框占位符样式 */
.form-input::placeholder {
  color: #9ca3af;
}

.dark .form-input::placeholder {
  color: #6b7280;
}

/* 下拉选择框特殊样式 */
select.form-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  appearance: none;
}

.dark select.form-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.dark select.form-input:hover {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d1d5db' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* 文本区域特殊样式 */
textarea.form-input {
  resize: vertical;
  min-height: 6rem;
}

.dark textarea.form-input {
  resize: vertical;
}

/* 复选框样式增强 */
input[type="checkbox"] {
  transition: all 0.2s ease-in-out;
}

.dark input[type="checkbox"] {
  background-color: #374151;
  border-color: #6b7280;
}

.dark input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.dark input[type="checkbox"]:hover {
  border-color: #9ca3af;
}

/* 表单容器增强 */
.form-container {
  background: transparent;
}

.dark .form-container {
  background: linear-gradient(145deg, rgba(31, 41, 55, 0.8), rgba(17, 24, 39, 0.9));
  border: 1px solid rgba(75, 85, 99, 0.3);
  box-shadow: 
    inset 0 1px 0 rgba(156, 163, 175, 0.1),
    0 10px 25px -5px rgba(0, 0, 0, 0.4),
    0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.form-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* 状态指示器 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  width: fit-content;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.dark .status-pending {
  background-color: #451a03;
  color: #fbbf24;
}

.status-processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.dark .status-processing {
  background-color: #1e3a8a;
  color: #60a5fa;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.dark .status-completed {
  background-color: #064e3b;
  color: #34d399;
}

.status-failed {
  background-color: #fee2e2;
  color: #991b1b;
}

.dark .status-failed {
  background-color: #7f1d1d;
  color: #f87171;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

.dark .status-cancelled {
  background-color: #374151;
  color: #9ca3af;
}

/* 进度条 */
.progress {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.dark .progress {
  background-color: #374151;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--primary-color);
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
}

/* 上传区域 */
.upload-zone {
  border: 2px dashed var(--border-secondary);
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
  border-color: var(--primary-color);
  background-color: #eff6ff;
}

.dark .upload-zone:hover,
.dark .upload-zone.dragover {
  background-color: #1e3a8a;
}

.upload-zone.active {
  border-color: var(--success-color);
  background-color: #ecfdf5;
}

.dark .upload-zone.active {
  background-color: #064e3b;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform 0.2s ease-in-out;
}

.dark .modal-content {
  background: var(--bg-secondary);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.dark .modal-header {
  border-bottom-color: var(--border-secondary);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 0 1.5rem 1.5rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* 通知组件 */
.notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  color: white;
  font-weight: 500;
  z-index: 1100;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  background-color: var(--success-color);
}

.notification.error {
  background-color: var(--error-color);
}

.notification.warning {
  background-color: var(--warning-color);
}

.notification.info {
  background-color: var(--primary-color);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.dark .loading-spinner {
  border-color: #374151;
  border-top-color: var(--primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hide {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 769px) {
  .desktop-hide {
    display: none !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* 标签页容器样式 - 简约现代化 */
.tab-navigation {
  background: transparent;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 0;
  display: flex;
  justify-content: flex-start;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.dark .tab-navigation {
  border-bottom-color: #374151;
}

/* 标签页按钮样式 - 极简设计 */
.tab-button,
.tab-btn {
  position: relative;
  padding: 1rem 0.75rem;
  margin: 0 1.5rem 0 0;
  color: #6b7280;
  background: transparent;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  outline: none;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}

.tab-button:last-child,
.tab-btn:last-child {
  margin-right: 0;
}

/* 标签按钮悬停效果 - 微妙过渡 */
.tab-button:hover,
.tab-btn:hover {
  color: #374151;
}

/* 活跃标签样式 - 简洁突出 */
.tab-button.active,
.tab-btn.active {
  color: #3b82f6;
  font-weight: 600;
  border-bottom-color: #3b82f6;
}

/* 暗黑模式标签样式 */
.dark .tab-button,
.dark .tab-btn {
  color: #9ca3af;
}

.dark .tab-button:hover,
.dark .tab-btn:hover {
  color: #f3f4f6;
}

.dark .tab-button.active,
.dark .tab-btn.active {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

/* 标签内容样式 */
.tab-content {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.tab-content.hidden {
  display: none;
  opacity: 0;
}

.tab-content:not(.hidden) {
  display: block;
  opacity: 1;
  animation: tabFadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block !important;
  opacity: 1;
  animation: tabFadeIn 0.3s ease-in-out;
}

@keyframes tabFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 辅助工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

.transition-all {
  transition: all 0.2s ease-in-out;
}

/* 图表容器 */
.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
}

/* 文件图标 */
.file-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #3b82f6;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.6rem;
  letter-spacing: 0.025em;
  padding: 0.25rem;
  line-height: 1;
  text-align: center;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.file-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 暗黑模式下的文件图标 */
.dark .file-icon {
  background-color: #2563eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .file-icon:hover {
  background-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* DOCX文件特定样式 */
.file-icon.docx {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.dark .file-icon.docx {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

/* 较大尺寸的文件图标 */
.file-icon-small {
  width: 2rem;
  height: 2rem;
  font-size: 0.625rem;
  padding: 0.1875rem;
}

.file-icon.w-6 {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.5rem;
  padding: 0.125rem;
}

/* 数据表格 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th,
.data-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.data-table tr:hover {
  background-color: #f9fafb;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-state-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #9ca3af;
}

/* 标签页容器样式 - 简约现代化 */
.tab-navigation {
  background: transparent;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 0;
  display: flex;
  justify-content: flex-start;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.dark .tab-navigation {
  border-bottom-color: #374151;
}

/* 标签页按钮样式 - 极简设计 */
.tab-button,
.tab-btn {
  position: relative;
  padding: 1rem 0.75rem;
  margin: 0 1.5rem 0 0;
  color: #6b7280;
  background: transparent;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  outline: none;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}

.tab-button:last-child,
.tab-btn:last-child {
  margin-right: 0;
}

/* 标签按钮悬停效果 - 微妙过渡 */
.tab-button:hover,
.tab-btn:hover {
  color: #374151;
}

/* 活跃标签样式 - 简洁突出 */
.tab-button.active,
.tab-btn.active {
  color: #3b82f6;
  font-weight: 600;
  border-bottom-color: #3b82f6;
}

/* 暗黑模式标签样式 */
.dark .tab-button,
.dark .tab-btn {
  color: #9ca3af;
}

.dark .tab-button:hover,
.dark .tab-btn:hover {
  color: #f3f4f6;
}

.dark .tab-button.active,
.dark .tab-btn.active {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

/* 标签内容样式 */
.tab-content {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.tab-content.hidden {
  display: none;
  opacity: 0;
}

.tab-content:not(.hidden) {
  display: block;
  opacity: 1;
  animation: tabFadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block !important;
  opacity: 1;
  animation: tabFadeIn 0.3s ease-in-out;
}

@keyframes tabFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 