"""
检测引擎重构后的全面测试

该测试文件验证重构后的规则引擎功能，包括：
1. $ref引用解析功能
2. 规则加载和执行
3. 检查函数的正确性
4. 结果格式的一致性
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from app.checkers.rule_engine import RuleEngine
from app.services.document_analyzer import CHECK_FUNCTIONS
from app.models.check_result import CheckResult, CheckSeverity
from app.core.exceptions import ConfigurationError
from app.services.document_processor import DocumentData


class TestRuleEngineRefactored:
    """测试重构后的规则引擎"""

    def test_rule_engine_initialization(self, rule_engine):
        """测试规则引擎初始化"""
        assert rule_engine is not None
        assert rule_engine.check_functions == CHECK_FUNCTIONS
        assert rule_engine.rules == {}
        assert rule_engine.definitions == {}
        assert rule_engine.metadata == {}

    def test_load_rules_from_file_success(self, rule_engine, sample_rule_config):
        """测试成功加载规则文件"""
        # 创建临时规则文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_rule_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            # 验证加载结果
            assert rule_engine.metadata["name"] == "测试标准"
            assert rule_engine.metadata["version"] == "1.0.0"
            assert "format.body_text" in rule_engine.rules
            assert rule_engine.definitions is not None
            assert len(rule_engine.execution_plan) == 1
        finally:
            Path(temp_file).unlink()

    def test_load_rules_missing_keys(self, rule_engine):
        """测试加载缺少必需键的规则文件"""
        invalid_config = {"metadata": {}}  # 缺少其他必需键
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(invalid_config, f)
            temp_file = f.name

        try:
            with pytest.raises(ConfigurationError):
                rule_engine.load_rules_from_file(temp_file)
        finally:
            Path(temp_file).unlink()

    def test_ref_resolution_basic(self, rule_engine, sample_rule_config):
        """测试基本的$ref引用解析"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_rule_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            # 测试引用解析
            rule_config = rule_engine.rules["format.body_text"]
            resolved_params = rule_engine._resolve_params(rule_config["parameters"])
            
            # 验证引用被正确解析
            assert "font_family_chinese" in resolved_params
            assert resolved_params["font_family_chinese"]["value"] == "宋体"
            assert resolved_params["font_size"]["value"] == "小四"
            assert resolved_params["alignment"]["value"] == "justify"
        finally:
            Path(temp_file).unlink()

    def test_ref_resolution_circular_reference(self, rule_engine):
        """测试循环引用检测"""
        circular_config = {
            "metadata": {"standard_id": "test", "name": "test", "version": "1.0.0"},
            "definitions": {
                "styles": {
                    "style_a": {"$ref": "#/definitions/styles/style_b"},
                    "style_b": {"$ref": "#/definitions/styles/style_a"}
                }
            },
            "rules": {
                "test": {
                    "test_rule": {
                        "name": "测试规则",
                        "check_function": "check_text_format",
                        "parameters": {"$ref": "#/definitions/styles/style_a"}
                    }
                }
            },
            "execution_plan": []
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(circular_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            with pytest.raises(ConfigurationError, match="循环引用"):
                rule_engine._resolve_params({"$ref": "#/definitions/styles/style_a"})
        finally:
            Path(temp_file).unlink()

    def test_ref_resolution_invalid_path(self, rule_engine, sample_rule_config):
        """测试无效引用路径处理"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_rule_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            with pytest.raises(ConfigurationError, match="无效的引用路径"):
                rule_engine._resolve_params({"$ref": "#/definitions/nonexistent/path"})
        finally:
            Path(temp_file).unlink()

    def test_ref_merging_strategy(self, rule_engine):
        """测试引用合并策略：本地值覆盖引用值"""
        merge_config = {
            "metadata": {"standard_id": "test", "name": "test", "version": "1.0.0"},
            "definitions": {
                "properties": {
                    "base_style": {
                        "font_family": "宋体",
                        "font_size": 12,
                        "bold": False
                    }
                }
            },
            "rules": {
                "test": {
                    "merge_test": {
                        "name": "合并测试",
                        "check_function": "check_text_format",
                        "parameters": {
                            "style": {
                                "$ref": "#/definitions/properties/base_style",
                                "font_family": "黑体",  # 本地值覆盖
                                "italic": True  # 本地值新增
                            }
                        }
                    }
                }
            },
            "execution_plan": []
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(merge_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            rule_config = rule_engine.rules["test.merge_test"]
            resolved_params = rule_engine._resolve_params(rule_config["parameters"])
            
            # 验证合并结果
            style = resolved_params["style"]
            assert style["font_family"] == "黑体"  # 本地值覆盖
            assert style["font_size"] == 12  # 引用值保留
            assert style["bold"] == False  # 引用值保留
            assert style["italic"] == True  # 本地值新增
        finally:
            Path(temp_file).unlink()

    @pytest.mark.asyncio
    async def test_execute_rule_success(self, rule_engine, sample_rule_config, sample_document_data):
        """测试成功执行规则"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_rule_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            result = await rule_engine.execute_rule("format.body_text", sample_document_data)
            
            # 验证结果
            assert isinstance(result, CheckResult)
            assert result.rule_id == "format.body_text"
            assert result.rule_name == "正文格式检查"
            assert hasattr(result, 'passed')
            assert hasattr(result, 'execution_time')
        finally:
            Path(temp_file).unlink()

    @pytest.mark.asyncio
    async def test_execute_rule_unknown_rule(self, rule_engine, sample_document_data):
        """测试执行未知规则"""
        result = await rule_engine.execute_rule("unknown.rule", sample_document_data)
        
        assert isinstance(result, CheckResult)
        assert not result.passed
        assert "未知规则" in result.message
        assert result.severity == CheckSeverity.ERROR

    @pytest.mark.asyncio
    async def test_execute_rule_unknown_function(self, rule_engine, sample_document_data):
        """测试执行未知检查函数的规则"""
        invalid_config = {
            "metadata": {"standard_id": "test", "name": "test", "version": "1.0.0"},
            "definitions": {},
            "rules": {
                "test": {
                    "invalid_function": {
                        "name": "无效函数测试",
                        "check_function": "nonexistent_function",
                        "parameters": {}
                    }
                }
            },
            "execution_plan": []
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(invalid_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            result = await rule_engine.execute_rule("test.invalid_function", sample_document_data)
            
            assert isinstance(result, CheckResult)
            assert not result.passed
            assert "未注册" in result.message
            assert result.severity == CheckSeverity.ERROR
        finally:
            Path(temp_file).unlink()

    @pytest.mark.asyncio
    async def test_execute_check_full_plan(self, rule_engine, sample_rule_config, sample_document_data):
        """测试执行完整的检查计划"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_rule_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine.load_rules_from_file(temp_file)
            
            results = await rule_engine.execute_check(sample_document_data)
            
            # 验证结果
            assert isinstance(results, list)
            assert len(results) == 1  # 执行计划中有一个规则
            assert all(isinstance(r, CheckResult) for r in results)
        finally:
            Path(temp_file).unlink()


class TestRealRuleFile:
    """测试真实的规则文件"""

    def test_load_hbkj_bachelor_2024(self, rule_engine):
        """测试加载河北科技学院学士学位论文规则"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine.load_rules_from_file(rule_file)
        
        # 验证加载结果
        assert rule_engine.metadata["standard_id"] == "hbkj_bachelor_2024"
        assert rule_engine.metadata["name"] == "河北科技学院学士学位论文检测标准"
        assert len(rule_engine.rules) > 0
        assert len(rule_engine.execution_plan) > 0
        
        # 验证关键规则存在
        expected_rules = [
            "structure.section_order",
            "format.level_1_title",
            "format.level_2_title",
            "content.chinese_abstract_word_count"
        ]
        
        for rule_id in expected_rules:
            assert rule_id in rule_engine.rules, f"规则 {rule_id} 不存在"

    def test_ref_resolution_in_real_file(self, rule_engine):
        """测试真实文件中的引用解析"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine.load_rules_from_file(rule_file)
        
        # 测试一级标题格式规则的引用解析
        rule_config = rule_engine.rules.get("format.level_1_title")
        if rule_config:
            resolved_params = rule_engine._resolve_params(rule_config["parameters"])
            
            # 验证样式引用被正确解析
            assert "style" in resolved_params
            style = resolved_params["style"]
            assert "font_family" in style
            assert "font_size" in style
            assert "alignment" in style
            assert "bold" in style
            
            # 验证具体值
            assert style["font_family"]["value"] == "黑体"
            assert style["font_size"]["value"] == "三号"
            assert style["alignment"]["value"] == "center"
            assert style["bold"]["value"] == True
