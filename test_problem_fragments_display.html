<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题片段显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { color: #10b981; background: #ecfdf5; padding: 15px; border-radius: 6px; }
        .error { color: #ef4444; background: #fef2f2; padding: 15px; border-radius: 6px; }
        .warning { color: #f59e0b; background: #fffbeb; padding: 15px; border-radius: 6px; }
        .info { color: #3b82f6; background: #eff6ff; padding: 15px; border-radius: 6px; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #2563eb; }
        .fragment-group {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
        }
        .group-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .fragment-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        .fragment-item:last-child {
            border-bottom: none;
        }
        .severity-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        .severity-severe { background: #ef4444; }
        .severity-general { background: #f59e0b; }
        .severity-suggestion { background: #10b981; }
        .original-text {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .problem-details {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
        }
        .auto-fixable {
            color: #10b981;
            font-size: 12px;
        }
        .manual-fix {
            color: #ef4444;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 问题片段显示测试</h1>
        <p>测试修复后的问题片段结构映射，验证中文关键词问题是否能正确分组显示。</p>

        <div class="test-section">
            <h2>📋 API响应数据对比</h2>
            <div id="api-comparison"></div>
        </div>

        <div class="test-section">
            <h2>🎯 修复前后对比</h2>
            <button onclick="showBeforeFix()">显示修复前</button>
            <button onclick="showAfterFix()">显示修复后</button>
            <button onclick="showGroupedDisplay()">显示分组效果</button>
            <div id="comparison-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 问题片段分组显示</h2>
            <div id="grouped-fragments"></div>
        </div>
    </div>

    <script>
        // 模拟修复前的API响应（所有问题都归类为"正文"）
        const beforeFixData = {
            "fragments": [
                {
                    "fragment_id": "frag_e5345618",
                    "structure": "正文",  // ❌ 错误：应该是"中文关键词"
                    "category": "format",
                    "severity": "severe",
                    "original_text": "关键词:人工智能;机器学习",
                    "problem_description": "中文关键词格式检查失败：关键词段落应左对齐，当前：3",
                    "rule_id": "format.chinese_keywords",
                    "rule_name": "中文关键词格式检查",
                    "auto_fixable": false
                }
            ]
        }

        // 模拟修复后的API响应（正确的结构分类）
        const afterFixData = {
            "fragments": [
                {
                    "fragment_id": "frag_e5345618",
                    "structure": "中文关键词",  // ✅ 正确：现在正确识别为"中文关键词"
                    "category": "format",
                    "severity": "severe",
                    "original_text": "关键词:人工智能;机器学习",
                    "problem_description": "使用了英文冒号和英文分号，应使用中文冒号和中文分号",
                    "rule_id": "format.chinese_keywords",
                    "rule_name": "中文关键词格式检查",
                    "auto_fixable": true
                },
                {
                    "fragment_id": "frag_12345678",
                    "structure": "中文关键词",
                    "category": "format",
                    "severity": "general",
                    "original_text": "关键词：算法设计；数据结构；",
                    "problem_description": "最后一个关键词后不应有分号",
                    "rule_id": "format.chinese_keywords_separator",
                    "rule_name": "中文关键词分隔符检查",
                    "auto_fixable": true
                },
                {
                    "fragment_id": "frag_87654321",
                    "structure": "正文",
                    "category": "format",
                    "severity": "general",
                    "original_text": "1.2.1 国内发展现状",
                    "problem_description": "三级标题字号应为小四号(12pt)",
                    "rule_id": "format.headings",
                    "rule_name": "标题格式检查",
                    "auto_fixable": true
                }
            ]
        }

        function showBeforeFix() {
            const resultsDiv = document.getElementById('comparison-results')
            let html = '<h3>❌ 修复前：所有问题都归类为"正文"</h3>'
            
            const grouped = groupFragmentsByStructure(beforeFixData.fragments)
            
            html += '<div class="error">问题：中文关键词的格式问题被错误地归类为"正文"问题，用户无法快速定位到相关章节。</div>'
            html += renderGroupedFragments(grouped)
            
            resultsDiv.innerHTML = html
        }

        function showAfterFix() {
            const resultsDiv = document.getElementById('comparison-results')
            let html = '<h3>✅ 修复后：问题正确分组到对应结构</h3>'
            
            const grouped = groupFragmentsByStructure(afterFixData.fragments)
            
            html += '<div class="success">修复效果：中文关键词问题现在正确归类到"中文关键词"分组，用户可以快速定位问题。</div>'
            html += renderGroupedFragments(grouped)
            
            resultsDiv.innerHTML = html
        }

        function showGroupedDisplay() {
            const fragmentsDiv = document.getElementById('grouped-fragments')
            const grouped = groupFragmentsByStructure(afterFixData.fragments)
            fragmentsDiv.innerHTML = renderGroupedFragments(grouped)
        }

        function groupFragmentsByStructure(fragments) {
            const grouped = {}
            fragments.forEach(fragment => {
                if (!grouped[fragment.structure]) {
                    grouped[fragment.structure] = []
                }
                grouped[fragment.structure].push(fragment)
            })
            return grouped
        }

        function renderGroupedFragments(grouped) {
            let html = ''
            
            Object.entries(grouped).forEach(([structure, fragments]) => {
                html += `<div class="fragment-group">`
                html += `<div class="group-header">`
                html += `📋 ${structure} (${fragments.length} 个问题)`
                html += `</div>`
                
                fragments.forEach((fragment, index) => {
                    const severityClass = `severity-${fragment.severity}`
                    html += `<div class="fragment-item">`
                    html += `<div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">`
                    html += `<span style="font-weight: bold;">问题 ${index + 1}</span>`
                    html += `<span class="severity-badge ${severityClass}">${fragment.severity}</span>`
                    html += `</div>`
                    html += `<div class="original-text">${fragment.original_text}</div>`
                    html += `<div class="problem-details"><strong>问题：</strong>${fragment.problem_description}</div>`
                    html += `<div class="problem-details"><strong>规则：</strong>${fragment.rule_name} (${fragment.rule_id})</div>`
                    html += `<div class="${fragment.auto_fixable ? 'auto-fixable' : 'manual-fix'}">`
                    html += `${fragment.auto_fixable ? '✅ 可自动修复' : '❌ 需手动修复'}`
                    html += `</div>`
                    html += `</div>`
                })
                
                html += `</div>`
            })
            
            return html
        }

        function showApiComparison() {
            const comparisonDiv = document.getElementById('api-comparison')
            
            let html = '<h3>📊 API响应数据对比</h3>'
            
            html += '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">'
            
            // 修复前
            html += '<div>'
            html += '<h4 style="color: #ef4444;">❌ 修复前</h4>'
            html += '<pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-size: 12px; overflow-x: auto;">'
            html += JSON.stringify(beforeFixData.fragments[0], null, 2)
            html += '</pre>'
            html += '<div class="error">问题：structure 字段为 "正文"</div>'
            html += '</div>'
            
            // 修复后
            html += '<div>'
            html += '<h4 style="color: #10b981;">✅ 修复后</h4>'
            html += '<pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-size: 12px; overflow-x: auto;">'
            html += JSON.stringify(afterFixData.fragments[0], null, 2)
            html += '</pre>'
            html += '<div class="success">修复：structure 字段为 "中文关键词"</div>'
            html += '</div>'
            
            html += '</div>'
            
            comparisonDiv.innerHTML = html
        }

        // 页面加载时显示API对比
        document.addEventListener('DOMContentLoaded', function() {
            showApiComparison()
            showGroupedDisplay()
            console.log('🔍 问题片段显示测试页面已加载')
        })
    </script>
</body>
</html>
