"""
COM组件线程安全封装

提供COM组件的线程安全操作：
- 线程本地存储管理
- COM初始化和清理
- 线程池管理
- 线程安全的资源访问
"""

import threading
import queue
import time
from typing import Callable, Any, Optional, Dict, List
from concurrent.futures import ThreadPoolExecutor, Future
from datetime import datetime
import structlog

try:
    import pythoncom
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    pythoncom = None

from app.core.config import settings

logger = structlog.get_logger()


class COMThreadError(Exception):
    """COM线程异常"""
    pass


class COMThreadLocal:
    """COM线程本地存储"""
    
    def __init__(self):
        self._local = threading.local()
    
    def initialize_com(self) -> bool:
        """
        初始化当前线程的COM环境
        
        Returns:
            bool: 初始化是否成功
        """
        if not COM_AVAILABLE:
            raise COMThreadError("pywin32库未安装，无法初始化COM")
        
        try:
            # 检查是否已经初始化
            if hasattr(self._local, 'com_initialized') and self._local.com_initialized:
                return True
            
            # 初始化COM
            pythoncom.CoInitialize()
            self._local.com_initialized = True
            self._local.thread_id = threading.get_ident()
            self._local.initialized_at = datetime.utcnow()
            
            logger.debug(f"线程 {self._local.thread_id} COM环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化COM环境失败: {str(e)}")
            return False
    
    def cleanup_com(self) -> bool:
        """
        清理当前线程的COM环境
        
        Returns:
            bool: 清理是否成功
        """
        if not COM_AVAILABLE:
            return True
        
        try:
            if hasattr(self._local, 'com_initialized') and self._local.com_initialized:
                pythoncom.CoUninitialize()
                self._local.com_initialized = False
                
                thread_id = getattr(self._local, 'thread_id', 'Unknown')
                logger.debug(f"线程 {thread_id} COM环境清理成功")
            
            return True
            
        except Exception as e:
            logger.error(f"清理COM环境失败: {str(e)}")
            return False
    
    def is_com_initialized(self) -> bool:
        """
        检查当前线程的COM环境是否已初始化
        
        Returns:
            bool: 是否已初始化
        """
        return hasattr(self._local, 'com_initialized') and self._local.com_initialized
    
    def get_thread_info(self) -> Dict[str, Any]:
        """
        获取当前线程的COM信息
        
        Returns:
            dict: 线程信息
        """
        if not hasattr(self._local, 'com_initialized'):
            return {
                'thread_id': threading.get_ident(),
                'com_initialized': False,
                'initialized_at': None,
                'uptime_seconds': 0
            }
        
        uptime = 0
        if hasattr(self._local, 'initialized_at') and self._local.initialized_at:
            uptime = (datetime.utcnow() - self._local.initialized_at).total_seconds()
        
        return {
            'thread_id': getattr(self._local, 'thread_id', threading.get_ident()),
            'com_initialized': self._local.com_initialized,
            'initialized_at': getattr(self._local, 'initialized_at', None),
            'uptime_seconds': uptime
        }


class COMThreadManager:
    """COM线程管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.thread_local = COMThreadLocal()
        self.executor = None
        self.active_threads = {}
        self.lock = threading.Lock()
        self.shutdown_event = threading.Event()
    
    def start(self) -> bool:
        """
        启动线程池
        
        Returns:
            bool: 启动是否成功
        """
        try:
            with self.lock:
                if self.executor is not None:
                    logger.warning("线程池已经启动")
                    return True
                
                logger.info(f"正在启动COM线程池，工作线程数: {self.max_workers}")
                
                # 创建线程池
                self.executor = ThreadPoolExecutor(
                    max_workers=self.max_workers,
                    thread_name_prefix="COM-Worker"
                )
                
                self.shutdown_event.clear()
                logger.info("COM线程池启动成功")
                return True
                
        except Exception as e:
            logger.error(f"启动COM线程池失败: {str(e)}")
            return False
    
    def stop(self, wait: bool = True, timeout: float = 30.0) -> bool:
        """
        停止线程池
        
        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
            
        Returns:
            bool: 停止是否成功
        """
        try:
            with self.lock:
                if self.executor is None:
                    logger.warning("线程池未启动")
                    return True
                
                logger.info("正在停止COM线程池...")
                
                # 设置停止事件
                self.shutdown_event.set()
                
                # 关闭线程池
                self.executor.shutdown(wait=wait, timeout=timeout)
                self.executor = None
                
                # 清理活跃线程记录
                self.active_threads.clear()
                
                logger.info("COM线程池已停止")
                return True
                
        except Exception as e:
            logger.error(f"停止COM线程池失败: {str(e)}")
            return False
    
    def submit_com_task(self, func: Callable, *args, **kwargs) -> Future:
        """
        提交COM任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Future: 任务Future对象
        """
        if self.executor is None:
            if not self.start():
                raise COMThreadError("无法启动COM线程池")
        
        # 包装函数以确保COM环境正确初始化
        def com_wrapper():
            thread_id = threading.get_ident()
            
            try:
                # 记录活跃线程
                with self.lock:
                    self.active_threads[thread_id] = {
                        'started_at': datetime.utcnow(),
                        'function': func.__name__ if hasattr(func, '__name__') else str(func)
                    }
                
                # 初始化COM环境
                if not self.thread_local.initialize_com():
                    raise COMThreadError("无法初始化COM环境")
                
                # 执行任务
                logger.debug(f"线程 {thread_id} 开始执行COM任务: {func.__name__ if hasattr(func, '__name__') else 'Unknown'}")
                result = func(*args, **kwargs)
                
                logger.debug(f"线程 {thread_id} COM任务执行成功")
                return result
                
            except Exception as e:
                logger.error(f"线程 {thread_id} COM任务执行失败: {str(e)}")
                raise e
                
            finally:
                # 清理COM环境
                self.thread_local.cleanup_com()
                
                # 移除活跃线程记录
                with self.lock:
                    self.active_threads.pop(thread_id, None)
        
        return self.executor.submit(com_wrapper)
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取线程池状态
        
        Returns:
            dict: 状态信息
        """
        with self.lock:
            active_count = len(self.active_threads)
            
            # 获取活跃线程详情
            active_details = []
            for thread_id, info in self.active_threads.items():
                uptime = (datetime.utcnow() - info['started_at']).total_seconds()
                active_details.append({
                    'thread_id': thread_id,
                    'function': info['function'],
                    'started_at': info['started_at'].isoformat(),
                    'uptime_seconds': uptime
                })
            
            return {
                'running': self.executor is not None,
                'max_workers': self.max_workers,
                'active_threads': active_count,
                'active_details': active_details,
                'shutdown_requested': self.shutdown_event.is_set()
            }


class COMSafeExecutor:
    """COM安全执行器"""
    
    def __init__(self, thread_manager: Optional[COMThreadManager] = None):
        self.thread_manager = thread_manager or COMThreadManager()
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_execution_time': 0.0
        }
        self.stats_lock = threading.Lock()
    
    def execute(self, func: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """
        安全执行COM函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            timeout: 执行超时时间
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        start_time = time.time()
        
        try:
            with self.stats_lock:
                self.execution_stats['total_executions'] += 1
            
            # 提交任务到线程池
            future = self.thread_manager.submit_com_task(func, *args, **kwargs)
            
            # 等待结果
            if timeout:
                result = future.result(timeout=timeout)
            else:
                result = future.result()
            
            # 记录成功统计
            execution_time = time.time() - start_time
            with self.stats_lock:
                self.execution_stats['successful_executions'] += 1
                self.execution_stats['total_execution_time'] += execution_time
            
            return result
            
        except Exception as e:
            # 记录失败统计
            execution_time = time.time() - start_time
            with self.stats_lock:
                self.execution_stats['failed_executions'] += 1
                self.execution_stats['total_execution_time'] += execution_time
            
            logger.error(f"COM函数执行失败: {str(e)}")
            raise e
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Returns:
            dict: 统计信息
        """
        with self.stats_lock:
            total = self.execution_stats['total_executions']
            success_rate = (self.execution_stats['successful_executions'] / total * 100) if total > 0 else 0
            avg_time = (self.execution_stats['total_execution_time'] / total) if total > 0 else 0
            
            return {
                'total_executions': total,
                'successful_executions': self.execution_stats['successful_executions'],
                'failed_executions': self.execution_stats['failed_executions'],
                'success_rate': round(success_rate, 2),
                'total_execution_time': round(self.execution_stats['total_execution_time'], 2),
                'avg_execution_time': round(avg_time, 2)
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self.stats_lock:
            self.execution_stats = {
                'total_executions': 0,
                'successful_executions': 0,
                'failed_executions': 0,
                'total_execution_time': 0.0
            }


# 全局实例
_thread_manager = None
_safe_executor = None
_manager_lock = threading.Lock()


def get_com_thread_manager() -> COMThreadManager:
    """
    获取全局COM线程管理器实例
    
    Returns:
        COMThreadManager: 线程管理器实例
    """
    global _thread_manager
    
    with _manager_lock:
        if _thread_manager is None:
            max_workers = getattr(settings, 'COM_MAX_WORKERS', 4)
            _thread_manager = COMThreadManager(max_workers=max_workers)
        
        return _thread_manager


def get_com_safe_executor() -> COMSafeExecutor:
    """
    获取全局COM安全执行器实例
    
    Returns:
        COMSafeExecutor: 安全执行器实例
    """
    global _safe_executor
    
    with _manager_lock:
        if _safe_executor is None:
            thread_manager = get_com_thread_manager()
            _safe_executor = COMSafeExecutor(thread_manager)
        
        return _safe_executor


def cleanup_com_threading():
    """清理COM线程资源"""
    global _thread_manager, _safe_executor
    
    with _manager_lock:
        if _thread_manager is not None:
            _thread_manager.stop()
            _thread_manager = None
        
        if _safe_executor is not None:
            _safe_executor = None
        
        logger.info("COM线程资源已清理")


def com_thread_safe(timeout: Optional[float] = None):
    """
    COM线程安全装饰器
    
    Args:
        timeout: 执行超时时间
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs) -> Any:
            executor = get_com_safe_executor()
            return executor.execute(func, *args, timeout=timeout, **kwargs)
        
        wrapper.__name__ = func.__name__
        wrapper.__doc__ = func.__doc__
        return wrapper
    
    return decorator 