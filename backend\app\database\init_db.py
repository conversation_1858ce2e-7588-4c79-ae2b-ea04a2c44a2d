"""
数据库初始化，包含DDL语句

PostgreSQL版本 - 使用SQLAlchemy异步会话
"""
import asyncio
from typing import TYPE_CHECKING
from sqlalchemy import text

from app.core.logging import logger

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession

# PostgreSQL DDL语句
TABLES_DDL = {
    "users": """
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(50) PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(255) NOT NULL UNIQUE,
            hashed_password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            check_balance INTEGER NOT NULL DEFAULT 0 CHECK (check_balance >= 0),
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
    """,
    "users_username_idx": "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users (username);",
    "users_email_idx": "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users (email);",

    "tasks": """
        CREATE TABLE IF NOT EXISTS tasks (
            task_id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT NOT NULL,
            task_type VARCHAR(50) NOT NULL DEFAULT 'paper_check',
            analysis_options JSONB,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            started_at TIMESTAMP WITH TIME ZONE,
            completed_at TIMESTAMP WITH TIME ZONE,
            processing_time INTEGER,
            error_message TEXT,
            result JSONB,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
        );
    """,
    "tasks_status_idx": "CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks (status);",
    "tasks_created_at_idx": "CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks (created_at);",
    "tasks_type_idx": "CREATE INDEX IF NOT EXISTS idx_tasks_type ON tasks (task_type);",
    
    "documents": """
        CREATE TABLE IF NOT EXISTS documents (
            document_id VARCHAR(50) PRIMARY KEY,
            task_id VARCHAR(50) NOT NULL,
            title TEXT,
            author VARCHAR(255),
            keywords TEXT,
            abstract TEXT,
            pages INTEGER,
            words INTEGER,
            tables INTEGER,
            images INTEGER,
            created_date TIMESTAMP WITH TIME ZONE,
            modified_date TIMESTAMP WITH TIME ZONE,
            analyzed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            FOREIGN KEY(task_id) REFERENCES tasks(task_id) ON DELETE CASCADE
        );
    """,
    "documents_task_id_idx": "CREATE INDEX IF NOT EXISTS idx_documents_task_id ON documents (task_id);",
    
    "content_elements": """
        CREATE TABLE IF NOT EXISTS content_elements (
            element_id VARCHAR(50) PRIMARY KEY,
            document_id VARCHAR(50) NOT NULL,
            element_type VARCHAR(50) NOT NULL,
            content TEXT,
            style JSONB,
            font_name VARCHAR(100),
            font_size REAL,
            is_bold BOOLEAN,
            is_italic BOOLEAN,
            is_underline BOOLEAN,
            alignment VARCHAR(20),
            position INTEGER NOT NULL,
            page_number INTEGER,
            metadata JSONB,
            FOREIGN KEY(document_id) REFERENCES documents(document_id) ON DELETE CASCADE
        );
    """,
    "content_elements_doc_id_idx": "CREATE INDEX IF NOT EXISTS idx_content_elements_document_id ON content_elements (document_id);",
    "content_elements_type_idx": "CREATE INDEX IF NOT EXISTS idx_content_elements_type ON content_elements (element_type);",
    
    "images": """
        CREATE TABLE IF NOT EXISTS images (
            image_id VARCHAR(50) PRIMARY KEY,
            document_id VARCHAR(50) NOT NULL,
            hash VARCHAR(64) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            original_width REAL,
            original_height REAL,
            display_width REAL,
            display_height REAL,
            position INTEGER NOT NULL,
            page_number INTEGER,
            caption TEXT,
            properties JSONB,
            FOREIGN KEY(document_id) REFERENCES documents(document_id) ON DELETE CASCADE
        );
    """,
    "images_hash_idx": "CREATE INDEX IF NOT EXISTS idx_images_hash ON images (hash);",
    
    "paper_check_results": """
        CREATE TABLE IF NOT EXISTS paper_check_results (
            result_id VARCHAR(50) PRIMARY KEY,
            document_id VARCHAR(50) NOT NULL,
            standard_name VARCHAR(100),
            overall_score REAL CHECK (overall_score >= 0 AND overall_score <= 100),
            compliance_status VARCHAR(50),
            summary TEXT,
            checked_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            FOREIGN KEY(document_id) REFERENCES documents(document_id) ON DELETE CASCADE
        );
    """,
    
    "problems": """
        CREATE TABLE IF NOT EXISTS problems (
            problem_id VARCHAR(50) PRIMARY KEY,
            result_id VARCHAR(50) NOT NULL,
            problem_type VARCHAR(100) NOT NULL,
            severity VARCHAR(50),
            description TEXT NOT NULL,
            location TEXT,
            suggestion TEXT,
            element_id VARCHAR(50),
            FOREIGN KEY(result_id) REFERENCES paper_check_results(result_id) ON DELETE CASCADE
        );
    """,
    "problems_result_id_idx": "CREATE INDEX IF NOT EXISTS idx_problems_result_id ON problems (result_id);",
    "problems_type_idx": "CREATE INDEX IF NOT EXISTS idx_problems_type ON problems (problem_type);",
    
    "orders": """
        CREATE TABLE IF NOT EXISTS orders (
            order_id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            plan_id VARCHAR(50) NOT NULL,
            amount DECIMAL(10, 2) NOT NULL CHECK (amount > 0),
            payment_method VARCHAR(20) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            paid_at TIMESTAMP WITH TIME ZONE,
            FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
        );
    """,
    "orders_user_id_idx": "CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders (user_id);",
    "orders_status_idx": "CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status);",
    "orders_created_at_idx": "CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders (created_at);",
}

# PostgreSQL特有的功能扩展
EXTENSIONS_DDL = [
    "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",  # UUID生成函数
    "CREATE EXTENSION IF NOT EXISTS \"pg_trgm\";",    # 文本相似度搜索
]

# 触发器函数用于自动更新updated_at字段
TRIGGER_FUNCTIONS_DDL = {
    "update_modified_column": """
        CREATE OR REPLACE FUNCTION update_modified_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """,
}

# 触发器
TRIGGERS_DDL = {
    "users_update_trigger": """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_trigger WHERE tgname = 'update_users_modtime'
            ) THEN
                CREATE TRIGGER update_users_modtime
                    BEFORE UPDATE ON users
                    FOR EACH ROW
                    EXECUTE FUNCTION update_modified_column();
            END IF;
        END $$;
    """,
    "tasks_update_trigger": """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_trigger WHERE tgname = 'update_tasks_modtime'
            ) THEN
                CREATE TRIGGER update_tasks_modtime
                    BEFORE UPDATE ON tasks
                    FOR EACH ROW
                    EXECUTE FUNCTION update_modified_column();
            END IF;
        END $$;
    """,
}


async def create_all_tables(session: "AsyncSession"):
    """
    在给定的数据库会话上创建所有表、索引、扩展和触发器
    """
    logger.info("开始初始化或验证数据库表结构...")
    
    try:
        # 创建扩展
        for extension_ddl in EXTENSIONS_DDL:
            try:
                await session.execute(text(extension_ddl))
                logger.info(f"扩展已启用: {extension_ddl}")
            except Exception as e:
                logger.warning(f"扩展创建失败（可能已存在）: {e}")
        
        # 创建触发器函数
        for name, ddl in TRIGGER_FUNCTIONS_DDL.items():
            logger.info(f"正在创建触发器函数: {name}")
            await session.execute(text(ddl))
        
        # 创建表和索引
        for name, ddl in TABLES_DDL.items():
            logger.info(f"正在执行 DDL: {name}")
            await session.execute(text(ddl))
        
        # 创建触发器
        for name, ddl in TRIGGERS_DDL.items():
            logger.info(f"正在创建触发器: {name}")
            await session.execute(text(ddl))
        
        # 提交事务
        await session.commit()
        logger.info("所有表、索引、扩展和触发器已成功创建或验证存在。")
        
    except Exception as e:
        await session.rollback()
        logger.error(f"数据库初始化失败: {str(e)}")
        raise 