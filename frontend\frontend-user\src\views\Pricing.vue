<template>
  <BaseLayout 
    title="价格方案" 
    description="选择适合您的价格方案，灵活的按需付费模式"
  >
      <!-- 价格卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <!-- 基础套餐 -->
      <BaseCard class="text-center">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">基础套餐</h3>
            <div class="mb-6">
              <span class="text-4xl font-bold text-blue-600 dark:text-blue-400">¥9</span>
              <span class="text-gray-600 dark:text-gray-300">/ 3次检测</span>
            </div>
            <ul class="text-left space-y-3 mb-8">
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                支持所有分析类型
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                文件大小限制：10MB
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                详细分析报告
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                7天报告保存
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                在线客服支持
              </li>
            </ul>
        <BaseButton @click="selectPlan('basic')" variant="secondary" class="w-full">
              选择此方案
        </BaseButton>
      </BaseCard>

        <!-- 标准套餐 - 推荐 -->
      <BaseCard class="text-center relative border-2 border-blue-500 dark:border-blue-400">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-600 dark:bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
              推荐
            </span>
          </div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">标准套餐</h3>
            <div class="mb-6">
              <span class="text-4xl font-bold text-blue-600 dark:text-blue-400">¥25</span>
              <span class="text-gray-600 dark:text-gray-300">/ 10次检测</span>
              <p class="text-sm text-green-600 dark:text-green-400 mt-1">节省 ¥15</p>
            </div>
            <ul class="text-left space-y-3 mb-8">
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                包含基础套餐所有功能
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                文件大小限制：50MB
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                15天报告保存
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                批量处理支持
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                优先处理队列
              </li>
            </ul>
        <BaseButton @click="selectPlan('standard')" variant="primary" class="w-full">
              选择此方案
        </BaseButton>
      </BaseCard>

        <!-- 专业套餐 -->
      <BaseCard class="text-center">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">专业套餐</h3>
            <div class="mb-6">
              <span class="text-4xl font-bold text-blue-600 dark:text-blue-400">¥99</span>
              <span class="text-gray-600 dark:text-gray-300">/ 50次检测</span>
              <p class="text-sm text-green-600 dark:text-green-400 mt-1">节省 ¥51</p>
            </div>
            <ul class="text-left space-y-3 mb-8">
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                包含标准套餐所有功能
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                文件大小限制：200MB
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                30天报告保存
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
            API接口支持
              </li>
              <li class="flex items-center text-gray-700 dark:text-gray-300">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
            专属客户经理
              </li>
            </ul>
        <BaseButton @click="selectPlan('professional')" variant="secondary" class="w-full">
              选择此方案
        </BaseButton>
      </BaseCard>
      </div>

      <!-- 企业定制 -->
    <BaseCard class="mb-12">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">企业定制方案</h3>
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
          为大型企业和机构提供定制化解决方案
        </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <div class="h-16 w-16 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                  </svg>
                </div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">私有化部署</h4>
            <p class="text-gray-600 dark:text-gray-300">支持本地化部署，确保数据安全</p>
              </div>
          <div>
            <div class="h-16 w-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                  </svg>
                </div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">定制规则</h4>
            <p class="text-gray-600 dark:text-gray-300">根据企业标准定制检测规则</p>
              </div>
          <div>
            <div class="h-16 w-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944a11.955 11.955 0 00-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                  </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">技术支持</h4>
            <p class="text-gray-600 dark:text-gray-300">7×24小时专业技术支持</p>
          </div>
        </div>
        <BaseButton @click="contactSales" variant="primary" size="lg">
          联系销售团队
        </BaseButton>
      </div>
    </BaseCard>

      <!-- 常见问题 -->
    <BaseCard>
      <template #header>
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">常见问题</h3>
      </template>
      
      <div class="space-y-4">
        <div v-for="(faq, index) in faqs" :key="index" class="border-b border-gray-200 dark:border-gray-600 pb-4">
          <BaseButton 
            @click="toggleFAQ(index)" 
            variant="secondary" 
            class="w-full text-left justify-between p-0"
            :append-icon="faq.open ? 'M19 9l-7 7-7-7' : 'M19 9l-7 7-7-7'"
          >
            <span class="text-lg font-medium text-gray-900 dark:text-white">{{ faq.question }}</span>
          </BaseButton>
              <div v-show="faq.open" class="mt-4 text-gray-600 dark:text-gray-300">
                {{ faq.answer }}
          </div>
        </div>
      </div>
    </BaseCard>
  </BaseLayout>

    <!-- 购买确认模态框 -->
  <div v-if="showPurchaseModal" class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-60 backdrop-blur-sm">
    <div class="flex min-h-screen items-center justify-center p-4">
      <BaseCard class="relative w-full max-w-lg">
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">确认购买</h3>
            <button @click="hidePurchaseModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
          </div>
        </template>
        
        <!-- 等待支付界面 -->
        <div v-if="isWaitingForPayment" class="text-center py-8">
          <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">请扫码支付</h4>
          <img :src="qrCodeUrl" alt="支付二维码" class="mx-auto w-48 h-48 mb-4 border dark:border-gray-600 rounded-lg"/>
          <p class="text-gray-600 dark:text-gray-300">订单号: {{ getOrderNumber(currentOrderId) }}</p>
          <div class="mt-6">
            <BaseButton @click="cancelPayment" variant="secondary">取消支付</BaseButton>
          </div>
        </div>

        <!-- 套餐确认界面 -->
        <div v-else class="space-y-6">
          <div class="text-center">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ selectedPlanData?.title }}</h4>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">{{ selectedPlanData?.price }}</div>
            <p class="text-gray-600 dark:text-gray-300">{{ selectedPlanData?.description }}</p>
          </div>
          
          <div class="border-t border-b dark:border-gray-600 py-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600 dark:text-gray-300">套餐费用</span>
              <span class="font-semibold text-gray-900 dark:text-white">¥{{ selectedPlanData?.amount.toFixed(2) }}</span>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">支付方式</label>
            <div class="flex space-x-4">
              <label v-for="method in paymentMethods" :key="method.id" 
                class="flex items-center text-gray-700 dark:text-gray-300 cursor-pointer p-3 border rounded-lg flex-1 transition-all"
                :class="{ 'border-blue-500 bg-blue-50 dark:bg-blue-900/20': paymentMethod === method.id }">
                <input v-model="paymentMethod" type="radio" :value="method.id" class="sr-only">
                <component :is="method.icon" class="w-6 h-6 mr-3"/>
                <span>{{ method.name }}</span>
              </label>
            </div>
          </div>
        </div>

        <template #footer>
          <div v-if="!isWaitingForPayment" class="flex space-x-3 justify-end">
              <BaseButton @click="hidePurchaseModal" variant="secondary">取消</BaseButton>
              <BaseButton @click="processPayment" variant="primary">
              确认支付 ¥{{ selectedPlanData?.amount.toFixed(2) }}
              </BaseButton>
          </div>
        </template>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import { PaymentApi } from '@/services/paymentApi'
import { $notify } from '@/utils/useNotifications'

const WechatPayIcon = defineAsyncComponent(() => import('@/components/icons/WechatPayIcon.vue'))
const AlipayIcon = defineAsyncComponent(() => import('@/components/icons/AlipayIcon.vue'))

const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()
const paymentApi = new PaymentApi()

// 用户菜单相关
const userMenuRef = ref<HTMLElement>()
const userMenuOpen = ref(false)

// 购买模态框
const showPurchaseModal = ref(false)
const selectedPlan = ref<string | null>(null)
const paymentMethod = ref('wechat')
const isWaitingForPayment = ref(false)
const qrCodeUrl = ref('')
const currentOrderId = ref('')
const paymentPoller = ref<any>(null)

// 支付方式数据
const paymentMethods = [
  { id: 'wechat', name: '微信支付', icon: WechatPayIcon },
  { id: 'alipay', name: '支付宝', icon: AlipayIcon },
]

// 套餐数据
const plans = {
  basic: { id: 'basic', title: '基础套餐', price: '¥9', description: '/ 3次检测', amount: 9 },
  standard: { id: 'standard', title: '标准套餐', price: '¥25', description: '/ 10次检测', amount: 25 },
  professional: { id: 'professional', title: '专业套餐', price: '¥99', description: '/ 50次检测', amount: 99 }
}

// 常见问题
const faqs = ref([
  {
    question: '如何购买检测次数？',
    answer: '选择适合的套餐后，点击"选择此方案"按钮，系统会引导您完成支付流程。支付成功后，检测次数会立即添加到您的账户中。',
    open: false
  },
  {
    question: '检测次数有使用期限吗？',
    answer: '检测次数购买后永久有效，没有使用期限。您可以根据自己的需要随时使用。',
    open: false
  },
  {
    question: '支持哪些支付方式？',
    answer: '我们支持微信支付、支付宝、银行卡支付等多种支付方式。企业客户还可以申请对公转账。',
    open: false
  },
  {
    question: '可以开具发票吗？',
    answer: '可以。我们支持开具电子发票和纸质发票。在订单页面填写发票信息即可。企业客户可以开具增值税专用发票。',
    open: false
  }
])

// 计算当前选中套餐数据
const selectedPlanData = computed(() => {
  if (!selectedPlan.value) return null
  return plans[selectedPlan.value as keyof typeof plans]
})

// 获取订单号显示格式（与Orders.vue保持一致）
const getOrderNumber = (orderId: string) => {
  // 如果订单号以 "order_" 开头，去掉前缀并显示完整的12位编号
  if (orderId.startsWith('order_')) {
    return `#${orderId.substring(6).toUpperCase()}`
  }
  // 兼容其他格式的订单号
  return `#${orderId.slice(-12).toUpperCase()}`
}

// 切换用户菜单
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    userMenuOpen.value = false
  }
}

// 选择套餐
const selectPlan = (planId: string) => {
  if (!localStorage.getItem('access_token')) {
    $notify.warning('请先登录后再进行购买。')
    router.push('/auth');
    return;
  }
  selectedPlan.value = planId
  showPurchaseModal.value = true
}

// 隐藏购买模态框
const hidePurchaseModal = () => {
  showPurchaseModal.value = false
  selectedPlan.value = null
  isWaitingForPayment.value = false
  if (paymentPoller.value) {
    clearInterval(paymentPoller.value)
    paymentPoller.value = null
  }
}

// 处理支付
const processPayment = async () => {
  if (!selectedPlan.value) return;

  try {
    const response = await paymentApi.createOrder({
      plan_id: selectedPlan.value,
      payment_method: paymentMethod.value,
    });
    
    qrCodeUrl.value = response.qr_code_url || ''
    currentOrderId.value = response.order_id
    isWaitingForPayment.value = true
    
    startPollingOrderStatus(response.order_id)

  } catch (error) {
    console.error("创建订单失败:", error)
    $notify.error("创建订单失败，请稍后再试。")
  }
}

const startPollingOrderStatus = (orderId: string) => {
  paymentPoller.value = setInterval(async () => {
    try {
      const order = await paymentApi.getOrderStatus(orderId)
      if (order.status === 'paid') {
        clearInterval(paymentPoller.value)
        paymentPoller.value = null
        $notify.success('支付成功！您的账户余额已更新。')
        hidePurchaseModal()
        // 刷新用户数据
        await userStore.fetchCurrentUser()
      }
    } catch (error) {
      console.error("查询订单状态失败:", error)
      // 错误时继续轮询
    }
  }, 3000)
}

const cancelPayment = async () => {
  if (currentOrderId.value) {
    try {
      // 调用后端API取消订单
      await paymentApi.cancelOrder(currentOrderId.value)
      $notify.warning('支付已取消，订单已关闭')
    } catch (error) {
      console.error('取消订单失败:', error)
      // 即使取消失败，也要关闭支付界面
      $notify.error('取消订单失败，但支付界面已关闭')
    }
  }
  
  isWaitingForPayment.value = false
  if (paymentPoller.value) {
    clearInterval(paymentPoller.value)
    paymentPoller.value = null
  }
  
  // 重置订单ID
  currentOrderId.value = ''
  qrCodeUrl.value = ''
}

// 联系销售团队
const contactSales = () => {
  console.log('联系销售团队')
  // 这里可以跳转到联系页面或弹出联系表单
}

// 切换FAQ
const toggleFAQ = (index: number) => {
  faqs.value[index].open = !faqs.value[index].open
}

// 退出登录
const logout = () => {
  userStore.logout()
  router.push('/auth')
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script> 