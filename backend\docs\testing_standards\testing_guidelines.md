# Word文档分析服务 - 测试标准和指导

## 📋 文档概述

本文档定义了Word文档分析服务项目的测试标准、最佳实践和指导原则，基于Stage 9测试优化阶段的成功实践制定。

**文档版本**: v1.0  
**制定日期**: 2025-06-17  
**适用范围**: 所有项目开发人员和测试人员

## 🎯 测试目标和原则

### 测试目标
1. **质量保障**: 确保代码质量和功能正确性
2. **回归防护**: 防止新功能破坏现有功能
3. **文档化**: 测试即文档，说明代码行为
4. **快速反馈**: 提供快速的开发反馈循环
5. **持续改进**: 支持代码重构和优化

### 测试原则
- **测试驱动**: 优先编写测试，后实现功能
- **全面覆盖**: 追求高代码覆盖率和场景覆盖
- **独立性**: 测试之间相互独立，可并行执行
- **可重复**: 测试结果稳定可重复
- **快速执行**: 优化测试执行速度

## 🏗️ 测试架构标准

### 测试分层架构

```
┌─────────────────────────────────────────────────┐
│                端到端测试 (E2E)                  │  ← 少量，关键业务流程
├─────────────────────────────────────────────────┤
│                集成测试 (Integration)            │  ← 适量，模块间交互
├─────────────────────────────────────────────────┤
│                单元测试 (Unit)                   │  ← 大量，核心业务逻辑
└─────────────────────────────────────────────────┘
```

### 测试类型定义

| 测试类型 | 占比 | 执行速度 | 覆盖范围 | 示例 |
|----------|------|----------|----------|------|
| 单元测试 | 70% | 快 | 单个函数/类 | 数据模型验证 |
| 集成测试 | 20% | 中 | 模块间交互 | API端点测试 |
| 端到端测试 | 10% | 慢 | 完整业务流程 | 文档处理流程 |

## 📁 测试目录结构标准

```
tests/
├── conftest.py                 # 全局测试配置和fixtures
├── pytest.ini                 # pytest配置文件
├── unit/                      # 单元测试
│   ├── test_core_modules.py   # 核心模块测试
│   ├── test_models.py         # 数据模型测试
│   ├── test_services.py       # 业务服务测试
│   └── test_utils.py          # 工具函数测试
├── integration/               # 集成测试
│   ├── test_api_endpoints.py  # API端点测试
│   ├── test_database.py       # 数据库集成测试
│   └── test_external_services.py # 外部服务集成测试
├── error_handling/            # 错误处理测试
│   ├── test_error_scenarios.py # 异常场景测试
│   └── test_retry_mechanisms.py # 重试机制测试
├── performance/               # 性能测试
│   ├── test_benchmarks.py     # 性能基准测试
│   └── test_load.py           # 负载测试
├── fixtures/                  # 测试数据和夹具
│   ├── test_data.py           # 测试数据工厂
│   └── sample_files/          # 示例文件
└── utils/                     # 测试工具
    ├── helpers.py             # 测试辅助函数
    └── assertions.py          # 自定义断言
```

## 🔧 测试框架配置标准

### pytest配置 (pytest.ini)

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=30
    -v
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    error_handling: 错误处理测试
    slow: 慢速测试
    external: 需要外部依赖的测试
    windows_only: 仅Windows平台测试
asyncio_mode = auto
```

### 覆盖率配置 (.coveragerc)

```ini
[run]
source = app
omit = 
    app/tests/*
    app/__pycache__/*
    app/*/__pycache__/*
    */venv/*
    */virtualenv/*
    */site-packages/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov
```

## 📝 测试命名规范

### 测试文件命名
- **单元测试**: `test_<module_name>.py`
- **集成测试**: `test_<feature_name>_integration.py`
- **性能测试**: `test_<feature_name>_performance.py`

### 测试函数命名
使用描述性命名，遵循 `test_<action>_<condition>_<expected_result>` 模式：

```python
# ✅ 好的命名示例
def test_create_task_with_valid_data_should_return_success():
    """使用有效数据创建任务应该返回成功"""
    pass

def test_create_task_with_invalid_file_size_should_raise_validation_error():
    """使用无效文件大小创建任务应该抛出验证错误"""
    pass

def test_word_com_interface_when_word_not_available_should_gracefully_degrade():
    """Word COM接口在Word不可用时应该优雅降级"""
    pass

# ❌ 不好的命名示例
def test_task():
    pass

def test_create():
    pass

def test_error():
    pass
```

### 测试类命名
```python
class TestTaskManager:
    """任务管理器测试类"""
    pass

class TestDocumentProcessor:
    """文档处理器测试类"""
    pass

class TestAPIEndpoints:
    """API端点测试类"""
    pass
```

## 🧪 测试编写标准

### 单元测试标准

```python
import pytest
from unittest.mock import Mock, patch
from app.models.task import TaskModel

class TestTaskModel:
    """任务模型单元测试"""
    
    def test_task_creation_with_valid_data(self):
        """测试使用有效数据创建任务"""
        # Arrange
        task_data = {
            "id": "test-task-001",
            "filename": "test.docx",
            "file_size": 1024,
            "task_type": "analysis",
            "status": "pending"
        }
        
        # Act
        task = TaskModel(**task_data)
        
        # Assert
        assert task.id == "test-task-001"
        assert task.filename == "test.docx"
        assert task.status == "pending"
        assert task.file_size == 1024
    
    def test_task_creation_with_invalid_file_size(self):
        """测试使用无效文件大小创建任务"""
        # Arrange
        task_data = {
            "id": "test-task-002",
            "filename": "test.docx",
            "file_size": -1,  # 无效的文件大小
            "task_type": "analysis",
            "status": "pending"
        }
        
        # Act & Assert
        with pytest.raises(ValueError) as exc_info:
            TaskModel(**task_data)
        
        assert "文件大小必须大于0" in str(exc_info.value)
```

### 集成测试标准

```python
import pytest
from httpx import AsyncClient
from app.main import app

class TestAPIEndpoints:
    """API端点集成测试"""
    
    @pytest.mark.asyncio
    async def test_health_check_endpoint(self):
        """测试健康检查端点"""
        # Arrange
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # Act
            response = await client.get("/health")
            
            # Assert
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_task_creation_endpoint(self):
        """测试任务创建端点"""
        # Arrange
        task_data = {
            "filename": "test.docx",
            "file_size": 1024,
            "task_type": "analysis"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            
            # Act
            response = await client.post("/api/v1/tasks", json=task_data)
            
            # Assert
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "task_id" in data["data"]
```

### 错误处理测试标准

```python
import pytest
from unittest.mock import Mock, patch
from app.core.exceptions import DocumentAnalysisException

class TestErrorHandling:
    """错误处理测试"""
    
    def test_document_analysis_exception_handling(self):
        """测试文档分析异常处理"""
        # Arrange & Act & Assert
        with pytest.raises(DocumentAnalysisException) as exc_info:
            raise DocumentAnalysisException("文档分析失败")
        
        assert "文档分析失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_database_connection_error_handling(self):
        """测试数据库连接错误处理"""
        # Arrange
        with patch('app.database.connection.get_connection') as mock_conn:
            mock_conn.side_effect = Exception("数据库连接失败")
            
            # Act & Assert
            with pytest.raises(Exception) as exc_info:
                from app.database.connection import get_connection
                await get_connection()
            
            assert "数据库连接失败" in str(exc_info.value)
```

## 🎭 Mock和Fixture标准

### Fixture设计原则
1. **作用域合理**: 根据需要选择合适的作用域 (function, class, module, session)
2. **资源清理**: 确保资源正确清理，避免测试间相互影响
3. **参数化**: 支持参数化以测试多种场景
4. **文档完整**: 提供清晰的文档说明

### 常用Fixture示例

```python
import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock

@pytest.fixture(scope="session")
async def test_database():
    """测试数据库fixture - PostgreSQL测试数据库"""
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
    from sqlalchemy.orm import sessionmaker
    from app.database.init_db import create_tables
    
    # 使用测试数据库
    test_db_url = "postgresql+asyncpg://postgres:password@localhost:5432/word_service_test"
    engine = create_async_engine(test_db_url, echo=False)
    
    # 创建表结构
    await create_tables(engine)
    
    # 创建会话工厂
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    yield async_session
    
    # 清理
    await engine.dispose()

@pytest.fixture
def mock_redis():
    """Mock Redis连接"""
    redis_mock = Mock()
    redis_mock.ping = Mock(return_value=True)
    redis_mock.set = Mock(return_value=True)
    redis_mock.get = Mock(return_value=None)
    return redis_mock

@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path, ignore_errors=True)

@pytest.fixture(params=["small", "medium", "large"])
def sample_file_size(request):
    """参数化文件大小fixture"""
    sizes = {
        "small": 1024,
        "medium": 1024 * 1024,
        "large": 10 * 1024 * 1024
    }
    return sizes[request.param]
```

### Mock策略标准

#### 外部依赖Mock
```python
# Word COM接口Mock
@pytest.fixture
def mock_word_com():
    """Mock Word COM接口"""
    with patch('win32com.client.Dispatch') as mock_dispatch:
        word_app = MagicMock()
        word_app.Visible = False
        word_app.Documents = MagicMock()
        
        doc = MagicMock()
        doc.Name = "test.docx"
        doc.Words.Count = 1000
        
        word_app.Documents.Open.return_value = doc
        mock_dispatch.return_value = word_app
        
        yield word_app

# 数据库Mock
@pytest.fixture
def mock_database():
    """Mock数据库连接"""
    with patch('app.database.connection.get_connection') as mock_conn:
        conn = Mock()
        conn.execute = Mock()
        conn.fetchone = Mock(return_value=None)
        conn.fetchall = Mock(return_value=[])
        mock_conn.return_value = conn
        yield conn
```

## 📊 代码覆盖率标准

### 覆盖率目标
- **总体覆盖率**: ≥ 80%
- **核心业务逻辑**: ≥ 90%
- **API端点**: ≥ 85%
- **错误处理**: ≥ 75%

### 覆盖率分级标准

| 覆盖率等级 | 范围 | 状态 | 要求 |
|------------|------|------|------|
| 🏆 优秀 | ≥90% | 绿色 | 保持现状 |
| 📈 良好 | 70-89% | 黄色 | 持续改进 |
| 📉 待改进 | 50-69% | 橙色 | 优先提升 |
| ❌ 不合格 | <50% | 红色 | 立即改进 |

### 覆盖率监控
```bash
# 生成覆盖率报告
pytest --cov=app --cov-report=html --cov-report=term-missing

# 设置覆盖率阈值
pytest --cov=app --cov-fail-under=80

# 查看详细覆盖率
coverage report --show-missing
```

## 🚀 性能测试标准

### 性能测试类型
1. **基准测试**: 建立性能基线
2. **负载测试**: 正常负载下的性能
3. **压力测试**: 高负载下的性能
4. **稳定性测试**: 长时间运行稳定性

### 性能指标标准

| 指标类型 | 目标值 | 测量方法 |
|----------|--------|----------|
| API响应时间 | <200ms | HTTP请求测试 |
| 任务处理时间 | <30s/文档 | 端到端测试 |
| 并发处理能力 | ≥10个任务 | 并发测试 |
| 内存使用 | <500MB | 资源监控 |

### 性能测试示例

```python
import pytest
import time
import asyncio
from app.services.task_manager import TaskManager

class TestPerformance:
    """性能测试"""
    
    @pytest.mark.performance
    def test_task_creation_performance(self):
        """测试任务创建性能"""
        start_time = time.time()
        
        # 创建100个任务
        for i in range(100):
            task_data = {
                "id": f"perf-test-{i}",
                "filename": f"test-{i}.docx",
                "file_size": 1024,
                "task_type": "analysis"
            }
            # 模拟任务创建
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 性能断言：100个任务创建应该在1秒内完成
        assert duration < 1.0, f"任务创建耗时过长: {duration:.2f}秒"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_processing_performance(self):
        """测试并发处理性能"""
        task_manager = TaskManager(max_workers=5)
        
        start_time = time.time()
        
        # 提交10个并发任务
        tasks = []
        for i in range(10):
            task = asyncio.create_task(
                task_manager.submit_task(f"concurrent-{i}", "analysis")
            )
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 性能断言：10个并发任务应该在5秒内完成
        assert duration < 5.0, f"并发任务处理耗时过长: {duration:.2f}秒"
```

## 🔄 持续集成标准

### CI/CD流水线配置

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio httpx
    
    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml --cov-report=term-missing
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    
    - name: Quality Gate
      run: |
        pytest --cov=app --cov-fail-under=80
```

### 测试执行策略
1. **快速反馈**: 优先运行单元测试
2. **并行执行**: 支持并行测试执行
3. **智能跳过**: 跳过不相关的测试
4. **失败快速**: 遇到失败立即停止

## 📋 测试检查清单

### 代码提交前检查
- [ ] 所有测试通过
- [ ] 代码覆盖率达标
- [ ] 新功能有对应测试
- [ ] 测试命名规范
- [ ] Mock使用合理
- [ ] 性能测试通过

### 代码审查检查
- [ ] 测试逻辑正确
- [ ] 测试场景完整
- [ ] 异常处理测试
- [ ] 边界条件测试
- [ ] 测试可维护性
- [ ] 测试文档完整

### 发布前检查
- [ ] 全量测试通过
- [ ] 性能测试达标
- [ ] 集成测试通过
- [ ] 回归测试通过
- [ ] 覆盖率报告生成
- [ ] 测试报告归档

## 🛠️ 测试工具和环境

### 必需工具
- **pytest**: 测试框架
- **pytest-cov**: 覆盖率监控
- **pytest-asyncio**: 异步测试支持
- **httpx**: HTTP客户端测试
- **unittest.mock**: Mock对象

### 可选工具
- **pytest-xdist**: 并行测试执行
- **pytest-benchmark**: 性能基准测试
- **pytest-html**: HTML测试报告
- **allure-pytest**: 详细测试报告

### 环境要求
- **Python**: 3.8+
- **操作系统**: Windows 10/11
- **内存**: ≥4GB
- **磁盘空间**: ≥1GB

## 📚 最佳实践总结

### 测试设计最佳实践
1. **AAA模式**: Arrange-Act-Assert结构
2. **单一职责**: 每个测试只验证一个行为
3. **独立性**: 测试之间相互独立
4. **可读性**: 测试代码清晰易懂
5. **可维护性**: 易于修改和扩展

### 常见反模式避免
- ❌ 测试依赖外部环境
- ❌ 测试之间有依赖关系
- ❌ 测试逻辑过于复杂
- ❌ 缺少异常情况测试
- ❌ Mock使用过度或不当

### 团队协作规范
1. **统一标准**: 遵循团队测试标准
2. **代码审查**: 测试代码也需要审查
3. **知识分享**: 分享测试经验和技巧
4. **持续改进**: 定期优化测试流程
5. **文档维护**: 及时更新测试文档

## 📞 支持和反馈

### 技术支持
- **文档问题**: 查看项目Wiki或提交Issue
- **工具问题**: 参考官方文档或社区支持
- **环境问题**: 联系项目维护者

### 改进建议
- **标准更新**: 根据项目发展更新测试标准
- **工具升级**: 评估和引入新的测试工具
- **流程优化**: 持续优化测试流程和效率

---

**文档维护者**: 项目开发团队  
**最后更新**: 2025-06-17  
**版本**: v1.0  
**下次审查**: 2025-09-17 