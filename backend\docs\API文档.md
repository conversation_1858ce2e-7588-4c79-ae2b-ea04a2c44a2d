# Word文档分析服务 - 后端API文档 (完整版)

## 📋 文档信息

**服务名称**: Word文档分析服务  
**API版本**: v1.0.0  
**基础URL**: `http://localhost:8000` (开发环境)  
**文档更新时间**: 2024-12-19  
**API端点总数**: 42个  
**验证状态**: ✅ 93.8%通过，生产就绪  

## 🔗 快速链接

- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs) (推荐使用)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **健康检查**: [http://localhost:8000/health](http://localhost:8000/health)

## 🏗️ API架构概览

### 模块分布
| 模块 | 端点数量 | 功能描述 |
|------|----------|----------|
| 🔐 认证 | 5个 | 用户注册、登录、信息管理 |
| 📄 文档 | 13个 | 文档上传、分析、导出 |
| 📋 任务 | 10个 | 任务创建、监控、管理 |
| 🖼️ 图片 | 7个 | 图片提取、下载、管理 |
| 💳 支付 | 5个 | 订单、支付、套餐管理 |
| 🖥️ 系统 | 11个 | 统计、监控、维护 |
| 🔗 WebSocket | 1个 | 实时通信 |
| ❤️ 健康检查 | 2个 | 服务状态检查 |

### 服务架构
```
┌─────────────────────────────────────────┐
│              前端应用                    │
├─────────────────────────────────────────┤
│              API网关                     │
├─────────────────────────────────────────┤
│  认证服务 │ 文档服务 │ 任务服务 │ 系统服务  │
├─────────────────────────────────────────┤
│        Word COM接口 │ 检测引擎 (v2.0)     │
├─────────────────────────────────────────┤
│      PostgreSQL数据库 │ Redis缓存       │
└─────────────────────────────────────────┘
```

## 🔐 认证机制

### JWT Token认证
所有需要认证的API都使用JWT Bearer Token认证。

**请求头格式**:
```http
Authorization: Bearer <your_jwt_token>
```

**Token获取流程**:
1. 用户注册: `POST /api/v1/auth/register`
2. 用户登录: `POST /api/v1/auth/login`
3. 获取Token，在后续请求中使用

**Token有效期**: 24小时  
**刷新机制**: 使用refresh_token刷新access_token

### 前端认证实现示例
```javascript
// 存储Token
const token = localStorage.getItem('access_token');

// API请求配置
const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  headers: {
    'Authorization': token ? `Bearer ${token}` : ''
  }
});

// 响应拦截器处理401
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Token过期，跳转登录
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## 📊 统一响应格式

### 成功响应
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": 1703234567,
  "request_id": "req_123456789"
}
```

### 错误响应
```json
{
  "success": false,
  "code": 400,
  "message": "错误描述",
  "data": null,
  "timestamp": 1703234567,
  "request_id": "req_123456789"
}
```

### 状态码说明
- **200**: 成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未认证或Token过期
- **403**: 权限不足
- **404**: 资源不存在
- **413**: 文件过大
- **415**: 不支持的文件格式
- **422**: 请求验证失败
- **500**: 服务器内部错误
- **503**: 服务不可用

## 🔑 1. 认证API (5个端点)

### 1.1 用户注册
**端点**: `POST /api/v1/auth/register`  
**描述**: 新用户注册  
**认证**: 无需认证  

**请求参数**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**参数说明**:
- `username` (string, 必需): 用户名，3-20字符，仅字母数字下划线
- `email` (string, 必需): 邮箱地址
- `password` (string, 必需): 密码，最少8字符

**前端调用示例**:
```javascript
const registerUser = async (userData) => {
  try {
    const response = await apiClient.post('/api/v1/auth/register', userData);
    const { token, refresh_token, user } = response.data.data;
    
    // 存储Token
    localStorage.setItem('access_token', token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return { success: true, user };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '注册失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 201,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "user_id": "user_123",
      "username": "testuser",
      "email": "<EMAIL>",
      "is_active": true
    },
    "expires_in": 86400
  }
}
```

### 1.2 用户登录
**端点**: `POST /api/v1/auth/login`  
**描述**: 用户登录获取Token  
**认证**: 无需认证  

**请求参数** (表单格式):
```
username=testuser&password=password123
```

**Content-Type**: `application/x-www-form-urlencoded`

**前端调用示例**:
```javascript
const loginUser = async (username, password) => {
  try {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    
    const response = await apiClient.post('/api/v1/auth/login', formData);
    const { access_token, refresh_token, user } = response.data.data;
    
    // 存储Token
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return { success: true, user };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '登录失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 86400,
    "user": {
      "user_id": "user_123",
      "username": "testuser", 
      "email": "<EMAIL>",
      "is_active": true
    }
  }
}
```

### 1.3 获取当前用户信息
**端点**: `GET /api/v1/auth/me`  
**描述**: 获取当前用户基本信息  
**认证**: 需要Bearer Token  

**前端调用示例**:
```javascript
const getCurrentUser = async () => {
  try {
    const response = await apiClient.get('/api/v1/auth/me');
    return { success: true, user: response.data.data };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取用户信息失败' 
    };
  }
};
```

### 1.4 获取用户详细信息
**端点**: `GET /api/v1/auth/profile`  
**描述**: 获取用户详细信息和统计数据  
**认证**: 需要Bearer Token  

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取用户详细信息成功",
  "data": {
    "user_id": "user_123",
    "username": "testuser",
    "email": "<EMAIL>",
    "check_balance": 10,
    "created_at": "2024-12-19T10:00:00Z",
    "statistics": {
      "total_tasks": 28,
      "completed_tasks": 25,
      "total_documents": 15
    }
  }
}
```

### 1.5 用户登出
**端点**: `POST /api/v1/auth/logout`  
**描述**: 用户登出，使Token失效  
**认证**: 需要Bearer Token  

**前端调用示例**:
```javascript
const logoutUser = async () => {
  try {
    await apiClient.post('/api/v1/auth/logout');
    
    // 清除本地Token
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    
    return { success: true };
  } catch (error) {
    // 即使失败也清除本地Token
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    return { success: true };
  }
};
```

## 📄 2. 文档管理API (13个端点)

### 2.1 上传文档
**端点**: `POST /api/v1/documents/upload`  
**描述**: 上传Word文档并创建分析任务  
**认证**: 需要Bearer Token  
**Content-Type**: `multipart/form-data`  

**请求参数**:
- `file` (File, 必需): Word文档文件 (.doc/.docx)
- `analysis_type` (string, 可选): 分析类型，默认"paper_check"
  - `paper_check`: 论文检测
  - `format_check`: 格式检查
  - `content_analysis`: 内容分析
- `options` (string, 可选): 分析选项的JSON字符串

**文件要求**:
- 格式: .doc, .docx
- 大小: 最大50MB
- 编码: 支持中文内容

**前端调用示例**:
```javascript
const uploadDocument = async (file, analysisType = 'paper_check', options = {}) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('analysis_type', analysisType);
    formData.append('options', JSON.stringify(options));
    
    const response = await apiClient.post('/api/v1/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        console.log(`上传进度: ${progress}%`);
      }
    });
    
    return { success: true, data: response.data.data };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '上传失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "分析任务已创建，正在等待处理",
  "data": {
    "task_id": "task_abc123",
    "filename": "论文.docx",
    "file_size": 2560385,
    "status": "pending_analysis",
    "remaining_balance": 9
  }
}
```

### 2.2 获取文档详情
**端点**: `GET /api/v1/documents/{document_id}`  
**描述**: 获取文档基本信息  
**认证**: 需要Bearer Token  

**路径参数**:
- `document_id` (string): 文档ID

**前端调用示例**:
```javascript
const getDocumentDetail = async (documentId) => {
  try {
    const response = await apiClient.get(`/api/v1/documents/${documentId}`);
    return { success: true, document: response.data.data };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取文档失败' 
    };
  }
};
```

### 2.3 获取文档列表
**端点**: `GET /api/v1/documents/`  
**描述**: 获取用户的文档列表  
**认证**: 需要Bearer Token  

**查询参数**:
- `page` (integer, 可选): 页码，默认1
- `limit` (integer, 可选): 每页数量，默认20，最大100
- `status` (string, 可选): 状态筛选

**前端调用示例**:
```javascript
const getDocuments = async (page = 1, limit = 20, status = null) => {
  try {
    const params = { page, limit };
    if (status) params.status = status;
    
    const response = await apiClient.get('/api/v1/documents/', { params });
    return { 
      success: true, 
      documents: response.data.data.documents,
      pagination: response.data.data.pagination 
    };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取文档列表失败' 
    };
  }
};
```

### 2.4 删除文档
**端点**: `DELETE /api/v1/documents/{document_id}`  
**描述**: 删除指定文档  
**认证**: 需要Bearer Token  

### 2.5 启动文档分析
**端点**: `POST /api/v1/documents/{document_id}/analyze`
**描述**: 对已上传的文档启动分析（使用v2.0检测引擎）
**认证**: 需要Bearer Token

**请求体**:
```json
{
  "analysis_type": "paper_check",
  "options": {
    "standard": "gbt_7714_2015",
    "check_format": true,
    "check_references": true
  }
}
```

**🆕 v2.0检测引擎特性**:
- **分层规则系统**: 支持模块化配置和引用机制
- **智能错误处理**: 提供用户友好的错误提示和改进建议
- **全面检测覆盖**: 结构、格式、内容三维度检测
- **高性能执行**: 毫秒级响应，支持大文档处理

### 2.6 获取文档检测报告
**端点**: `GET /api/v1/documents/{document_id}/report`  
**描述**: 获取文档检测报告  
**认证**: 需要Bearer Token  

**查询参数**:
- `format` (string, 可选): 报告格式，支持json/html/text，默认json

**前端调用示例**:
```javascript
const getDocumentReport = async (documentId, format = 'json') => {
  try {
    const response = await apiClient.get(
      `/api/v1/documents/${documentId}/report`,
      { params: { format } }
    );
    return { success: true, report: response.data.data };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取报告失败' 
    };
  }
};
```

### 2.7 获取文档内容
**端点**: `GET /api/v1/documents/{document_id}/content`  
**描述**: 获取文档的解析内容  
**认证**: 需要Bearer Token  

**查询参数**:
- `include_text` (boolean): 是否包含文本内容，默认true
- `include_images` (boolean): 是否包含图片信息，默认true
- `element_type` (string): 内容元素类型筛选

### 2.8 获取文档分析结果
**端点**: `GET /api/v1/documents/{document_id}/analysis`  
**描述**: 获取文档的分析结果  
**认证**: 需要Bearer Token  

### 2.9 获取文档内容元素
**端点**: `GET /api/v1/documents/{document_id}/elements`  
**描述**: 获取文档的内容元素列表  
**认证**: 需要Bearer Token  

**查询参数**:
- `element_type` (string): 元素类型筛选
- `page` (integer): 页码，默认1
- `limit` (integer): 每页数量，默认50，最大200

### 2.10 获取文档图片列表
**端点**: `GET /api/v1/documents/{document_id}/images`  
**描述**: 获取文档中的图片列表  
**认证**: 需要Bearer Token  

### 2.11 获取文档问题列表
**端点**: `GET /api/v1/documents/{document_id}/problems`  
**描述**: 获取文档检测发现的问题  
**认证**: 需要Bearer Token  

**查询参数**:
- `severity` (string): 问题严重程度筛选 (critical/warning/info)
- `category` (string): 问题类别筛选
- `page` (integer): 页码
- `limit` (integer): 每页数量

### 2.12 根据任务获取文档
**端点**: `GET /api/v1/documents/task/{task_id}`  
**描述**: 根据任务ID获取相关文档  
**认证**: 需要Bearer Token  

### 2.13 导出文档报告
**端点**: `GET /api/v1/documents/{document_id}/export`  
**描述**: 导出文档报告  
**认证**: 需要Bearer Token  

**查询参数**:
- `format` (string): 导出格式 (json/pdf/html)
- `include_problems` (boolean): 是否包含问题详情
- `include_suggestions` (boolean): 是否包含修改建议

## 📋 3. 任务管理API (10个端点)

### 3.1 创建任务
**端点**: `POST /api/v1/tasks/`  
**描述**: 创建新的分析任务  
**认证**: 需要Bearer Token  

**请求体**:
```json
{
  "task_type": "paper_check",
  "file_path": "/path/to/document.docx",
  "options": {
    "priority": 5,
    "timeout": 300
  },
  "description": "论文检测任务"
}
```

### 3.2 上传文件创建任务
**端点**: `POST /api/v1/tasks/upload`  
**描述**: 上传文件并创建任务  
**认证**: 需要Bearer Token  
**Content-Type**: `multipart/form-data`

### 3.3 获取任务详情
**端点**: `GET /api/v1/tasks/{task_id}`  
**描述**: 获取指定任务的详细信息  
**认证**: 需要Bearer Token  

**前端调用示例**:
```javascript
const getTaskDetail = async (taskId) => {
  try {
    const response = await apiClient.get(`/api/v1/tasks/${taskId}`);
    return { success: true, task: response.data };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取任务失败' 
    };
  }
};
```

### 3.4 获取任务状态
**端点**: `GET /api/v1/tasks/{task_id}/status`  
**描述**: 获取任务状态信息  
**认证**: 不需要认证  

**前端轮询示例**:
```javascript
const pollTaskStatus = (taskId, callback, interval = 2000) => {
  const poll = async () => {
    try {
      const response = await apiClient.get(`/api/v1/tasks/${taskId}/status`);
      const task = response.data.data;
      
      callback({ success: true, task });
      
      // 如果任务未完成，继续轮询
      if (!['completed', 'failed', 'cancelled'].includes(task.status)) {
        setTimeout(poll, interval);
      }
    } catch (error) {
      callback({ 
        success: false, 
        message: error.response?.data?.message || '获取状态失败' 
      });
    }
  };
  
  poll();
};
```

### 3.5 取消任务
**端点**: `POST /api/v1/tasks/{task_id}/cancel`  
**描述**: 取消指定任务  
**认证**: 不需要认证  

### 3.6 获取任务列表
**端点**: `GET /api/v1/tasks/`  
**描述**: 获取任务列表，支持分页和筛选  
**认证**: 需要Bearer Token  

**查询参数**:
- `status` (TaskStatus): 任务状态筛选
- `task_type` (TaskType): 任务类型筛选
- `page` (integer): 页码，默认1
- `limit` (integer): 每页数量，默认20，最大100

**前端调用示例**:
```javascript
const getTasks = async (filters = {}) => {
  try {
    const response = await apiClient.get('/api/v1/tasks/', { 
      params: {
        page: 1,
        limit: 20,
        ...filters
      }
    });
    
    return { 
      success: true, 
      tasks: response.data.data.tasks,
      pagination: response.data.data.pagination 
    };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取任务列表失败' 
    };
  }
};
```

### 3.7 更新任务状态
**端点**: `PUT /api/v1/tasks/{task_id}/status`  
**描述**: 更新任务状态  
**认证**: 不需要认证  

**请求体**:
```json
{
  "status": "completed",
  "message": "任务完成"
}
```

### 3.8 删除任务
**端点**: `DELETE /api/v1/tasks/{task_id}`  
**描述**: 彻底删除任务和相关文件  
**认证**: 需要Bearer Token  

### 3.9 批量删除任务
**端点**: `POST /api/v1/tasks/batch-delete`  
**描述**: 批量删除多个任务  
**认证**: 需要Bearer Token  

**请求体**:
```json
{
  "task_ids": ["task_123", "task_456", "task_789"]
}
```

### 3.10 获取任务进度
**端点**: `GET /api/v1/tasks/{task_id}/progress`  
**描述**: 获取任务执行进度  
**认证**: 不需要认证  

## 🖼️ 4. 图片管理API (7个端点)

### 4.1 获取文档图片列表
**端点**: `GET /api/v1/images/document/{document_id}`  
**描述**: 获取指定文档的图片列表  
**认证**: 需要Bearer Token  

### 4.2 获取图片信息
**端点**: `GET /api/v1/images/{image_id}`  
**描述**: 获取图片详细信息  
**认证**: 需要Bearer Token  

### 4.3 下载图片
**端点**: `GET /api/v1/images/{image_id}/download`  
**描述**: 下载原始图片文件  
**认证**: 不需要认证  

**前端调用示例**:
```javascript
const downloadImage = async (imageId, filename) => {
  try {
    const response = await apiClient.get(
      `/api/v1/images/${imageId}/download`,
      { responseType: 'blob' }
    );
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename || `image_${imageId}.jpg`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '下载失败' 
    };
  }
};
```

### 4.4 获取图片缩略图
**端点**: `GET /api/v1/images/{image_id}/thumbnail`  
**描述**: 获取图片缩略图  
**认证**: 不需要认证  

**查询参数**:
- `size` (integer): 缩略图大小，默认200，范围50-500

### 4.5 获取任务相关图片
**端点**: `GET /api/v1/images/task/{task_id}`  
**描述**: 获取任务相关的所有图片  
**认证**: 需要Bearer Token  

### 4.6 获取图片列表
**端点**: `GET /api/v1/images/`  
**描述**: 获取图片列表，支持筛选  
**认证**: 需要Bearer Token  

**查询参数**:
- `format` (string): 图片格式筛选
- `min_width` (integer): 最小宽度筛选
- `min_height` (integer): 最小高度筛选
- `page` (integer): 页码
- `limit` (integer): 每页数量

### 4.7 删除图片
**端点**: `DELETE /api/v1/images/{image_id}`  
**描述**: 删除指定图片  
**认证**: 需要Bearer Token  

## 💳 5. 支付系统API (5个端点)

### 5.1 创建支付订单
**端点**: `POST /api/v1/payments/create-order`  
**描述**: 创建支付订单  
**认证**: 需要Bearer Token  

**请求体**:
```json
{
  "plan_id": "standard",
  "payment_method": "alipay"
}
```

**支付方式**:
- `alipay`: 支付宝
- `wechat`: 微信支付

**前端调用示例**:
```javascript
const createOrder = async (planId, paymentMethod) => {
  try {
    const response = await apiClient.post('/api/v1/payments/create-order', {
      plan_id: planId,
      payment_method: paymentMethod
    });
    
    return { 
      success: true, 
      orderInfo: response.data.data 
    };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '创建订单失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order_id": "order_abc123",
    "qr_code_url": "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=order:order_abc123",
    "message": "订单已创建，请扫描二维码完成支付"
  }
}
```

### 5.2 获取订单状态
**端点**: `GET /api/v1/payments/order/{order_id}`  
**描述**: 查询订单支付状态  
**认证**: 需要Bearer Token  

**前端轮询示例**:
```javascript
const pollOrderStatus = (orderId, callback, interval = 3000) => {
  const poll = async () => {
    try {
      const response = await apiClient.get(`/api/v1/payments/order/${orderId}`);
      const order = response.data.data;
      
      callback({ success: true, order });
      
      // 如果订单未支付，继续轮询
      if (order.status === 'pending') {
        setTimeout(poll, interval);
      }
    } catch (error) {
      callback({ 
        success: false, 
        message: error.response?.data?.message || '查询订单失败' 
      });
    }
  };
  
  poll();
};
```

### 5.3 取消订单
**端点**: `POST /api/v1/payments/order/{order_id}/cancel`  
**描述**: 取消待支付订单  
**认证**: 需要Bearer Token  

### 5.4 获取支付历史
**端点**: `GET /api/v1/payments/`  
**描述**: 获取用户支付历史  
**认证**: 需要Bearer Token  

**查询参数**:
- `skip` (integer): 跳过数量，默认0
- `limit` (integer): 返回数量，默认20
- `status` (string): 状态筛选
- `search` (string): 搜索关键词
- `date_range` (string): 日期范围 (today/week/month/year)

### 5.5 获取套餐信息
**端点**: `GET /api/v1/payments/plans`  
**描述**: 获取可用的支付套餐  
**认证**: 不需要认证  

**前端调用示例**:
```javascript
const getPaymentPlans = async () => {
  try {
    const response = await apiClient.get('/api/v1/payments/plans');
    return { 
      success: true, 
      plans: response.data.data.plans 
    };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取套餐失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取套餐信息成功",
  "data": {
    "plans": [
      {
        "plan_id": "basic",
        "name": "基础套餐",
        "price": 9.0,
        "checks": 10,
        "description": "包含 10 次检测"
      },
      {
        "plan_id": "standard", 
        "name": "标准套餐",
        "price": 25.0,
        "checks": 30,
        "description": "包含 30 次检测"
      },
      {
        "plan_id": "professional",
        "name": "专业套餐", 
        "price": 99.0,
        "checks": 150,
        "description": "包含 150 次检测"
      }
    ]
  }
}
```

## 🖥️ 6. 系统管理API (11个端点)

### 6.1 获取系统统计
**端点**: `GET /api/v1/system/stats`  
**描述**: 获取系统统计信息  
**认证**: 不需要认证  

**前端调用示例**:
```javascript
const getSystemStats = async () => {
  try {
    const response = await apiClient.get('/api/v1/system/stats');
    return { 
      success: true, 
      stats: response.data.data 
    };
  } catch (error) {
    return { 
      success: false, 
      message: error.response?.data?.message || '获取统计失败' 
    };
  }
};
```

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取系统统计信息成功",
  "data": {
    "total_tasks": 1250,
    "completed_tasks": 1180,
    "failed_tasks": 25,
    "pending_tasks": 10,
    "processing_tasks": 5,
    "total_documents": 980,
    "total_problems": 2450,
    "avg_compliance_score": 85.6
  }
}
```

### 6.2 获取任务类型统计
**端点**: `GET /api/v1/system/stats/tasks`  
**描述**: 获取各类型任务的统计信息  
**认证**: 不需要认证  

### 6.3 获取问题统计
**端点**: `GET /api/v1/system/stats/problems`  
**描述**: 获取问题严重程度统计  
**认证**: 不需要认证  

### 6.4 获取系统性能
**端点**: `GET /api/v1/system/performance`  
**描述**: 获取系统性能信息  
**认证**: 不需要认证  

**响应示例**:
```json
{
  "success": true,
  "code": 200,
  "message": "获取系统性能信息成功",
  "data": {
    "cpu_usage": 15.2,
    "memory_usage": 68.5,
    "disk_usage": 45.8,
    "active_tasks": 5,
    "queue_size": 10,
    "uptime": "3 days, 12:45:30"
  }
}
```

### 6.5 获取分析报告
**端点**: `GET /api/v1/system/report`  
**描述**: 获取系统分析报告  
**认证**: 不需要认证  

**查询参数**:
- `period` (string): 统计周期 (1d/7d/30d/90d)，默认7d
- `include_details` (boolean): 是否包含详细信息，默认false

### 6.6 获取系统日志
**端点**: `GET /api/v1/system/logs`  
**描述**: 获取系统日志  
**认证**: 需要管理员权限  

**查询参数**:
- `level` (string): 日志级别，默认INFO
- `limit` (integer): 返回条数，默认100，最大1000
- `module` (string): 模块筛选

### 6.7 清理旧数据
**端点**: `POST /api/v1/system/maintenance/cleanup`  
**描述**: 清理旧数据  
**认证**: 需要管理员权限  

**查询参数**:
- `days` (integer): 清理多少天前的数据，默认30
- `dry_run` (boolean): 是否为试运行，默认true

### 6.8 获取系统信息
**端点**: `GET /api/v1/system/info`  
**描述**: 获取系统基本信息  
**认证**: 不需要认证  

### 6.9 获取系统配置
**端点**: `GET /api/v1/system/config`  
**描述**: 获取系统配置信息  
**认证**: 不需要认证  

### 6.10 获取系统健康状态
**端点**: `GET /api/v1/system/health`  
**描述**: 获取系统健康状态  
**认证**: 不需要认证  

### 6.11 清理系统数据
**端点**: `POST /api/v1/system/cleanup`  
**描述**: 清理系统数据  
**认证**: 需要管理员权限  

## 🔗 7. WebSocket API (1个端点)

### 7.1 实时通信连接
**端点**: `GET /api/v1/ws`  
**协议**: WebSocket  
**描述**: 建立WebSocket连接，接收实时通知  

**前端实现示例**:
```javascript
class WebSocketService {
  constructor(token) {
    this.token = token;
    this.ws = null;
    this.listeners = {};
  }
  
  connect() {
    const wsUrl = `ws://localhost:8000/api/v1/ws?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      // 自动重连
      setTimeout(() => this.connect(), 5000);
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }
  
  handleMessage(data) {
    const { type, payload } = data;
    if (this.listeners[type]) {
      this.listeners[type].forEach(callback => callback(payload));
    }
  }
  
  on(type, callback) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(callback);
  }
  
  off(type, callback) {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter(cb => cb !== callback);
    }
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// 使用示例
const wsService = new WebSocketService(localStorage.getItem('access_token'));
wsService.connect();

// 监听任务状态更新
wsService.on('task_status_update', (data) => {
  console.log('任务状态更新:', data);
  // 更新UI
});

// 监听文档分析完成
wsService.on('document_analysis_complete', (data) => {
  console.log('文档分析完成:', data);
  // 更新UI
});
```

**消息格式**:
```json
{
  "type": "task_status_update",
  "payload": {
    "task_id": "task_123",
    "status": "completed",
    "progress": 100,
    "message": "分析完成"
  }
}
```

## ❤️ 8. 健康检查API (2个端点)

### 8.1 基础健康检查
**端点**: `GET /health`  
**描述**: 基础服务健康检查  
**认证**: 不需要认证  

**前端调用示例**:
```javascript
const checkHealth = async () => {
  try {
    const response = await fetch('http://localhost:8000/health');
    const data = await response.json();
    return { success: true, health: data };
  } catch (error) {
    return { success: false, message: '服务不可用' };
  }
};
```

### 8.2 详细健康检查
**端点**: `GET /health/detailed`  
**描述**: 详细的系统健康检查  
**认证**: 不需要认证  

## 🔧 错误处理

### 常见错误类型
```javascript
// 标准错误处理
const handleApiError = (error) => {
  if (error.response) {
    // 服务器返回错误
    const { status, data } = error.response;
    switch (status) {
      case 400:
        return '请求参数错误';
      case 401:
        // 清除Token，跳转登录
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        return '请重新登录';
      case 403:
        return '权限不足';
      case 404:
        return '资源不存在';
      case 413:
        return '文件过大，请上传小于50MB的文件';
      case 415:
        return '不支持的文件格式，请上传Word文档';
      case 422:
        return '请求验证失败，请检查输入';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 503:
        return '服务暂时不可用，请稍后重试';
      default:
        return data?.message || '请求失败';
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络';
  } else {
    // 其他错误
    return '请求处理失败';
  }
};
```

### 重试机制
```javascript
const apiWithRetry = async (apiCall, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      // 对于网络错误或5xx错误进行重试
      if (error.code === 'NETWORK_ERROR' || 
          (error.response?.status >= 500 && error.response?.status < 600)) {
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        continue;
      }
      
      throw error;
    }
  }
};
```

## 🚀 最佳实践

### 1. API客户端配置
```javascript
// 创建API客户端
const createApiClient = () => {
  const client = axios.create({
    baseURL: 'http://localhost:8000',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  });
  
  // 请求拦截器 - 添加Token
  client.interceptors.request.use(
    config => {
      const token = localStorage.getItem('access_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    error => Promise.reject(error)
  );
  
  // 响应拦截器 - 统一错误处理
  client.interceptors.response.use(
    response => response,
    error => {
      const message = handleApiError(error);
      // 可以在这里触发全局通知
      console.error('API错误:', message);
      return Promise.reject(error);
    }
  );
  
  return client;
};

export const apiClient = createApiClient();
```

### 2. 状态管理集成 (Pinia示例)
```javascript
// stores/api.js
import { defineStore } from 'pinia';
import { apiClient } from '@/services/api';

export const useApiStore = defineStore('api', {
  state: () => ({
    loading: false,
    error: null
  }),
  
  actions: {
    async apiCall(apiFunction, ...args) {
      this.loading = true;
      this.error = null;
      
      try {
        const result = await apiFunction(...args);
        return result;
      } catch (error) {
        this.error = handleApiError(error);
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
});
```

### 3. 类型定义 (TypeScript)
```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp: number;
  request_id: string;
}

export interface Document {
  document_id: string;
  task_id: string;
  filename: string;
  file_size: number;
  page_count?: number;
  word_count?: number;
  created_at: string;
  status: string;
}

export interface Task {
  task_id: string;
  user_id: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface PaymentPlan {
  plan_id: string;
  name: string;
  price: number;
  checks: number;
  description: string;
}
```

## 📝 总结

本API文档包含了Word文档分析服务的**42个API端点**，涵盖了：

- ✅ **完整的用户认证流程**
- ✅ **文档上传和分析功能** 
- ✅ **任务管理和状态跟踪**
- ✅ **图片处理和下载**
- ✅ **支付系统和订单管理**
- ✅ **系统监控和维护**
- ✅ **实时通信支持**

**开发建议**:
1. 优先使用 **Swagger UI** (`http://localhost:8000/docs`) 进行API测试
2. 实现统一的**错误处理机制**
3. 使用**WebSocket**接收实时状态更新
4. 对文件上传实现**进度显示**
5. 对任务状态实现**轮询或WebSocket更新**

**重要提示**:
- 所有需要认证的API都使用 **Bearer Token**
- 文件上传限制 **50MB**，仅支持 **.doc/.docx** 格式
- 任务状态变更会通过 **WebSocket** 实时推送
- API响应格式已统一，错误处理机制完善

这份文档为前端开发提供了完整的API接口说明，可以直接用于开发集成！ 