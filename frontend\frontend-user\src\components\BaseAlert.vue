<template>
  <Transition
    enter-active-class="transition ease-out duration-300 transform"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      :class="alertClasses"
      role="alert"
      :aria-describedby="`alert-${id}`"
    >
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <svg :class="iconClasses" fill="currentColor" viewBox="0 0 20 20">
          <path v-if="type === 'success'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          <path v-else-if="type === 'error'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          <path v-else-if="type === 'warning'" fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          <path v-else-if="type === 'info'" fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>

      <!-- 内容区域 -->
      <div class="ml-3 flex-1">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- 标题 -->
            <h3 v-if="title" :class="titleClasses" :id="`alert-${id}`">
              {{ title }}
            </h3>
            
            <!-- 消息内容 -->
            <div :class="messageClasses">
              <slot>{{ message }}</slot>
            </div>

            <!-- 操作按钮 -->
            <div v-if="actions.length > 0" class="mt-4">
              <div class="flex space-x-3">
                <button
                  v-for="(action, index) in actions"
                  :key="index"
                  :class="actionButtonClasses(action.type)"
                  @click="handleAction(action)"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>
          </div>

          <!-- 关闭按钮 -->
          <div v-if="closable" class="ml-4 flex-shrink-0">
            <button
              :class="closeButtonClasses"
              @click="close"
              aria-label="关闭"
            >
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue'

export interface AlertAction {
  label: string
  type?: 'primary' | 'secondary'
  handler: () => void
}

interface Props {
  // 显示状态
  modelValue?: boolean
  // 警告类型
  type?: 'success' | 'error' | 'warning' | 'info'
  // 标题
  title?: string
  // 消息内容
  message?: string
  // 是否可关闭
  closable?: boolean
  // 自动关闭时间(毫秒)，0表示不自动关闭
  duration?: number
  // 操作按钮
  actions?: AlertAction[]
  // 唯一标识
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: true,
  type: 'info',
  closable: true,
  duration: 0,
  actions: () => [],
  id: () => `alert-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'action': [action: AlertAction]
}>()

const visible = ref(props.modelValue)

// 计算样式类
const alertClasses = computed(() => {
  const baseClasses = 'rounded-md p-4 shadow-sm border transition-all duration-200'
  
  const typeClasses = {
    success: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
    error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
    warning: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
    info: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
  }
  
  return `${baseClasses} ${typeClasses[props.type]} flex`
})

const iconClasses = computed(() => {
  const baseClasses = 'h-5 w-5'
  
  const typeClasses = {
    success: 'text-green-400 dark:text-green-300',
    error: 'text-red-400 dark:text-red-300',
    warning: 'text-yellow-400 dark:text-yellow-300',
    info: 'text-blue-400 dark:text-blue-300'
  }
  
  return `${baseClasses} ${typeClasses[props.type]}`
})

const titleClasses = computed(() => {
  const typeClasses = {
    success: 'text-green-800 dark:text-green-200',
    error: 'text-red-800 dark:text-red-200',
    warning: 'text-yellow-800 dark:text-yellow-200',
    info: 'text-blue-800 dark:text-blue-200'
  }
  
  return `text-sm font-medium ${typeClasses[props.type]}`
})

const messageClasses = computed(() => {
  const typeClasses = {
    success: 'text-green-700 dark:text-green-300',
    error: 'text-red-700 dark:text-red-300',
    warning: 'text-yellow-700 dark:text-yellow-300',
    info: 'text-blue-700 dark:text-blue-300'
  }
  
  const marginClass = props.title ? 'mt-1' : ''
  
  return `text-sm ${typeClasses[props.type]} ${marginClass}`
})

const closeButtonClasses = computed(() => {
  const typeClasses = {
    success: 'text-green-400 dark:text-green-300 hover:text-green-500 dark:hover:text-green-200',
    error: 'text-red-400 dark:text-red-300 hover:text-red-500 dark:hover:text-red-200',
    warning: 'text-yellow-400 dark:text-yellow-300 hover:text-yellow-500 dark:hover:text-yellow-200',
    info: 'text-blue-400 dark:text-blue-300 hover:text-blue-500 dark:hover:text-blue-200'
  }
  
  return `inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 ${typeClasses[props.type]}`
})

const actionButtonClasses = (actionType: string = 'secondary') => {
  const baseClasses = 'text-sm font-medium rounded-md px-3 py-1.5 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  if (actionType === 'primary') {
    const primaryClasses = {
      success: 'bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 focus:ring-green-500',
      error: 'bg-red-600 dark:bg-red-500 text-white hover:bg-red-700 dark:hover:bg-red-600 focus:ring-red-500',
      warning: 'bg-yellow-600 dark:bg-yellow-500 text-white hover:bg-yellow-700 dark:hover:bg-yellow-600 focus:ring-yellow-500',
      info: 'bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 focus:ring-blue-500'
    }
    return `${baseClasses} ${primaryClasses[props.type]}`
  } else {
    const secondaryClasses = {
      success: 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 hover:bg-green-200 dark:hover:bg-green-700 focus:ring-green-500',
      error: 'bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-700 focus:ring-red-500',
      warning: 'bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-700 focus:ring-yellow-500',
      info: 'bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-700 focus:ring-blue-500'
    }
    return `${baseClasses} ${secondaryClasses[props.type]}`
  }
}

// 关闭方法
const close = () => {
  visible.value = false
  emit('update:modelValue', false)
  emit('close')
}

// 处理操作按钮点击
const handleAction = (action: AlertAction) => {
  action.handler()
  emit('action', action)
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

// 监听visible变化
watch(visible, (newValue) => {
  if (!newValue) {
    emit('update:modelValue', false)
  }
})

// 自动关闭
onMounted(() => {
  if (props.duration > 0) {
    setTimeout(() => {
      close()
    }, props.duration)
  }
})
</script>

<style scoped>
/* 额外的样式如有需要可以在这里添加 */
</style> 