# Word文档分析服务 - 暗黑模式设计规范

## 📋 设计概述

为了提供更好的用户体验，特别是在低光环境下的使用舒适度，我们为用户端添加了暗黑模式功能。该功能支持明亮模式和暗黑模式的无缝切换，并保持用户的偏好设置。

## 🎨 色彩系统

### 明亮模式 (Light Mode)
```css
/* 背景色系 */
--bg-primary: #ffffff      /* 主要背景 */
--bg-secondary: #f9fafb    /* 次要背景 */
--bg-tertiary: #f3f4f6     /* 第三级背景 */

/* 文字色系 */
--text-primary: #111827    /* 主要文字 */
--text-secondary: #6b7280  /* 次要文字 */
--text-tertiary: #9ca3af   /* 第三级文字 */

/* 边框色系 */
--border-primary: #e5e7eb  /* 主要边框 */
--border-secondary: #d1d5db /* 次要边框 */

/* 品牌色系 */
--primary-500: #3b82f6     /* 主品牌色 */
--primary-600: #2563eb     /* 深品牌色 */
--primary-700: #1d4ed8     /* 更深品牌色 */
```

### 暗黑模式 (Dark Mode)
```css
/* 背景色系 */
--bg-primary: #111827      /* 主要背景 */
--bg-secondary: #1f2937    /* 次要背景 */
--bg-tertiary: #374151     /* 第三级背景 */

/* 文字色系 */
--text-primary: #f9fafb    /* 主要文字 */
--text-secondary: #d1d5db  /* 次要文字 */
--text-tertiary: #9ca3af   /* 第三级文字 */

/* 边框色系 */
--border-primary: #374151  /* 主要边框 */
--border-secondary: #4b5563 /* 次要边框 */

/* 品牌色系 */
--primary-500: #60a5fa     /* 主品牌色 */
--primary-600: #3b82f6     /* 深品牌色 */
--primary-700: #2563eb     /* 更深品牌色 */
```

## 🔧 实现方案

### 1. Tailwind CSS Dark Mode
使用 Tailwind CSS 的 `dark:` 前缀实现暗黑模式样式：

```html
<!-- 明亮模式：白色背景，深色文字 -->
<!-- 暗黑模式：深色背景，浅色文字 -->
<div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
```

### 2. 主题切换器组件
在导航栏添加主题切换按钮：

```html
<button id="theme-toggle" class="theme-toggle-btn">
  <!-- 太阳图标 (明亮模式时显示) -->
  <svg class="sun-icon hidden dark:block">...</svg>
  <!-- 月亮图标 (暗黑模式时显示) -->
  <svg class="moon-icon block dark:hidden">...</svg>
</button>
```

### 3. JavaScript 主题管理
```javascript
class ThemeManager {
  constructor() {
    this.init();
  }
  
  init() {
    // 从localStorage获取保存的主题
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
      this.enableDarkMode();
    } else {
      this.enableLightMode();
    }
    
    this.setupToggleButton();
    this.watchSystemChanges();
  }
  
  enableDarkMode() {
    document.documentElement.classList.add('dark');
    localStorage.setItem('theme', 'dark');
    this.updateToggleButton('dark');
  }
  
  enableLightMode() {
    document.documentElement.classList.remove('dark');
    localStorage.setItem('theme', 'light');
    this.updateToggleButton('light');
  }
  
  toggleTheme() {
    if (document.documentElement.classList.contains('dark')) {
      this.enableLightMode();
    } else {
      this.enableDarkMode();
    }
  }
}
```

## 🎯 设计原则

### 1. 对比度要求
- **文字对比度**: 最小4.5:1 (WCAG AA标准)
- **图标对比度**: 最小3:1
- **按钮对比度**: 最小4.5:1

### 2. 颜色适配规则
- **背景渐变**: 明亮模式使用浅色渐变，暗黑模式使用深色渐变
- **阴影效果**: 暗黑模式中减少阴影强度，增加边框来区分层次
- **品牌色调整**: 暗黑模式中稍微调亮品牌色，提高可见性

### 3. 组件适配
- **卡片组件**: 暗黑模式使用深灰色背景和浅色边框
- **表单组件**: 输入框背景调整，占位符颜色适配
- **按钮组件**: 调整按钮背景和文字颜色
- **图标组件**: 自动适配图标颜色

## 📱 响应式适配

### 移动端优化
- 主题切换按钮在移动端保持可用性
- 触摸友好的切换交互
- 保持与其他移动端元素的一致性

### 平板端优化
- 合理的触摸目标大小
- 优化的布局间距
- 与桌面端体验的连续性

## 🔄 状态管理

### 用户偏好保存
- 使用 `localStorage` 保存用户主题偏好
- 支持系统主题自动检测
- 在页面刷新后保持主题状态

### 动态切换动画
```css
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
```

## 🧪 测试标准

### 视觉测试
- [ ] 所有页面在两种模式下都正常显示
- [ ] 文字对比度符合无障碍标准
- [ ] 图标和按钮在两种模式下都清晰可见
- [ ] 颜色搭配协调，无视觉冲突

### 功能测试
- [ ] 主题切换按钮正常工作
- [ ] 用户偏好正确保存和恢复
- [ ] 系统主题检测正常
- [ ] 页面刷新后主题保持

### 兼容性测试
- [ ] 主流浏览器兼容性测试
- [ ] 移动设备兼容性测试
- [ ] 无障碍访问测试

## 🎨 实现清单

### 用户端页面 (12个页面)
- [ ] index.html - 首页
- [ ] auth.html - 登录注册
- [ ] forgot-password.html - 忘记密码
- [ ] reset-password.html - 重置密码
- [ ] dashboard.html - 仪表盘
- [ ] upload.html - 文档上传
- [ ] documents.html - 文档管理
- [ ] document-detail.html - 文档详情
- [ ] tasks.html - 任务中心
- [ ] pricing.html - 套餐购买
- [ ] orders.html - 订单管理
- [ ] profile.html - 个人中心

### 公共页面 (3个页面)
- [ ] help.html - 帮助中心
- [ ] maintenance.html - 维护页面
- [ ] 404.html - 错误页面

### 共享组件
- [ ] 主题切换器组件
- [ ] 主题管理JavaScript类
- [ ] 暗黑模式CSS样式库
- [ ] 图标适配

## 🚀 实施计划

### Phase 1: 核心实现 (1-2天)
1. 创建主题管理JavaScript类
2. 实现主题切换器组件
3. 建立暗黑模式CSS规范

### Phase 2: 用户端页面实现 (3-4天)
1. 首页和认证页面暗黑模式
2. 仪表盘和文档管理暗黑模式
3. 其他用户功能页面暗黑模式

### Phase 3: 测试和优化 (1天)
1. 全面功能测试
2. 视觉效果调优
3. 性能优化

---

**创建时间**: 2024-01-22  
**版本**: v1.0  
**负责人**: 前端开发团队 