<template>
  <teleport to="body">
    <transition name="modal-backdrop" appear>
      <div
        v-if="visible"
        class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm"
        @click="handleCancel"
        role="dialog"
        :aria-labelledby="titleId"
        :aria-describedby="contentId"
        aria-modal="true"
      >
        <transition name="modal-content" appear>
          <div
            @click.stop
            class="relative w-full max-w-md bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-y-auto"
          >
            <!-- 图标区域 -->
            <div class="px-6 pt-6">
              <div :class="[
                'mx-auto flex items-center justify-center w-12 h-12 rounded-full mb-4',
                iconBgClass
              ]">
                <!-- Warning Icon -->
                <svg v-if="type === 'warning'" :class="['w-6 h-6', iconColorClass]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                
                <!-- Danger Icon -->
                <svg v-else-if="type === 'danger'" :class="['w-6 h-6', iconColorClass]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                
                <!-- Success Icon -->
                <svg v-else-if="type === 'success'" :class="['w-6 h-6', iconColorClass]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                
                <!-- Info Icon -->
                <svg v-else :class="['w-6 h-6', iconColorClass]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="px-6 pb-6 text-center">
              <h3 
                :id="titleId"
                class="text-lg font-semibold text-gray-900 dark:text-white mb-3"
              >
                {{ title }}
              </h3>
              <p 
                :id="contentId"
                class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed"
              >
                {{ message }}
              </p>
            </div>
            
            <!-- 按钮区域 -->
            <div class="flex space-x-3 px-6 pb-6">
              <button
                @click="handleCancel"
                class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors"
              >
                {{ cancelText }}
              </button>
              <button
                @click="handleConfirm"
                :class="[
                  'flex-1 px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors',
                  confirmButtonClass
                ]"
                :disabled="loading"
              >
                <span v-if="loading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  处理中...
                </span>
                <span v-else>{{ confirmText }}</span>
              </button>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface ConfirmDialogProps {
  title?: string
  message?: string
  type?: 'info' | 'warning' | 'danger' | 'success'
  confirmText?: string
  cancelText?: string
  loading?: boolean
}

const props = withDefaults(defineProps<ConfirmDialogProps>(), {
  title: '确认操作',
  message: '确定要执行此操作吗？',
  type: 'info',
  confirmText: '确定',
  cancelText: '取消',
  loading: false
})

const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

// 显示状态
const visible = ref(false)

// 生成唯一ID
const titleId = computed(() => `confirm-title-${Math.random().toString(36).substr(2, 9)}`)
const contentId = computed(() => `confirm-content-${Math.random().toString(36).substr(2, 9)}`)

// 图标背景样式
const iconBgClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'bg-yellow-100 dark:bg-yellow-900/30'
    case 'danger':
      return 'bg-red-100 dark:bg-red-900/30'
    case 'success':
      return 'bg-green-100 dark:bg-green-900/30'
    default:
      return 'bg-blue-100 dark:bg-blue-900/30'
  }
})

// 图标颜色样式
const iconColorClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'text-yellow-600 dark:text-yellow-400'
    case 'danger':
      return 'text-red-600 dark:text-red-400'
    case 'success':
      return 'text-green-600 dark:text-green-400'
    default:
      return 'text-blue-600 dark:text-blue-400'
  }
})

// 确认按钮样式
const confirmButtonClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
    case 'danger':
      return 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    case 'success':
      return 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
    default:
      return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
  }
})

// 显示对话框
const show = () => {
  visible.value = true
}

// 隐藏对话框
const hide = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm')
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  hide()
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!visible.value) return

  if (event.key === 'Escape') {
    handleCancel()
  } else if (event.key === 'Enter') {
    handleConfirm()
  }
}

// 暴露方法
defineExpose({
  show,
  hide
})

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 超丝滑动画优化 */
.modal-backdrop-enter-active {
  transition: all 0.08s ease-out;
}

.modal-backdrop-leave-active {
  transition: all 0.12s ease-in;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
  backdrop-filter: blur(0px);
}

.modal-content-enter-active {
  transition: all 0.18s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-content-leave-active {
  transition: all 0.12s ease-in;
}

.modal-content-enter-from {
  transform: scale(0.92) translateY(-12px);
  opacity: 0;
}

.modal-content-leave-to {
  transform: scale(0.98) translateY(4px);
  opacity: 0;
}

/* 性能优化 */
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  will-change: opacity, backdrop-filter;
}

.modal-content-enter-active,
.modal-content-leave-active {
  will-change: transform, opacity;
}
</style> 