# ==================================================
# Word文档分析服务 - 环境诊断脚本
# ==================================================

param(
    [string]$Mode = "full",                 # full, quick, service, system, network
    [switch]$Fix = $false,                  # 尝试自动修复发现的问题
    [switch]$Export = $false,               # 导出诊断报告
    [string]$OutputPath = ".\diagnostics", # 报告输出路径
    [switch]$Verbose = $false,              # 详细输出
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
Word文档分析服务 - 环境诊断脚本

用法:
    .\diagnostics.ps1 [参数]

参数:
    -Mode           诊断模式 [默认: full]
                   full:     完整诊断(所有检查)
                   quick:    快速诊断(基本检查)
                   service:  服务状态诊断
                   system:   系统环境诊断
                   network:  网络连接诊断
    
    -Fix            尝试自动修复发现的问题 [默认: false]
    -Export         导出诊断报告到文件 [默认: false]
    -OutputPath     报告输出路径 [默认: .\diagnostics]
    -Verbose        显示详细信息 [默认: false]
    -Help           显示此帮助信息

示例:
    .\diagnostics.ps1                               # 完整诊断
    .\diagnostics.ps1 -Mode quick                   # 快速诊断
    .\diagnostics.ps1 -Mode service                 # 服务诊断
    .\diagnostics.ps1 -Fix                          # 诊断并自动修复
    .\diagnostics.ps1 -Export -OutputPath "D:\Reports"  # 导出报告
    .\diagnostics.ps1 -Verbose                      # 详细输出
"@ -ForegroundColor Green
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Continue"

# 全局变量
$DiagnosticResults = @{
    Checks = @()
    Errors = @()
    Warnings = @()
    Info = @()
    FixedIssues = @()
}

$TotalChecks = 0
$PassedChecks = 0
$FailedChecks = 0
$WarningChecks = 0

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO"  { 
            Write-Host $logMessage -ForegroundColor Green 
            $DiagnosticResults.Info += $Message
        }
        "WARN"  { 
            Write-Host $logMessage -ForegroundColor Yellow 
            $DiagnosticResults.Warnings += $Message
        }
        "ERROR" { 
            Write-Host $logMessage -ForegroundColor Red 
            $DiagnosticResults.Errors += $Message
        }
        "DEBUG" { 
            if ($Verbose) {
                Write-Host $logMessage -ForegroundColor Gray 
            }
        }
        "PASS"  { 
            Write-Host $logMessage -ForegroundColor Green 
            $DiagnosticResults.Info += $Message
        }
        "FAIL"  { 
            Write-Host $logMessage -ForegroundColor Red 
            $DiagnosticResults.Errors += $Message
        }
        default { Write-Host $logMessage }
    }
}

# 检查结果记录
function Add-CheckResult {
    param(
        [string]$CheckName,
        [string]$Status,  # PASS, FAIL, WARN
        [string]$Message,
        [string]$Details = "",
        [string]$FixSuggestion = ""
    )
    
    $script:TotalChecks++
    
    $result = @{
        Name = $CheckName
        Status = $Status
        Message = $Message
        Details = $Details
        FixSuggestion = $FixSuggestion
        Timestamp = Get-Date
    }
    
    $DiagnosticResults.Checks += $result
    
    switch ($Status) {
        "PASS" { 
            $script:PassedChecks++
            Write-Log "✓ $CheckName: $Message" "PASS"
        }
        "FAIL" { 
            $script:FailedChecks++
            Write-Log "✗ $CheckName: $Message" "FAIL"
            if ($FixSuggestion) {
                Write-Log "  建议: $FixSuggestion" "INFO"
            }
        }
        "WARN" { 
            $script:WarningChecks++
            Write-Log "⚠ $CheckName: $Message" "WARN"
        }
    }
    
    if ($Verbose -and $Details) {
        Write-Log "  详情: $Details" "DEBUG"
    }
}

# 系统环境检查
function Test-SystemEnvironment {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "系统环境检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    # 检查操作系统
    try {
        $os = Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, OSArchitecture
        Add-CheckResult "操作系统" "PASS" "Windows $($os.WindowsProductName) $($os.WindowsVersion)" "$($os.OSArchitecture)"
    } catch {
        Add-CheckResult "操作系统" "FAIL" "无法获取操作系统信息" $_.Exception.Message
    }
    
    # 检查PowerShell版本
    $psVersion = $PSVersionTable.PSVersion
    if ($psVersion.Major -ge 5) {
        Add-CheckResult "PowerShell版本" "PASS" "PowerShell $($psVersion.ToString())" ""
    } else {
        Add-CheckResult "PowerShell版本" "WARN" "PowerShell $($psVersion.ToString()) 版本较低" "" "建议升级到PowerShell 5.0或更高版本"
    }
    
    # 检查.NET Framework
    try {
        $netVersions = Get-ChildItem "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse | 
                      Get-ItemProperty -Name Version, Release -ErrorAction SilentlyContinue | 
                      Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } | 
                      Select-Object PSChildName, Version, Release
        
        $latestNet = $netVersions | Sort-Object Release -Descending | Select-Object -First 1
        if ($latestNet.Release -ge 461808) {  # .NET Framework 4.7.2
            Add-CheckResult ".NET Framework" "PASS" ".NET Framework $($latestNet.Version)" "Release: $($latestNet.Release)"
        } else {
            Add-CheckResult ".NET Framework" "WARN" ".NET Framework $($latestNet.Version) 版本较低" "Release: $($latestNet.Release)" "建议升级到.NET Framework 4.7.2或更高版本"
        }
    } catch {
        Add-CheckResult ".NET Framework" "FAIL" "无法检查.NET Framework版本" $_.Exception.Message
    }
    
    # 检查Python环境
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python (\d+\.\d+\.\d+)") {
            $version = $matches[1]
            $versionParts = $version.Split('.')
            if ([int]$versionParts[0] -eq 3 -and [int]$versionParts[1] -ge 8) {
                Add-CheckResult "Python环境" "PASS" "Python $version" ""
            } else {
                Add-CheckResult "Python环境" "WARN" "Python $version 版本过低" "" "建议使用Python 3.8或更高版本"
            }
        } else {
            Add-CheckResult "Python环境" "FAIL" "Python未安装或未配置" "" "请安装Python 3.8+"
        }
    } catch {
        Add-CheckResult "Python环境" "FAIL" "Python检查失败" $_.Exception.Message "请安装Python 3.8+"
    }
    
    # 检查系统资源
    try {
        $memory = Get-ComputerInfo | Select-Object TotalPhysicalMemory, AvailablePhysicalMemory
        $totalMemoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 2)
        $availableMemoryGB = [math]::Round($memory.AvailablePhysicalMemory / 1GB, 2)
        $memoryUsagePercent = [math]::Round((($memory.TotalPhysicalMemory - $memory.AvailablePhysicalMemory) / $memory.TotalPhysicalMemory) * 100, 2)
        
        if ($totalMemoryGB -ge 4) {
            Add-CheckResult "系统内存" "PASS" "总内存: ${totalMemoryGB}GB, 可用: ${availableMemoryGB}GB" "使用率: ${memoryUsagePercent}%"
        } else {
            Add-CheckResult "系统内存" "WARN" "内存不足: ${totalMemoryGB}GB" "使用率: ${memoryUsagePercent}%" "建议至少4GB内存"
        }
    } catch {
        Add-CheckResult "系统内存" "FAIL" "无法检查内存信息" $_.Exception.Message
    }
    
    # 检查磁盘空间
    try {
        $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
        foreach ($drive in $disk) {
            $totalSpaceGB = [math]::Round($drive.Size / 1GB, 2)
            $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
            $usagePercent = [math]::Round((($drive.Size - $drive.FreeSpace) / $drive.Size) * 100, 2)
            
            if ($freeSpaceGB -ge 10) {
                Add-CheckResult "磁盘空间($($drive.DeviceID))" "PASS" "总空间: ${totalSpaceGB}GB, 可用: ${freeSpaceGB}GB" "使用率: ${usagePercent}%"
            } elseif ($freeSpaceGB -ge 5) {
                Add-CheckResult "磁盘空间($($drive.DeviceID))" "WARN" "磁盘空间不足: ${freeSpaceGB}GB" "使用率: ${usagePercent}%" "建议清理磁盘空间"
            } else {
                Add-CheckResult "磁盘空间($($drive.DeviceID))" "FAIL" "磁盘空间严重不足: ${freeSpaceGB}GB" "使用率: ${usagePercent}%" "立即清理磁盘空间"
            }
        }
    } catch {
        Add-CheckResult "磁盘空间" "FAIL" "无法检查磁盘空间" $_.Exception.Message
    }
}

# Docker环境检查
function Test-DockerEnvironment {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Docker环境检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $script:TotalChecks++
    try {
        $dockerVersion = docker --version 2>&1
        if ($dockerVersion -match "Docker version") {
            $script:PassedChecks++
            Write-Log "✓ Docker已安装: $dockerVersion" "PASS"
        } else {
            $script:FailedChecks++
            Write-Log "✗ Docker未正确安装" "FAIL"
        }
    } catch {
        $script:FailedChecks++
        Write-Log "✗ Docker检查失败: $($_.Exception.Message)" "FAIL"
    }
}

# 服务状态检查
function Test-ServiceStatus {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "服务状态检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $services = @("word-service-app", "word-service-redis", "word-service-nginx")
    
    foreach ($service in $services) {
        $script:TotalChecks++
        try {
            $status = docker ps -a --filter "name=$service" --format "{{.Status}}" 2>&1
            if ($status -match "Up") {
                $script:PassedChecks++
                Write-Log "✓ $service 运行正常" "PASS"
            } else {
                $script:FailedChecks++
                Write-Log "✗ $service 未运行" "FAIL"
            }
        } catch {
            $script:FailedChecks++
            Write-Log "✗ $service 检查失败: $($_.Exception.Message)" "FAIL"
        }
    }
}

# 网络连接检查
function Test-NetworkConnectivity {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "网络连接检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $testUrls = @(
        @{ Url = "http://localhost:8000/health"; Description = "主服务健康检查" },
        @{ Url = "http://localhost:6379"; Description = "Redis连接"; Type = "TCP" },
        @{ Url = "http://localhost"; Description = "Nginx代理" }
    )
    
    foreach ($test in $testUrls) {
        try {
            if ($test.Type -eq "TCP") {
                # TCP连接测试
                $tcpClient = New-Object System.Net.Sockets.TcpClient
                $connect = $tcpClient.BeginConnect("localhost", 6379, $null, $null)
                $wait = $connect.AsyncWaitHandle.WaitOne(3000, $false)
                
                if ($wait) {
                    $tcpClient.EndConnect($connect)
                    Add-CheckResult $test.Description "PASS" "TCP连接成功" ""
                } else {
                    Add-CheckResult $test.Description "FAIL" "TCP连接超时" "" "检查Redis服务是否启动"
                }
                $tcpClient.Close()
            } else {
                # HTTP连接测试
                $response = Invoke-WebRequest -Uri $test.Url -TimeoutSec 10 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Add-CheckResult $test.Description "PASS" "HTTP连接成功" "状态码: $($response.StatusCode)"
                } else {
                    Add-CheckResult $test.Description "WARN" "HTTP响应异常" "状态码: $($response.StatusCode)"
                }
            }
        } catch {
            Add-CheckResult $test.Description "FAIL" "连接失败" $_.Exception.Message "检查服务是否启动"
        }
    }
}

# 配置文件检查
function Test-ConfigurationFiles {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "配置文件检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $configFiles = @(
        @{ Path = ".\config.yaml"; Description = "主配置文件"; Required = $true },
        @{ Path = ".\docker-compose.yml"; Description = "Docker编排文件"; Required = $true },
        @{ Path = ".\Dockerfile"; Description = "Docker镜像文件"; Required = $true },
        @{ Path = ".\requirements.txt"; Description = "Python依赖文件"; Required = $true },
        @{ Path = ".\env.example"; Description = "环境变量示例"; Required = $false }
    )
    
    foreach ($config in $configFiles) {
        if (Test-Path $config.Path) {
            $fileInfo = Get-Item $config.Path
            $sizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
            Add-CheckResult $config.Description "PASS" "配置文件存在" "大小: ${sizeKB}KB, 修改时间: $($fileInfo.LastWriteTime)"
        } else {
            if ($config.Required) {
                Add-CheckResult $config.Description "FAIL" "必需的配置文件不存在" $config.Path "请检查项目文件完整性"
            } else {
                Add-CheckResult $config.Description "WARN" "可选配置文件不存在" $config.Path
            }
        }
    }
}

# Word COM接口检查
function Test-WordCOMInterface {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "Word COM接口检查" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    try {
        # 检查Word是否安装
        $wordApp = New-Object -ComObject Word.Application
        $wordVersion = $wordApp.Version
        $wordApp.Quit()
        
        Add-CheckResult "Microsoft Word" "PASS" "Word $wordVersion 已安装" ""
        
        # 检查COM接口权限
        try {
            python -c "import win32com.client; word = win32com.client.Dispatch('Word.Application'); word.Quit()" 2>&1
            Add-CheckResult "Word COM接口" "PASS" "COM接口权限正常" ""
        } catch {
            Add-CheckResult "Word COM接口" "FAIL" "COM接口权限问题" $_.Exception.Message "请以管理员权限运行"
        }
        
    } catch {
        Add-CheckResult "Microsoft Word" "FAIL" "Word未安装或无法访问" $_.Exception.Message "请安装Microsoft Word"
    }
}

# 自动修复功能
function Invoke-AutoFix {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "自动修复" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    $fixedCount = 0
    
    # 修复目录权限问题
    $requiredDirs = @(".\data", ".\logs", ".\data\uploads", ".\data\temp")
    foreach ($dir in $requiredDirs) {
        if (!(Test-Path $dir)) {
            try {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-Log "创建目录: $dir" "INFO"
                $DiagnosticResults.FixedIssues += "创建缺失目录: $dir"
                $fixedCount++
            } catch {
                Write-Log "创建目录失败: $dir - $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    # 修复Docker服务问题
    try {
        $dockerInfo = docker info 2>&1
        if ($dockerInfo -match "error") {
            Write-Log "尝试重启Docker服务..." "INFO"
            Restart-Service -Name "Docker Desktop Service" -Force
            Start-Sleep -Seconds 10
            $DiagnosticResults.FixedIssues += "重启Docker服务"
            $fixedCount++
        }
    } catch {
        Write-Log "无法重启Docker服务: $($_.Exception.Message)" "WARN"
    }
    
    # 清理临时文件
    try {
        $tempFiles = Get-ChildItem -Path ".\data\temp" -Recurse -File | Where-Object { $_.CreationTime -lt (Get-Date).AddDays(-1) }
        if ($tempFiles.Count -gt 0) {
            $tempFiles | Remove-Item -Force
            Write-Log "清理了 $($tempFiles.Count) 个临时文件" "INFO"
            $DiagnosticResults.FixedIssues += "清理 $($tempFiles.Count) 个临时文件"
            $fixedCount++
        }
    } catch {
        Write-Log "清理临时文件失败: $($_.Exception.Message)" "WARN"
    }
    
    if ($fixedCount -gt 0) {
        Write-Log "自动修复完成，共修复 $fixedCount 个问题" "INFO"
    } else {
        Write-Log "未发现可自动修复的问题" "INFO"
    }
}

# 导出诊断报告
function Export-DiagnosticReport {
    if (!(Test-Path $OutputPath)) {
        New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    }
    
    $reportFile = Join-Path $OutputPath "diagnostic_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $report = @{
        Timestamp = Get-Date
        Summary = @{
            TotalChecks = $TotalChecks
            PassedChecks = $PassedChecks
            FailedChecks = $FailedChecks
            WarningChecks = $WarningChecks
        }
        Results = $DiagnosticResults
        SystemInfo = @{
            ComputerName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            OSVersion = (Get-ComputerInfo).WindowsProductName
        }
    }
    
    $report | ConvertTo-Json -Depth 10 | Set-Content -Path $reportFile -Encoding UTF8
    Write-Log "诊断报告已导出到: $reportFile" "INFO"
}

# 主程序
Write-Host "========================================" -ForegroundColor Green
Write-Host "Word文档分析服务 - 环境诊断" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "诊断模式: $Mode" -ForegroundColor Yellow
Write-Host "自动修复: $Fix" -ForegroundColor Yellow
Write-Host "导出报告: $Export" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$startTime = Get-Date

try {
    # 根据模式执行不同的检查
    switch ($Mode.ToLower()) {
        "full" {
            Test-SystemEnvironment
            Test-DockerEnvironment
            Test-ServiceStatus
            Test-NetworkConnectivity
            Test-ConfigurationFiles
            Test-WordCOMInterface
        }
        "quick" {
            Test-SystemEnvironment
            Test-DockerEnvironment
            Test-ServiceStatus
        }
        "service" {
            Test-ServiceStatus
            Test-NetworkConnectivity
        }
        "system" {
            Test-SystemEnvironment
            Test-ConfigurationFiles
            Test-WordCOMInterface
        }
        "network" {
            Test-NetworkConnectivity
        }
        default {
            Write-Log "无效的诊断模式: $Mode" "ERROR"
            exit 1
        }
    }
    
    # 自动修复
    if ($Fix) {
        Invoke-AutoFix
    }
    
    # 导出报告
    if ($Export) {
        Export-DiagnosticReport
    }
    
    # 显示总结
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "诊断完成" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "总检查项目: $TotalChecks" -ForegroundColor Cyan
    Write-Host "通过: $PassedChecks" -ForegroundColor Green
    Write-Host "失败: $FailedChecks" -ForegroundColor Red
    Write-Host "警告: $WarningChecks" -ForegroundColor Yellow
    Write-Host "耗时: $([math]::Round($duration, 2)) 秒" -ForegroundColor Cyan
    
    if ($Fix -and $DiagnosticResults.FixedIssues.Count -gt 0) {
        Write-Host "已修复问题: $($DiagnosticResults.FixedIssues.Count) 个" -ForegroundColor Green
    }
    
    # 建议
    if ($FailedChecks -gt 0) {
        Write-Host ""
        Write-Host "建议:" -ForegroundColor Yellow
        Write-Host "- 检查失败的项目并按建议修复" -ForegroundColor Yellow
        Write-Host "- 使用 -Fix 参数尝试自动修复" -ForegroundColor Yellow
        Write-Host "- 使用 -Export 参数导出详细报告" -ForegroundColor Yellow
    }
    
    # 设置退出码
    if ($FailedChecks -gt 0) {
        exit 1
    } else {
        exit 0
    }
    
} catch {
    Write-Log "诊断过程出错: $($_.Exception.Message)" "ERROR"
    exit 1
} 