# 前端文件上传与文档分析流程分析

## 概述

本文档详细分析前端如何实现文件上传并进行文档分析的完整流程，包括用户交互、API调用、任务轮询和结果展示等各个环节。

## 1. 整体架构流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端(Vue3)
    participant Backend as 后端API
    participant TaskManager as 任务管理器
    participant Engine as 检测引擎v2.0
    participant DB as 数据库

    User->>Frontend: 选择文件
    Frontend->>Frontend: 文件验证
    User->>Frontend: 配置检测选项
    User->>Frontend: 点击开始分析
    
    Frontend->>Backend: POST /api/v1/documents/upload
    Backend->>DB: 保存文档记录
    Backend->>TaskManager: 创建分析任务
    Backend->>Frontend: 返回任务ID
    
    Frontend->>Frontend: 开始轮询任务状态
    
    TaskManager->>Engine: 执行文档分析
    Engine->>Engine: 结构检查
    Engine->>Engine: 格式检查
    Engine->>Engine: 内容检查
    Engine->>TaskManager: 返回检测结果
    
    TaskManager->>DB: 更新任务状态
    Frontend->>Backend: GET /api/v1/tasks/{task_id}/status
    Backend->>Frontend: 返回任务进度
    
    Frontend->>User: 显示分析结果
```

## 2. 前端实现详解

### 2.1 文件上传组件 (Upload.vue)

#### 核心功能
- **拖拽上传**: 支持拖拽文件到指定区域
- **文件选择**: 点击按钮选择文件
- **文件验证**: 格式、大小、权限验证
- **配置选项**: 检测标准、分析类型选择
- **进度监控**: 实时显示分析进度

#### 关键代码结构
```vue
<template>
  <!-- 拖拽上传区域 -->
  <div @drop="handleDrop" @dragover="handleDragOver">
    <!-- 文件选择界面 -->
  </div>
  
  <!-- 检测配置 -->
  <div class="analysis-options">
    <!-- 检测标准选择 -->
    <!-- 分析类型选择 -->
  </div>
  
  <!-- 开始分析按钮 -->
  <BaseButton @click="startAnalysis">开始分析</BaseButton>
</template>
```

### 2.2 文件处理流程

#### 文件选择与验证
```javascript
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    
    // 1. 文件格式验证
    if (!file.name.match(/\.(doc|docx)$/i)) {
      uploadError.value = '请选择 .doc 或 .docx 格式的文件'
      return
    }
    
    // 2. 文件大小验证
    if (file.size > 10 * 1024 * 1024) { // 10MB
      uploadError.value = '文件大小不能超过 10MB'
      return
    }
    
    // 3. 添加到选中文件列表
    selectedFiles.value = [file]
    uploadError.value = ''
  }
}
```

#### 拖拽上传支持
```javascript
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]
    // 执行相同的文件验证逻辑
    validateAndAddFile(file)
  }
}
```

### 2.3 分析配置

#### 检测标准选择
```javascript
const detectionStandards = ref([
  { value: 'hbkj_bachelor_2024', label: '河北科技学院学士学位论文' },
  { value: 'general_academic', label: '通用学术论文' },
  // 更多标准...
])

const detectionStandard = ref('hbkj_bachelor_2024')
```

#### 分析选项配置
```javascript
const analysisOptions = ref({
  check_format: true,      // 格式检查
  check_structure: true,   // 结构检查
  check_content: true,     // 内容检查
  check_references: true,  // 参考文献检查
  extract_images: false    // 图片提取
})
```

## 3. API调用流程

### 3.1 文档上传API (DocumentApi.uploadDocument)

#### 请求构建
```javascript
async uploadDocument(file: File, options?: DocumentUploadOptions) {
  // 1. 创建FormData
  const formData = new FormData()
  formData.append('file', file)
  formData.append('analysis_type', analysisType)
  
  // 2. 添加分析选项
  if (analysisOptions) {
    formData.append('options', JSON.stringify(analysisOptions))
  }
  
  // 3. 发送请求
  const response = await fetch('/api/v1/documents/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${authToken}`,
    },
    body: formData
  })
  
  return response.json()
}
```

#### 后端处理 (documents.py)
```python
@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    analysis_type: str = Form("paper_check"),
    options: str = Form("{}"),
    current_user: User = Depends(get_current_user)
):
    # 1. 文件验证
    # 2. 保存文件
    # 3. 创建文档记录
    # 4. 创建分析任务
    # 5. 返回任务ID
```

### 3.2 任务创建与调度

#### 任务管理器处理
```python
async def _execute_document_analysis_task(self, task):
    """执行文档分析任务"""
    try:
        # 步骤1: 文件验证
        await progress_tracker.update_progress(task.task_id, 10, "step_1", "正在验证文件")
        
        # 步骤2: 文档内容提取
        await progress_tracker.update_progress(task.task_id, 25, "step_2", "正在提取文档内容")
        analyzer = DocumentAnalyzer()
        analysis_result = await analyzer.analyze_document(task.file_path)
        
        # 步骤3: 格式规则检测 (使用检测引擎v2.0)
        await progress_tracker.update_progress(task.task_id, 50, "step_3", "正在执行格式检测")
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(f"config/rules/{detection_standard}.json")
        check_result = await rule_engine.execute_check(analysis_result)
        
        # 步骤4: 生成检测报告
        await progress_tracker.update_progress(task.task_id, 90, "step_4", "正在生成检测报告")
        
        # 完成任务
        result = {
            "analysis_result": analysis_result,
            "check_result": check_result,
            "detection_standard": detection_standard
        }
        await progress_tracker.complete_task(task.task_id, "分析完成", result)
        
    except Exception as e:
        await progress_tracker.fail_task(task.task_id, str(e))
```

## 4. 任务轮询机制

### 4.1 轮询服务 (PollingService)

#### 轮询控制器
```javascript
class PollingService {
  pollTaskStatus(taskId: string, onUpdate: (status) => void, options = {}) {
    const controller = {
      start: () => {
        // 开始轮询
        poll()
      },
      stop: () => {
        // 停止轮询
        clearTimeout(timeoutId)
      }
    }
    
    const poll = async () => {
      try {
        // 获取任务状态
        const taskStatus = await taskApi.getTaskStatus(taskId)
        onUpdate(taskStatus)
        
        // 检查是否完成
        if (taskStatus.status === 'completed' || taskStatus.status === 'failed') {
          controller.stop()
          return
        }
        
        // 继续轮询
        setTimeout(poll, interval)
      } catch (error) {
        // 错误处理和重试逻辑
      }
    }
    
    return controller
  }
}
```

### 4.2 前端轮询实现

#### 任务状态监控
```javascript
const startAnalysis = async () => {
  try {
    // 1. 上传文件并创建任务
    const response = await documentApi.uploadDocument(file, options)
    const taskId = response.task_id
    
    // 2. 开始轮询任务状态
    const poller = pollingService.pollTaskStatus(
      taskId,
      (status) => {
        // 更新进度显示
        updateProgress(status)
        
        // 检查是否完成
        if (status.status === 'completed') {
          handleAnalysisComplete(status.result)
        } else if (status.status === 'failed') {
          handleAnalysisError(status.error_message)
        }
      },
      {
        interval: 2000,        // 2秒轮询一次
        maxRetries: 10,        // 最大重试10次
        backoffFactor: 1.5     // 指数退避
      }
    )
    
    poller.start()
    
  } catch (error) {
    handleUploadError(error)
  }
}
```

## 5. 进度显示与用户体验

### 5.1 进度条组件

#### 分析步骤显示
```vue
<template>
  <div class="analysis-steps">
    <div v-for="step in analysisSteps" :key="step.id" class="step">
      <div :class="['step-indicator', step.status]">
        <CheckIcon v-if="step.status === 'completed'" />
        <LoadingIcon v-else-if="step.status === 'running'" />
        <NumberIcon v-else :number="step.number" />
      </div>
      <div class="step-content">
        <h4>{{ step.name }}</h4>
        <p>{{ step.description }}</p>
        <div v-if="step.status === 'running'" class="progress-bar">
          <div :style="{ width: step.progress + '%' }"></div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 步骤状态管理
```javascript
const analysisSteps = ref([
  { id: 'step_1', name: '文件验证', status: 'pending', progress: 0 },
  { id: 'step_2', name: '内容提取', status: 'pending', progress: 0 },
  { id: 'step_3', name: '格式检测', status: 'pending', progress: 0 },
  { id: 'step_4', name: '生成报告', status: 'pending', progress: 0 }
])

const updateProgress = (taskStatus) => {
  const { progress, current_step, step_message } = taskStatus
  
  // 更新当前步骤状态
  const currentStepIndex = analysisSteps.value.findIndex(s => s.id === current_step)
  if (currentStepIndex >= 0) {
    analysisSteps.value[currentStepIndex].status = 'running'
    analysisSteps.value[currentStepIndex].progress = progress
    analysisSteps.value[currentStepIndex].description = step_message
  }
  
  // 标记已完成的步骤
  for (let i = 0; i < currentStepIndex; i++) {
    analysisSteps.value[i].status = 'completed'
    analysisSteps.value[i].progress = 100
  }
}
```

### 5.2 错误处理

#### 用户友好的错误提示
```javascript
const handleUploadError = (error) => {
  let errorMessage = '上传失败，请重试'
  
  if (error.message.includes('402') || error.message.includes('余额')) {
    uploadError.value = 'INSUFFICIENT_BALANCE'
    return
  }
  
  if (error.message.includes('413') || error.message.includes('文件过大')) {
    errorMessage = '文件过大，请选择小于10MB的文件'
  } else if (error.message.includes('415') || error.message.includes('格式')) {
    errorMessage = '不支持的文件格式，请上传.doc或.docx文件'
  }
  
  uploadError.value = errorMessage
}
```

## 6. 结果展示

### 6.1 分析结果处理

#### 结果数据结构
```javascript
interface AnalysisResult {
  analysis_result: {
    document_info: DocumentInfo
    content_analysis: ContentAnalysis
    structure_analysis: StructureAnalysis
  }
  check_result: CheckResult[]
  detection_standard: string
  standard_name: string
}
```

#### 结果页面跳转
```javascript
const handleAnalysisComplete = (result) => {
  // 保存结果到本地存储
  localStorage.setItem('latest_analysis_result', JSON.stringify(result))
  
  // 跳转到结果页面
  router.push({
    name: 'DocumentDetail',
    params: { id: result.document_id },
    query: { tab: 'analysis' }
  })
}
```

### 6.2 检测结果展示

#### 问题分类显示
```vue
<template>
  <div class="analysis-results">
    <!-- 总体评分 -->
    <div class="overall-score">
      <ScoreCircle :score="overallScore" />
    </div>
    
    <!-- 问题分类 -->
    <div class="problem-categories">
      <div v-for="category in problemCategories" :key="category.name">
        <h3>{{ category.name }}</h3>
        <div class="problem-list">
          <div v-for="problem in category.problems" :key="problem.id">
            <ProblemItem :problem="problem" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 7. 性能优化

### 7.1 前端优化

- **文件预处理**: 客户端文件验证，减少无效请求
- **分片上传**: 大文件分片上传，提高成功率
- **缓存机制**: 结果缓存，避免重复分析
- **懒加载**: 结果页面组件懒加载

### 7.2 后端优化

- **异步处理**: 文件上传与分析分离
- **任务队列**: 使用队列管理分析任务
- **并发控制**: 限制同时处理的任务数量
- **缓存策略**: 分析结果缓存

## 8. 总结

前端文件上传与文档分析流程具有以下特点：

### 优势
1. **用户体验优秀**: 拖拽上传、实时进度、友好错误提示
2. **架构清晰**: 前后端分离，职责明确
3. **可扩展性强**: 支持多种检测标准和分析类型
4. **性能优异**: 异步处理，不阻塞用户操作

### 技术亮点
1. **检测引擎v2.0**: 分层规则系统，高性能检测
2. **任务轮询**: 智能轮询机制，自动重试和退避
3. **进度可视化**: 多步骤进度展示，用户体验佳
4. **错误处理**: 完善的错误分类和用户引导

这套文件上传与分析系统为用户提供了完整、流畅的文档检测体验，同时保证了系统的稳定性和可维护性。
