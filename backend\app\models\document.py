"""
Word文档分析服务 - 文档相关数据模型
"""

import uuid
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class ContentElementType(str, Enum):
    """内容元素类型枚举"""
    PARAGRAPH = "paragraph"
    HEADING = "heading"
    TABLE = "table"
    LIST = "list"
    IMAGE = "image"
    EQUATION = "equation"
    FOOTNOTE = "footnote"
    ENDNOTE = "endnote"
    HYPERLINK = "hyperlink"
    BOOKMARK = "bookmark"


class DocumentBase(BaseModel):
    """文档基础模型 - 匹配数据库表结构"""
    title: Optional[str] = Field(default=None, description="文档标题")
    author: Optional[str] = Field(default=None, description="文档作者")
    keywords: Optional[str] = Field(default=None, description="关键词")
    abstract: Optional[str] = Field(default=None, description="摘要")
    pages: Optional[int] = Field(default=None, description="页数")
    words: Optional[int] = Field(default=None, description="字数")
    tables: Optional[int] = Field(default=None, description="表格数")
    images: Optional[int] = Field(default=None, description="图片数")
    created_date: Optional[datetime] = Field(default=None, description="文档创建时间")
    modified_date: Optional[datetime] = Field(default=None, description="文档修改时间")

    class Config:
        from_attributes = True


class DocumentCreate(DocumentBase):
    """文档创建模型"""
    document_id: str = Field(default_factory=lambda: f"doc_{uuid.uuid4().hex}", description="文档ID")
    task_id: str = Field(..., description="关联的任务ID")
    analyzed_at: datetime = Field(default_factory=datetime.now, description="分析时间")


class DocumentInDB(DocumentCreate):
    """数据库中的文档模型"""
    pass


class Document(DocumentInDB):
    """API响应的文档模型"""
    content_elements: Optional[List['ContentElement']] = Field(default=None, description="内容元素列表")
    images_list: Optional[List['Image']] = Field(default=None, description="图片列表")


class ContentElementBase(BaseModel):
    """内容元素基础模型"""
    element_type: ContentElementType = Field(..., description="元素类型")
    content: Optional[str] = Field(default=None, description="文本内容")
    style: Optional[str] = Field(default=None, description="样式信息")
    font_name: Optional[str] = Field(default=None, description="字体名称")
    font_size: Optional[float] = Field(default=None, description="字体大小")
    is_bold: bool = Field(default=False, description="是否加粗")
    is_italic: bool = Field(default=False, description="是否斜体")
    is_underline: bool = Field(default=False, description="是否下划线")
    alignment: Optional[str] = Field(default=None, description="对齐方式")
    position: int = Field(..., description="在文档中的位置")
    page_number: Optional[int] = Field(default=None, description="页码")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="额外元数据")

    class Config:
        from_attributes = True


class ContentElementCreate(ContentElementBase):
    """内容元素创建模型"""
    element_id: str = Field(default_factory=lambda: f"elem_{uuid.uuid4().hex}", description="元素ID")
    document_id: str = Field(..., description="关联的文档ID")


class ContentElementInDB(ContentElementCreate):
    """数据库中的内容元素模型"""
    pass


class ContentElement(ContentElementInDB):
    """API响应的内容元素模型"""
    pass


class ImageBase(BaseModel):
    """图片基础模型"""
    hash: str = Field(..., description="图片哈希值")
    file_path: str = Field(..., description="文件路径")
    original_width: Optional[float] = Field(default=None, description="原始宽度")
    original_height: Optional[float] = Field(default=None, description="原始高度")
    display_width: Optional[float] = Field(default=None, description="显示宽度")
    display_height: Optional[float] = Field(default=None, description="显示高度")
    position: int = Field(..., description="在文档中的位置")
    page_number: Optional[int] = Field(default=None, description="页码")
    caption: Optional[str] = Field(default=None, description="图片说明")
    properties: Optional[Dict[str, Any]] = Field(default=None, description="图片属性")

    class Config:
        from_attributes = True


class ImageCreate(ImageBase):
    """图片创建模型"""
    image_id: str = Field(default_factory=lambda: f"img_{uuid.uuid4().hex}", description="图片ID")
    document_id: str = Field(..., description="关联的文档ID")


class ImageInDB(ImageCreate):
    """数据库中的图片模型"""
    pass


class Image(ImageInDB):
    """API响应的图片模型"""
    pass


# 更新前向引用
Document.model_rebuild() 