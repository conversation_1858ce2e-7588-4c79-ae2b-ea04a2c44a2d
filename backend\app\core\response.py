"""
统一API响应格式
"""

import time
import json
from datetime import datetime
from typing import Any, Optional
from fastapi.responses import JSONResponse
from pydantic import BaseModel


class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime和枚举对象"""
    def default(self, obj):
        from enum import Enum

        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Enum):
            return obj.value
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)


class APIResponse(BaseModel):
    """统一API响应模型"""
    success: bool
    code: int
    message: str
    data: Optional[Any] = None
    timestamp: int
    request_id: Optional[str] = None


def success_response(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200,
    request_id: Optional[str] = None
) -> JSONResponse:
    """创建成功响应"""
    response_data = APIResponse(
        success=True,
        code=code,
        message=message,
        data=data,
        timestamp=int(time.time()),
        request_id=request_id
    )
    
    # 使用自定义编码器处理datetime
    content = json.loads(json.dumps(response_data.model_dump(), cls=DateTimeEncoder))
    
    return JSONResponse(
        status_code=code,
        content=content
    )


def error_response(
    message: str = "操作失败",
    code: int = 500,
    data: Any = None,
    request_id: Optional[str] = None
) -> JSONResponse:
    """创建错误响应"""
    response_data = APIResponse(
        success=False,
        code=code,
        message=message,
        data=data,
        timestamp=int(time.time()),
        request_id=request_id
    )
    
    # 使用自定义编码器处理datetime
    content = json.loads(json.dumps(response_data.model_dump(), cls=DateTimeEncoder))
    
    return JSONResponse(
        status_code=code,
        content=content
    ) 