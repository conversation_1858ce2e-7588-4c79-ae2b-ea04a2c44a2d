<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置描述修复演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .before, .after {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .before { border-color: #ef4444; }
        .after { border-color: #10b981; }
        .header {
            padding: 15px;
            font-weight: bold;
            color: white;
        }
        .before .header { background: #ef4444; }
        .after .header { background: #10b981; }
        .content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            font-size: 14px;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .original-text {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 8px;
        }
        .position-info {
            font-size: 12px;
            color: #6b7280;
        }
        .position-bad {
            color: #ef4444;
            background: #fef2f2;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .position-good {
            color: #10b981;
            background: #ecfdf5;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .improvement {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .improvement h3 {
            color: #10b981;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 位置描述修复演示</h1>
        <p>修复"原文片段"中显示"位置：0"等无意义数字的问题，改为显示用户友好的位置描述。</p>

        <div class="improvement">
            <h3>🔧 修复内容</h3>
            <ul class="feature-list">
                <li><strong>位置显示优化</strong>：从"位置：0"改为"位置：中文关键词段落"</li>
                <li><strong>智能识别</strong>：根据规则类型和原文内容自动识别位置</li>
                <li><strong>用户友好</strong>：使用自然语言描述位置，便于快速定位</li>
                <li><strong>向后兼容</strong>：保留原始position字段，新增position_description字段</li>
            </ul>
        </div>

        <h2>📊 修复前后对比</h2>
        <div class="comparison">
            <div class="before">
                <div class="header">❌ 修复前</div>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>原文片段</th>
                                <th>问题详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <div class="original-text">关键词:人工智能;机器学习</div>
                                    <div class="position-info">
                                        <span class="position-bad">位置: 0</span>
                                    </div>
                                </td>
                                <td>对齐方式问题</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="color: #ef4444; margin-top: 10px;">
                        ❌ 问题：用户不知道"位置：0"是什么意思
                    </div>
                </div>
            </div>

            <div class="after">
                <div class="header">✅ 修复后</div>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>原文片段</th>
                                <th>问题详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <div class="original-text">关键词:人工智能;机器学习</div>
                                    <div class="position-info">
                                        <span class="position-good">位置: 中文关键词段落</span>
                                    </div>
                                </td>
                                <td>对齐方式问题</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="color: #10b981; margin-top: 10px;">
                        ✅ 改进：用户立即知道问题出现在哪个部分
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 支持的位置类型</h2>
        <div id="position-types-demo"></div>

        <h2>📋 技术实现</h2>
        <div class="code-block">
            <h3>1. 后端：添加位置描述生成</h3>
            <pre>
def _generate_position_description(self, result, position, original_text, document_data):
    """生成用户友好的位置描述"""
    rule_id = result.rule_id.lower()
    
    # 中文关键词相关问题
    if "chinese_keywords" in rule_id:
        return "中文关键词段落"
    
    # 英文关键词相关问题  
    elif "english_keywords" in rule_id:
        return "英文关键词段落"
    
    # 中文摘要相关问题
    elif "chinese_abstract" in rule_id:
        return "中文摘要段落"
    
    # 根据原文内容推断
    if original_text.startswith("关键词："):
        return "中文关键词段落"
    elif original_text.startswith("Keywords:"):
        return "英文关键词段落"
    
    return f"文档位置 {position}"
            </pre>
        </div>

        <div class="code-block">
            <h3>2. 前端：优先显示友好描述</h3>
            <pre>
&lt;div class="mt-1 text-xs text-gray-500"&gt;
  &lt;span v-if="fragment.position_description"&gt;
    位置: {{ fragment.position_description }}
  &lt;/span&gt;
  &lt;span v-else&gt;
    位置: {{ fragment.position }}
  &lt;/span&gt;
&lt;/div&gt;
            </pre>
        </div>

        <div class="code-block">
            <h3>3. API响应数据结构</h3>
            <pre>
{
  "fragment_id": "frag_82ff1898",
  "structure": "中文关键词",
  "category": "对齐方式问题",
  "position": 0,                           // 原始位置（保留）
  "position_description": "中文关键词段落",  // 新增：友好描述
  "original_text": "关键词:人工智能;机器学习",
  "problem_description": "当前为两端对齐，应为左对齐"
}
            </pre>
        </div>
    </div>

    <script>
        // 展示支持的位置类型
        function showPositionTypes() {
            const positionTypes = [
                {
                    original: "关键词:人工智能;机器学习",
                    before: "位置: 0",
                    after: "位置: 中文关键词段落",
                    rule: "format.chinese_keywords"
                },
                {
                    original: "Keywords: artificial intelligence; machine learning",
                    before: "位置: 1500",
                    after: "位置: 英文关键词段落",
                    rule: "format.english_keywords"
                },
                {
                    original: "摘要：本文研究了人工智能的发展...",
                    before: "位置: 500",
                    after: "位置: 中文摘要段落",
                    rule: "content.chinese_abstract"
                },
                {
                    original: "Abstract: This paper studies the development...",
                    before: "位置: 2000",
                    after: "位置: 英文摘要段落",
                    rule: "content.english_abstract"
                },
                {
                    original: "1.2.1 国内发展现状",
                    before: "位置: 3500",
                    after: "位置: 三级标题",
                    rule: "format.level_3_title"
                },
                {
                    original: "[1] 张三. 人工智能研究[J]. 计算机学报, 2023.",
                    before: "位置: 8000",
                    after: "位置: 参考文献部分",
                    rule: "format.references"
                }
            ]

            let html = `
            <table>
                <thead>
                    <tr>
                        <th>原文内容</th>
                        <th>修复前</th>
                        <th>修复后</th>
                        <th>规则类型</th>
                    </tr>
                </thead>
                <tbody>
            `

            positionTypes.forEach((item) => {
                html += `
                <tr>
                    <td>
                        <div class="original-text">${item.original}</div>
                    </td>
                    <td>
                        <span class="position-bad">${item.before}</span>
                    </td>
                    <td>
                        <span class="position-good">${item.after}</span>
                    </td>
                    <td>
                        <code>${item.rule}</code>
                    </td>
                </tr>
                `
            })

            html += `
                </tbody>
            </table>
            <div style="color: #10b981; margin-top: 15px;">
                ✅ 现在用户可以快速了解问题出现在文档的哪个部分！
            </div>
            `

            document.getElementById('position-types-demo').innerHTML = html
        }

        // 页面加载时显示位置类型
        document.addEventListener('DOMContentLoaded', showPositionTypes)
    </script>
</body>
</html>
