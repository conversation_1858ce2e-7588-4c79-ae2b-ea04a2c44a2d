version: '3.8'

# ==================================================
# Word文档分析服务 - 生产环境Docker Compose配置
# ==================================================

services:
  # ==================================================
  # 主应用服务集群
  # ==================================================
  word-service-1:
    image: word-service:${VERSION:-latest}
    container_name: word-service-1
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=1
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    env_file:
      - ../.env.production
    volumes:
      - word_data:/app/data
      - word_logs:/app/logs
      - ssl_certs:/app/ssl:ro
      - ../config.yaml:/app/config.yaml:ro
    networks:
      - word-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  word-service-2:
    image: word-service:${VERSION:-latest}
    container_name: word-service-2
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=2
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    env_file:
      - ../.env.production
    volumes:
      - word_data:/app/data
      - word_logs:/app/logs
      - ssl_certs:/app/ssl:ro
      - ../config.yaml:/app/config.yaml:ro
    networks:
      - word-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ==================================================
  # PostgreSQL数据库
  # ==================================================
  postgres:
    image: postgres:17.5-alpine
    container_name: word-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: word_service
      POSTGRES_USER: word_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./postgres/init:/docker-entrypoint-initdb.d
      - postgres_logs:/var/log/postgresql
    networks:
      - word-network
    ports:
      - "127.0.0.1:5432:5432"
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U word_user -d word_service"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ==================================================
  # Redis缓存服务
  # ==================================================
  redis:
    image: redis:7.2-alpine
    container_name: word-redis
    restart: unless-stopped
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    networks:
      - word-network
    ports:
      - "127.0.0.1:6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.2'
          memory: 512M

  # ==================================================
  # Nginx负载均衡器
  # ==================================================
  nginx:
    image: nginx:1.24-alpine
    container_name: word-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ssl_certs:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - word-network
    depends_on:
      - word-service-1
      - word-service-2
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # ==================================================
  # 监控服务
  # ==================================================
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: word-prometheus
    restart: unless-stopped
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    networks:
      - word-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.2'
          memory: 512M

  grafana:
    image: grafana/grafana:10.0.0
    container_name: word-grafana
    restart: unless-stopped
    ports:
      - "127.0.0.1:3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_SERVER_ROOT_URL=http://localhost:3000/grafana
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - word-network
    depends_on:
      - prometheus
    profiles:
      - monitoring
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.1'
          memory: 256M

  # ==================================================
  # 日志收集服务 (可选)
  # ==================================================
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: word-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - word-network
    ports:
      - "127.0.0.1:9200:9200"
    profiles:
      - logging
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 2G

  kibana:
    image: kibana:8.8.0
    container_name: word-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - kibana_data:/usr/share/kibana/data
    networks:
      - word-network
    ports:
      - "127.0.0.1:5601:5601"
    depends_on:
      - elasticsearch
    profiles:
      - logging
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.2'
          memory: 512M

  # ==================================================
  # 备份服务
  # ==================================================
  postgres-backup:
    image: prodrigestivill/postgres-backup-local:14
    container_name: word-postgres-backup
    restart: unless-stopped
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=word_service
      - POSTGRES_USER=word_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_EXTRA_OPTS=-Z9 --schema=public --blobs
      - SCHEDULE=@daily
      - BACKUP_KEEP_DAYS=7
      - BACKUP_KEEP_WEEKS=4
      - BACKUP_KEEP_MONTHS=6
      - HEALTHCHECK_PORT=8080
    volumes:
      - postgres_backups:/backups
    networks:
      - word-network
    depends_on:
      - postgres
    profiles:
      - backup

# ==================================================
# 网络配置
# ==================================================
networks:
  word-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********

# ==================================================
# 数据卷配置
# ==================================================
volumes:
  # 应用数据
  word_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ../data
  
  word_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ../logs

  # 数据库
  postgres_data:
    driver: local
  
  postgres_logs:
    driver: local
  
  postgres_backups:
    driver: local

  # 缓存
  redis_data:
    driver: local
  
  redis_logs:
    driver: local

  # SSL证书
  ssl_certs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ../ssl

  # Web服务器
  nginx_logs:
    driver: local

  # 监控
  prometheus_data:
    driver: local
  
  grafana_data:
    driver: local

  # 日志
  elasticsearch_data:
    driver: local
  
  kibana_data:
    driver: local

# ==================================================
# 扩展配置
# ==================================================
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

x-common-variables: &common-variables
  TZ: Asia/Shanghai
  LANG: en_US.UTF-8
  LC_ALL: en_US.UTF-8 