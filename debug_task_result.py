import requests
import json

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heibailan5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def debug_task_result(token, task_id):
    """调试任务结果"""
    try:
        task_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(task_url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            task_data = result.get("data", {})
            
            print("📊 任务基本信息:")
            print(f"   状态: {task_data.get('status')}")
            print(f"   进度: {task_data.get('progress')}%")
            print(f"   文件名: {task_data.get('filename')}")
            
            # 检查任务结果
            task_result = task_data.get("result", {})
            print(f"\n📋 任务结果结构:")
            print(f"   结果类型: {type(task_result)}")
            print(f"   结果键: {list(task_result.keys()) if isinstance(task_result, dict) else 'N/A'}")
            
            # 检查检测结果
            if "check_result" in task_result:
                check_result = task_result["check_result"]
                print(f"\n🔍 检测结果:")
                print(f"   类型: {type(check_result)}")
                if isinstance(check_result, dict):
                    print(f"   键: {list(check_result.keys())}")
                    
                    # 检查问题列表
                    if "problems" in check_result:
                        problems = check_result["problems"]
                        print(f"   问题数量: {len(problems)}")
                        
                        for i, problem in enumerate(problems[:3]):
                            print(f"\n   问题 {i+1}:")
                            print(f"     类型: {type(problem)}")
                            print(f"     内容: {problem}")
                    else:
                        print("   没有找到 'problems' 键")
                        
                    # 检查其他可能的键
                    for key in ["issues", "errors", "warnings", "violations"]:
                        if key in check_result:
                            print(f"   {key}: {len(check_result[key])} 个")
                else:
                    print(f"   检测结果内容: {check_result}")
            else:
                print("\n🔍 没有找到 'check_result' 键")
            
            # 检查问题片段
            if "problem_fragments" in task_result:
                fragments = task_result["problem_fragments"]
                print(f"\n📌 问题片段:")
                print(f"   数量: {len(fragments)}")
                print(f"   类型: {type(fragments)}")
            else:
                print("\n📌 没有找到 'problem_fragments' 键")
            
            # 检查其他相关字段
            for key in ["fragment_count", "severe_count", "compliance_score", "problems_found"]:
                if key in task_result:
                    print(f"   {key}: {task_result[key]}")
            
            # 输出完整结果到文件（用于调试）
            with open(f"task_result_{task_id}.json", "w", encoding="utf-8") as f:
                json.dump(task_result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整结果已保存到: task_result_{task_id}.json")
            
            return True
        else:
            print(f"❌ 获取任务失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 调试任务结果失败: {str(e)}")
        return False

def main():
    """主调试流程"""
    print("🔍 开始调试任务结果")
    print("=" * 50)
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌")
        return
    
    # 使用最新的任务ID
    task_id = "task_c3345acc3e4647b1b7586acbdb10a587"
    
    print(f"🔍 调试任务: {task_id}")
    debug_task_result(token, task_id)
    
    print("\n" + "=" * 50)
    print("🏁 调试完成")

if __name__ == "__main__":
    main()
