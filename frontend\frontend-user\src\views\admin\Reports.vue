<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">仪表盘</router-link>
            <router-link to="/admin/users" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">用户管理</router-link>
            <router-link to="/admin/documents" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">任务监控</router-link>
            <router-link to="/admin/system" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">系统设置</router-link>
            <router-link to="/admin/reports" class="text-red-600 dark:text-red-400 font-medium">报告中心</router-link>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题和操作 -->
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            报告中心
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            查看系统运营数据和分析报告
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button @click="exportReport" class="btn btn-secondary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            导出报告
          </button>
          <button @click="generateReport" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            生成报告
          </button>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <div class="card mb-8">
        <div class="card-body">
          <div class="flex flex-wrap items-end gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">时间范围</label>
              <select v-model="timeRange" @change="updateReports" class="form-input">
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="quarter">本季度</option>
                <option value="year">今年</option>
                <option value="custom">自定义</option>
              </select>
            </div>
            <div v-if="timeRange === 'custom'">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">开始日期</label>
              <input v-model="customStartDate" type="date" class="form-input">
            </div>
            <div v-if="timeRange === 'custom'">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">结束日期</label>
              <input v-model="customEndDate" type="date" class="form-input">
            </div>
            <div>
              <button @click="refreshReports" class="btn btn-secondary">
                  <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  刷新
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心指标 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ metrics.totalDocuments.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">文档分析总数</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ {{ metrics.documentsGrowth }}% 相比上月</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ metrics.activeUsers.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">活跃用户数</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ {{ metrics.usersGrowth }}% 相比上月</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">¥{{ metrics.monthlyRevenue.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">月收入</p>
            <p :class="[
              'text-xs mt-1',
              metrics.revenueGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            ]">
              {{ metrics.revenueGrowth >= 0 ? '↑' : '↓' }} {{ Math.abs(metrics.revenueGrowth) }}% 相比上月
            </p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ metrics.systemUptime }}%</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">系统可用率</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ {{ metrics.uptimeGrowth }}% 相比上月</p>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 用户增长趋势 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">用户增长趋势</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">过去30天的用户注册和活跃情况</p>
          </div>
          <div class="card-body">
            <div class="h-64 relative">
              <canvas ref="userGrowthChart" class="w-full h-full"></canvas>
            </div>
          </div>
        </div>

        <!-- 文档分析类型分布 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">分析类型分布</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">各种分析类型的使用占比</p>
          </div>
          <div class="card-body">
            <div class="h-64 relative">
              <canvas ref="analysisTypeChart" class="w-full h-full"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- 收入分析 -->
      <div class="card mb-8">
        <div class="card-header">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">收入分析</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">月度收入趋势和套餐收入对比</p>
        </div>
        <div class="card-body">
          <div class="h-80 relative">
            <canvas ref="revenueChart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- 详细报表 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 热门功能排行 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">热门功能排行</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">用户最常使用的功能统计</p>
          </div>
          <div class="card-body">
            <div class="space-y-4">
              <div v-for="(feature, index) in popularFeatures" :key="feature.name" class="flex items-center justify-between">
                <div class="flex items-center">
                  <div :class="[
                    'h-8 w-8 rounded text-center leading-8 font-medium text-sm mr-3',
                    index === 0 ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' :
                    index === 1 ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' :
                    index === 2 ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400' :
                    index === 3 ? 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400' :
                    'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400'
                  ]">{{ index + 1 }}</div>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">{{ feature.name }}</span>
                </div>
                <div class="flex items-center">
                  <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                    <div :class="[
                      'h-2 rounded-full',
                      index === 0 ? 'bg-blue-600 dark:bg-blue-400' :
                      index === 1 ? 'bg-green-600 dark:bg-green-400' :
                      index === 2 ? 'bg-yellow-600 dark:bg-yellow-400' :
                      index === 3 ? 'bg-purple-600 dark:bg-purple-400' :
                      'bg-red-600 dark:bg-red-400'
                    ]" :style="{ width: feature.percentage + '%' }"></div>
                  </div>
                  <span class="text-sm text-gray-600 dark:text-gray-400">{{ feature.percentage }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 常见问题统计 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">常见问题统计</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">文档检测中发现的问题类型</p>
          </div>
          <div class="card-body">
            <div class="space-y-3">
              <div v-for="issue in commonIssues" :key="issue.name" :class="[
                'flex items-center justify-between p-3 rounded',
                issue.color === 'red' ? 'bg-red-50 dark:bg-red-900/20' :
                issue.color === 'orange' ? 'bg-orange-50 dark:bg-orange-900/20' :
                issue.color === 'yellow' ? 'bg-yellow-50 dark:bg-yellow-900/20' :
                issue.color === 'blue' ? 'bg-blue-50 dark:bg-blue-900/20' :
                'bg-purple-50 dark:bg-purple-900/20'
              ]">
                <div class="flex items-center">
                  <div :class="[
                    'h-3 w-3 rounded-full mr-3',
                    issue.color === 'red' ? 'bg-red-500' :
                    issue.color === 'orange' ? 'bg-orange-500' :
                    issue.color === 'yellow' ? 'bg-yellow-500' :
                    issue.color === 'blue' ? 'bg-blue-500' :
                    'bg-purple-500'
                  ]"></div>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">{{ issue.name }}</span>
                </div>
                <span :class="[
                  'text-sm font-medium',
                  issue.color === 'red' ? 'text-red-600 dark:text-red-400' :
                  issue.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                  issue.color === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' :
                  issue.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                  'text-purple-600 dark:text-purple-400'
                ]">{{ issue.count.toLocaleString() }}次</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户反馈报告 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">用户反馈报告</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">用户满意度和反馈统计</p>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600 mb-2">{{ feedback.averageRating }}</div>
              <div class="text-sm text-gray-600 mb-2">平均评分</div>
              <div class="flex justify-center space-x-1">
                <svg v-for="star in 5" :key="star" :class="[
                  'h-5 w-5',
                  star <= Math.floor(feedback.averageRating) ? 'text-yellow-400' : 'text-gray-300'
                ]" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
              </div>
            </div>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600 mb-2">{{ feedback.recommendationRate }}%</div>
              <div class="text-sm text-gray-600">推荐意愿</div>
            </div>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-2">{{ feedback.monthlyFeedbacks }}</div>
              <div class="text-sm text-gray-600">本月反馈数</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'
import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  BarElement,
  BarController,
  ArcElement,
  DoughnutController,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

// 注册 Chart.js 组件
Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  BarElement,
  BarController,
  ArcElement,
  DoughnutController,
  Title,
  Tooltip,
  Legend
)

const router = useRouter()
const themeStore = useThemeStore()

// 图表引用
const userGrowthChart = ref<HTMLCanvasElement>()
const analysisTypeChart = ref<HTMLCanvasElement>()
const revenueChart = ref<HTMLCanvasElement>()

// Chart.js 实例
let userGrowthChartInstance: Chart | null = null
let analysisTypeChartInstance: Chart | null = null
let revenueChartInstance: Chart | null = null

// 组件状态
const showAdminMenu = ref(false)
const timeRange = ref('month')
const customStartDate = ref('')
const customEndDate = ref('')

// 核心指标
const metrics = reactive({
  totalDocuments: 2847,
  documentsGrowth: 12,
  activeUsers: 346,
  usersGrowth: 8,
  monthlyRevenue: 18456,
  revenueGrowth: -3,
  systemUptime: 96.2,
  uptimeGrowth: 0.5
})

// 热门功能排行
const popularFeatures = ref([
  { name: '论文格式检测', percentage: 85 },
  { name: '内容结构分析', percentage: 72 },
  { name: '引用格式检查', percentage: 68 },
  { name: '图表分析', percentage: 54 },
  { name: '语言质量检测', percentage: 41 }
])

// 常见问题统计
const commonIssues = ref([
  { name: '格式不规范', count: 1234, color: 'red' },
  { name: '引用错误', count: 987, color: 'orange' },
  { name: '图表问题', count: 756, color: 'yellow' },
  { name: '页码错误', count: 623, color: 'blue' },
  { name: '目录缺失', count: 412, color: 'purple' }
])

// 用户反馈
const feedback = reactive({
  averageRating: 4.8,
  recommendationRate: 92,
  monthlyFeedbacks: 156
})

// 方法


const exportReport = () => {
  console.log('导出报告')
  // 这里会打开导出对话框
  // TODO: 实现报告导出功能
}

const generateReport = () => {
  console.log('生成报告')
  // 这里会生成新的报告
  // TODO: 实现报告生成功能
}

const handleAdminLogout = () => {
  adminLogout(router)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  // 初始化用户增长趋势图
  if (userGrowthChart.value) {
    userGrowthChartInstance = new Chart(userGrowthChart.value, {
      type: 'line',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [
          {
            label: '新注册用户',
            data: [45, 52, 67, 81, 95, 123, 145, 167, 189, 234, 278, 346],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: false
          },
          {
            label: '活跃用户',
            data: [38, 45, 58, 72, 85, 108, 125, 142, 165, 198, 245, 289],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: false
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          },
          title: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          }
        }
      }
    })
  }

  // 初始化分析类型分布图
  if (analysisTypeChart.value) {
    analysisTypeChartInstance = new Chart(analysisTypeChart.value, {
      type: 'doughnut',
      data: {
        labels: ['论文检测', '格式检查', '结构分析', '引用验证', '其他'],
        datasets: [{
          data: [42, 28, 18, 8, 4],
          backgroundColor: [
            '#3b82f6',  // 蓝色
            '#10b981',  // 绿色
            '#f59e0b',  // 橙色
            '#ef4444',  // 红色
            '#8b5cf6'   // 紫色
          ],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle'
            }
          }
        },
        cutout: '60%'
      }
    })
  }

  // 初始化收入分析图
  if (revenueChart.value) {
    revenueChartInstance = new Chart(revenueChart.value, {
      type: 'bar',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        datasets: [
          {
            label: '基础版',
            data: [2100, 2300, 2800, 3200, 3600, 4100, 4500, 4900, 5200, 5800, 6200, 6800],
            backgroundColor: '#3b82f6',
            borderRadius: 4
          },
          {
            label: '标准版',
            data: [3400, 3800, 4200, 4600, 5100, 5500, 6000, 6400, 6900, 7300, 7800, 8200],
            backgroundColor: '#10b981',
            borderRadius: 4
          },
          {
            label: '专业版',
            data: [1800, 2100, 2400, 2700, 3000, 3300, 3600, 3900, 4200, 4500, 4800, 5100],
            backgroundColor: '#f59e0b',
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          }
        },
        scales: {
          x: {
            stacked: true,
            grid: {
              display: false
            }
          },
          y: {
            stacked: true,
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          }
        }
      }
    })
  }
}

// 更新报告时重新初始化图表
const updateReports = () => {
  console.log('更新报告数据，时间范围:', timeRange.value)
  // 销毁现有图表
  destroyCharts()
  // 重新初始化图表
  setTimeout(() => {
    initCharts()
  }, 100)
}

const refreshReports = () => {
  console.log('刷新报告数据')
  // 销毁现有图表
  destroyCharts()
  // 重新初始化图表
  setTimeout(() => {
    initCharts()
  }, 100)
}

// 销毁图表实例
const destroyCharts = () => {
  if (userGrowthChartInstance) {
    userGrowthChartInstance.destroy()
    userGrowthChartInstance = null
  }
  if (analysisTypeChartInstance) {
    analysisTypeChartInstance.destroy()
    analysisTypeChartInstance = null
  }
  if (revenueChartInstance) {
    revenueChartInstance.destroy()
    revenueChartInstance = null
  }
}

onMounted(async () => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/reports')) {
    return
  }

  // 初始化图表
  initCharts()
})

// 在组件卸载时销毁图表
onBeforeUnmount(() => {
  destroyCharts()
})
</script>

<style scoped>
/* 报告中心特有样式 */
</style> 