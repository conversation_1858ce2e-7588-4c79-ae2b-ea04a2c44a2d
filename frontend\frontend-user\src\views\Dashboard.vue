<template>
  <!-- 使用BaseLayout组件 -->
  <BaseLayout
    title="欢迎回来！"
    description="这是您的文档分析控制台，管理您的所有文档和检测任务。"
  >
    <!-- 加载提示 -->
    <div v-if="isLoading" class="text-center py-16">
      <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-4 text-gray-600 dark:text-gray-400">正在加载仪表盘数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="text-center py-16 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <p class="text-red-600 dark:text-red-400">加载数据失败</p>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">{{ error }}</p>
      <BaseButton @click="loadDashboardData" class="mt-4" variant="primary">重试</BaseButton>
    </div>

    <!-- 主要内容 -->
    <div v-else>
      <!-- 快速操作 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <BaseCard variant="shadow" class="transition-all hover:shadow-lg hover:scale-105">
          <router-link to="/upload" class="block text-center">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-4">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">上传新文档</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">开始新的文档分析任务</p>
          </router-link>
        </BaseCard>
        
        <BaseCard variant="shadow" class="transition-all hover:shadow-lg hover:scale-105">
          <router-link to="/documents" class="block text-center">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-4">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">查看文档</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">管理您的所有文档</p>
          </router-link>
        </BaseCard>
        
        <BaseCard variant="shadow" class="transition-all hover:shadow-lg hover:scale-105">
          <router-link to="/pricing" class="block text-center">
            <div class="mx-auto h-12 w-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center mb-4">
              <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">购买套餐</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">获取更多检测次数</p>
          </router-link>
        </BaseCard>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 文档总数 -->
        <BaseCard>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">文档总数</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_documents || 0 }}</p>
              <p class="text-xs text-green-600">{{ stats.completed_tasks || 0 }} 已分析</p>
            </div>
            <div class="h-12 w-12 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
          </div>
        </BaseCard>
        
        <!-- 任务状态 -->
        <BaseCard>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">任务状态</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.completed_tasks || 0 }}</p>
              <p class="text-xs text-orange-600">{{ stats.processing_tasks || 0 }} 处理中</p>
            </div>
            <div class="h-12 w-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
        </BaseCard>
        
        <!-- 检测问题 -->
        <BaseCard>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">检测问题</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_problems || 0 }}</p>
              <p class="text-xs text-red-600">{{ criticalProblems || 0 }} 严重问题</p>
            </div>
            <div class="h-12 w-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
              <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
          </div>
        </BaseCard>
        
        <!-- 剩余次数 -->
        <BaseCard>
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">剩余次数</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ userStore.currentUser?.check_balance ?? 0 }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">基础套餐</p>
            </div>
            <div class="h-12 w-12 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
              <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
              </svg>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- 最近文档和进行中任务 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 最近文档 -->
        <BaseCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">最近文档</h3>
              <router-link to="/documents" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm transition-colors">查看全部</router-link>
          </div>
          </template>
          
            <div v-if="recentDocuments.length > 0" class="space-y-4">
              <div v-for="doc in recentDocuments" :key="doc.document_id" class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
                <div class="file-icon" :class="getFileType(doc.filename)">{{ getFileType(doc.filename) }}</div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 dark:text-white text-truncate">{{ doc.filename }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatTime(doc.created_at) }}</p>
                </div>
                <span class="status-badge" :class="getStatusClass(doc.status)">{{ getStatusText(doc.status) }}</span>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <p class="text-gray-500 dark:text-gray-400">暂无最近文档</p>
            </div>
        </BaseCard>
        
        <!-- 进行中任务 -->
        <BaseCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">进行中任务</h3>
              <router-link to="/tasks" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm transition-colors">查看全部</router-link>
            </div>
          </template>
          
          <div v-if="ongoingTasks.length > 0" class="space-y-4">
            <div v-for="task in ongoingTasks" :key="task.task_id" class="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600/50 transition-colors">
              <div class="flex items-center justify-between mb-2">
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ task.filename }}</p>
                <span class="text-xs" :class="getTaskStatusClass(task.status)">{{ getStatusText(task.status) }}</span>
              </div>
              <div class="progress">
                <div class="progress-bar" :style="{ width: task.progress + '%' }"></div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ getTaskTypeText(task.task_type) }}</p>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <p class="text-sm text-gray-500 dark:text-gray-400">没有进行中的任务</p>
          </div>
        </BaseCard>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import { systemApi, type SystemStats } from '@/services/systemApi'
import { DocumentApi } from '@/services/documentApi'
import { TaskApi } from '@/services/taskApi'
import type { Document, Task } from '@/types'
import { authApi } from '@/services/authApi' // 导入authApi

const userStore = useUserStore()
const documentApi = new DocumentApi()
const taskApi = new TaskApi()

// 加载和错误状态
const isLoading = ref(true)
const error = ref<string | null>(null)

// 定时器
let refreshTimer: number | null = null

// 统计数据
const stats = ref<SystemStats>({
  total_documents: 0,
  completed_tasks: 0,
  processing_tasks: 0,
  total_problems: 0,
} as SystemStats)
const criticalProblems = ref(0)

// 列表数据
const recentDocuments = ref<Document[]>([])
const ongoingTasks = ref<Task[]>([])

// 状态样式映射
const getStatusClass = (status: string) => {
  const classes: { [key: string]: string } = {
    'completed': 'status-completed',
    'processing': 'status-processing',
    'pending': 'status-pending',
    'failed': 'status-failed',
  }
  return classes[status] || 'status-pending'
}

// 状态文本转换
const getStatusText = (status: string) => {
  const statusTexts: { [key: string]: string } = {
    'completed': '已完成',
    'processing': '处理中',
    'pending': '待处理',
    'failed': '失败',
    'queued': '排队中',
    'running': '运行中'
  }
  return statusTexts[status] || status
}

// 任务类型文本转换
const getTaskTypeText = (taskType: string) => {
  const taskTypeTexts: { [key: string]: string } = {
    'paper_check': '论文检测',
    'format_check': '格式检查',
    'content_analysis': '结构分析'
  }
  return taskTypeTexts[taskType] || taskType
}

const getFileType = (filename: string) => {
  if (filename.endsWith('.docx')) return 'docx'
  if (filename.endsWith('.doc')) return 'doc'
  return 'file'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 任务状态样式映射
const getTaskStatusClass = (status: string) => {
  const classes: { [key: string]: string } = {
    'completed': 'text-blue-600 dark:text-blue-400',
    'queued': 'text-orange-600 dark:text-orange-400',
    'processing': 'text-yellow-600 dark:text-yellow-400',
    'pending': 'text-gray-600 dark:text-gray-400'
  }
  return classes[status] || 'text-gray-600 dark:text-gray-400'
}

// 加载数据
const loadDashboardData = async () => {
  if (isLoading.value) {
    // 只有首次加载时显示loading状态
  } else {
    // 后续刷新时不显示loading，避免界面闪烁
  }
  
  error.value = null
  try {
    // 🔥 修复：独立处理每个API调用，避免单个失败影响整体
    const [
      userProfileData,
      problemStatsData,
      recentDocsData,
      ongoingTasksData,
    ] = await Promise.allSettled([
      authApi.getUserProfile(),
      systemApi.getProblemStats(),
      documentApi.getRecentDocuments(3),
      taskApi.getOngoingTasks(3),
    ])

    // 处理用户统计数据
    const userStats = userProfileData.status === 'fulfilled' ? userProfileData.value : null
    if (userStats?.statistics) {
      stats.value = {
        total_documents: userStats.statistics.total_documents || 0,
        completed_tasks: userStats.statistics.completed_tasks || 0,
        total_tasks: userStats.statistics.total_tasks || 0,
        failed_tasks: 0,
        pending_tasks: 0,
        processing_tasks: 0,
        total_problems: 0
      }
    } else {
      stats.value = {
        total_documents: 0,
        completed_tasks: 0,
        total_tasks: 0,
        failed_tasks: 0,
        pending_tasks: 0,
        processing_tasks: 0,
        total_problems: 0
      }
    }
    
    // 处理问题统计数据
    const problemStats = problemStatsData.status === 'fulfilled' ? problemStatsData.value : []
    criticalProblems.value = problemStats.find(p => p.severity === 'critical')?.count ?? 0
    stats.value.total_problems = problemStats.reduce((sum, p) => sum + (p.count || 0), 0)
    
    // 处理最近文档数据
    const recentDocs = recentDocsData.status === 'fulfilled' ? recentDocsData.value : []
    recentDocuments.value = recentDocs
    
    // 🔥 关键修复：处理进行中任务数据
    const ongoingTasksList = ongoingTasksData.status === 'fulfilled' ? ongoingTasksData.value : []
    ongoingTasks.value = ongoingTasksList
    
    // 动态计算processing_tasks
    stats.value.processing_tasks = ongoingTasksList.length
    
    // 记录调试信息
    console.log('Dashboard数据加载结果:')
    console.log('- 进行中任务数量:', ongoingTasksList.length)
    console.log('- 进行中任务详情:', ongoingTasksList)
    console.log('- 任务状态统计:', stats.value)

    // 🔥 新增：确保total_tasks至少包含当前的completed_tasks + processing_tasks
    if (stats.value.total_tasks < stats.value.completed_tasks + stats.value.processing_tasks) {
      stats.value.total_tasks = stats.value.completed_tasks + stats.value.processing_tasks
    }

  } catch (err: any) {
    console.error('Failed to load dashboard data:', err)
    error.value = err.message || '加载仪表盘数据时发生未知错误'
  } finally {
    isLoading.value = false
  }
}

// 启动定时刷新
const startRefreshTimer = () => {
  // 每30秒刷新一次数据
  refreshTimer = setInterval(() => {
    loadDashboardData()
  }, 30000) // 30秒
}

// 停止定时刷新
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  loadDashboardData()
  startRefreshTimer()
})

onUnmounted(() => {
  stopRefreshTimer()
})
</script> 

<style scoped>
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700;
}

.card-hover {
  @apply hover:shadow-lg hover:scale-105 cursor-pointer;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between;
}

/* 文件图标样式 */
.file-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center text-xs font-bold text-white flex-shrink-0 uppercase;
}

.file-icon.docx {
  @apply bg-blue-600;
}

.file-icon.doc {
  @apply bg-blue-500;
}

/* 状态标签样式 */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize;
}

.status-completed {
  @apply bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400;
}

.status-processing {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-400;
}

.status-pending {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-400;
}

.status-failed {
  @apply bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400;
}

/* 进度条样式 */
.progress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-bar {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* 文本截断样式 */
.text-truncate {
  @apply truncate;
}
</style> 