import { apiService } from './api'

export interface UserProfile {
  id: string
  username: string
  email: string
  realname?: string
  phone?: string
  organization?: string
  bio?: string
  avatar?: string
  created_at: string
  updated_at: string
  check_balance: number
  is_active: boolean
}

export interface UserStats {
  totalDocuments: number
  completedTasks: number
  totalIssues: number
  remainingChecks: number
  totalAnalyses: number
  successfulAnalyses: number
  usageTime: number
  savedTime: number
  issueTypes: {
    name: string
    count: number
    percentage: number
    color: string
  }[]
  monthlyUsage: {
    month: string
    documents: number
    checks: number
  }[]
}

export interface UpdateProfileRequest {
  username?: string
  email?: string
  realname?: string
  phone?: string
  organization?: string
  bio?: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
}

export interface LoginRecord {
  id: string
  device: string
  ip_address: string
  location: string
  login_time: string
  is_current: boolean
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'zh-CN' | 'en-US'
  timezone: string
  defaultAnalysis: string
  autoRefresh: boolean
  showDetails: boolean
  animations: boolean
  preload: boolean
  maxConcurrentTasks: number
  soundNotifications: boolean
  autoSave: boolean
  compactMode: boolean
  advancedMode: boolean
  emailNotifications: boolean
  browserNotifications: boolean
  analysisNotifications: boolean
  weeklyReport: boolean
}

export interface NotificationSettings {
  email: {
    analysisComplete: boolean
    paymentSuccess: boolean
    weeklyReport: boolean
    systemUpdate: boolean
  }
  browser: {
    analysisComplete: boolean
    paymentSuccess: boolean
    systemUpdate: boolean
  }
  sms: {
    loginAlert: boolean
    paymentSuccess: boolean
  }
  notifications: {
    doNotDisturb: boolean
    doNotDisturbStart: string
    doNotDisturbEnd: string
    browserTaskComplete: boolean
    browserPaymentSuccess: boolean
    browserSystemUpdate: boolean
  }
}

export interface RecentActivity {
  id: string
  description: string
  timestamp: string
  type: 'upload' | 'analysis' | 'profile' | 'payment' | 'system'
}

export class UserApi {
  /**
   * 获取用户资料
   */
  async getUserProfile(): Promise<UserProfile> {
    return await apiService.get('/v1/auth/profile')
  }

  /**
   * 更新用户资料
   */
  async updateProfile(data: UpdateProfileRequest): Promise<UserProfile> {
    return await apiService.put('/v1/auth/profile', data)
  }

  /**
   * 修改密码
   */
  async changePassword(data: ChangePasswordRequest): Promise<void> {
    return await apiService.post('/v1/auth/change-password', data)
  }

  /**
   * 获取用户统计数据
   */
  async getUserStats(): Promise<UserStats> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 获取登录记录
   */
  async getLoginRecords(limit: number = 10): Promise<LoginRecord[]> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 终止其他会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    return await apiService.post(`/v1/auth/sessions/${sessionId}/terminate`)
  }

  /**
   * 获取用户偏好设置
   */
  async getPreferences(): Promise<UserPreferences> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 更新用户偏好设置
   */
  async updatePreferences(data: Partial<UserPreferences>): Promise<UserPreferences> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 获取通知设置
   */
  async getNotificationSettings(): Promise<NotificationSettings> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 更新通知设置
   */
  async updateNotificationSettings(data: Partial<NotificationSettings>): Promise<NotificationSettings> {
    // 后端暂时没有此端点，抛出错误让前端使用模拟数据
    throw new Error('API endpoint not available')
  }

  /**
   * 上传头像
   */
  async uploadAvatar(file: File): Promise<{ avatar_url: string }> {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return await apiService.post('/v1/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 删除头像
   */
  async deleteAvatar(): Promise<void> {
    return await apiService.delete('/v1/users/avatar')
  }

  /**
   * 启用/禁用两步验证
   */
  async toggleTwoFactor(enabled: boolean): Promise<void> {
    return await apiService.post('/v1/users/two-factor', { enabled })
  }

  /**
   * 获取账户安全信息
   */
  async getSecurityInfo(): Promise<{
    twoFactorEnabled: boolean
    lastPasswordChange: string
    activeSessionsCount: number
  }> {
    return await apiService.get('/v1/users/security-info')
  }

  /**
   * 导出用户数据
   */
  async exportUserData(): Promise<Blob> {
    const response = await apiService.get('/v1/users/export-data', {
      responseType: 'blob'
    })
    return response as Blob
  }

  /**
   * 删除账户
   */
  async deleteAccount(password: string): Promise<void> {
    return await apiService.post('/v1/users/delete-account', { password })
  }

  /**
   * 获取最近活动
   */
  async getRecentActivities(limit: number = 10): Promise<RecentActivity[]> {
    return await apiService.get('/v1/users/recent-activities', {
      params: { limit }
    })
  }
}

export const userApi = new UserApi()
export default userApi 