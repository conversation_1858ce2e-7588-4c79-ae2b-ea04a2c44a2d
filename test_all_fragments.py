"""
获取所有问题片段，查找中文关键词对齐问题
"""

import requests
import json

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heibailan5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def get_all_fragments(token, task_id):
    """获取所有问题片段"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 100  # 获取更多片段
        })
        
        if response.status_code == 200:
            data = response.json()
            fragments = data['data']['fragments']
            
            print(f"📊 总问题片段数量: {data['data']['total_count']}")
            print(f"📊 当前页片段数量: {len(fragments)}")
            
            # 显示所有问题片段
            for i, fragment in enumerate(fragments):
                print(f"\n📌 问题片段 {i+1}:")
                print(f"   ID: {fragment.get('fragment_id', 'N/A')}")
                print(f"   结构: {fragment.get('structure', 'N/A')}")
                print(f"   类别: {fragment.get('category', 'N/A')}")
                print(f"   严重程度: {fragment.get('severity', 'N/A')}")
                print(f"   规则ID: {fragment.get('rule_id', 'N/A')}")
                print(f"   问题描述: {fragment.get('problem_description', 'N/A')}")
                
                # 检查是否包含对齐相关的问题
                problem_desc = fragment.get('problem_description', '').lower()
                if "alignment" in problem_desc or "对齐" in problem_desc:
                    print(f"   🎯 发现对齐问题!")
                
                # 检查是否是中文关键词格式问题
                if "关键词" in fragment.get('problem_description', '') and "format" in fragment.get('rule_id', ''):
                    print(f"   🎯 发现中文关键词格式问题!")
            
            # 按规则ID分组统计
            rule_stats = {}
            for fragment in fragments:
                rule_id = fragment.get('rule_id', '未知')
                rule_stats[rule_id] = rule_stats.get(rule_id, 0) + 1
            
            print(f"\n📊 按规则ID分组统计:")
            for rule_id, count in sorted(rule_stats.items()):
                print(f"   {rule_id}: {count} 个")
                
                # 特别关注格式相关的规则
                if "format" in rule_id and "paragraph" in rule_id:
                    print(f"     🔍 这是段落格式检查规则，可能包含中文关键词对齐问题")
            
            return fragments
        else:
            print(f"❌ 获取问题片段失败: {response.status_code}")
            print(f"响应: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 获取问题片段失败: {str(e)}")
        return []

def main():
    """主测试流程"""
    print("🔍 获取所有问题片段，查找中文关键词对齐问题")
    print("=" * 60)
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌")
        return
    
    # 使用最新的任务ID
    task_id = "task_72f94cbac2374d63b2cf7a5d2d97d7e4"
    
    print(f"🔍 分析任务: {task_id}")
    
    fragments = get_all_fragments(token, task_id)
    
    if fragments:
        # 查找中文关键词相关的问题
        keywords_fragments = []
        alignment_fragments = []
        format_fragments = []
        
        for fragment in fragments:
            problem_desc = fragment.get('problem_description', '')
            rule_id = fragment.get('rule_id', '')
            
            # 查找关键词相关问题
            if "关键词" in problem_desc:
                keywords_fragments.append(fragment)
            
            # 查找对齐相关问题
            if "alignment" in problem_desc.lower() or "对齐" in problem_desc:
                alignment_fragments.append(fragment)
            
            # 查找格式相关问题
            if "format.paragraph" in rule_id:
                format_fragments.append(fragment)
        
        print(f"\n🎯 分析结果:")
        print(f"   关键词相关问题: {len(keywords_fragments)} 个")
        print(f"   对齐相关问题: {len(alignment_fragments)} 个")
        print(f"   段落格式问题: {len(format_fragments)} 个")
        
        if format_fragments:
            print(f"\n📌 段落格式问题详情:")
            for i, fragment in enumerate(format_fragments):
                print(f"\n   格式问题 {i+1}:")
                print(f"     ID: {fragment.get('fragment_id')}")
                print(f"     问题描述: {fragment.get('problem_description')}")
                
                # 检查是否包含中文关键词和对齐问题
                if "关键词" in fragment.get('problem_description', '') and "alignment" in fragment.get('problem_description', ''):
                    print(f"     ✅ 这就是我们要找的中文关键词对齐问题!")
        
        print(f"\n🌐 前端查看: http://localhost:3000/document/{task_id}/statistics")
    
    print("\n" + "=" * 60)
    print("🏁 分析完成")

if __name__ == "__main__":
    main()
