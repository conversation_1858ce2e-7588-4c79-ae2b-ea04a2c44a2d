"""
COM操作错误处理和重试机制

提供COM操作的重试装饰器、超时处理、异常分类等功能：
- 重试装饰器
- 超时控制
- 异常分类和处理策略
- 退避算法
"""

import time
import functools
import threading
from typing import Callable, Any, Optional, Dict, List, Type
from datetime import datetime, timedelta
import structlog

logger = structlog.get_logger()


class RetryError(Exception):
    """重试失败异常"""
    pass


class TimeoutError(Exception):
    """超时异常"""
    pass


class RetryableError(Exception):
    """可重试异常基类"""
    pass


class NonRetryableError(Exception):
    """不可重试异常基类"""
    pass


# COM相关的可重试异常类型
RETRYABLE_COM_ERRORS = [
    "The server threw an exception",
    "Call was rejected by callee",
    "The application called an interface that was marshalled for a different thread",
    "RPC_E_CALL_REJECTED",
    "RPC_E_SERVERCALL_RETRYLATER",
    "DISP_E_EXCEPTION",
]

# COM相关的不可重试异常类型
NON_RETRYABLE_COM_ERRORS = [
    "File not found",
    "Access denied",
    "Invalid file format",
    "Disk full",
    "Out of memory",
]


def is_retryable_error(error: Exception) -> bool:
    """
    判断异常是否可重试
    
    Args:
        error: 异常对象
        
    Returns:
        bool: 是否可重试
    """
    error_str = str(error).lower()
    
    # 检查是否为明确的不可重试错误
    for non_retryable in NON_RETRYABLE_COM_ERRORS:
        if non_retryable.lower() in error_str:
            return False
    
    # 检查是否为可重试错误
    for retryable in RETRYABLE_COM_ERRORS:
        if retryable.lower() in error_str:
            return True
    
    # 默认情况下，COM异常可重试
    if "com_error" in error_str or "pywintypes" in error_str:
        return True
    
    return False


def calculate_backoff_delay(attempt: int, base_delay: float = 1.0, max_delay: float = 60.0) -> float:
    """
    计算退避延迟时间（指数退避）
    
    Args:
        attempt: 当前重试次数（从1开始）
        base_delay: 基础延迟时间（秒）
        max_delay: 最大延迟时间（秒）
        
    Returns:
        float: 延迟时间（秒）
    """
    delay = base_delay * (2 ** (attempt - 1))
    return min(delay, max_delay)


class RetryConfig:
    """重试配置"""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        timeout: Optional[float] = None,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
        non_retryable_exceptions: Optional[List[Type[Exception]]] = None
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.timeout = timeout
        self.retryable_exceptions = retryable_exceptions or []
        self.non_retryable_exceptions = non_retryable_exceptions or []


def with_retry(config: Optional[RetryConfig] = None):
    """
    重试装饰器
    
    Args:
        config: 重试配置
        
    Returns:
        装饰器函数
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    # 检查超时
                    if config.timeout and (time.time() - start_time) > config.timeout:
                        raise TimeoutError(f"操作超时: {config.timeout}秒")
                    
                    # 执行函数
                    result = func(*args, **kwargs)
                    
                    # 成功执行，记录日志
                    if attempt > 1:
                        elapsed_time = time.time() - start_time
                        logger.info(
                            f"函数 {func.__name__} 在第 {attempt} 次尝试后成功",
                            elapsed_time=elapsed_time
                        )
                    
                    return result
                    
                except Exception as e:
                    last_exception = e
                    
                    # 检查是否为明确的不可重试异常
                    if any(isinstance(e, exc_type) for exc_type in config.non_retryable_exceptions):
                        logger.error(f"函数 {func.__name__} 遇到不可重试异常: {str(e)}")
                        raise e
                    
                    # 检查是否为可重试异常
                    should_retry = False
                    if config.retryable_exceptions:
                        should_retry = any(isinstance(e, exc_type) for exc_type in config.retryable_exceptions)
                    else:
                        should_retry = is_retryable_error(e)
                    
                    if not should_retry:
                        logger.error(f"函数 {func.__name__} 遇到不可重试异常: {str(e)}")
                        raise e
                    
                    # 如果是最后一次尝试，抛出异常
                    if attempt == config.max_attempts:
                        elapsed_time = time.time() - start_time
                        logger.error(
                            f"函数 {func.__name__} 在 {config.max_attempts} 次尝试后仍然失败",
                            elapsed_time=elapsed_time,
                            last_error=str(e)
                        )
                        raise RetryError(f"重试 {config.max_attempts} 次后仍然失败: {str(e)}")
                    
                    # 计算延迟时间
                    delay = calculate_backoff_delay(attempt, config.base_delay, config.max_delay)
                    
                    logger.warning(
                        f"函数 {func.__name__} 第 {attempt} 次尝试失败，{delay:.1f}秒后重试",
                        error=str(e),
                        attempt=attempt,
                        max_attempts=config.max_attempts
                    )
                    
                    # 等待后重试
                    time.sleep(delay)
            
            # 理论上不会到达这里
            raise last_exception
        
        return wrapper
    return decorator


class TimeoutContext:
    """超时上下文管理器"""
    
    def __init__(self, timeout: float, operation_name: str = "操作"):
        self.timeout = timeout
        self.operation_name = operation_name
        self.start_time = None
        self.timer = None
        self.timed_out = False
    
    def __enter__(self):
        self.start_time = time.time()
        self.timed_out = False
        
        # 设置超时定时器
        if self.timeout > 0:
            self.timer = threading.Timer(self.timeout, self._timeout_handler)
            self.timer.start()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.timer:
            self.timer.cancel()
        
        if self.timed_out:
            elapsed_time = time.time() - self.start_time if self.start_time else 0
            raise TimeoutError(f"{self.operation_name}超时: {elapsed_time:.1f}秒 (限制: {self.timeout}秒)")
    
    def _timeout_handler(self):
        """超时处理器"""
        self.timed_out = True
        logger.warning(f"{self.operation_name}超时: {self.timeout}秒")
    
    def check_timeout(self):
        """检查是否超时"""
        if self.timed_out:
            elapsed_time = time.time() - self.start_time if self.start_time else 0
            raise TimeoutError(f"{self.operation_name}超时: {elapsed_time:.1f}秒 (限制: {self.timeout}秒)")
        
        if self.start_time and self.timeout > 0:
            elapsed_time = time.time() - self.start_time
            if elapsed_time > self.timeout:
                self.timed_out = True
                raise TimeoutError(f"{self.operation_name}超时: {elapsed_time:.1f}秒 (限制: {self.timeout}秒)")


def with_timeout(timeout: float, operation_name: str = "操作"):
    """
    超时装饰器
    
    Args:
        timeout: 超时时间（秒）
        operation_name: 操作名称
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            with TimeoutContext(timeout, f"{operation_name} ({func.__name__})"):
                return func(*args, **kwargs)
        return wrapper
    return decorator


class RetryStats:
    """重试统计信息"""
    
    def __init__(self):
        self.total_attempts = 0
        self.successful_attempts = 0
        self.failed_attempts = 0
        self.retry_attempts = 0
        self.total_retry_time = 0.0
        self.error_counts = {}
        self.lock = threading.Lock()
    
    def record_attempt(self, success: bool, retry_count: int = 0, retry_time: float = 0.0, error: Optional[str] = None):
        """记录尝试结果"""
        with self.lock:
            self.total_attempts += 1
            
            if success:
                self.successful_attempts += 1
            else:
                self.failed_attempts += 1
            
            if retry_count > 0:
                self.retry_attempts += retry_count
                self.total_retry_time += retry_time
            
            if error:
                self.error_counts[error] = self.error_counts.get(error, 0) + 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            success_rate = (self.successful_attempts / self.total_attempts * 100) if self.total_attempts > 0 else 0
            avg_retry_time = (self.total_retry_time / self.retry_attempts) if self.retry_attempts > 0 else 0
            
            return {
                'total_attempts': self.total_attempts,
                'successful_attempts': self.successful_attempts,
                'failed_attempts': self.failed_attempts,
                'success_rate': round(success_rate, 2),
                'retry_attempts': self.retry_attempts,
                'total_retry_time': round(self.total_retry_time, 2),
                'avg_retry_time': round(avg_retry_time, 2),
                'error_counts': dict(self.error_counts)
            }
    
    def reset(self):
        """重置统计信息"""
        with self.lock:
            self.total_attempts = 0
            self.successful_attempts = 0
            self.failed_attempts = 0
            self.retry_attempts = 0
            self.total_retry_time = 0.0
            self.error_counts.clear()


# 全局重试统计实例
retry_stats = RetryStats()


# 预定义的重试配置
COM_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=10.0,
    timeout=30.0
)

WORD_OPERATION_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=0.5,
    max_delay=5.0,
    timeout=60.0
)

FILE_OPERATION_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=0.1,
    max_delay=1.0,
    timeout=10.0
) 