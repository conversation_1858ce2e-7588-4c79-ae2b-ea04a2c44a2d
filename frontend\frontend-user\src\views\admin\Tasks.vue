<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">仪表盘</router-link>
            <router-link to="/admin/users" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">用户管理</router-link>
            <router-link to="/admin/documents" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-red-600 dark:text-red-400 font-medium">任务监控</router-link>
            <router-link to="/admin/system" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">系统设置</router-link>
            <router-link to="/admin/reports" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">报告中心</router-link>
          </div>
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题和操作 -->
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            任务监控
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            实时监控系统任务和性能状态
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button @click="refreshTasks" class="btn btn-secondary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            刷新
          </button>
          <button @click="pauseAllTasks" class="btn btn-warning">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            暂停任务
          </button>
          <button @click="clearCompletedTasks" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            清理完成
          </button>
        </div>
      </div>
      <!-- 实时状态卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 7h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ taskStats.running }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">运行中任务</p>
          </div>
        </div>
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ taskStats.pending }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">等待中任务</p>
          </div>
        </div>
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ taskStats.completed.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">今日完成</p>
          </div>
        </div>
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ taskStats.failed }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">失败任务</p>
          </div>
        </div>
      </div>
      <!-- 系统性能监控 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 实时性能图表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统性能</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">实时CPU和内存使用情况</p>
          </div>
          <div class="card-body">
            <div class="h-48 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                <p class="text-gray-500 dark:text-gray-400">CPU: {{ systemPerformance.cpu }}% | 内存: {{ systemPerformance.memory }}%</p>
              </div>
            </div>
          </div>
        </div>
        <!-- 任务处理速度 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">任务处理速度</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">每小时任务完成数量</p>
          </div>
          <div class="card-body">
            <div class="h-48 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                </svg>
                <p class="text-gray-500 dark:text-gray-400">当前处理速度: {{ taskSpeed }} 任务/小时</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 实时任务列表 -->
      <div class="card mb-8">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">实时任务列表</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">当前正在运行和等待的任务</p>
            </div>
            <div class="flex space-x-2">
              <select v-model="taskFilter" @change="filterTasks" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option value="">所有任务</option>
                <option value="running">运行中</option>
                <option value="pending">等待中</option>
                <option value="completed">已完成</option>
                <option value="failed">失败</option>
              </select>
              <div class="flex items-center">
                <label class="custom-checkbox flex items-center cursor-pointer whitespace-nowrap">
                  <input type="checkbox" v-model="autoRefresh">
                  <span class="checkbox-visual flex-shrink-0"></span>
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">自动刷新</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    任务ID
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    任务类型
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    用户
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    进度
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    开始时间
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    预计完成
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="task in filteredTasks" :key="task.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" 
                      :class="getTaskIdColor(task.status)">
                    {{ task.id }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div :class="[
                        'h-8 w-8 rounded flex items-center justify-center mr-3',
                        getTaskIconColor(task.type, task.status)
                      ]">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path v-if="task.type === 'format_check'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                          <path v-else-if="task.type === 'structure_analysis'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                          <path v-else-if="task.type === 'citation_check'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                          <path v-else-if="task.type === 'comprehensive'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ getTaskTypeText(task.type) }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ task.fileName }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    {{ task.username }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="[
                      'status-badge text-xs',
                      task.status === 'running' ? 'status-pending' :
                      task.status === 'pending' ? 'status-warning' :
                      task.status === 'completed' ? 'status-completed' :
                      task.status === 'failed' ? 'status-failed' :
                      'status-failed'
                    ]">
                      <div v-if="task.status === 'running'" class="animate-spin inline-block w-3 h-3 border-2 border-white border-t-transparent rounded-full mr-1"></div>
                      {{ getStatusText(task.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div v-if="task.status === 'running'" class="flex items-center">
                      <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                        <div class="bg-blue-600 h-2 rounded-full" :style="{ width: task.progress + '%' }"></div>
                      </div>
                      <span class="text-xs text-gray-600 dark:text-gray-300">{{ task.progress }}%</span>
                    </div>
                    <div v-else-if="task.status === 'pending'" class="text-sm text-gray-500 dark:text-gray-400">
                      {{ task.queuePosition }}
                    </div>
                    <div v-else-if="task.status === 'completed'" class="text-sm text-green-500 dark:text-green-400">
                      {{ task.completionInfo }}
                    </div>
                    <div v-else-if="task.status === 'failed'" class="text-sm text-red-500 dark:text-red-400">
                      {{ task.errorMessage }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ formatTime(task.startTime) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ task.estimatedCompletion || '-' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <!-- 运行中任务 -->
                      <template v-if="task.status === 'running'">
                        <button @click="viewTask(task)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">查看</button>
                        <button @click="cancelTask(task)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">取消</button>
                      </template>
                      <!-- 等待中任务 -->
                      <template v-else-if="task.status === 'pending'">
                        <button @click="prioritizeTask(task)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">优先</button>
                        <button @click="cancelTask(task)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">取消</button>
                      </template>
                      <!-- 失败任务 -->
                      <template v-else-if="task.status === 'failed'">
                        <button @click="retryTask(task)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">重试</button>
                        <button @click="removeTask(task)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">移除</button>
                      </template>
                      <!-- 完成任务 -->
                      <template v-else-if="task.status === 'completed'">
                        <button @click="viewResult(task)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">查看结果</button>
                      </template>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <!-- 系统状态监控 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 服务状态 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">服务状态</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">各组件运行状态</p>
          </div>
          <div class="card-body space-y-3">
            <div v-for="service in systemServices" :key="service.name" class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ service.name }}</span>
              <span :class="[
                'status-badge text-xs',
                service.status === 'normal' ? 'status-completed' :
                service.status === 'warning' ? 'status-pending' :
                'status-failed'
              ]">
                {{ service.statusText }}
              </span>
            </div>
          </div>
        </div>
        <!-- 工作线程状态 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">工作线程</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">并发处理能力</p>
          </div>
          <div class="card-body space-y-3">
            <div v-for="worker in systemWorkers" :key="worker.name" class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ worker.name }}</span>
              <div class="flex items-center">
                <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                  <div :class="[
                    'h-2 rounded-full',
                    worker.usage >= 90 ? 'bg-red-600' :
                    worker.usage >= 80 ? 'bg-yellow-600' :
                    'bg-green-600'
                  ]" :style="{ width: worker.usage + '%' }"></div>
                </div>
                <span class="text-xs text-gray-600 dark:text-gray-300">{{ worker.usage }}%</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 队列状态 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">队列状态</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">任务队列信息</p>
          </div>
          <div class="card-body space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">高优先级</span>
              <span class="text-sm font-bold text-red-600 dark:text-red-400">{{ queueStats.high }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">普通优先级</span>
              <span class="text-sm font-bold text-blue-600 dark:text-blue-400">{{ queueStats.normal }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">低优先级</span>
              <span class="text-sm font-bold text-gray-600 dark:text-gray-300">{{ queueStats.low }}</span>
            </div>
            <div class="border-t dark:border-gray-600 pt-3">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">队列总数</span>
                <span class="text-sm font-bold text-gray-900 dark:text-white">{{ queueStats.total }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">平均等待</span>
                <span class="text-sm font-bold text-gray-900 dark:text-white">{{ queueStats.avgWait }}分钟</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'
import { $notify } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'

const router = useRouter()
const themeStore = useThemeStore()
// 组件状态
const showAdminMenu = ref(false)
const taskFilter = ref('')
const autoRefresh = ref(true)
// 任务统计
const taskStats = reactive({
  running: 12,
  pending: 28,
  completed: 1247,
  failed: 3
})
// 系统性能
const systemPerformance = reactive({
  cpu: 72,
  memory: 76
})
const taskSpeed = ref(85)
// 系统服务状态
const systemServices = ref([
  { name: '任务调度器', status: 'normal', statusText: '正常' },
  { name: '文档处理器', status: 'normal', statusText: '正常' },
  { name: '分析引擎', status: 'warning', statusText: '高负载' },
  { name: '通知服务', status: 'normal', statusText: '正常' },
  { name: '存储服务', status: 'normal', statusText: '正常' }
])
// 工作线程状态
const systemWorkers = ref([
  { name: 'Worker-01', usage: 85 },
  { name: 'Worker-02', usage: 60 },
  { name: 'Worker-03', usage: 92 },
  { name: 'Worker-04', usage: 45 }
])
// 队列状态
const queueStats = reactive({
  high: 3,
  normal: 25,
  low: 0,
  total: 28,
  avgWait: 3.5
})
// 模拟任务数据
const allTasks = ref([
  {
    id: 'TASK-001234',
    type: 'format_check',
    fileName: '毕业论文_最终版.docx',
    username: '张三',
    status: 'running',
    progress: 75,
    startTime: '2024-01-20 16:30:15',
    estimatedCompletion: '约 2 分钟'
  },
  {
    id: 'TASK-001235',
    type: 'structure_analysis',
    fileName: '学术论文草稿.docx',
    username: '李四',
    status: 'pending',
    queuePosition: '队列第 3 位',
    startTime: '2024-01-20 16:32:20',
    estimatedCompletion: '约 8 分钟'
  },
  {
    id: 'TASK-001236',
    type: 'comprehensive',
    fileName: '研究报告.pdf',
    username: '王五',
    status: 'failed',
    errorMessage: '文件格式错误',
    startTime: '2024-01-20 16:25:10',
    estimatedCompletion: '-'
  },
  {
    id: 'TASK-001237',
    type: 'citation_check',
    fileName: '论文初稿.docx',
    username: '赵六',
    status: 'completed',
    completionInfo: '100% - 发现 2 个问题',
    startTime: '2024-01-20 16:20:05',
    estimatedCompletion: '16:22:18'
  }
])
// 计算属性
const filteredTasks = computed(() => {
  if (!taskFilter.value) return allTasks.value
  return allTasks.value.filter(task => task.status === taskFilter.value)
})
// 方法
const getTaskIdColor = (status: string) => {
  switch (status) {
    case 'running': return 'text-blue-600'
    case 'pending': return 'text-yellow-600'
    case 'completed': return 'text-green-600'
    case 'failed': return 'text-red-600'
    default: return 'text-gray-600'
  }
}
const getTaskIconColor = (type: string, status: string) => {
  if (status === 'failed') return 'bg-red-100 text-red-600'
  if (status === 'completed') return 'bg-green-100 text-green-600'
  if (status === 'running') return 'bg-blue-100 text-blue-600'
  if (status === 'pending') return 'bg-yellow-100 text-yellow-600'
  switch (type) {
    case 'format_check': return 'bg-blue-100 text-blue-600'
    case 'structure_analysis': return 'bg-yellow-100 text-yellow-600'
    case 'citation_check': return 'bg-green-100 text-green-600'
    case 'comprehensive': return 'bg-purple-100 text-purple-600'
    default: return 'bg-gray-100 text-gray-600'
  }
}
const getTaskTypeText = (type: string) => {
  const typeMap = {
    format_check: '论文格式检测',
    structure_analysis: '结构分析',
    citation_check: '引用检查',
    comprehensive: '综合分析'
  }
  return typeMap[type as keyof typeof typeMap] || type
}
const getStatusText = (status: string) => {
  const statusMap = {
    running: '运行中',
    pending: '等待中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || status
}
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  })
}
const filterTasks = () => {
  // 筛选逻辑已在computed中处理
}
const refreshTasks = () => {
  $notify.info('刷新任务列表')
}
const pauseAllTasks = async () => {
  const result = await $confirm.warning('确定要暂停所有运行中的任务吗？这可能会影响用户。', {
    title: '暂停所有任务',
    confirmText: '确定暂停',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在暂停所有任务...')
  }
}
const clearCompletedTasks = async () => {
  const result = await $confirm.warning('确定要清理所有已完成的任务吗？此操作不可恢复。', {
    title: '清理已完成任务',
    confirmText: '确定清理',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在清理已完成任务...')
  }
}
const viewTask = (task: any) => {
  $notify.info(`查看任务: ${task.id}`)
}
const cancelTask = async (task: any) => {
  const result = await $confirm.warning(`确定要取消任务 "${task.id}" 吗？取消后无法恢复。`, {
    title: '取消任务',
    confirmText: '确定取消',
    cancelText: '保留任务'
  })
  if (result) {
    task.status = 'failed'
    $notify.warning(`任务 "${task.id}" 已取消`)
  }
}
const prioritizeTask = (task: any) => {
  $notify.success(`任务 "${task.id}" 已设置为高优先级`)
}
const retryTask = async (task: any) => {
  const result = await $confirm.info(`确定要重试任务 "${task.id}" 吗？`, {
    title: '重试任务',
    confirmText: '重试',
    cancelText: '取消'
  })
  if (result) {
    task.status = 'pending'
    task.queuePosition = '队列第 1 位'
    $notify.success(`任务 "${task.id}" 已重新加入队列`)
  }
}
const removeTask = async (task: any) => {
  const result = await $confirm.danger(`确定要移除任务 "${task.id}" 吗？此操作不可恢复。`, {
    title: '移除任务',
    confirmText: '确定移除',
    cancelText: '取消'
  })
  if (result) {
    const index = allTasks.value.findIndex(t => t.id === task.id)
    if (index > -1) {
      allTasks.value.splice(index, 1)
      $notify.success(`任务 "${task.id}" 已移除`)
    }
  }
}
const viewResult = (task: any) => {
  $notify.info(`查看任务结果: ${task.id}`)
}
const handleAdminLogout = () => {
  adminLogout(router)
}
let refreshInterval: number | null = null
onMounted(async () => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/tasks')) {
    return
  }

  if (autoRefresh.value) {
    refreshInterval = setInterval(() => {
      // 模拟数据更新
      systemPerformance.cpu = Math.floor(Math.random() * 30) + 45
      systemPerformance.memory = Math.floor(Math.random() * 20) + 65
    }, 5000)
  }
})
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
<style scoped>
/* 任务监控特有样式 */
</style> 
