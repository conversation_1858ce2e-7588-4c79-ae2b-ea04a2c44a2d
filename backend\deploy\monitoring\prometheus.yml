# ==================================================
# Word文档分析服务 - Prometheus监控配置
# ==================================================

global:
  scrape_interval: 15s              # 全局抓取间隔
  evaluation_interval: 15s          # 规则评估间隔
  external_labels:
    monitor: 'word-service-monitor'
    environment: 'production'

# 规则文件配置
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Word服务监控
  - job_name: 'word-service'
    static_configs:
      - targets: ['word-service:8000']
    scrape_interval: 15s
    metrics_path: /api/v1/metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 30s
    metrics_path: /nginx_status
    scrape_timeout: 5s

  # 系统监控 (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # 容器监控 (cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

# 远程写入配置 (可选)
# remote_write:
#   - url: "https://your-remote-storage/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://your-remote-storage/api/v1/read"
#     basic_auth:
#       username: "username"
