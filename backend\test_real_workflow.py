#!/usr/bin/env python3
"""
真实工作流程测试脚本

模拟完整的用户上传文档和分析流程，测试系统的端到端功能。
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path
import sys


class RealWorkflowTester:
    """真实工作流程测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.access_token = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def login(self, username: str, password: str):
        """用户登录"""
        print(f"🔐 正在登录用户: {username}")
        
        login_data = {
            "username": username,
            "password": password
        }
        
        async with self.session.post(
            f"{self.base_url}/api/v1/auth/login",
            data=login_data
        ) as response:
            if response.status == 200:
                result = await response.json()
                print(f"🔍 登录响应: {result}")
                # 访问令牌在 data.access_token 中
                data = result.get("data", {})
                self.access_token = data.get("access_token")
                print(f"✅ 登录成功，获取到访问令牌")
                print(f"🔑 令牌长度: {len(self.access_token) if self.access_token else 0}")
                return True
            else:
                error_text = await response.text()
                print(f"❌ 登录失败: {response.status} - {error_text}")
                return False
    
    async def upload_document(self, file_path: str, analysis_type: str = "paper_check", detection_standard: str = "hbkj_bachelor_2024"):
        """上传文档"""
        print(f"📤 正在上传文档: {file_path}")
        print(f"🎯 使用检测标准: {detection_standard}")

        if not self.access_token:
            print("❌ 未登录，无法上传文档")
            return None

        file_path = Path(file_path)
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return None

        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }

        # 🔥 修复：使用与config.py一致的标准名称
        from app.core.config import DETECTION_STANDARD_NAMES
        standard_names = DETECTION_STANDARD_NAMES
        standard_name = standard_names.get(detection_standard, "未知检测标准")

        # 创建multipart form data
        data = aiohttp.FormData()
        data.add_field('file',
                      open(file_path, 'rb'),
                      filename=file_path.name,
                      content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        data.add_field('analysis_type', analysis_type)
        data.add_field('options', json.dumps({
            "check_format": True,
            "check_structure": True,
            "check_content": True,
            "check_references": True,
            "detection_standard": detection_standard,  # 🎯 添加检测标准
            "standard_name": standard_name             # 🎯 添加标准名称
        }))
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/documents/upload",
                headers=headers,
                data=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"🔍 上传响应: {result}")
                    print(f"✅ 文档上传成功")
                    # 检查响应结构
                    data = result.get('data', result)  # 可能在data中，也可能在根级别
                    print(f"   文档ID: {data.get('document', {}).get('id') if isinstance(data.get('document'), dict) else data.get('document_id')}")
                    print(f"   任务ID: {data.get('task_id')}")
                    return result
                else:
                    error_text = await response.text()
                    print(f"❌ 文档上传失败: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"❌ 上传过程中发生异常: {e}")
            return None
    
    async def poll_task_status(self, task_id: str, max_wait_time: int = 300):
        """轮询任务状态"""
        print(f"🔄 开始轮询任务状态: {task_id}")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        start_time = time.time()
        last_progress = -1
        
        while time.time() - start_time < max_wait_time:
            try:
                async with self.session.get(
                    f"{self.base_url}/api/v1/tasks/{task_id}/status",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        status = await response.json()
                        print(f"🔍 任务状态响应: {status}")

                        # 检查响应结构，可能在data中
                        data = status.get('data', status)
                        current_progress = data.get('progress', 0)
                        task_status = data.get('status', 'unknown')
                        current_step = data.get('current_step', '')
                        step_message = data.get('step_message', '')
                        
                        # 只在进度变化时打印
                        if current_progress != last_progress:
                            print(f"📊 任务进度: {current_progress}% - {step_message}")
                            last_progress = current_progress
                        
                        # 检查任务是否完成
                        if task_status == 'completed':
                            print(f"✅ 任务完成!")
                            # 从data中获取结果，如果没有result字段，返回整个data
                            result = data.get('result', data)
                            return result
                        elif task_status == 'failed':
                            error_msg = status.get('error_message', '未知错误')
                            print(f"❌ 任务失败: {error_msg}")
                            return None
                        elif task_status in ['pending', 'running']:
                            # 继续等待
                            await asyncio.sleep(2)
                        else:
                            print(f"⚠️ 未知任务状态: {task_status}")
                            await asyncio.sleep(2)
                    else:
                        print(f"❌ 获取任务状态失败: {response.status}")
                        await asyncio.sleep(5)
                        
            except Exception as e:
                print(f"❌ 轮询过程中发生异常: {e}")
                await asyncio.sleep(5)
        
        print(f"⏰ 任务轮询超时 ({max_wait_time}秒)")
        return None
    
    async def get_document_details(self, document_id: int):
        """获取文档详情"""
        print(f"📄 获取文档详情: {document_id}")
        
        headers = {
            "Authorization": f"Bearer {self.access_token}"
        }
        
        async with self.session.get(
            f"{self.base_url}/api/v1/documents/{document_id}",
            headers=headers
        ) as response:
            if response.status == 200:
                document = await response.json()
                print(f"✅ 获取文档详情成功")
                return document
            else:
                error_text = await response.text()
                print(f"❌ 获取文档详情失败: {response.status} - {error_text}")
                return None
    
    def print_analysis_summary(self, result):
        """打印分析结果摘要"""
        print("\n" + "="*60)
        print(" 📋 分析结果摘要")
        print("="*60)
        
        if not result:
            print("❌ 无分析结果")
            return
        
        # 检测结果统计
        check_results = result.get('check_results', [])
        if check_results:
            total_checks = len(check_results)
            passed_checks = sum(1 for r in check_results if r.get('passed', False))
            failed_checks = total_checks - passed_checks
            
            print(f"📊 检测统计:")
            print(f"   总检测项: {total_checks}")
            print(f"   通过项: {passed_checks}")
            print(f"   失败项: {failed_checks}")
            print(f"   通过率: {passed_checks/total_checks*100:.1f}%")
            
            # 显示失败的检测项
            if failed_checks > 0:
                print(f"\n❌ 检测失败项:")
                for i, result_item in enumerate(check_results, 1):
                    if not result_item.get('passed', False):
                        rule_name = result_item.get('rule_name', '未知规则')
                        message = result_item.get('message', '无消息')
                        severity = result_item.get('severity', 'unknown')
                        print(f"   {i}. {rule_name}")
                        print(f"      问题: {message}")
                        print(f"      严重程度: {severity}")
                        print()
        
        # 文档信息
        doc_info = result.get('document_info', {})
        if doc_info:
            print(f"📄 文档信息:")
            print(f"   页数: {doc_info.get('page_count', 'N/A')}")
            print(f"   字数: {doc_info.get('word_count', 'N/A')}")
            print(f"   段落数: {doc_info.get('paragraph_count', 'N/A')}")
        
        # 检测标准信息
        detection_standard = result.get('detection_standard', 'unknown')
        standard_name = result.get('standard_name', '未知标准')
        print(f"🎯 检测标准: {detection_standard}")
        print(f"📋 标准名称: {standard_name}")

        # 显示检测标准的详细信息
        if detection_standard == 'hbkj_bachelor_2024':
            print(f"📝 检测范围: 封面信息、任务书、开题报告、诚信声明、标题格式、章节结构")
        elif detection_standard == 'gbt_7713_1_2006':
            print(f"📝 检测范围: 封面信息、标题层次、章节结构、字体样式、段落格式")
        elif detection_standard == 'gbt_7714_2015':
            print(f"📝 检测范围: 专著著录、期刊著录、引用编号、文献类型标识")

        print("="*60)


async def main():
    """主测试流程"""
    print("🚀 开始真实工作流程测试")
    print("="*60)

    # 测试参数
    username = "8966097"
    password = "heibailan5112"
    test_file = "../docs/test.docx"
    detection_standard = "hbkj_bachelor_2024"

    print(f"👤 测试账号: {username}")
    print(f"📄 测试文件: {test_file}")
    print(f"🎯 检测标准: {detection_standard} (河北科技学院本科论文检查)")
    print("="*60)
    
    # 检查测试文件是否存在
    if not Path(test_file).exists():
        print(f"❌ 测试文件不存在: {test_file}")
        print("请确保测试文件存在后重新运行")
        return
    
    async with RealWorkflowTester() as tester:
        # 步骤1: 用户登录
        login_success = await tester.login(username, password)
        if not login_success:
            print("❌ 登录失败，测试终止")
            return
        
        # 步骤2: 上传文档（使用河北科技学院本科论文检查标准）
        upload_result = await tester.upload_document(test_file, "paper_check", "hbkj_bachelor_2024")
        if not upload_result:
            print("❌ 文档上传失败，测试终止")
            return
        
        # 从响应中获取数据
        data = upload_result.get('data', upload_result)
        document_id = data.get('document', {}).get('id') if isinstance(data.get('document'), dict) else data.get('document_id')
        task_id = data.get('task_id')
        
        if not task_id:
            print("❌ 未获取到任务ID，测试终止")
            return
        
        # 步骤3: 轮询任务状态
        analysis_result = await tester.poll_task_status(task_id)
        if not analysis_result:
            print("❌ 任务执行失败或超时")
            return
        
        # 步骤4: 获取文档详情
        if document_id:
            document_details = await tester.get_document_details(document_id)
            if document_details:
                print(f"✅ 文档状态: {document_details.get('status', 'unknown')}")
        
        # 步骤5: 显示分析结果
        tester.print_analysis_summary(analysis_result)
        
        print("\n🎉 真实工作流程测试完成!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
