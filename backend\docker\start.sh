#!/bin/sh

set -e

echo "🚀 启动 Paper Check Frontend 容器..."

# 设置时区
if [ -n "$TZ" ]; then
    echo "⏰ 设置时区为: $TZ"
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime
    echo $TZ > /etc/timezone
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p /var/log/nginx
mkdir -p /var/cache/nginx/api
mkdir -p /var/run
mkdir -p /tmp

# 设置权限
echo "🔐 设置目录权限..."
chown -R nginx:nginx /var/log/nginx
chown -R nginx:nginx /var/cache/nginx
chown -R nginx:nginx /usr/share/nginx/html
chmod -R 755 /usr/share/nginx/html

# 检查nginx配置
echo "🔍 检查nginx配置..."
nginx -t

# 设置环境变量
echo "🌐 设置环境变量..."
export NGINX_WORKER_PROCESSES=${NGINX_WORKER_PROCESSES:-auto}
export NGINX_WORKER_CONNECTIONS=${NGINX_WORKER_CONNECTIONS:-1024}
export NGINX_CLIENT_MAX_BODY_SIZE=${NGINX_CLIENT_MAX_BODY_SIZE:-50M}

# 替换配置中的环境变量
if [ -f /etc/nginx/nginx.conf ]; then
    sed -i "s/worker_processes auto;/worker_processes $NGINX_WORKER_PROCESSES;/" /etc/nginx/nginx.conf
    sed -i "s/worker_connections 1024;/worker_connections $NGINX_WORKER_CONNECTIONS;/" /etc/nginx/nginx.conf
fi

if [ -f /etc/nginx/conf.d/default.conf ]; then
    sed -i "s/client_max_body_size 50M;/client_max_body_size $NGINX_CLIENT_MAX_BODY_SIZE;/" /etc/nginx/conf.d/default.conf
fi

# 处理SSL证书
if [ -d "/etc/nginx/ssl" ] && [ -f "/etc/nginx/ssl/fullchain.pem" ]; then
    echo "🔐 SSL证书已找到"
else
    echo "⚠️ SSL证书未找到，使用自签名证书"
    mkdir -p /etc/nginx/ssl
    
    # 生成自签名证书
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/nginx/ssl/privkey.pem \
        -out /etc/nginx/ssl/fullchain.pem \
        -subj "/C=CN/ST=Beijing/L=Beijing/O=PaperCheck/OU=IT/CN=papercheck.com"
    
    echo "✅ 自签名证书已生成"
fi

# 处理index.html中的环境变量
if [ -f "/usr/share/nginx/html/index.html" ]; then
    echo "🔧 处理index.html中的环境变量..."
    
    # 如果有环境变量需要注入到前端，在这里处理
    # 例如：API_BASE_URL, APP_VERSION等
    if [ -n "$VITE_API_BASE_URL" ]; then
        echo "📡 设置API基础URL: $VITE_API_BASE_URL"
        # 这里可以添加替换逻辑，如果前端需要的话
    fi
    
    if [ -n "$VITE_APP_VERSION" ]; then
        echo "📋 设置应用版本: $VITE_APP_VERSION"
        # 这里可以添加版本信息注入
    fi
fi

# 创建状态页面
echo "📊 创建状态页面..."
cat > /usr/share/nginx/html/status.json << EOF
{
    "status": "ok",
    "service": "papercheck-frontend",
    "version": "${VITE_APP_VERSION:-unknown}",
    "build_time": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "uptime": "$(cat /proc/uptime | cut -d' ' -f1)",
    "environment": "${NODE_ENV:-production}"
}
EOF

# 创建健康检查页面
echo "🏥 创建健康检查页面..."
cat > /usr/share/nginx/html/health.txt << EOF
healthy
EOF

# 创建robots.txt (如果不存在)
if [ ! -f "/usr/share/nginx/html/robots.txt" ]; then
    echo "🤖 创建robots.txt..."
    cat > /usr/share/nginx/html/robots.txt << EOF
User-agent: *
Disallow: /api/
Disallow: /admin/
Disallow: /uploads/
Allow: /

Sitemap: https://papercheck.com/sitemap.xml
EOF
fi

# 创建基础的sitemap.xml (如果不存在)
if [ ! -f "/usr/share/nginx/html/sitemap.xml" ]; then
    echo "🗺️ 创建基础sitemap.xml..."
    cat > /usr/share/nginx/html/sitemap.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://papercheck.com/</loc>
        <lastmod>$(date -u +%Y-%m-%d)</lastmod>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://papercheck.com/login</loc>
        <lastmod>$(date -u +%Y-%m-%d)</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://papercheck.com/dashboard</loc>
        <lastmod>$(date -u +%Y-%m-%d)</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
</urlset>
EOF
fi

# 显示系统信息
echo "📋 系统信息:"
echo "   主机名: $(hostname)"
echo "   时间: $(date)"
echo "   时区: $(cat /etc/timezone 2>/dev/null || echo 'UTC')"
echo "   用户: $(whoami)"
echo "   工作目录: $(pwd)"
echo "   Nginx版本: $(nginx -v 2>&1)"
echo "   环境: ${NODE_ENV:-production}"

# 显示资源使用情况
echo "💾 资源使用情况:"
echo "   内存: $(free -h | awk 'NR==2{printf "%.1f/%.1fGB (%.0f%%)", $3/1024, $2/1024, $3*100/$2}')"
echo "   磁盘: $(df -h / | awk 'NR==2{printf "%s/%s (%s)", $3, $2, $5}')"

# 显示网络配置
echo "🌐 网络配置:"
echo "   IP地址: $(hostname -i 2>/dev/null || echo 'unknown')"
if command -v ss >/dev/null 2>&1; then
    echo "   监听端口: $(ss -tuln | grep LISTEN | awk '{print $5}' | cut -d: -f2 | sort -n | uniq | tr '\n' ' ')"
fi

# 最后检查配置
echo "🔍 最终配置检查..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ 配置检查通过"
else
    echo "❌ 配置检查失败"
    exit 1
fi

# 启动信号处理
handle_signal() {
    echo "📡 收到停止信号，正在优雅关闭..."
    nginx -s quit
    wait $!
    echo "✅ 服务已停止"
    exit 0
}

trap handle_signal TERM INT

# 启动nginx
echo "🚀 启动nginx..."
nginx -g "daemon off;" &

# 等待nginx启动
sleep 2

# 检查nginx是否正常启动
if pgrep nginx > /dev/null; then
    echo "✅ Paper Check Frontend 启动成功!"
    echo "🌐 服务运行在: http://localhost (HTTP) 和 https://localhost (HTTPS)"
    echo "🏥 健康检查: http://localhost/health"
    echo "📊 状态信息: http://localhost/status"
    echo ""
    echo "🎉 Ready to serve requests!"
else
    echo "❌ nginx启动失败"
    exit 1
fi

# 等待nginx进程
wait $! 