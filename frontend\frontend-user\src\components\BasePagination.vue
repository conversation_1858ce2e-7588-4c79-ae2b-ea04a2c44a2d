<template>
  <div v-if="total > 0" class="mt-8">
    <!-- 移动端分页 -->
    <div class="block md:hidden space-y-4">
      <!-- 移动端简化信息 -->
      <div class="text-center text-sm text-gray-600 dark:text-gray-300">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页 ({{ total }} 条记录)
      </div>
      
      <!-- 移动端分页控件 -->
      <div class="flex items-center justify-center space-x-2">
        <BaseButton @click="$emit('page-change', currentPage - 1)" 
                    :disabled="currentPage === 1 || disabled"
                    variant="secondary" size="sm" class="flex-shrink-0">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
          </svg>
        </BaseButton>
        
        <!-- 移动端页码选择器 -->
        <div class="flex items-center space-x-1">
          <BaseButton v-if="showFirstPage" @click="$emit('page-change', 1)"
                      :variant="1 === currentPage ? 'primary' : 'secondary'"
                      :disabled="disabled"
                      size="sm" class="min-w-[2.5rem] flex justify-center">
            1
          </BaseButton>
          
          <span v-if="showFirstEllipsis" class="px-2 text-gray-400">...</span>
          
          <BaseButton v-for="page in visiblePages" :key="page" 
                      @click="$emit('page-change', page)"
                      :variant="page === currentPage ? 'primary' : 'secondary'"
                      :disabled="disabled"
                      size="sm" class="min-w-[2.5rem] flex justify-center">
            {{ page }}
          </BaseButton>
          
          <span v-if="showLastEllipsis" class="px-2 text-gray-400">...</span>
          
          <BaseButton v-if="showLastPage" @click="$emit('page-change', totalPages)"
                      :variant="totalPages === currentPage ? 'primary' : 'secondary'"
                      :disabled="disabled"
                      size="sm" class="min-w-[2.5rem] flex justify-center">
            {{ totalPages }}
          </BaseButton>
        </div>
        
        <BaseButton @click="$emit('page-change', currentPage + 1)" 
                    :disabled="currentPage === totalPages || disabled"
                    variant="secondary" size="sm" class="flex-shrink-0">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </BaseButton>
      </div>
    </div>

    <!-- 桌面端分页 -->
    <div class="hidden md:flex items-center justify-between">
      <div class="text-sm text-gray-700 dark:text-gray-300">
        显示第 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, total) }} 条，共 {{ total }} 条记录
      </div>
      <div class="flex space-x-1">
        <BaseButton @click="$emit('page-change', currentPage - 1)" 
                    :disabled="currentPage === 1 || disabled"
                    variant="secondary" size="sm">
          上一页
        </BaseButton>
        
        <BaseButton v-if="showFirstPage" @click="$emit('page-change', 1)"
                    :variant="1 === currentPage ? 'primary' : 'secondary'"
                    :disabled="disabled"
                    size="sm" class="min-w-[2.5rem] flex justify-center">
          1
        </BaseButton>
        
        <span v-if="showFirstEllipsis" class="flex items-center px-2 text-gray-400">...</span>
        
        <BaseButton v-for="page in visiblePages" :key="page" 
                    @click="$emit('page-change', page)"
                    :variant="page === currentPage ? 'primary' : 'secondary'"
                    :disabled="disabled"
                    size="sm" class="min-w-[2.5rem] flex justify-center">
          {{ page }}
        </BaseButton>
        
        <span v-if="showLastEllipsis" class="flex items-center px-2 text-gray-400">...</span>
        
        <BaseButton v-if="showLastPage" @click="$emit('page-change', totalPages)"
                    :variant="totalPages === currentPage ? 'primary' : 'secondary'"
                    :disabled="disabled"
                    size="sm" class="min-w-[2.5rem] flex justify-center">
          {{ totalPages }}
        </BaseButton>
        
        <BaseButton @click="$emit('page-change', currentPage + 1)" 
                    :disabled="currentPage === totalPages || disabled"
                    variant="secondary" size="sm">
          下一页
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseButton from './BaseButton.vue'

interface Props {
  currentPage: number
  pageSize: number
  total: number
  disabled?: boolean
  maxVisiblePages?: number
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  maxVisiblePages: 5
})

defineEmits<{
  'page-change': [page: number]
}>()

// 计算总页数
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

// 计算可见页码范围
const visiblePages = computed(() => {
  const current = props.currentPage
  const maxVisible = props.maxVisiblePages
  const total = totalPages.value
  
  // 如果总页数小于等于最大可见页数，显示所有页码
  if (total <= maxVisible) {
    const pages = []
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
    return pages
  }
  
  // 计算中间显示的页码
  const pages = []
  const sidePages = Math.floor(maxVisible / 2)
  
  let start = Math.max(current - sidePages, 1)
  let end = Math.min(current + sidePages, total)
  
  // 调整边界情况
  if (end - start + 1 < maxVisible) {
    if (start === 1) {
      end = Math.min(start + maxVisible - 1, total)
    } else {
      start = Math.max(end - maxVisible + 1, 1)
    }
  }
  
  // 避免显示第一页和最后一页
  if (start <= 2) start = 3
  if (end >= total - 1) end = total - 2
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 是否显示第一页
const showFirstPage = computed(() => {
  return totalPages.value > 1 && !visiblePages.value.includes(1)
})

// 是否显示最后一页
const showLastPage = computed(() => {
  return totalPages.value > 1 && !visiblePages.value.includes(totalPages.value)
})

// 是否显示第一个省略号
const showFirstEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value.length > 0 && visiblePages.value[0] > 2
})

// 是否显示最后一个省略号
const showLastEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value.length > 0 && visiblePages.value[visiblePages.value.length - 1] < totalPages.value - 1
})
</script> 