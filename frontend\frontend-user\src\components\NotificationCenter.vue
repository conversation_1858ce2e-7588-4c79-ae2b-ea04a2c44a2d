<template>
  <div class="notification-center">
    <!-- 通知触发按钮 -->
    <div class="relative">
      <button
        @click="toggleNotifications"
        :class="[
          'relative text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700',
          compact ? 'p-1.5' : 'p-2',
          { 'text-blue-600 dark:text-blue-400': showNotifications }
        ]"
      >
        <!-- 🔔 通知图标选项 - v2.0 (已修复) -->
        
        <svg 
          :class="[
            'fill-none stroke-current',
            compact ? 'h-5 w-5' : 'h-6 w-6',
            { 'animate-bounce': unreadCount > 0 && Date.now() - lastNotificationTime < 5000 }
          ]"
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
        </svg>
        
        <!-- 未读通知数量徽章 -->
        <span
          v-if="unreadCount > 0"
          :class="[
            'absolute bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium animate-pulse',
            compact ? '-top-0.5 -right-0.5 h-4 w-4' : '-top-1 -right-1 h-5 w-5'
          ]"
        >
          {{ unreadCount > 99 ? '99+' : unreadCount }}
        </span>
        
        <!-- 新消息提醒动画圆圈 -->
        <span
          v-if="unreadCount > 0 && Date.now() - lastNotificationTime < 3000"
          :class="[
            'absolute bg-red-500 rounded-full animate-ping',
            compact ? '-top-0.5 -right-0.5 h-4 w-4' : '-top-1 -right-1 h-5 w-5'
          ]"
        ></span>
      </button>

      <!-- 通知面板 -->
      <div
        v-show="showNotifications"
        :class="[
          'absolute right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50 overflow-hidden',
          compact ? 'w-72 max-h-80' : 'w-80 max-h-96'
        ]"
      >
        <!-- 通知头部 -->
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">通知中心</h3>
          <div class="flex space-x-2">
            <button
              v-if="notifications.length > 0"
              @click="markAllAsRead"
              class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              全部已读
            </button>
            <button
              @click="clearAllNotifications"
              class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            >
              清空
            </button>
          </div>
        </div>

        <!-- 通知列表 -->
        <div class="max-h-80 overflow-y-auto">
          <div v-if="notifications.length === 0" class="p-6 text-center text-gray-500 dark:text-gray-400">
            <svg class="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <p>暂无通知</p>
          </div>

          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="px-4 py-3 border-b border-gray-100 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
            :class="{ 'bg-blue-50 dark:bg-blue-900/20': !notification.read }"
            @click="markAsRead(notification.id)"
          >
            <div class="flex items-start space-x-3">
              <!-- 通知图标 -->
              <div class="flex-shrink-0 mt-1">
                <div
                  class="h-8 w-8 rounded-full flex items-center justify-center"
                  :class="getNotificationIconClass(notification.level)"
                >
                  <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path v-if="notification.level === 'success'" fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    <path v-else-if="notification.level === 'error'" fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    <path v-else-if="notification.level === 'warning'" fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    <path v-else fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </div>

              <!-- 通知内容 -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {{ notification.title }}
                  </p>
                  <div class="flex items-center space-x-2">
                    <span v-if="!notification.read" class="h-2 w-2 bg-blue-500 rounded-full"></span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {{ formatTime(notification.timestamp) }}
                    </span>
                  </div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                  {{ notification.message }}
                </p>
                
                <!-- 操作按钮 -->
                <div v-if="notification.action" class="mt-2">
                  <button
                    @click.stop="handleNotificationAction(notification)"
                    class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                  >
                    {{ notification.action.text }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- WebSocket连接状态 -->
        <div class="px-4 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
          <div class="flex items-center justify-between text-xs">
            <span class="text-gray-500 dark:text-gray-400">实时通知</span>
            <div class="flex items-center space-x-1">
              <div
                class="h-2 w-2 rounded-full"
                :class="getConnectionStatusClass()"
              ></div>
              <span class="text-gray-500 dark:text-gray-400">
                {{ getConnectionStatusText() }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 点击外部关闭 -->
    <div
      v-if="showNotifications"
      class="fixed inset-0 z-40"
      @click="showNotifications = false"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import websocketService, { 
  WebSocketStatus
} from '@/services/websocketService'

interface Notification {
  id: string
  title: string
  message: string
  level: 'info' | 'success' | 'warning' | 'error'
  timestamp: number
  read: boolean
  action?: {
    text: string
    url: string
  }
}

interface Props {
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false
})

const router = useRouter()

// 响应式状态
const showNotifications = ref(false)
const notifications = ref<Notification[]>([])
const lastNotificationTime = ref(0)

// 计算属性
const unreadCount = computed(() => 
  notifications.value.filter(n => !n.read).length
)

// WebSocket订阅取消函数
let unsubscribeNotifications: (() => void) | null = null

// 方法
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
}

const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
  const newNotification: Notification = {
    ...notification,
    id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    read: false
  }
  
  // 更新最后通知时间
  lastNotificationTime.value = Date.now()
  
  // 添加到列表开头
  notifications.value.unshift(newNotification)
  
  // 限制通知数量（最多保留100条）
  if (notifications.value.length > 100) {
    notifications.value = notifications.value.slice(0, 100)
  }
  
  // 保存到本地存储
  saveNotificationsToStorage()
  
  // 显示浏览器通知（如果用户允许）
  showBrowserNotification(newNotification)
}

const markAsRead = (notificationId: string) => {
  const notification = notifications.value.find(n => n.id === notificationId)
  if (notification && !notification.read) {
    notification.read = true
    saveNotificationsToStorage()
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(notification => {
    notification.read = true
  })
  saveNotificationsToStorage()
}

const clearAllNotifications = () => {
  notifications.value = []
  saveNotificationsToStorage()
}

const handleNotificationAction = (notification: Notification) => {
  if (notification.action?.url) {
    if (notification.action.url.startsWith('http')) {
      // 外部链接
      window.open(notification.action.url, '_blank')
    } else {
      // 内部路由
      router.push(notification.action.url)
      showNotifications.value = false
    }
  }
  markAsRead(notification.id)
}

const getNotificationIconClass = (level: string) => {
  const classes = {
    success: 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400',
    error: 'bg-red-100 dark:bg-red-900/50 text-red-600 dark:text-red-400',
    warning: 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-400',
    info: 'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400'
  }
  return classes[level as keyof typeof classes] || classes.info
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
}

const getConnectionStatusClass = () => {
  const status = websocketService.status.value
  switch (status) {
    case WebSocketStatus.CONNECTED:
      return 'bg-green-500'
    case WebSocketStatus.CONNECTING:
    case WebSocketStatus.RECONNECTING:
      return 'bg-yellow-500'
    case WebSocketStatus.ERROR:
    case WebSocketStatus.DISCONNECTED:
      return 'bg-red-500'
    default:
      return 'bg-gray-500'
  }
}

const getConnectionStatusText = () => {
  const status = websocketService.status.value
  switch (status) {
    case WebSocketStatus.CONNECTED:
      return '已连接'
    case WebSocketStatus.CONNECTING:
      return '连接中'
    case WebSocketStatus.RECONNECTING:
      return '重连中'
    case WebSocketStatus.ERROR:
      return '连接错误'
    case WebSocketStatus.DISCONNECTED:
      return '已断开'
    default:
      return '未知状态'
  }
}

const saveNotificationsToStorage = () => {
  try {
    localStorage.setItem('notifications', JSON.stringify(notifications.value))
  } catch (error) {
    console.error('Failed to save notifications to storage:', error)
  }
}

const loadNotificationsFromStorage = () => {
  try {
    const stored = localStorage.getItem('notifications')
    if (stored) {
      notifications.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('Failed to load notifications from storage:', error)
  }
}

const showBrowserNotification = (notification: Notification) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.ico',
      tag: notification.id
    })
  }
}

const requestNotificationPermission = async () => {
  if ('Notification' in window && Notification.permission === 'default') {
    await Notification.requestPermission()
  }
}

// 生命周期
onMounted(() => {
  // 加载本地存储的通知
  loadNotificationsFromStorage()
  
  // 请求通知权限
  requestNotificationPermission()
  
  // 订阅WebSocket系统通知
  unsubscribeNotifications = websocketService.subscribeToNotifications((data) => {
    addNotification({
      title: data.title,
      message: data.message,
      level: data.level,
      action: data.action
    })
  })
  
  // 检查用户是否已登录，如果已登录则尝试连接WebSocket
  const token = localStorage.getItem('access_token')
  if (token && websocketService.status.value === WebSocketStatus.DISCONNECTED) {
    websocketService.connect(token)
  }
})

// 监听用户登录状态变化
const checkUserLoginStatus = () => {
  const token = localStorage.getItem('access_token')
  if (token && websocketService.status.value === WebSocketStatus.DISCONNECTED) {
    // 用户已登录但WebSocket未连接，尝试连接
    websocketService.connectAfterLogin(token)
  } else if (!token && websocketService.status.value !== WebSocketStatus.DISCONNECTED) {
    // 用户已登出但WebSocket仍连接，断开连接
    websocketService.disconnect()
  }
}

// 监听storage变化（用户在其他标签页登录/登出）
window.addEventListener('storage', (e) => {
  if (e.key === 'access_token') {
    checkUserLoginStatus()
  }
})

onUnmounted(() => {
  // 取消WebSocket订阅
  if (unsubscribeNotifications) {
    unsubscribeNotifications()
  }
})

// 暴露方法供外部使用
defineExpose({
  addNotification,
  markAsRead,
  markAllAsRead,
  clearAllNotifications
})
</script>

<style scoped>
.notification-center {
  position: relative;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}
</style> 