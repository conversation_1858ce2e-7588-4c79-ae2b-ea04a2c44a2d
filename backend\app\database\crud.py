"""
Word文档分析服务 - PostgreSQL数据库CRUD操作
使用SQLAlchemy异步会话
"""

import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy import text, select, update, delete, func, inspect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from passlib.context import CryptContext

from app.models.task import TaskInDB, TaskCreate, TaskUpdate, TaskStatus
from app.models.document import (
    DocumentInDB, DocumentCreate, 
    ContentElementInDB, ContentElementCreate,
    ImageInDB, ImageCreate
)
from app.models.paper_check import (
    PaperCheckResultInDB, PaperCheckResultCreate,
    ProblemInDB, ProblemCreate
)
from app.models.user import UserCreate, UserInDB
from app.models.document_structure import (
    DocumentStructureCreate, DocumentStructureInDB,
    DocumentContentCache, DocumentStructureStats
)
from app.core.logging import logger
from app.security import get_password_hash

# ==============================================================================
# 任务 (Task) 表的CRUD操作
# ==============================================================================

async def create_task(session: AsyncSession, task: TaskCreate) -> TaskInDB:
    """在数据库中创建一条新任务记录"""
    try:
        # 将 Pydantic 模型转换为字典
        task_data = task.model_dump()
        
        # 将字典类型的字段序列化为JSON字符串
        if 'analysis_options' in task_data and task_data['analysis_options'] is not None:
            task_data['analysis_options'] = json.dumps(task_data['analysis_options'])
        
        query = text("""
            INSERT INTO tasks (
                task_id, user_id, filename, file_path, file_size, 
                analysis_options, status, created_at, task_type
            ) VALUES (
                :task_id, :user_id, :filename, :file_path, :file_size, 
                :analysis_options, :status, :created_at, :task_type
            )
        """)
        
        await session.execute(query, task_data)
        await session.commit()
        
        # 修复：直接返回 Pydantic 模型，而不是再次查询数据库
        return TaskInDB(**task.model_dump())
        
    except Exception as e:
        await session.rollback()
        logger.error(f"创建任务失败: {str(e)}")
        raise


async def get_task(session: AsyncSession, task_id: str) -> Optional[TaskInDB]:
    """根据任务ID从数据库中获取任务记录"""
    try:
        query = text("SELECT * FROM tasks WHERE task_id = :task_id")
        result = await session.execute(query, {"task_id": task_id})
        row = result.fetchone()
        
        if row:
            task_data = dict(row._mapping)
            # 将JSON字符串反序列化为字典 (对于JSONB字段，PostgreSQL已经自动反序列化)
            if 'analysis_options' in task_data and task_data['analysis_options'] is not None:
                if isinstance(task_data['analysis_options'], str):
                    try:
                        task_data['analysis_options'] = json.loads(task_data['analysis_options'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['analysis_options'] = {}
                elif not isinstance(task_data['analysis_options'], dict):
                    task_data['analysis_options'] = {}
            if 'result' in task_data and task_data['result'] is not None:
                if isinstance(task_data['result'], str):
                    try:
                        task_data['result'] = json.loads(task_data['result'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['result'] = {}
                elif not isinstance(task_data['result'], dict):
                    task_data['result'] = {}
            return TaskInDB(**task_data)
        return None
    except Exception as e:
        logger.error(f"获取任务 {task_id} 失败: {e}")
        return None


async def get_tasks(
    session: AsyncSession,
    skip: int = 0, 
    limit: int = 20, 
    status: Optional[TaskStatus] = None,
    task_type: Optional[str] = None
) -> List[TaskInDB]:
    """从数据库中获取任务列表（支持分页和筛选）"""
    try:
        query = "SELECT * FROM tasks"
        params = {}
        conditions = []
        
        if status:
            conditions.append("status = :status")
            params["status"] = status.value
        
        if task_type:
            conditions.append("task_type = :task_type")
            params["task_type"] = task_type
            
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
            
        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        params.update({"limit": limit, "skip": skip})
        
        result = await session.execute(text(query), params)
        rows = result.fetchall()
        
        tasks = []
        for row in rows:
            task_data = dict(row._mapping)
            if 'analysis_options' in task_data and task_data['analysis_options'] is not None:
                if isinstance(task_data['analysis_options'], str):
                    try:
                        task_data['analysis_options'] = json.loads(task_data['analysis_options'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['analysis_options'] = {}
                elif not isinstance(task_data['analysis_options'], dict):
                    task_data['analysis_options'] = {}
            if 'result' in task_data and task_data['result'] is not None:
                if isinstance(task_data['result'], str):
                    try:
                        task_data['result'] = json.loads(task_data['result'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['result'] = {}
                elif not isinstance(task_data['result'], dict):
                    task_data['result'] = {}
            tasks.append(TaskInDB(**task_data))
            
        return tasks
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return []


async def get_user_tasks(
    session: AsyncSession,
    user_id: str,
    skip: int = 0, 
    limit: int = 20, 
    status: Optional[TaskStatus] = None,
    task_type: Optional[str] = None
) -> List[TaskInDB]:
    """从数据库中获取指定用户的任务列表（支持分页和筛选）"""
    try:
        query = "SELECT * FROM tasks WHERE user_id = :user_id"
        params = {"user_id": user_id}
        conditions = []
        
        if status:
            conditions.append("status = :status")
            params["status"] = status.value
        
        if task_type:
            conditions.append("task_type = :task_type")
            params["task_type"] = task_type
            
        if conditions:
            query += " AND " + " AND ".join(conditions)
            
        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        params.update({"limit": limit, "skip": skip})
        
        result = await session.execute(text(query), params)
        rows = result.fetchall()
        
        tasks = []
        for row in rows:
            task_data = dict(row._mapping)
            if 'analysis_options' in task_data and task_data['analysis_options'] is not None:
                if isinstance(task_data['analysis_options'], str):
                    try:
                        task_data['analysis_options'] = json.loads(task_data['analysis_options'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['analysis_options'] = {}
                elif not isinstance(task_data['analysis_options'], dict):
                    task_data['analysis_options'] = {}
            if 'result' in task_data and task_data['result'] is not None:
                if isinstance(task_data['result'], str):
                    try:
                        task_data['result'] = json.loads(task_data['result'])
                    except (json.JSONDecodeError, TypeError):
                        task_data['result'] = {}
                elif not isinstance(task_data['result'], dict):
                    task_data['result'] = {}
            tasks.append(TaskInDB(**task_data))
            
        return tasks
    except Exception as e:
        logger.error(f"获取用户任务列表失败: {e}")
        return []


async def count_tasks(
    session: AsyncSession,
    status: Optional[TaskStatus] = None,
    task_type: Optional[str] = None
) -> int:
    """计算数据库中的任务总数（支持筛选）"""
    try:
        query = "SELECT COUNT(*) FROM tasks"
        params = {}
        conditions = []
        
        if status:
            conditions.append("status = :status")
            params["status"] = status.value
        
        if task_type:
            conditions.append("task_type = :task_type")
            params["task_type"] = task_type
            
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
            
        result = await session.execute(text(query), params)
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算任务总数失败: {e}")
        return 0


async def count_user_tasks(
    session: AsyncSession,
    user_id: str,
    status: Optional[TaskStatus] = None,
    task_type: Optional[str] = None
) -> int:
    """计算指定用户的任务总数（支持筛选）"""
    try:
        query = "SELECT COUNT(*) FROM tasks WHERE user_id = :user_id"
        params = {"user_id": user_id}
        conditions = []
        
        if status:
            conditions.append("status = :status")
            params["status"] = status.value
        
        if task_type:
            conditions.append("task_type = :task_type")
            params["task_type"] = task_type
            
        if conditions:
            query += " AND " + " AND ".join(conditions)
            
        result = await session.execute(text(query), params)
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算用户任务总数失败: {e}")
        return 0


async def count_user_documents(session: AsyncSession, user_id: str) -> int:
    """计算指定用户的文档总数"""
    try:
        # 由于文档记录关联到任务，我们从任务表中统计用户文档数量
        query = "SELECT COUNT(*) FROM tasks WHERE user_id = :user_id AND file_path IS NOT NULL"
        result = await session.execute(text(query), {"user_id": user_id})
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算用户文档总数失败: {e}")
        return 0


async def update_task(session: AsyncSession, task_id: str, task_update: TaskUpdate) -> Optional[TaskInDB]:
    """更新数据库中的任务记录"""
    try:
        # 获取非None的字段进行更新
        update_data = task_update.model_dump(exclude_unset=True)
        
        if not update_data:
            return await get_task(session, task_id)
            
        # 将字典类型的字段序列化为JSON字符串
        if 'analysis_options' in update_data and update_data['analysis_options'] is not None:
            update_data['analysis_options'] = json.dumps(update_data['analysis_options'])
        if 'result' in update_data and update_data['result'] is not None:
            # 🔥 调试：记录结果序列化
            logger.info(f"序列化任务结果 {task_id}")
            logger.info(f"原始结果类型: {type(update_data['result'])}")
            logger.info(f"原始结果大小: {len(str(update_data['result']))} 字符")
            try:
                # 🔥 修复：使用自定义序列化器处理枚举对象
                def json_serializer(obj):
                    """自定义JSON序列化器，处理枚举和其他特殊对象"""
                    from enum import Enum
                    from datetime import datetime

                    if isinstance(obj, Enum):
                        return obj.value
                    elif isinstance(obj, datetime):
                        return obj.isoformat()
                    elif hasattr(obj, 'to_dict'):
                        return obj.to_dict()
                    elif hasattr(obj, '__dict__'):
                        return obj.__dict__
                    else:
                        return str(obj)

                serialized_result = json.dumps(update_data['result'], ensure_ascii=False, default=json_serializer)
                update_data['result'] = serialized_result
                logger.info(f"结果序列化成功，JSON长度: {len(serialized_result)}")
            except Exception as json_e:
                logger.error(f"结果序列化失败: {str(json_e)}")
                logger.error(f"序列化错误详情: {type(json_e).__name__}: {json_e}")
                raise
            
        # 构建SET子句
        set_clause = ", ".join([f"{key} = :{key}" for key in update_data.keys()])
        
        query = text(f"UPDATE tasks SET {set_clause} WHERE task_id = :task_id")
        update_data['task_id'] = task_id
        
        await session.execute(query, update_data)
        await session.commit()
        
        return await get_task(session, task_id)
        
    except Exception as e:
        await session.rollback()
        logger.error(f"更新任务 {task_id} 失败: {e}")
        return None


async def delete_task(session: AsyncSession, task_id: str) -> bool:
    """删除数据库中的任务记录"""
    try:
        query = text("DELETE FROM tasks WHERE task_id = :task_id")
        result = await session.execute(query, {"task_id": task_id})
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        await session.rollback()
        logger.error(f"删除任务 {task_id} 失败: {e}")
        return False


# ==============================================================================
# 文档 (Document) 表的CRUD操作
# ==============================================================================

async def create_document(session: AsyncSession, document: DocumentCreate) -> DocumentInDB:
    """在数据库中创建一条新文档记录"""
    try:
        doc_data = document.model_dump()
        
        query = text("""
            INSERT INTO documents (
                document_id, task_id, title, author, keywords, abstract, 
                pages, words, tables, images, created_at, modified_at, analyzed_at
            ) VALUES (
                :document_id, :task_id, :title, :author, :keywords, :abstract, 
                :pages, :words, :tables, :images, :created_at, :modified_at, :analyzed_at
            )
        """)
        
        await session.execute(query, doc_data)
        await session.commit()
        
        return await get_document(session, document.document_id)
    except Exception as e:
        logger.error(f"创建文档失败: {e}")
        raise


async def get_document(session: AsyncSession, document_id: str) -> Optional[DocumentInDB]:
    """根据文档ID从数据库中获取文档记录"""
    try:
        query = text("SELECT * FROM documents WHERE document_id = :document_id")
        result = await session.execute(query, {"document_id": document_id})
        row = result.fetchone()
        
        if row:
            return DocumentInDB(**dict(row._mapping))
        return None
    except Exception as e:
        logger.error(f"获取文档 {document_id} 失败: {e}")
        return None


async def get_document_by_task_id(session: AsyncSession, task_id: str) -> Optional[DocumentInDB]:
    """根据任务ID从数据库中获取文档记录"""
    try:
        query = text("SELECT * FROM documents WHERE task_id = :task_id")
        result = await session.execute(query, {"task_id": task_id})
        row = result.fetchone()
        
        if row:
            return DocumentInDB(**dict(row._mapping))
        return None
    except Exception as e:
        logger.error(f"获取文档失败: {e}")
        return None


async def delete_document(session: AsyncSession, document_id: str) -> bool:
    """删除数据库中的文档记录"""
    try:
        query = text("DELETE FROM documents WHERE document_id = :document_id")
        result = await session.execute(query, {"document_id": document_id})
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        await session.rollback()
        logger.error(f"删除文档记录失败: {e}")
        return False


# ==============================================================================
# 用户 (User) 表的CRUD操作
# ==============================================================================

async def get_user_by_id(session: AsyncSession, user_id: str) -> Optional[UserInDB]:
    """根据ID从数据库中获取用户记录"""
    try:
        query = text("SELECT * FROM users WHERE id = :user_id")
        result = await session.execute(query, {"user_id": user_id})
        row = result.fetchone()
        
        if row:
            user_data = dict(row._mapping)
            return UserInDB(**user_data)
        return None
    except Exception as e:
        logger.error(f"获取用户失败 (ID: {user_id}): {str(e)}")
        raise e


async def get_user_by_username(session: AsyncSession, username: str) -> Optional[UserInDB]:
    """根据用户名从数据库中获取用户记录"""
    try:
        query = text("SELECT * FROM users WHERE username = :username")
        result = await session.execute(query, {"username": username})
        row = result.fetchone()
        
        if row:
            user_data = dict(row._mapping)
            return UserInDB(**user_data)
        return None
    except Exception as e:
        logger.error(f"获取用户失败 (用户名: {username}): {str(e)}")
        raise e


async def get_user_by_email(session: AsyncSession, email: str) -> Optional[UserInDB]:
    """根据邮箱从数据库中获取用户记录"""
    try:
        query = text("SELECT * FROM users WHERE email = :email")
        result = await session.execute(query, {"email": email})
        row = result.fetchone()
        
        if row:
            user_data = dict(row._mapping)
            return UserInDB(**user_data)
        return None
    except Exception as e:
        logger.error(f"获取用户失败 (邮箱: {email}): {str(e)}")
        raise e


async def create_user(session: AsyncSession, user_in: UserCreate, hashed_password: str) -> UserInDB:
    """在数据库中创建一条新用户记录"""
    try:
        # 生成用户ID
        import uuid
        user_id = f"user_{uuid.uuid4().hex}"
        
        query = text("""
            INSERT INTO users (
                id, username, email, hashed_password, full_name, is_active
            ) VALUES (
                :id, :username, :email, :hashed_password, :full_name, :is_active
            )
        """)
        
        await session.execute(query, {
            "id": user_id,
            "username": user_in.username,
            "email": user_in.email,
            "hashed_password": hashed_password,
            "full_name": user_in.full_name,
            "is_active": True
        })
        await session.commit()
        
        # 返回创建的用户
        return await get_user_by_id(session, user_id)
    except Exception as e:
        await session.rollback()
        logger.error(f"创建用户失败: {e}")
        raise


async def update_user_check_balance(session: AsyncSession, user_id: str, checks_to_add: int) -> Optional[UserInDB]:
    """
    更新用户的检测次数余额
    
    :param user_id: 用户ID
    :param checks_to_add: 要增加的检测次数
    :return: 更新后的用户信息
    """
    try:
        query = text("""
            UPDATE users
            SET check_balance = check_balance + :checks_to_add, updated_at = :now
            WHERE id = :user_id
        """)
        
        await session.execute(query, {
            "checks_to_add": checks_to_add,
            "user_id": user_id,
            "now": datetime.utcnow()
        })
        await session.commit()
        
        return await get_user_by_id(session, user_id)
    except Exception as e:
        await session.rollback()
        logger.error(f"更新用户 {user_id} 余额失败: {e}")
        return None


# ==============================================================================
# 数据库状态和统计信息
# ==============================================================================

async def get_database_stats(session: AsyncSession) -> Dict[str, Any]:
    """获取数据库统计信息"""
    try:
        stats = {}
        
        # 任务统计
        result = await session.execute(text("SELECT COUNT(*) FROM tasks"))
        stats['total_tasks'] = result.fetchone()[0]
        
        result = await session.execute(text("SELECT COUNT(*) FROM tasks WHERE status = 'completed'"))
        stats['completed_tasks'] = result.fetchone()[0]
        
        result = await session.execute(text("SELECT COUNT(*) FROM tasks WHERE status = 'failed'"))
        stats['failed_tasks'] = result.fetchone()[0]
        
        # 文档统计
        result = await session.execute(text("SELECT COUNT(*) FROM documents"))
        stats['total_documents'] = result.fetchone()[0]
        
        # 用户统计
        result = await session.execute(text("SELECT COUNT(*) FROM users"))
        stats['total_users'] = result.fetchone()[0]
        
        return stats
    except Exception as e:
        logger.error(f"获取数据库统计信息失败: {e}")
        return {}


async def count_documents(session: AsyncSession) -> int:
    """计算文档总数"""
    try:
        result = await session.execute(text("SELECT COUNT(*) FROM documents"))
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算文档总数失败: {e}")
        return 0


async def count_problems(session: AsyncSession, severity: Optional[str] = None) -> int:
    """计算问题总数（支持按严重程度筛选）"""
    try:
        base_query = "SELECT COUNT(*) FROM problems"
        params = {}
        
        if severity:
            base_query += " WHERE severity = :severity"
            params["severity"] = severity
        
        result = await session.execute(text(base_query), params)
        row = result.fetchone()
        return int(row[0]) if row and row[0] is not None else 0
    except Exception as e:
        logger.error(f"计算问题总数失败: {e}")
        return 0


async def get_average_compliance_score(session: AsyncSession) -> Optional[float]:
    """获取平均合规分数"""
    try:
        result = await session.execute(text("SELECT AVG(overall_score) FROM paper_check_results WHERE overall_score IS NOT NULL"))
        row = result.fetchone()
        return float(row[0]) if row and row[0] is not None else None
    except Exception as e:
        logger.error(f"获取平均合规分数失败: {e}")
        return None


# ==============================================================================
# 系统清理相关函数
# ==============================================================================

async def count_completed_tasks_before(session: AsyncSession, cutoff_date: datetime) -> int:
    """计算指定日期之前的已完成任务数量"""
    try:
        result = await session.execute(
            text("SELECT COUNT(*) FROM tasks WHERE status IN ('completed', 'failed', 'cancelled') AND completed_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算已完成任务数量失败: {e}")
        return 0


async def delete_completed_tasks_before(session: AsyncSession, cutoff_date: datetime) -> int:
    """删除指定日期之前的已完成任务"""
    try:
        # 先获取要删除的任务数量
        count_result = await session.execute(
            text("SELECT COUNT(*) FROM tasks WHERE status IN ('completed', 'failed', 'cancelled') AND completed_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        count = count_result.fetchone()[0]
        
        # 删除任务
        await session.execute(
            text("DELETE FROM tasks WHERE status IN ('completed', 'failed', 'cancelled') AND completed_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        await session.commit()
        
        return count
    except Exception as e:
        await session.rollback()
        logger.error(f"删除旧任务失败: {e}")
        raise e


async def count_problems_before(session: AsyncSession, cutoff_date: datetime) -> int:
    """计算指定日期之前的问题数量"""
    try:
        result = await session.execute(
            text("SELECT COUNT(*) FROM problems WHERE created_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算旧问题数量失败: {e}")
        return 0


async def delete_problems_before(session: AsyncSession, cutoff_date: datetime) -> int:
    """删除指定日期之前的问题记录"""
    try:
        # 先获取要删除的问题数量
        count_result = await session.execute(
            text("SELECT COUNT(*) FROM problems WHERE created_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        count = count_result.fetchone()[0]
        
        # 删除问题记录
        await session.execute(
            text("DELETE FROM problems WHERE created_at < :cutoff_date"),
            {"cutoff_date": cutoff_date}
        )
        await session.commit()
        
        return count
    except Exception as e:
        await session.rollback()
        logger.error(f"删除旧问题记录失败: {e}")
        raise e


async def count_tasks_before(session: AsyncSession, before_date: datetime, status: Optional[TaskStatus] = None) -> int:
    """计算指定日期之前的任务数量"""
    try:
        query = "SELECT COUNT(*) FROM tasks WHERE created_at < :before_date"
        params = {"before_date": before_date}
        
        if status:
            query += " AND status = :status"
            params["status"] = status.value
            
        result = await session.execute(text(query), params)
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算旧任务数量失败: {e}")
        return 0


async def get_recent_completed_tasks(session: AsyncSession, task_type: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
    """获取最近完成的任务（用于计算平均处理时间）"""
    try:
        query = "SELECT * FROM tasks WHERE status = 'completed' AND processing_time IS NOT NULL"
        params = {}
        
        if task_type:
            query += " AND task_type = :task_type"
            params["task_type"] = task_type
            
        query += " ORDER BY completed_at DESC LIMIT :limit"
        params["limit"] = limit
        
        result = await session.execute(text(query), params)
        rows = result.fetchall()
        
        tasks = []
        for row in rows:
            task_data = dict(row._mapping)
            if 'analysis_options' in task_data and task_data['analysis_options'] is not None:
                try:
                    task_data['analysis_options'] = json.loads(task_data['analysis_options'])
                except (json.JSONDecodeError, TypeError):
                    task_data['analysis_options'] = {}
            if 'result' in task_data and task_data['result'] is not None:
                try:
                    task_data['result'] = json.loads(task_data['result'])
                except (json.JSONDecodeError, TypeError):
                    task_data['result'] = {}
            tasks.append(TaskInDB(**task_data))
            
        return tasks
    except Exception as e:
        logger.error(f"获取最近完成的任务失败: {e}")
        return []


# ==============================================================================
# 文档和检测结果相关函数
# ==============================================================================

async def get_paper_check_result_by_document(session: AsyncSession, document_id: str) -> Optional[PaperCheckResultInDB]:
    """根据文档ID获取检测结果"""
    try:
        query = text("SELECT * FROM paper_check_results WHERE document_id = :document_id ORDER BY created_at DESC LIMIT 1")
        result = await session.execute(query, {"document_id": document_id})
        row = result.fetchone()
        
        if row:
            result_data = dict(row._mapping)
            if 'detailed_results' in result_data and result_data['detailed_results'] is not None:
                try:
                    result_data['detailed_results'] = json.loads(result_data['detailed_results'])
                except (json.JSONDecodeError, TypeError):
                    result_data['detailed_results'] = {}
            return PaperCheckResultInDB(**result_data)
        return None
    except Exception as e:
        logger.error(f"获取检测结果失败: {e}")
        return None


async def get_problems_by_result(session: AsyncSession, result_id: str, severity: Optional[str] = None, 
                                category: Optional[str] = None, skip: int = 0, 
                                limit: int = 50) -> List[ProblemInDB]:
    """根据检测结果ID获取问题列表"""
    try:
        query = "SELECT * FROM problems WHERE result_id = :result_id"
        params = {"result_id": result_id}
        conditions = []
        
        if severity:
            conditions.append("severity = :severity")
            params["severity"] = severity
            
        if category:
            conditions.append("category = :category")
            params["category"] = category
            
        if conditions:
            query += " AND " + " AND ".join(conditions)
            
        query += " ORDER BY severity DESC, id LIMIT :limit OFFSET :skip"
        params.update({"limit": limit, "skip": skip})
        
        result = await session.execute(text(query), params)
        rows = result.fetchall()
        
        problems = []
        for row in rows:
            problem_data = dict(row._mapping)
            if 'details' in problem_data and problem_data['details'] is not None:
                try:
                    problem_data['details'] = json.loads(problem_data['details'])
                except (json.JSONDecodeError, TypeError):
                    problem_data['details'] = {}
            problems.append(ProblemInDB(**problem_data))
            
        return problems
    except Exception as e:
        logger.error(f"获取问题列表失败: {e}")
        return []


async def count_tasks_since(session: AsyncSession, start_date: datetime, status: Optional[TaskStatus] = None) -> int:
    """计算指定时间以来的任务数量"""
    try:
        base_query = "SELECT COUNT(*) FROM tasks WHERE created_at >= :start_date"
        params = {"start_date": start_date}
        
        if status:
            base_query += " AND status = :status"
            params["status"] = status.value
        
        result = await session.execute(text(base_query), params)
        row = result.fetchone()
        return int(row[0]) if row and row[0] is not None else 0
    except Exception as e:
        logger.error(f"计算指定时间以来的任务数量失败: {e}")
        return 0


async def get_average_compliance_score_since(session: AsyncSession, start_date: datetime) -> Optional[float]:
    """获取指定时间以来的平均合规分数"""
    try:
        result = await session.execute(
            text("SELECT AVG(overall_score) FROM paper_check_results WHERE checked_at >= :start_date"),
            {"start_date": start_date}
        )
        row = result.fetchone()
        return float(row[0]) if row and row[0] is not None else None
    except Exception as e:
        logger.error(f"获取指定时间以来的平均合规分数失败: {e}")
        return None


async def get_top_problems_since(session: AsyncSession, start_date: datetime, limit: int = 10) -> List[Dict[str, Any]]:
    """获取指定时间以来的热门问题"""
    try:
        result = await session.execute(
            text("""
                SELECT problem_type, COUNT(*) as count, AVG(CAST(severity_level AS INTEGER)) as avg_severity
                FROM problems p
                JOIN paper_check_results pcr ON p.result_id = pcr.result_id
                WHERE pcr.checked_at >= :start_date
                GROUP BY problem_type
                ORDER BY count DESC, avg_severity DESC
                LIMIT :limit
            """),
            {"start_date": start_date, "limit": limit}
        )
        
        rows = result.fetchall()
        problems = []
        for row in rows:
            problems.append({
                "problem_type": row[0],
                "count": int(row[1]),
                "avg_severity": float(row[2]) if row[2] else 0,
                "percentage": 0  # 这里可以后续计算百分比
            })
            
        return problems
    except Exception as e:
        logger.error(f"获取指定时间以来的热门问题失败: {e}")
        return []


async def get_tasks_since(session: AsyncSession, start_date: datetime, status: Optional[TaskStatus] = None, limit: int = 100) -> List[Dict[str, Any]]:
    """获取指定时间以来的任务列表"""
    try:
        base_query = """
            SELECT task_id, status, task_type, created_at, completed_at, progress
            FROM tasks 
            WHERE created_at >= :start_date
        """
        params = {"start_date": start_date}
        
        if status:
            base_query += " AND status = :status"
            params["status"] = status.value
            
        base_query += " ORDER BY created_at DESC LIMIT :limit"
        params["limit"] = limit
        
        result = await session.execute(text(base_query), params)
        rows = result.fetchall()
        
        tasks = []
        for row in rows:
            tasks.append({
                "task_id": row[0],
                "status": row[1],
                "task_type": row[2],
                "created_at": row[3],
                "completed_at": row[4],
                "progress": row[5]
            })
            
        return tasks
    except Exception as e:
        logger.error(f"获取指定时间以来的任务列表失败: {e}")
        return []


# ==============================================================================
# 文档内容 (ContentElement) 表的CRUD操作
# ==============================================================================

async def create_content_element(session: AsyncSession, content_element: ContentElementCreate) -> ContentElementInDB:
    """在数据库中创建一条新的文档内容记录"""
    try:
        content_data = content_element.model_dump()
        
        query = text("""
            INSERT INTO content_elements (
                element_id, document_id, element_type, content, page_number, 
                created_at
            ) VALUES (
                :element_id, :document_id, :element_type, :content, :page_number, 
                :created_at
            )
        """)
        
        await session.execute(query, content_data)
        await session.commit()
        
        return await get_content_element(session, content_element.element_id)
    except Exception as e:
        logger.error(f"创建文档内容失败: {e}")
        raise


async def get_content_element(session: AsyncSession, element_id: str) -> Optional[ContentElementInDB]:
    """根据元素ID从数据库中获取文档内容记录"""
    try:
        query = text("SELECT * FROM content_elements WHERE element_id = :element_id")
        result = await session.execute(query, {"element_id": element_id})
        row = result.fetchone()
        
        if row:
            return ContentElementInDB(**dict(row._mapping))
        return None
    except Exception as e:
        logger.error(f"获取文档内容失败: {e}")
        return None


async def get_content_elements_by_document(session: AsyncSession, document_id: str) -> List[ContentElementInDB]:
    """根据文档ID从数据库中获取文档内容记录列表"""
    try:
        query = text("SELECT * FROM content_elements WHERE document_id = :document_id")
        result = await session.execute(query, {"document_id": document_id})
        rows = result.fetchall()
        
        content_elements = []
        for row in rows:
            content_elements.append(ContentElementInDB(**dict(row._mapping)))
        return content_elements
    except Exception as e:
        logger.error(f"获取文档内容列表失败: {e}")
        return []


async def update_content_element(session: AsyncSession, element_id: str, content_update: ContentElementCreate) -> Optional[ContentElementInDB]:
    """更新数据库中的文档内容记录"""
    try:
        # 获取非None的字段进行更新
        update_data = content_update.model_dump(exclude_unset=True)
        
        if not update_data:
            return await get_content_element(session, element_id)
            
        # 构建SET子句
        set_clause = ", ".join([f"{key} = :{key}" for key in update_data.keys()])
        
        query = text(f"UPDATE content_elements SET {set_clause} WHERE element_id = :element_id")
        update_data['element_id'] = element_id
        
        await session.execute(query, update_data)
        await session.commit()
        
        return await get_content_element(session, element_id)
    except Exception as e:
        logger.error(f"更新文档内容失败: {e}")
        return None


async def delete_content_element(session: AsyncSession, element_id: str) -> bool:
    """删除数据库中的文档内容记录"""
    try:
        query = text("DELETE FROM content_elements WHERE element_id = :element_id")
        result = await session.execute(query, {"element_id": element_id})
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        logger.error(f"删除文档内容失败: {e}")
        return False


# ==============================================================================
# 图片 (Images) 表的CRUD操作
# ==============================================================================

async def create_image(session: AsyncSession, image: ImageCreate) -> ImageInDB:
    """在数据库中创建一条新的图片记录"""
    try:
        image_data = image.model_dump()
        
        query = text("""
            INSERT INTO images (
                image_id, document_id, filename, filepath, file_size, format_type,
                width, height, hash_value, extracted_at, metadata, created_at
            ) VALUES (
                :image_id, :document_id, :filename, :filepath, :file_size, :format_type,
                :width, :height, :hash_value, :extracted_at, :metadata, :created_at
            )
        """)
        
        await session.execute(query, image_data)
        await session.commit()
        
        return await get_image(session, image.image_id)
    except Exception as e:
        logger.error(f"创建图片记录失败: {e}")
        raise


async def get_image(session: AsyncSession, image_id: str) -> Optional[ImageInDB]:
    """根据图片ID从数据库中获取图片记录"""
    try:
        query = text("SELECT * FROM images WHERE image_id = :image_id")
        result = await session.execute(query, {"image_id": image_id})
        row = result.fetchone()
        
        if row:
            return ImageInDB(**dict(row._mapping))
        return None
    except Exception as e:
        logger.error(f"获取图片记录失败: {e}")
        return None


async def get_images_by_document(session: AsyncSession, document_id: str) -> List[ImageInDB]:
    """根据文档ID从数据库中获取图片记录列表"""
    try:
        query = text("SELECT * FROM images WHERE document_id = :document_id ORDER BY created_at")
        result = await session.execute(query, {"document_id": document_id})
        rows = result.fetchall()
        
        images = []
        for row in rows:
            images.append(ImageInDB(**dict(row._mapping)))
        return images
    except Exception as e:
        logger.error(f"获取文档图片列表失败: {e}")
        return []


async def get_images(session: AsyncSession, skip: int = 0, limit: int = 50, filters: Optional[dict] = None) -> List[ImageInDB]:
    """获取图片列表"""
    try:
        base_query = "SELECT * FROM images"
        params = {"skip": skip, "limit": limit}
        
        # 构建筛选条件
        where_conditions = []
        if filters:
            if filters.get('format'):
                where_conditions.append("format_type = :format")
                params['format'] = filters['format']
            if filters.get('min_width'):
                where_conditions.append("width >= :min_width")
                params['min_width'] = filters['min_width']
            if filters.get('min_height'):
                where_conditions.append("height >= :min_height")
                params['min_height'] = filters['min_height']
        
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        base_query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        
        result = await session.execute(text(base_query), params)
        rows = result.fetchall()
        
        images = []
        for row in rows:
            images.append(ImageInDB(**dict(row._mapping)))
        return images
    except Exception as e:
        logger.error(f"获取图片列表失败: {e}")
        return []


async def update_image(session: AsyncSession, image_id: str, image_update: ImageCreate) -> Optional[ImageInDB]:
    """更新数据库中的图片记录"""
    try:
        # 获取非None的字段进行更新
        update_data = image_update.model_dump(exclude_unset=True)
        
        if not update_data:
            return await get_image(session, image_id)
            
        # 构建SET子句
        set_clause = ", ".join([f"{key} = :{key}" for key in update_data.keys()])
        
        query = text(f"UPDATE images SET {set_clause} WHERE image_id = :image_id")
        update_data['image_id'] = image_id
        
        await session.execute(query, update_data)
        await session.commit()
        
        return await get_image(session, image_id)
    except Exception as e:
        logger.error(f"更新图片记录失败: {e}")
        return None


async def delete_image(session: AsyncSession, image_id: str) -> bool:
    """删除数据库中的图片记录"""
    try:
        query = text("DELETE FROM images WHERE image_id = :image_id")
        result = await session.execute(query, {"image_id": image_id})
        await session.commit()
        return result.rowcount > 0
    except Exception as e:
        logger.error(f"删除图片记录失败: {e}")
        return False


async def count_images(session: AsyncSession, filters: Optional[dict] = None) -> int:
    """计算图片总数"""
    try:
        base_query = "SELECT COUNT(*) FROM images"
        params = {}
        
        # 构建筛选条件
        where_conditions = []
        if filters:
            if filters.get('format'):
                where_conditions.append("format_type = :format")
                params['format'] = filters['format']
            if filters.get('min_width'):
                where_conditions.append("width >= :min_width")
                params['min_width'] = filters['min_width']
            if filters.get('min_height'):
                where_conditions.append("height >= :min_height")
                params['min_height'] = filters['min_height']
        
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        result = await session.execute(text(base_query), params)
        row = result.fetchone()
        return int(row[0]) if row and row[0] is not None else 0
    except Exception as e:
        logger.error(f"计算图片总数失败: {e}")
        return 0


# ==============================================================================
# 文档列表 - 扩展函数
# ==============================================================================

async def get_documents(session: AsyncSession, skip: int = 0, limit: int = 20, status: Optional[str] = None) -> List[DocumentInDB]:
    """获取文档列表"""
    try:
        base_query = "SELECT * FROM documents"
        params = {"skip": skip, "limit": limit}
        
        # 注意：documents表没有status字段，状态信息在tasks表中
        # 如果需要按状态筛选，应该JOIN tasks表
        if status:
            # JOIN with tasks table to filter by status
            base_query = """
                SELECT d.* FROM documents d 
                JOIN tasks t ON d.task_id = t.task_id 
                WHERE t.status = :status
            """
            params["status"] = status
            
        base_query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        
        result = await session.execute(text(base_query), params)
        rows = result.fetchall()
        
        documents = []
        for row in rows:
            documents.append(DocumentInDB(**dict(row._mapping)))
        return documents
    except Exception as e:
        logger.error(f"获取文档列表失败: {e}")
        return []


async def get_task_by_document(session: AsyncSession, document_id: str) -> Optional[TaskInDB]:
    """根据文档ID获取关联的任务"""
    try:
        # 通过document表中的task_id字段关联
        document = await get_document(session, document_id)
        if document and document.task_id:
            return await get_task(session, document.task_id)
        return None
    except Exception as e:
        logger.error(f"获取文档关联任务失败: {e}")
        return None


async def get_documents_by_task(session: AsyncSession, task_id: str) -> List[DocumentInDB]:
    """根据任务ID获取所有关联的文档"""
    try:
        query = text("SELECT * FROM documents WHERE task_id = :task_id ORDER BY created_at")
        result = await session.execute(query, {"task_id": task_id})
        rows = result.fetchall()
        
        documents = []
        for row in rows:
            documents.append(DocumentInDB(**dict(row._mapping)))
        return documents
    except Exception as e:
        logger.error(f"获取任务关联文档失败: {e}")
        return []


# ==============================================================================
# 订单 (Orders) 表的CRUD操作  
# ==============================================================================

async def create_order(session: AsyncSession, order_data: dict) -> Optional[dict]:
    """在数据库中创建订单记录"""
    try:
        query = text("""
            INSERT INTO orders (order_id, user_id, plan_id, amount, payment_method, status, created_at)
            VALUES (:order_id, :user_id, :plan_id, :amount, :payment_method, :status, :created_at)
            RETURNING *
        """)
        
        result = await session.execute(query, order_data)
        await session.commit()
        
        row = result.fetchone()
        if row:
            return dict(row._mapping)
        return None
        
    except Exception as e:
        await session.rollback()
        logger.error(f"创建订单失败: {str(e)}")
        raise


async def get_order_by_id(session: AsyncSession, order_id: str) -> Optional[dict]:
    """根据订单ID获取订单详情"""
    try:
        query = text("SELECT * FROM orders WHERE order_id = :order_id")
        result = await session.execute(query, {"order_id": order_id})
        row = result.fetchone()
        
        if row:
            return dict(row._mapping)
        return None
    except Exception as e:
        logger.error(f"获取订单 {order_id} 失败: {str(e)}")
        return None


async def update_order_status(session: AsyncSession, order_id: str, status: str, paid_at: Optional[datetime] = None) -> Optional[dict]:
    """更新订单状态"""
    try:
        if paid_at:
            query = text("""
                UPDATE orders 
                SET status = :status, paid_at = :paid_at 
                WHERE order_id = :order_id
                RETURNING *
            """)
            params = {"order_id": order_id, "status": status, "paid_at": paid_at}
        else:
            query = text("""
                UPDATE orders 
                SET status = :status 
                WHERE order_id = :order_id
                RETURNING *
            """)
            params = {"order_id": order_id, "status": status}
        
        result = await session.execute(query, params)
        await session.commit()
        
        row = result.fetchone()
        if row:
            return dict(row._mapping)
        return None
        
    except Exception as e:
        await session.rollback()
        logger.error(f"更新订单状态失败: {str(e)}")
        raise


async def get_user_orders(session: AsyncSession, user_id: str, skip: int = 0, limit: int = 20, 
                          status: Optional[str] = None, search: Optional[str] = None, 
                          date_range: Optional[str] = None) -> List[dict]:
    """获取用户的订单历史（支持筛选）"""
    try:
        base_query = "SELECT * FROM orders WHERE user_id = :user_id"
        params = {"user_id": user_id, "limit": limit, "skip": skip}
        where_conditions = []
        
        # 状态筛选
        if status:
            where_conditions.append("status = :status")
            params["status"] = status
        
        # 搜索筛选（订单号或套餐名称）
        if search:
            where_conditions.append("(order_id ILIKE :search OR plan_id ILIKE :search)")
            params["search"] = f"%{search}%"
        
        # 日期范围筛选
        if date_range:
            if date_range == "today":
                where_conditions.append("DATE(created_at) = CURRENT_DATE")
            elif date_range == "week":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '7 days'")
            elif date_range == "month":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '30 days'")
            elif date_range == "year":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '365 days'")
        
        if where_conditions:
            base_query += " AND " + " AND ".join(where_conditions)
        
        base_query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
        
        result = await session.execute(text(base_query), params)
        rows = result.fetchall()
        
        orders = []
        for row in rows:
            orders.append(dict(row._mapping))
        return orders
        
    except Exception as e:
        logger.error(f"获取用户订单失败: {str(e)}")
        return []


async def count_user_orders(session: AsyncSession, user_id: str, status: Optional[str] = None, 
                           search: Optional[str] = None, date_range: Optional[str] = None) -> int:
    """计算用户订单总数（支持筛选）"""
    try:
        base_query = "SELECT COUNT(*) FROM orders WHERE user_id = :user_id"
        params = {"user_id": user_id}
        where_conditions = []
        
        # 状态筛选
        if status:
            where_conditions.append("status = :status")
            params["status"] = status
        
        # 搜索筛选（订单号或套餐名称）
        if search:
            where_conditions.append("(order_id ILIKE :search OR plan_id ILIKE :search)")
            params["search"] = f"%{search}%"
        
        # 日期范围筛选
        if date_range:
            if date_range == "today":
                where_conditions.append("DATE(created_at) = CURRENT_DATE")
            elif date_range == "week":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '7 days'")
            elif date_range == "month":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '30 days'")
            elif date_range == "year":
                where_conditions.append("created_at >= CURRENT_DATE - INTERVAL '365 days'")
        
        if where_conditions:
            base_query += " AND " + " AND ".join(where_conditions)
        
        result = await session.execute(text(base_query), params)
        row = result.fetchone()
        return row[0] if row else 0
    except Exception as e:
        logger.error(f"计算用户订单总数失败: {str(e)}")
        return 0


# ==============================================================================
# 文档结构统计 (Document Structure) 表的CRUD操作
# ==============================================================================

async def create_document_structure(
    session: AsyncSession,
    structure: DocumentStructureCreate
) -> DocumentStructureInDB:
    """创建文档结构统计记录"""
    try:
        structure_data = structure.model_dump()

        # 将样式信息序列化为JSON
        if 'style_info' in structure_data and structure_data['style_info'] is not None:
            structure_data['style_info'] = json.dumps(structure_data['style_info'])

        query = text("""
            INSERT INTO document_structures (
                structure_id, document_id, structure_name, structure_type, status,
                word_count, reference_count, page_number, paragraph_index,
                style_info, created_at
            ) VALUES (
                :structure_id, :document_id, :structure_name, :structure_type, :status,
                :word_count, :reference_count, :page_number, :paragraph_index,
                :style_info, :created_at
            )
        """)

        await session.execute(query, structure_data)
        await session.commit()

        return DocumentStructureInDB(**structure.model_dump())

    except Exception as e:
        await session.rollback()
        logger.error(f"创建文档结构统计失败: {str(e)}")
        raise


async def create_document_structures_batch(
    session: AsyncSession,
    structures: List[DocumentStructureCreate]
) -> List[DocumentStructureInDB]:
    """批量创建文档结构统计记录"""
    try:
        if not structures:
            return []

        # 准备批量插入的数据
        values_list = []
        for structure in structures:
            structure_data = structure.model_dump()

            # 将样式信息序列化为JSON
            if 'style_info' in structure_data and structure_data['style_info'] is not None:
                structure_data['style_info'] = json.dumps(structure_data['style_info'])

            values_list.append(structure_data)

        # 构建批量插入SQL
        query = text("""
            INSERT INTO document_structures (
                structure_id, document_id, structure_name, structure_type, status,
                word_count, reference_count, page_number, paragraph_index,
                style_info, created_at
            ) VALUES (
                :structure_id, :document_id, :structure_name, :structure_type, :status,
                :word_count, :reference_count, :page_number, :paragraph_index,
                :style_info, :created_at
            )
        """)

        # 执行批量插入
        await session.execute(query, values_list)
        await session.commit()

        return [DocumentStructureInDB(**structure.model_dump()) for structure in structures]

    except Exception as e:
        await session.rollback()
        logger.error(f"批量创建文档结构统计失败: {str(e)}")
        raise


async def get_document_structures(
    session: AsyncSession,
    document_id: str
) -> List[DocumentStructureInDB]:
    """获取文档的所有结构统计"""
    try:
        query = text("""
            SELECT * FROM document_structures
            WHERE document_id = :document_id
            ORDER BY page_number, paragraph_index
        """)

        result = await session.execute(query, {"document_id": document_id})
        rows = result.fetchall()

        structures = []
        for row in rows:
            structure_data = dict(row._mapping)

            # 反序列化样式信息
            if 'style_info' in structure_data and structure_data['style_info'] is not None:
                if isinstance(structure_data['style_info'], str):
                    try:
                        structure_data['style_info'] = json.loads(structure_data['style_info'])
                    except (json.JSONDecodeError, TypeError):
                        structure_data['style_info'] = {}
                elif not isinstance(structure_data['style_info'], dict):
                    structure_data['style_info'] = {}

            structures.append(DocumentStructureInDB(**structure_data))

        return structures

    except Exception as e:
        logger.error(f"获取文档结构统计失败: {str(e)}")
        return []


async def get_document_structure_stats(
    session: AsyncSession,
    document_id: str
) -> DocumentStructureStats:
    """获取文档结构统计汇总"""
    try:
        # 获取所有结构
        structures = await get_document_structures(session, document_id)

        # 计算统计信息
        total_structures = len(structures)
        standard_structures = len([s for s in structures if s.structure_type == "standard"])
        non_standard_structures = len([s for s in structures if s.structure_type == "non-standard"])
        missing_structures = len([s for s in structures if s.status == "missing"])
        extra_structures = len([s for s in structures if s.status == "extra"])
        total_words = sum(s.word_count for s in structures)
        total_references = sum(s.reference_count for s in structures)

        return DocumentStructureStats(
            document_id=document_id,
            total_structures=total_structures,
            standard_structures=standard_structures,
            non_standard_structures=non_standard_structures,
            missing_structures=missing_structures,
            extra_structures=extra_structures,
            total_words=total_words,
            total_references=total_references,
            structures=structures
        )

    except Exception as e:
        logger.error(f"获取文档结构统计汇总失败: {str(e)}")
        return DocumentStructureStats(document_id=document_id)


async def delete_document_structures(
    session: AsyncSession,
    document_id: str
) -> bool:
    """删除文档的所有结构统计"""
    try:
        query = text("DELETE FROM document_structures WHERE document_id = :document_id")
        await session.execute(query, {"document_id": document_id})
        await session.commit()
        return True
    except Exception as e:
        await session.rollback()
        logger.error(f"删除文档结构统计失败: {str(e)}")
        return False


# ==============================================================================
# 文档内容缓存 (Document Content Cache) 表的CRUD操作
# ==============================================================================

async def create_document_content_cache(
    session: AsyncSession,
    cache: DocumentContentCache
) -> DocumentContentCache:
    """创建文档内容缓存记录"""
    try:
        cache_data = cache.model_dump()

        query = text("""
            INSERT INTO document_content_cache (
                cache_id, document_id, content_type, file_path, file_size, created_at
            ) VALUES (
                :cache_id, :document_id, :content_type, :file_path, :file_size, :created_at
            )
            ON CONFLICT (document_id, content_type)
            DO UPDATE SET
                file_path = EXCLUDED.file_path,
                file_size = EXCLUDED.file_size,
                created_at = EXCLUDED.created_at
        """)

        await session.execute(query, cache_data)
        await session.commit()

        return cache

    except Exception as e:
        await session.rollback()
        logger.error(f"创建文档内容缓存失败: {str(e)}")
        raise


async def get_document_content_cache(
    session: AsyncSession,
    document_id: str,
    content_type: str = "full_content"
) -> Optional[DocumentContentCache]:
    """获取文档内容缓存"""
    try:
        query = text("""
            SELECT * FROM document_content_cache
            WHERE document_id = :document_id AND content_type = :content_type
        """)

        result = await session.execute(query, {
            "document_id": document_id,
            "content_type": content_type
        })
        row = result.fetchone()

        if row:
            cache_data = dict(row._mapping)
            return DocumentContentCache(**cache_data)
        return None

    except Exception as e:
        logger.error(f"获取文档内容缓存失败: {str(e)}")
        return None


async def delete_document_content_cache(
    session: AsyncSession,
    document_id: str
) -> bool:
    """删除文档内容缓存"""
    try:
        # 先获取缓存记录以删除文件
        query = text("SELECT * FROM document_content_cache WHERE document_id = :document_id")
        result = await session.execute(query, {"document_id": document_id})
        rows = result.fetchall()

        # 删除缓存文件
        import os
        for row in rows:
            cache_data = dict(row._mapping)
            file_path = cache_data.get('file_path')
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"删除缓存文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除缓存文件失败: {file_path} - {str(e)}")

        # 删除数据库记录
        delete_query = text("DELETE FROM document_content_cache WHERE document_id = :document_id")
        await session.execute(delete_query, {"document_id": document_id})
        await session.commit()
        return True

    except Exception as e:
        await session.rollback()
        logger.error(f"删除文档内容缓存失败: {str(e)}")
        return False


def save_document_content_to_file(content_data: Dict[str, Any], file_path: str) -> bool:
    """将文档内容保存到文件"""
    try:
        import os
        import json

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存为JSON格式
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(content_data, f, ensure_ascii=False, indent=2)

        logger.info(f"文档内容已保存到: {file_path}")
        return True
    except Exception as e:
        logger.error(f"保存文档内容到文件失败: {str(e)}")
        return False


def load_document_content_from_file(file_path: str) -> Optional[Dict[str, Any]]:
    """从文件加载文档内容"""
    try:
        import os
        import json

        if not os.path.exists(file_path):
            logger.warning(f"缓存文件不存在: {file_path}")
            return None

        with open(file_path, 'r', encoding='utf-8') as f:
            content_data = json.load(f)

        logger.info(f"从文件加载文档内容: {file_path}")
        return content_data
    except Exception as e:
        logger.error(f"从文件加载文档内容失败: {str(e)}")
        return None