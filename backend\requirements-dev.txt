# ==================================================
# Word文档分析服务 - 开发环境依赖
# ==================================================

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0  # 并行测试
httpx==0.25.2

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5  # 安全检查
safety==2.3.5  # 依赖安全检查

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
mkdocs-swagger-ui-tag==0.6.6

# 调试工具
ipdb==0.13.13
pdbpp==0.10.3
rich==13.7.0  # 美化输出

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0
line-profiler==4.1.1

# 开发服务器
watchdog==3.0.0  # 文件监控

# API测试
requests==2.31.0
faker==20.1.0  # 测试数据生成

# 预提交钩子
pre-commit==3.6.0

# 类型检查增强
types-redis==********
types-PyYAML==*********
types-requests==********* 