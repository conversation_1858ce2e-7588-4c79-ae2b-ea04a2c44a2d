#!/usr/bin/env python3
"""
测试关键词数量检查功能的完整流程
"""

import requests
import json

def test_keywords_complete_flow():
    """测试关键词检查的完整流程"""
    
    print("🔍 测试关键词数量检查完整流程")
    print("=" * 60)
    
    try:
        # 1. 测试API规则配置
        print("1️⃣ 测试API规则配置...")
        response = requests.get('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024')
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
        api_data = response.json()['data']
        content_rules = api_data.get('rules', {}).get('content', {})
        
        # 检查关键词规则
        chinese_keywords_rule = content_rules.get('chinese_keywords_count')
        english_keywords_rule = content_rules.get('english_keywords_count')
        
        if not chinese_keywords_rule:
            print("❌ 中文关键词规则缺失！")
            return False
            
        if not english_keywords_rule:
            print("❌ 英文关键词规则缺失！")
            return False
            
        print("✅ API规则配置正确")
        print(f"   中文关键词: {chinese_keywords_rule['parameters']['min']}-{chinese_keywords_rule['parameters']['max']}{chinese_keywords_rule['parameters']['unit']}")
        print(f"   英文关键词: {english_keywords_rule['parameters']['min']}-{english_keywords_rule['parameters']['max']}{english_keywords_rule['parameters']['unit']}")
        
        # 2. 模拟前端解析逻辑
        print("\n2️⃣ 模拟前端解析逻辑...")
        
        requirements = {}
        rule_mapping = {
            'chinese_abstract_word_count': '中文摘要',
            'english_abstract_word_count': '英文摘要',
            'chinese_keywords_count': '中文关键词',
            'english_keywords_count': '英文关键词',
            'references_item_count': '参考文献'
        }
        
        for rule_key, rule_config in content_rules.items():
            if rule_key in rule_mapping:
                structure_name = rule_mapping[rule_key]
                params = rule_config.get('parameters', {})
                
                requirements[structure_name] = {
                    'min': params.get('min'),
                    'max': params.get('max'),
                    'unit': params.get('unit'),
                    'rule_name': rule_config.get('name')
                }
        
        print("✅ 前端解析成功")
        for name, req in requirements.items():
            if req['min'] and req['max']:
                requirement_text = f"{req['min']}-{req['max']}{req['unit']}"
            elif req['min']:
                requirement_text = f"≥{req['min']}{req['unit']}"
            else:
                requirement_text = "-"
            print(f"   {name}: {requirement_text}")
        
        # 3. 模拟文档分析数据
        print("\n3️⃣ 模拟文档分析数据...")
        
        # 模拟从后端获取的文档结构数据
        mock_structures = [
            {'name': '中文摘要', 'content': {'word_count': 355}, 'count': '355字'},
            {'name': '英文摘要', 'content': {'word_count': 194}, 'count': '194词'},
            {'name': '中文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '英文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '参考文献', 'content': {'word_count': 14}, 'count': '中文11条外文3条'}
        ]
        
        print("✅ 模拟数据准备完成")
        for structure in mock_structures:
            print(f"   {structure['name']}: {structure['count']}")
        
        # 4. 生成字数分析数据
        print("\n4️⃣ 生成字数分析数据...")
        
        analysis_results = []
        
        for structure in mock_structures:
            name = structure['name']
            current_count = structure['content']['word_count']
            count_display = structure['count']
            
            # 获取标准要求
            requirement = requirements.get(name)
            
            if requirement:
                min_val = requirement['min']
                max_val = requirement['max']
                unit = requirement['unit']
                
                # 生成标准要求显示
                if min_val and max_val:
                    standard_req = f"{min_val}-{max_val}{unit}"
                elif min_val:
                    standard_req = f"≥{min_val}{unit}"
                else:
                    standard_req = "-"
                
                # 分析结果
                if min_val and max_val:
                    if min_val <= current_count <= max_val:
                        result = "✅ 达标"
                    elif current_count < min_val:
                        result = "⚠️ 不足"
                    else:
                        result = "⚠️ 过多"
                elif min_val:
                    result = "✅ 达标" if current_count >= min_val else "⚠️ 不足"
                else:
                    result = "无要求"
            else:
                standard_req = "-"
                result = "无要求"
            
            analysis_results.append({
                'structure': name,
                'standard_requirement': standard_req,
                'current_situation': count_display,
                'analysis_result': result
            })
        
        # 5. 显示完整的字数分析结果
        print("\n5️⃣ 字数分析结果表格:")
        print("-" * 80)
        print(f"{'结构名称':<12} {'标准要求':<15} {'当前情况':<15} {'分析结果':<10}")
        print("-" * 80)
        
        for result in analysis_results:
            print(f"{result['structure']:<12} {result['standard_requirement']:<15} {result['current_situation']:<15} {result['analysis_result']:<10}")
        
        # 6. 验证关键词分析结果
        print("\n6️⃣ 验证关键词分析结果...")
        
        keywords_results = [r for r in analysis_results if '关键词' in r['structure']]
        
        if len(keywords_results) != 2:
            print(f"❌ 关键词结果数量错误，期望2个，实际{len(keywords_results)}个")
            return False
        
        for result in keywords_results:
            name = result['structure']
            standard = result['standard_requirement']
            current = result['current_situation']
            analysis = result['analysis_result']
            
            print(f"✅ {name}:")
            print(f"   标准要求: {standard}")
            print(f"   当前情况: {current}")
            print(f"   分析结果: {analysis}")
            
            # 验证具体数值
            if name == '中文关键词':
                if standard == '3-5个' and current == '5个' and analysis == '✅ 达标':
                    print(f"   ✅ {name}分析正确")
                else:
                    print(f"   ❌ {name}分析错误")
                    return False
            elif name == '英文关键词':
                if standard == '3-5个' and current == '5个' and analysis == '✅ 达标':
                    print(f"   ✅ {name}分析正确")
                else:
                    print(f"   ❌ {name}分析错误")
                    return False
        
        print("\n🎉 所有测试通过！关键词数量检查功能完整正常！")
        
        # 7. 总结
        print("\n📋 功能总结:")
        print("✅ 后端规则文件：已添加中文关键词和英文关键词的3-5个标准要求")
        print("✅ 后端API接口：正确返回关键词检查规则配置")
        print("✅ 前端解析逻辑：能够正确解析关键词规则映射")
        print("✅ 字数分析功能：正确显示关键词的标准要求和分析结果")
        print("✅ 统一数据源：前后端都从同一个规则文件读取配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_keywords_complete_flow()
    exit(0 if success else 1)
