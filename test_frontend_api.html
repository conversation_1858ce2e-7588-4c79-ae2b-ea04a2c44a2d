<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API规则配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端API规则配置测试</h1>
        
        <div class="test-section">
            <div class="test-title">🔗 API连接测试</div>
            <div id="api-test">
                <div class="result loading">正在测试API连接...</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 检测标准配置</div>
            <div id="standard-config"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 字数要求解析</div>
            <div id="requirements-parsing"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 字数分析功能测试</div>
            <div id="analysis-test"></div>
        </div>
    </div>

    <script>
        // 模拟前端API服务
        class ApiService {
            async get(url) {
                const response = await fetch(`http://localhost:8000/api${url}`)
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                const data = await response.json()
                return data.data // 返回data字段的内容
            }
        }

        const apiService = new ApiService()

        // 存储检测标准配置
        let detectionStandard = null

        // 从API获取字数要求配置
        const getWordRequirementsFromStandard = () => {
            const requirements = {}

            // 如果还没有加载检测标准配置，返回空对象
            if (!detectionStandard?.rules?.content) {
                return requirements
            }

            const contentRules = detectionStandard.rules.content

            // 从API获取的规则中解析字数要求
            Object.entries(contentRules).forEach(([ruleKey, rule]) => {
                const params = rule.parameters
                if (params && params.unit) {
                    // 根据规则名称映射到结构名称
                    let structureName = ''
                    if (ruleKey === 'chinese_abstract_word_count') {
                        structureName = '中文摘要'
                    } else if (ruleKey === 'english_abstract_word_count') {
                        structureName = '英文摘要'
                    } else if (ruleKey === 'references_item_count') {
                        structureName = '参考文献'
                    }

                    if (structureName) {
                        requirements[structureName] = {
                            min: params.min,
                            max: params.max,
                            unit: params.unit
                        }
                    }
                }
            })

            return requirements
        }

        // 模拟文档结构数据
        const mockDocumentStructures = [
            {
                name: '中文摘要',
                content: { word_count: 355 },
                count: '355字'
            },
            {
                name: '英文摘要',
                content: { word_count: 194 },
                count: '194词'
            },
            {
                name: '参考文献',
                content: { word_count: 15 },
                count: '中文11条外文3条'
            }
        ]

        // 生成字数分析数据
        const generateWordAnalysisData = (structures) => {
            const requirements = getWordRequirementsFromStandard()
            const data = []

            structures.forEach(structure => {
                const name = structure.name
                const currentCount = structure.content.word_count || 0
                const countDisplay = structure.count || `${currentCount}字`
                
                // 获取标准要求
                const requirement = requirements[name]
                let standardRequirement = '-'
                let analysisResult = '无要求'
                
                if (requirement) {
                    if (requirement.min && requirement.max) {
                        standardRequirement = `${requirement.min}-${requirement.max}${requirement.unit}`
                    } else if (requirement.min) {
                        standardRequirement = `≥${requirement.min}${requirement.unit}`
                    }
                    
                    // 分析是否符合要求
                    if (requirement.min && requirement.max) {
                        if (currentCount >= requirement.min && currentCount <= requirement.max) {
                            analysisResult = '✅ 达标'
                        } else if (currentCount < requirement.min) {
                            analysisResult = '⚠️ 不足'
                        } else {
                            analysisResult = '⚠️ 过多'
                        }
                    } else if (requirement.min) {
                        analysisResult = currentCount >= requirement.min ? '✅ 达标' : '⚠️ 不足'
                    }
                }

                data.push({
                    structure: name,
                    standardRequirement,
                    currentSituation: countDisplay,
                    analysisResult
                })
            })

            return data
        }

        // 执行测试
        async function runTests() {
            try {
                // 测试1: API连接
                document.getElementById('api-test').innerHTML = '<div class="result loading">正在连接API...</div>'
                
                detectionStandard = await apiService.get('/v1/system/detection-standards/hbkj_bachelor_2024')
                
                document.getElementById('api-test').innerHTML = '<div class="result success">✅ API连接成功！</div>'
                
                // 测试2: 显示检测标准配置
                const metadata = detectionStandard.metadata
                let configHtml = `
                    <div class="result info">
                        📋 标准名称: ${metadata.name}<br>
                        📝 版本: ${metadata.version}<br>
                        📄 描述: ${metadata.description}
                    </div>
                `
                
                // 显示原始JSON数据
                configHtml += '<div class="json-display">' + JSON.stringify(detectionStandard, null, 2) + '</div>'
                document.getElementById('standard-config').innerHTML = configHtml
                
                // 测试3: 字数要求解析
                const requirements = getWordRequirementsFromStandard()
                let requirementsHtml = '<table><tr><th>结构</th><th>最小值</th><th>最大值</th><th>单位</th></tr>'
                
                Object.entries(requirements).forEach(([name, req]) => {
                    requirementsHtml += `<tr><td>${name}</td><td>${req.min || '-'}</td><td>${req.max || '-'}</td><td>${req.unit}</td></tr>`
                })
                requirementsHtml += '</table>'
                
                // 检查英文摘要是否包含在要求中
                const hasEnglishAbstract = requirements['英文摘要']
                if (hasEnglishAbstract) {
                    requirementsHtml += '<div class="result success">✅ 英文摘要标准要求已正确从API获取！</div>'
                    requirementsHtml += `<div class="result info">📋 英文摘要要求: ${hasEnglishAbstract.min}-${hasEnglishAbstract.max}${hasEnglishAbstract.unit}</div>`
                } else {
                    requirementsHtml += '<div class="result error">❌ 英文摘要标准要求解析失败！</div>'
                }
                
                document.getElementById('requirements-parsing').innerHTML = requirementsHtml
                
                // 测试4: 字数分析功能
                const analysisData = generateWordAnalysisData(mockDocumentStructures)
                let analysisHtml = '<table><tr><th>结构名称</th><th>标准要求</th><th>当前情况</th><th>分析结果</th></tr>'
                
                analysisData.forEach(item => {
                    analysisHtml += `<tr><td>${item.structure}</td><td>${item.standardRequirement}</td><td>${item.currentSituation}</td><td>${item.analysisResult}</td></tr>`
                })
                analysisHtml += '</table>'
                
                // 检查英文摘要的分析结果
                const englishAbstractData = analysisData.find(item => item.structure === '英文摘要')
                if (englishAbstractData && englishAbstractData.standardRequirement !== '-') {
                    analysisHtml += '<div class="result success">✅ 英文摘要字数分析功能正常！</div>'
                    analysisHtml += `<div class="result info">📊 英文摘要分析: 标准要求${englishAbstractData.standardRequirement}，当前${englishAbstractData.currentSituation}，结果${englishAbstractData.analysisResult}</div>`
                } else {
                    analysisHtml += '<div class="result error">❌ 英文摘要字数分析功能异常！</div>'
                }
                
                document.getElementById('analysis-test').innerHTML = analysisHtml
                
            } catch (error) {
                console.error('测试失败:', error)
                document.getElementById('api-test').innerHTML = `<div class="result error">❌ 测试失败: ${error.message}</div>`
            }
        }

        // 页面加载完成后执行测试
        document.addEventListener('DOMContentLoaded', runTests)
    </script>
</body>
</html>
