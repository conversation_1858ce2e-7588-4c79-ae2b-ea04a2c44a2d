import requests
import json

try:
    # 测试检测标准API
    response = requests.get('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024')
    
    if response.status_code == 200:
        data = response.json()

        print('✅ API响应成功')
        print('📋 响应数据结构:', json.dumps(data, indent=2, ensure_ascii=False)[:500] + '...')

        # 检查数据结构
        if 'data' in data:
            detection_data = data['data']
            metadata = detection_data.get('metadata', {})
            print('📋 检测标准:', metadata.get('name', '未知'))
            print('📝 版本:', metadata.get('version', '未知'))
        else:
            print('❌ 响应数据格式异常，缺少data字段')
        
        # 检查规则配置
        if 'data' in data:
            detection_data = data['data']
            rules = detection_data.get('rules', {})
            content_rules = rules.get('content', {})
        else:
            content_rules = {}
        
        print('\n=== 内容检查规则 ===')
        for rule_key, rule_config in content_rules.items():
            name = rule_config.get('name', rule_key)
            params = rule_config.get('parameters', {})
            min_val = params.get('min')
            max_val = params.get('max')
            unit = params.get('unit', '')
            
            requirement = ''
            if min_val and max_val:
                requirement = f'{min_val}-{max_val}{unit}'
            elif min_val:
                requirement = f'≥{min_val}{unit}'
            elif max_val:
                requirement = f'≤{max_val}{unit}'
            
            print(f'📊 {name}: {requirement}')
        
        # 特别检查英文摘要规则
        english_rule = content_rules.get('english_abstract_word_count')
        if english_rule:
            print('\n✅ 英文摘要规则配置正确！')
            params = english_rule['parameters']
            print(f'   要求: {params["min"]}-{params["max"]}{params["unit"]}')
        else:
            print('\n❌ 英文摘要规则缺失！')
            
    else:
        print('❌ API请求失败:', response.status_code)
        print(response.text)
        
except Exception as e:
    print('❌ 测试失败:', str(e))
