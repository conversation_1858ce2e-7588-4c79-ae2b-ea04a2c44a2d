# ==================================================
# Word文档分析服务 - 性能优化配置
# ==================================================

# 数据库性能优化
database:
  # 连接池优化
  pool_size: 30                    # 增加连接池大小
  max_overflow: 50                 # 增加溢出连接数
  pool_timeout: 60                 # 增加连接超时时间
  pool_recycle: 1800               # 减少连接回收时间
  pool_pre_ping: true              # 启用连接预检
  
  # 查询优化
  echo: false                      # 生产环境禁用SQL回显
  query_cache_size: 1000           # 查询缓存大小
  statement_timeout: 30            # SQL语句超时时间
  
  # 事务优化
  autocommit: false                # 禁用自动提交
  isolation_level: "READ_COMMITTED" # 设置隔离级别

# Redis性能优化
redis:
  # 连接池优化
  max_connections: 50              # 增加最大连接数
  min_connections: 10              # 设置最小连接数
  connection_pool_size: 30         # 连接池大小
  timeout: 10                      # 增加超时时间
  socket_timeout: 5                # Socket超时
  socket_connect_timeout: 5        # 连接超时
  
  # 性能优化
  retry_on_timeout: true           # 超时重试
  health_check_interval: 30        # 健康检查间隔
  max_idle_time: 300               # 最大空闲时间
  
  # 内存优化
  max_memory_policy: "allkeys-lru" # 内存淘汰策略
  compression: true                # 启用压缩
  
# 任务处理性能优化
tasks:
  # 并发优化
  max_concurrent: 20               # 增加最大并发任务数
  worker_count: 8                  # 增加工作进程数
  batch_size: 50                   # 增加批处理大小
  
  # 超时优化
  timeout: 900                     # 增加任务超时时间(15分钟)
  heartbeat_interval: 30           # 心跳间隔
  result_expires: 3600             # 结果过期时间
  
  # 队列优化
  queue_max_size: 1000             # 队列最大大小
  prefetch_count: 10               # 预取数量
  ack_late: true                   # 延迟确认
  
  # 重试优化
  retry_attempts: 5                # 增加重试次数
  retry_delay: 15                  # 增加重试延迟
  retry_backoff: true              # 启用指数退避
  max_retry_delay: 300             # 最大重试延迟

# Word COM接口性能优化
word_com:
  # 实例池优化
  pool_size: 8                     # 增加实例池大小
  min_pool_size: 2                 # 最小池大小
  max_pool_size: 12                # 最大池大小
  pool_timeout: 120                # 池获取超时
  
  # 操作优化
  startup_timeout: 90              # 增加启动超时
  operation_timeout: 600           # 增加操作超时(10分钟)
  restart_interval: 30             # 减少重启间隔
  
  # 内存优化
  memory_limit: 512                # 内存限制(MB)
  gc_interval: 100                 # 垃圾回收间隔
  cleanup_on_exit: true            # 退出时清理
  
  # 性能设置
  visible: false                   # 禁用可见性
  display_alerts: false            # 禁用警告
  enable_events: false             # 禁用事件
  screen_updating: false           # 禁用屏幕更新
  calculation: "manual"            # 手动计算模式

# 文件处理性能优化
files:
  # 上传优化
  max_size: 104857600              # 增加到100MB
  chunk_size: 8192                 # 文件块大小
  buffer_size: 65536               # 缓冲区大小
  
  # 并发优化
  max_concurrent_uploads: 10       # 最大并发上传数
  upload_timeout: 600              # 上传超时时间
  
  # 存储优化
  temp_retention_hours: 6          # 减少临时文件保留时间
  compression_enabled: true        # 启用文件压缩
  compression_level: 6             # 压缩级别
  
  # 图片优化
  image_quality: 85                # 降低图片质量以节省空间
  image_max_width: 1920            # 图片最大宽度
  image_max_height: 1080           # 图片最大高度
  thumbnail_size: 200              # 缩略图大小

# 缓存性能优化
cache:
  # 内存缓存
  default_ttl: 3600                # 默认TTL(1小时)
  max_entries: 100000              # 最大缓存条目数
  memory_limit: 512                # 内存限制(MB)
  
  # 分层缓存
  l1_cache_size: 1000              # L1缓存大小
  l2_cache_size: 10000             # L2缓存大小
  
  # 特定缓存TTL
  document_analysis_ttl: 7200      # 文档分析缓存(2小时)
  user_session_ttl: 1800           # 用户会话缓存(30分钟)
  system_stats_ttl: 300            # 系统统计缓存(5分钟)
  api_response_ttl: 600            # API响应缓存(10分钟)
  
  # 缓存策略
  eviction_policy: "lru"           # LRU淘汰策略
  write_through: false             # 禁用写穿透
  write_behind: true               # 启用写回
  
# 网络性能优化
network:
  # 连接优化
  keepalive_timeout: 75            # Keep-Alive超时
  max_keepalive_requests: 1000     # 最大Keep-Alive请求数
  
  # 缓冲优化
  send_buffer_size: 65536          # 发送缓冲区大小
  receive_buffer_size: 65536       # 接收缓冲区大小
  
  # 压缩优化
  compression_enabled: true        # 启用压缩
  compression_level: 6             # 压缩级别
  compression_min_size: 1024       # 最小压缩大小
  
# 日志性能优化
logging:
  # 异步日志
  async_logging: true              # 启用异步日志
  buffer_size: 8192                # 日志缓冲区大小
  flush_interval: 5                # 刷新间隔(秒)
  
  # 日志轮转
  max_size: 104857600              # 增加到100MB
  backup_count: 20                 # 增加备份数量
  compression: true                # 启用日志压缩
  
  # 性能日志
  performance_logging: true        # 启用性能日志
  slow_query_threshold: 1.0        # 慢查询阈值(秒)
  request_logging: true            # 启用请求日志

# 监控性能优化
monitoring:
  # 指标收集
  metrics_interval: 30             # 指标收集间隔
  detailed_metrics: true           # 详细指标
  
  # 性能监控
  cpu_threshold: 80                # CPU使用率阈值
  memory_threshold: 85             # 内存使用率阈值
  disk_threshold: 90               # 磁盘使用率阈值
  
  # 响应时间监控
  response_time_threshold: 2.0     # 响应时间阈值(秒)
  error_rate_threshold: 0.05       # 错误率阈值(5%)
  
# 系统资源优化
system:
  # 进程优化
  max_workers: 8                   # 最大工作进程数
  worker_memory_limit: 1024        # 工作进程内存限制(MB)
  
  # 文件描述符
  max_open_files: 65536            # 最大打开文件数
  
  # 内存优化
  gc_threshold: 700                # 垃圾回收阈值
  memory_pool_size: 256            # 内存池大小(MB)
  
# 安全性能平衡
security:
  # 加密优化
  bcrypt_rounds: 12                # 平衡安全性和性能
  jwt_algorithm: "HS256"           # 高效的JWT算法
  
  # 会话优化
  session_timeout: 3600            # 会话超时(1小时)
  max_sessions_per_user: 5         # 每用户最大会话数
