# Word文档分析服务 - Windows云服务器原生部署指南

## 概述

本文档提供在Windows云服务器上原生部署Word文档分析服务的详细步骤。原生部署意味着所有组件都直接安装在Windows系统上，不使用Docker容器。

### 部署架构
- **操作系统**: Windows Server 2019/2022 或 Windows 10/11
- **数据库**: PostgreSQL 17.5 原生安装
- **缓存**: Redis 7.x 原生安装  
- **Python环境**: Python 3.12
- **Word组件**: Microsoft Word (COM接口)
- **Web服务器**: Nginx (可选)

### 服务端口分配
- PostgreSQL: 5432
- Redis: 6379
- FastAPI应用: 8000
- Nginx: 80/443 (如使用)

## 第一部分：系统环境准备

### 1.1 Windows系统配置

#### 1.1.1 系统更新
```powershell
# 检查Windows更新
Get-WindowsUpdate

# 安装所有可用更新
Install-WindowsUpdate -AcceptAll -AutoReboot
```

#### 1.1.2 启用必要的Windows功能
```powershell
# 启用IIS（如果需要）
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole

# 启用.NET Framework 3.5
Enable-WindowsOptionalFeature -Online -FeatureName NetFx3

# 启用Windows子系统（WSL，可选）
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux
```

#### 1.1.3 防火墙配置
```powershell
# 创建入站规则允许PostgreSQL
New-NetFirewallRule -DisplayName "PostgreSQL" -Direction Inbound -Protocol TCP -LocalPort 5432 -Action Allow

# 创建入站规则允许Redis
New-NetFirewallRule -DisplayName "Redis" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow

# 创建入站规则允许FastAPI
New-NetFirewallRule -DisplayName "FastAPI" -Direction Inbound -Protocol TCP -LocalPort 8000 -Action Allow

# 创建入站规则允许HTTP/HTTPS
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

### 1.2 创建服务目录结构
```powershell
# 创建应用根目录
New-Item -ItemType Directory -Path "C:\Services" -Force
New-Item -ItemType Directory -Path "C:\Services\WordService" -Force
New-Item -ItemType Directory -Path "C:\Services\PostgreSQL" -Force
New-Item -ItemType Directory -Path "C:\Services\Redis" -Force
New-Item -ItemType Directory -Path "C:\Services\Logs" -Force
New-Item -ItemType Directory -Path "C:\Services\Data" -Force
```

## 第二部分：PostgreSQL数据库安装

### 2.1 下载和安装PostgreSQL

#### 2.1.1 下载PostgreSQL 17.5
```powershell
# 下载PostgreSQL安装包
$url = "https://get.enterprisedb.com/postgresql/postgresql-17.5-1-windows-x64.exe"
$output = "C:\temp\postgresql-17.5-1-windows-x64.exe"
Invoke-WebRequest -Uri $url -OutFile $output
```

#### 2.1.2 静默安装PostgreSQL
```powershell
# 静默安装（修改密码为你的密码）
$installArgs = @(
    "--mode", "unattended",
    "--unattendedmodeui", "none",
    "--superpassword", "your_secure_password",
    "--enable-components", "server,pgAdmin,stackbuilder,commandlinetools",
    "--datadir", "C:\Services\PostgreSQL\data",
    "--servicename", "postgresql-x64-17",
    "--serviceaccount", "NetworkService",
    "--servicepassword", "",
    "--serverport", "5432"
)

Start-Process -FilePath "C:\temp\postgresql-17.5-1-windows-x64.exe" -ArgumentList $installArgs -Wait
```

#### 2.1.3 配置PostgreSQL环境变量
```powershell
# 添加PostgreSQL到系统PATH
$pgPath = "C:\Program Files\PostgreSQL\17\bin"
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
if ($currentPath -notlike "*$pgPath*") {
    [Environment]::SetEnvironmentVariable("Path", "$currentPath;$pgPath", "Machine")
}

# 重新加载环境变量
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine")
```

### 2.2 配置PostgreSQL

#### 2.2.1 修改postgresql.conf配置
```powershell
# 编辑postgresql.conf
$pgConfigPath = "C:\Services\PostgreSQL\data\postgresql.conf"

# 备份原配置文件
Copy-Item $pgConfigPath "$pgConfigPath.backup"

# 修改配置
$configContent = @"
# 连接设置
listen_addresses = '*'
port = 5432
max_connections = 200

# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB

# 日志设置
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 10MB
log_min_duration_statement = 1000
log_statement = 'all'

# 其他设置
timezone = 'Asia/Shanghai'
datestyle = 'iso, mdy'
default_text_search_config = 'pg_catalog.english'
"@

Add-Content -Path $pgConfigPath -Value $configContent
```

#### 2.2.2 配置pg_hba.conf访问权限
```powershell
$pgHbaPath = "C:\Services\PostgreSQL\data\pg_hba.conf"

# 备份原文件
Copy-Item $pgHbaPath "$pgHbaPath.backup"

# 添加访问规则
$hbaContent = @"
# 本地连接
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# 允许局域网连接（根据需要调整IP范围）
host    all             all             10.0.0.0/8              md5
host    all             all             **********/12           md5
host    all             all             ***********/16          md5
"@

Set-Content -Path $pgHbaPath -Value $hbaContent
```

#### 2.2.3 重启PostgreSQL服务
```powershell
# 重启PostgreSQL服务
Restart-Service postgresql-x64-17

# 验证服务状态
Get-Service postgresql-x64-17
```

### 2.3 创建数据库和用户

#### 2.3.1 连接数据库并创建用户
```powershell
# 使用psql创建数据库和用户
$env:PGPASSWORD = "your_secure_password"

# 创建数据库用户
psql -U postgres -h localhost -c "CREATE USER word_service_user WITH ENCRYPTED PASSWORD 'word_service_password';"

# 创建数据库
psql -U postgres -h localhost -c "CREATE DATABASE word_service OWNER word_service_user;"

# 授予权限
psql -U postgres -h localhost -c "GRANT ALL PRIVILEGES ON DATABASE word_service TO word_service_user;"

# 验证连接
psql -U word_service_user -h localhost -d word_service -c "SELECT version();"
```

## 第三部分：Redis缓存安装

### 3.1 下载和安装Redis

#### 3.1.1 下载Redis for Windows
```powershell
# 下载Redis-x64-********.msi (或最新版本)
$redisUrl = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.msi"
$redisOutput = "C:\temp\Redis-x64-3.0.504.msi"
Invoke-WebRequest -Uri $redisUrl -OutFile $redisOutput

# 安装Redis
Start-Process msiexec.exe -ArgumentList "/i", "C:\temp\Redis-x64-3.0.504.msi", "/quiet" -Wait
```

#### 3.1.2 配置Redis
```powershell
# Redis配置文件路径
$redisConfigPath = "C:\Program Files\Redis\redis.windows.conf"

# 备份原配置
Copy-Item $redisConfigPath "$redisConfigPath.backup"

# 修改配置
$redisConfig = @"
# 网络配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 60

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
dir C:\Services\Redis\

# 日志配置
loglevel notice
logfile C:\Services\Logs\redis.log

# 安全配置
requirepass redis_password_here
"@

Set-Content -Path $redisConfigPath -Value $redisConfig
```

#### 3.1.3 启动Redis服务
```powershell
# 停止现有Redis服务
Stop-Service Redis -ErrorAction SilentlyContinue

# 重新启动Redis服务
Start-Service Redis

# 验证Redis服务
Get-Service Redis

# 测试Redis连接
redis-cli -h localhost -p 6379 -a redis_password_here ping
```

## 第四部分：Python环境安装

### 4.1 安装Python 3.12

#### 4.1.1 下载并安装Python
```powershell
# 下载Python 3.12
$pythonUrl = "https://www.python.org/ftp/python/3.12.0/python-3.12.0-amd64.exe"
$pythonOutput = "C:\temp\python-3.12.0-amd64.exe"
Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonOutput

# 静默安装Python
$pythonArgs = @(
    "/quiet",
    "InstallAllUsers=1",
    "PrependPath=1",
    "Include_test=0",
    "Include_doc=0",
    "Include_pip=1",
    "Include_launcher=1"
)

Start-Process -FilePath $pythonOutput -ArgumentList $pythonArgs -Wait
```

#### 4.1.2 验证Python安装
```powershell
# 重新加载PATH
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine")

# 验证Python版本
python --version
pip --version
```

#### 4.1.3 升级pip和安装基础包
```powershell
# 升级pip
python -m pip install --upgrade pip

# 安装virtualenv
pip install virtualenv
```

### 4.2 创建虚拟环境

#### 4.2.1 创建虚拟环境
```powershell
# 切换到服务目录
cd C:\Services\WordService

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 升级虚拟环境中的pip
python -m pip install --upgrade pip
```

## 第五部分：Microsoft Word安装

### 5.1 安装Microsoft Word

#### 5.1.1 安装Office套件
由于Word COM接口需要，必须安装完整的Microsoft Office或至少Word组件。

**选项1：Office 365/2021**
- 从Microsoft官网下载Office安装包
- 选择包含Word的许可证
- 按标准流程安装

**选项2：Volume License版本**
- 适用于企业环境
- 支持批量部署和管理

#### 5.1.2 验证Word COM接口
```powershell
# 验证Word COM接口可用性
$word = New-Object -ComObject Word.Application
$word.Visible = $false
$doc = $word.Documents.Add()
$doc.Content.Text = "Test Document"
$doc.Close()
$word.Quit()
Write-Host "Word COM interface is working correctly"
```

#### 5.1.3 配置Word安全设置
```powershell
# 注册表路径（根据Office版本调整）
$regPath = "HKCU:\Software\Microsoft\Office\16.0\Word\Security"

# 创建注册表项（如果不存在）
if (!(Test-Path $regPath)) {
    New-Item -Path $regPath -Force
}

# 设置宏安全级别（允许程序化访问）
Set-ItemProperty -Path $regPath -Name "VBAWarnings" -Value 1
Set-ItemProperty -Path $regPath -Name "AccessVBOM" -Value 1
```

## 第六部分：应用部署

### 6.1 下载源代码

#### 6.1.1 克隆代码仓库
```powershell
# 切换到服务目录
cd C:\Services\WordService

# 克隆仓库（替换为实际仓库地址）
git clone https://github.com/your-repo/paper-check-win.git .

# 或者手动上传代码文件
```

#### 6.1.2 安装Python依赖
```powershell
# 确保虚拟环境已激活
.\venv\Scripts\Activate.ps1

# 切换到backend目录
cd backend

# 安装依赖包
pip install -r requirements.txt

# 安装Windows特定依赖
pip install pywin32
```

### 6.2 配置应用

#### 6.2.1 创建配置文件
```powershell
# 切换到backend目录
cd C:\Services\WordService\backend

# 复制配置模板
Copy-Item config.yaml.template config.yaml
```

#### 6.2.2 编辑配置文件
编辑 `config.yaml` 文件：

```yaml
# 应用配置
app:
  name: "Word Document Analysis Service"
  version: "1.0.0"
  debug: false
  secret_key: "your-secret-key-here-change-in-production"
  
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  
# 数据库配置
database:
  url: "postgresql+asyncpg://word_service_user:word_service_password@localhost:5432/word_service"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  
# Redis配置
redis:
  url: "redis://:redis_password_here@localhost:6379/0"
  max_connections: 20
  
# 日志配置
logging:
  level: "INFO"
  format: "json"
  file: "C:\\Services\\Logs\\word_service.log"
  max_size: "100MB"
  backup_count: 5
  
# 文件上传配置
upload:
  max_size: 52428800  # 50MB
  allowed_extensions: [".docx", ".doc"]
  upload_dir: "C:\\Services\\Data\\uploads"
  
# 任务配置
tasks:
  max_workers: 4
  timeout: 300
  
# Word COM配置
word:
  visible: false
  display_alerts: false
  timeout: 30
```

#### 6.2.3 创建必要目录
```powershell
# 创建上传目录
New-Item -ItemType Directory -Path "C:\Services\Data\uploads" -Force

# 创建日志目录
New-Item -ItemType Directory -Path "C:\Services\Logs" -Force

# 设置目录权限
icacls "C:\Services\Data" /grant "Everyone:(OI)(CI)F" /T
icacls "C:\Services\Logs" /grant "Everyone:(OI)(CI)F" /T
```

### 6.3 初始化数据库

#### 6.3.1 运行数据库初始化脚本
```powershell
# 确保在backend目录并激活虚拟环境
cd C:\Services\WordService\backend
..\venv\Scripts\Activate.ps1

# 运行数据库初始化
python -c "
from app.database.init_db import init_database
import asyncio
asyncio.run(init_database())
"
```

#### 6.3.2 验证数据库表
```powershell
# 连接数据库验证表结构
$env:PGPASSWORD = "word_service_password"
psql -U word_service_user -h localhost -d word_service -c "\dt"
```

## 第七部分：服务启动和验证

### 7.1 启动应用服务

#### 7.1.1 测试启动
```powershell
# 确保在backend目录
cd C:\Services\WordService\backend

# 激活虚拟环境
..\venv\Scripts\Activate.ps1

# 测试启动应用
python -m uvicorn app.main:app --host 127.0.0.1 --port 8000
```

#### 7.1.2 验证服务健康状态
在另一个PowerShell窗口中：

```powershell
# 测试健康检查端点
Invoke-RestMethod -Uri "http://localhost:8000/health" -Method GET

# 测试API文档
Start-Process "http://localhost:8000/docs"
```

### 7.2 创建Windows服务

#### 7.2.1 安装nssm (Non-Sucking Service Manager)
```powershell
# 下载nssm
$nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
$nssmOutput = "C:\temp\nssm-2.24.zip"
Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmOutput

# 解压nssm
Expand-Archive -Path $nssmOutput -DestinationPath "C:\temp\"

# 复制nssm到系统目录
Copy-Item "C:\temp\nssm-2.24\win64\nssm.exe" "C:\Windows\System32\"
```

#### 7.2.2 创建WordService Windows服务
```powershell
# 使用nssm创建服务
nssm install WordService "C:\Services\WordService\venv\Scripts\python.exe"
nssm set WordService Parameters "-m uvicorn app.main:app --host 0.0.0.0 --port 8000"
nssm set WordService AppDirectory "C:\Services\WordService\backend"
nssm set WordService AppStdout "C:\Services\Logs\wordservice_stdout.log"
nssm set WordService AppStderr "C:\Services\Logs\wordservice_stderr.log"
nssm set WordService Description "Word Document Analysis Service"
nssm set WordService Start SERVICE_AUTO_START

# 启动服务
Start-Service WordService

# 验证服务状态
Get-Service WordService
```

### 7.3 配置开机自启动

#### 7.3.1 设置服务自动启动
```powershell
# 设置PostgreSQL自动启动
Set-Service -Name "postgresql-x64-17" -StartupType Automatic

# 设置Redis自动启动
Set-Service -Name "Redis" -StartupType Automatic

# 设置WordService自动启动
Set-Service -Name "WordService" -StartupType Automatic

# 验证启动类型
Get-Service "postgresql-x64-17", "Redis", "WordService" | Select-Object Name, StartType, Status
```

## 第八部分：Nginx反向代理（可选）

### 8.1 安装Nginx

#### 8.1.1 下载和安装Nginx
```powershell
# 下载Nginx
$nginxUrl = "http://nginx.org/download/nginx-1.24.0.zip"
$nginxOutput = "C:\temp\nginx-1.24.0.zip"
Invoke-WebRequest -Uri $nginxUrl -OutFile $nginxOutput

# 解压到服务目录
Expand-Archive -Path $nginxOutput -DestinationPath "C:\Services\"
Rename-Item "C:\Services\nginx-1.24.0" "C:\Services\nginx"
```

#### 8.1.2 配置Nginx
创建 `C:\Services\nginx\conf\nginx.conf`：

```nginx
worker_processes auto;
error_log C:/Services/Logs/nginx_error.log;
pid C:/Services/nginx/logs/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';
                   
    access_log C:/Services/Logs/nginx_access.log main;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 上传大小限制
    client_max_body_size 50M;
    
    # 上游服务器配置
    upstream wordservice {
        server 127.0.0.1:8000;
    }
    
    # HTTP服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 静态文件处理
        location /static/ {
            alias C:/Services/WordService/frontend/dist/static/;
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
        
        # API代理
        location /api/ {
            proxy_pass http://wordservice;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # 健康检查
        location /health {
            proxy_pass http://wordservice/health;
            access_log off;
        }
        
        # 根路径
        location / {
            try_files $uri $uri/ /index.html;
            root C:/Services/WordService/frontend/dist/;
            index index.html;
        }
    }
}
```

#### 8.1.3 创建Nginx Windows服务
```powershell
# 使用nssm创建Nginx服务
nssm install nginx "C:\Services\nginx\nginx.exe"
nssm set nginx AppDirectory "C:\Services\nginx"
nssm set nginx Description "Nginx HTTP Server"
nssm set nginx Start SERVICE_AUTO_START

# 启动Nginx服务
Start-Service nginx

# 验证服务
Get-Service nginx
```

## 第九部分：监控和维护

### 9.1 日志管理

#### 9.1.1 配置日志轮转
创建PowerShell脚本 `C:\Services\Scripts\rotate_logs.ps1`：

```powershell
# 日志轮转脚本
$logDir = "C:\Services\Logs"
$maxSize = 100MB
$keepDays = 30

Get-ChildItem $logDir -Filter "*.log" | ForEach-Object {
    if ($_.Length -gt $maxSize) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $newName = $_.BaseName + "_" + $timestamp + ".log"
        Move-Item $_.FullName (Join-Path $logDir $newName)
        New-Item -Path $_.FullName -ItemType File
    }
}

# 删除旧日志
Get-ChildItem $logDir -Filter "*_*.log" | Where-Object {
    $_.CreationTime -lt (Get-Date).AddDays(-$keepDays)
} | Remove-Item -Force
```

#### 9.1.2 创建定时任务
```powershell
# 创建计划任务进行日志轮转
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Services\Scripts\rotate_logs.ps1"
$trigger = New-ScheduledTaskTrigger -Daily -At "02:00"
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "WordService_LogRotation" -Action $action -Trigger $trigger -Settings $settings
```

### 9.2 性能监控

#### 9.2.1 系统监控脚本
创建监控脚本 `C:\Services\Scripts\monitor.ps1`：

```powershell
# 系统监控脚本
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# 检查服务状态
$services = @("postgresql-x64-17", "Redis", "WordService", "nginx")
$serviceStatus = @{}

foreach ($service in $services) {
    $status = Get-Service $service -ErrorAction SilentlyContinue
    $serviceStatus[$service] = if ($status) { $status.Status } else { "Not Found" }
}

# 检查端口状态
$ports = @(5432, 6379, 8000, 80)
$portStatus = @{}

foreach ($port in $ports) {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
    $portStatus[$port] = $connection.TcpTestSucceeded
}

# 检查磁盘空间
$diskSpace = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | Select-Object DeviceID, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}, @{Name="TotalSize(GB)";Expression={[math]::Round($_.Size/1GB,2)}}

# 输出监控结果
$monitorResult = @{
    Timestamp = $timestamp
    Services = $serviceStatus
    Ports = $portStatus
    DiskSpace = $diskSpace
}

$monitorResult | ConvertTo-Json -Depth 3 | Out-File "C:\Services\Logs\monitor_$(Get-Date -Format 'yyyyMMdd').log" -Append
```

## 第十部分：故障排除

### 10.1 常见问题及解决方案

#### 10.1.1 PostgreSQL连接问题
```powershell
# 检查PostgreSQL服务状态
Get-Service postgresql-x64-17

# 检查端口监听
netstat -an | findstr :5432

# 测试数据库连接
$env:PGPASSWORD = "word_service_password"
psql -U word_service_user -h localhost -d word_service -c "SELECT 1;"

# 查看PostgreSQL日志
Get-Content "C:\Services\PostgreSQL\data\log\*.log" -Tail 50
```

#### 10.1.2 Redis连接问题
```powershell
# 检查Redis服务状态
Get-Service Redis

# 测试Redis连接
redis-cli -h localhost -p 6379 -a redis_password_here ping

# 查看Redis日志
Get-Content "C:\Services\Logs\redis.log" -Tail 50
```

#### 10.1.3 Word COM接口问题
```powershell
# 验证Word COM接口
try {
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    $word.Quit()
    Write-Host "Word COM interface is working"
} catch {
    Write-Host "Word COM interface error: $($_.Exception.Message)"
}

# 检查Word进程
Get-Process WINWORD -ErrorAction SilentlyContinue
```

#### 10.1.4 应用服务问题
```powershell
# 检查WordService状态
Get-Service WordService

# 查看应用日志
Get-Content "C:\Services\Logs\wordservice_stderr.log" -Tail 50

# 手动测试应用
cd C:\Services\WordService\backend
..\venv\Scripts\Activate.ps1
python -c "from app.main import app; print('App loaded successfully')"
```

### 10.2 性能优化

#### 10.2.1 数据库优化
```sql
-- 连接数据库
psql -U word_service_user -h localhost -d word_service

-- 查看连接数
SELECT count(*) FROM pg_stat_activity;

-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 更新统计信息
ANALYZE;
```

#### 10.2.2 应用性能优化
```powershell
# 检查内存使用
Get-Process python | Select-Object ProcessName, WorkingSet, VirtualMemorySize

# 检查CPU使用
Get-Counter "\Process(python)\% Processor Time"

# 检查磁盘I/O
Get-Counter "\LogicalDisk(C:)\Disk Reads/sec", "\LogicalDisk(C:)\Disk Writes/sec"
```

## 第十一部分：备份和恢复

### 11.1 数据库备份

#### 11.1.1 创建备份脚本
```powershell
# 创建备份脚本 C:\Services\Scripts\backup_db.ps1
$backupDir = "C:\Services\Backups\Database"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "word_service_backup_$timestamp.sql"

# 创建备份目录
if (!(Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir -Force
}

# 设置PostgreSQL密码
$env:PGPASSWORD = "word_service_password"

# 执行备份
pg_dump -U word_service_user -h localhost -d word_service -f "$backupDir\$backupFile"

# 压缩备份文件
Compress-Archive -Path "$backupDir\$backupFile" -DestinationPath "$backupDir\$backupFile.zip"
Remove-Item "$backupDir\$backupFile"

# 删除7天前的备份
Get-ChildItem $backupDir -Filter "*.zip" | Where-Object {
    $_.CreationTime -lt (Get-Date).AddDays(-7)
} | Remove-Item -Force

Write-Host "Database backup completed: $backupFile.zip"
```

#### 11.1.2 设置自动备份
```powershell
# 创建每日备份任务
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Services\Scripts\backup_db.ps1"
$trigger = New-ScheduledTaskTrigger -Daily -At "01:00"
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "WordService_DatabaseBackup" -Action $action -Trigger $trigger -Settings $settings
```

### 11.2 应用备份

#### 11.2.1 配置文件备份
```powershell
# 创建配置备份脚本
$configBackupDir = "C:\Services\Backups\Config"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

if (!(Test-Path $configBackupDir)) {
    New-Item -ItemType Directory -Path $configBackupDir -Force
}

# 备份配置文件
Copy-Item "C:\Services\WordService\backend\config.yaml" "$configBackupDir\config_$timestamp.yaml"
Copy-Item "C:\Services\nginx\conf\nginx.conf" "$configBackupDir\nginx_$timestamp.conf"
Copy-Item "C:\Services\PostgreSQL\data\postgresql.conf" "$configBackupDir\postgresql_$timestamp.conf"
Copy-Item "C:\Services\PostgreSQL\data\pg_hba.conf" "$configBackupDir\pg_hba_$timestamp.conf"
```

## 总结

本文档提供了在Windows云服务器上原生部署Word文档分析服务的完整指南。部署完成后，您将拥有：

1. **高性能的数据库服务** - PostgreSQL 17.5 原生安装
2. **可靠的缓存服务** - Redis 7.x 原生安装
3. **稳定的Python环境** - Python 3.12 + 虚拟环境
4. **功能完整的Word服务** - COM接口支持
5. **生产级的监控和备份** - 完整的运维工具

### 安全建议

1. **定期更新系统和软件包**
2. **使用强密码和密钥认证**
3. **配置防火墙规则**
4. **定期备份数据**
5. **监控系统日志**
6. **限制远程访问权限**

### 性能优化建议

1. **根据服务器配置调整数据库参数**
2. **使用SSD存储提高I/O性能**
3. **配置适当的连接池大小**
4. **定期清理日志文件**
5. **监控资源使用情况**

按照本指南操作，您可以在Windows云服务器上成功部署一个稳定、高性能的Word文档分析服务。 