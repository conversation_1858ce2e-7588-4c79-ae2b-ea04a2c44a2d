"""
安全相关工具函数，包括密码哈希和JWT令牌处理
"""

from datetime import datetime, timedelta
from typing import Any, Union, Dict, Optional

from jose import jwt, JWTError
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from pydantic import BaseModel

from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

ALGORITHM = settings.security.algorithm
SECRET_KEY = settings.security.secret_key

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token")

class TokenData(BaseModel):
    user_id: Optional[str] = None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证明文密码和哈希密码是否匹配"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码的哈希值"""
    return pwd_context.hash(password)


def create_access_token(
    subject: Dict[str, Any], expires_delta: Optional[timedelta] = None
) -> tuple[str, str]:
    """
    创建JWT访问令牌和刷新令牌
    :param subject: 包含用户信息的字典 {"sub": username, "user_id": user_id}
    :param expires_delta: 令牌过期时间
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.security.access_token_expire_minutes
        )
    
    refresh_expire = expire + timedelta(days=settings.security.refresh_token_expire_days)
    
    # 修复JWT subject字段问题：sub必须是字符串，其他信息放在自定义字段
    to_encode = {
        "exp": expire, 
        "sub": subject.get("sub"),  # 用户名作为sub字符串
        "user_id": subject.get("user_id"),  # 用户ID放在自定义字段
        "iat": datetime.utcnow()  # 签发时间
    }
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    to_encode_refresh = {
        "exp": refresh_expire, 
        "sub": subject.get("sub"),
        "user_id": subject.get("user_id"),
        "iat": datetime.utcnow()
    }
    encoded_refresh_jwt = jwt.encode(to_encode_refresh, SECRET_KEY, algorithm=ALGORITHM)

    return encoded_jwt, encoded_refresh_jwt

def decode_token(token: str) -> Optional[Dict[str, Any]]:
    """解码JWT令牌"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError as e:
        # 记录JWT错误以便调试
        from app.core.logging import logger
        logger.warning(f"JWT解码失败: {str(e)}")
        return None 

async def get_current_user_id(token: str = Depends(oauth2_scheme)) -> str:
    """
    解码JWT并返回用户ID
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
        # 可以选择性地在这里从数据库验证用户是否存在
    except JWTError:
        raise credentials_exception
    return user_id 