#!/usr/bin/env python3
"""
测试重构后的逻辑：统一从规则文件读取标准要求
"""

import requests
import json

def test_unified_rule_source():
    """测试统一的规则数据源"""
    
    print("🔍 测试统一规则数据源逻辑")
    print("=" * 50)
    
    try:
        # 1. 从API获取检测标准配置
        print("1️⃣ 从API获取检测标准配置...")
        response = requests.get('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024')
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
        api_data = response.json()['data']
        print("✅ API数据获取成功")
        
        # 2. 解析规则配置
        print("\n2️⃣ 解析规则配置...")
        content_rules = api_data.get('rules', {}).get('content', {})
        
        # 模拟前端的解析逻辑
        requirements = {}
        rule_mapping = {
            'chinese_abstract_word_count': '中文摘要',
            'english_abstract_word_count': '英文摘要', 
            'references_item_count': '参考文献'
        }
        
        for rule_key, rule_config in content_rules.items():
            if rule_key in rule_mapping:
                structure_name = rule_mapping[rule_key]
                params = rule_config.get('parameters', {})
                
                requirements[structure_name] = {
                    'min': params.get('min'),
                    'max': params.get('max'),
                    'unit': params.get('unit'),
                    'rule_name': rule_config.get('name')
                }
                
                print(f"✅ 解析规则: {structure_name}")
                print(f"   - 规则名称: {rule_config.get('name')}")
                print(f"   - 要求: {params.get('min', '')}-{params.get('max', '')}{params.get('unit', '')}")
        
        # 3. 验证英文摘要规则
        print("\n3️⃣ 验证英文摘要规则...")
        english_abstract_req = requirements.get('英文摘要')
        
        if english_abstract_req:
            print("✅ 英文摘要规则存在")
            print(f"   - 最小值: {english_abstract_req['min']}")
            print(f"   - 最大值: {english_abstract_req['max']}")
            print(f"   - 单位: {english_abstract_req['unit']}")
            print(f"   - 规则名称: {english_abstract_req['rule_name']}")
            
            # 验证具体数值
            if (english_abstract_req['min'] == 300 and 
                english_abstract_req['max'] == 500 and 
                english_abstract_req['unit'] == '词'):
                print("✅ 英文摘要规则配置正确！")
            else:
                print("❌ 英文摘要规则配置错误！")
                return False
        else:
            print("❌ 英文摘要规则缺失！")
            return False
        
        # 4. 模拟字数分析功能
        print("\n4️⃣ 模拟字数分析功能...")
        
        # 模拟文档结构数据
        mock_structures = [
            {'name': '中文摘要', 'content': {'word_count': 355}, 'count': '355字'},
            {'name': '英文摘要', 'content': {'word_count': 194}, 'count': '194词'},
            {'name': '参考文献', 'content': {'word_count': 15}, 'count': '中文11条外文3条'}
        ]
        
        analysis_results = []
        
        for structure in mock_structures:
            name = structure['name']
            current_count = structure['content']['word_count']
            count_display = structure['count']
            
            # 获取标准要求
            requirement = requirements.get(name)
            
            if requirement:
                min_val = requirement['min']
                max_val = requirement['max']
                unit = requirement['unit']
                
                # 生成标准要求显示
                if min_val and max_val:
                    standard_req = f"{min_val}-{max_val}{unit}"
                elif min_val:
                    standard_req = f"≥{min_val}{unit}"
                else:
                    standard_req = "-"
                
                # 分析结果
                if min_val and max_val:
                    if min_val <= current_count <= max_val:
                        result = "✅ 达标"
                    elif current_count < min_val:
                        result = "⚠️ 不足"
                    else:
                        result = "⚠️ 过多"
                elif min_val:
                    result = "✅ 达标" if current_count >= min_val else "⚠️ 不足"
                else:
                    result = "无要求"
            else:
                standard_req = "-"
                result = "无要求"
            
            analysis_results.append({
                'structure': name,
                'standard_requirement': standard_req,
                'current_situation': count_display,
                'analysis_result': result
            })
        
        # 显示分析结果
        print("\n📊 字数分析结果:")
        print("-" * 80)
        print(f"{'结构名称':<12} {'标准要求':<15} {'当前情况':<15} {'分析结果':<10}")
        print("-" * 80)
        
        for result in analysis_results:
            print(f"{result['structure']:<12} {result['standard_requirement']:<15} {result['current_situation']:<15} {result['analysis_result']:<10}")
        
        # 5. 验证英文摘要分析结果
        print("\n5️⃣ 验证英文摘要分析结果...")
        english_result = next((r for r in analysis_results if r['structure'] == '英文摘要'), None)
        
        if english_result:
            print(f"✅ 英文摘要分析完成")
            print(f"   - 标准要求: {english_result['standard_requirement']}")
            print(f"   - 当前情况: {english_result['current_situation']}")
            print(f"   - 分析结果: {english_result['analysis_result']}")
            
            # 验证分析逻辑
            if (english_result['standard_requirement'] == '300-500词' and
                english_result['current_situation'] == '194词' and
                english_result['analysis_result'] == '⚠️ 不足'):
                print("✅ 英文摘要分析逻辑正确！")
            else:
                print("❌ 英文摘要分析逻辑错误！")
                return False
        else:
            print("❌ 英文摘要分析结果缺失！")
            return False
        
        print("\n🎉 所有测试通过！统一规则数据源逻辑正确！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_unified_rule_source()
    exit(0 if success else 1)
