"""
WebSocket API路由

提供实时通信功能：
- 任务状态推送
- 文档处理进度
- 系统通知
- 连接管理
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Set, Optional, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from fastapi.websockets import WebSocketState

from app.core.logging import logger
from app.security import decode_token

router = APIRouter()


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接: {connection_id: {"websocket": WebSocket, "user_id": int, "last_ping": float}}
        self.active_connections: Dict[str, Dict[str, Any]] = {}
        # 用户连接映射: {user_id: Set[connection_id]}
        self.user_connections: Dict[int, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, connection_id: str, user_id: int):
        """建立连接"""
        await websocket.accept()
        
        # 记录连接
        self.active_connections[connection_id] = {
            "websocket": websocket,
            "user_id": user_id,
            "last_ping": time.time(),
            "connected_at": datetime.now().isoformat()
        }
        
        # 添加到用户连接映射
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(connection_id)
        
        logger.info(f"WebSocket连接建立: connection_id={connection_id}, user_id={user_id}")
        
        # 发送连接确认消息
        await self.send_personal_message({
            "type": "connection_established",
            "data": {
                "connection_id": connection_id,
                "message": "WebSocket连接已建立",
                "server_time": datetime.now().isoformat()
            },
            "timestamp": int(time.time() * 1000)
        }, connection_id)
    
    def disconnect(self, connection_id: str):
        """断开连接"""
        if connection_id in self.active_connections:
            connection_info = self.active_connections[connection_id]
            user_id = connection_info["user_id"]
            
            # 从活跃连接中移除
            del self.active_connections[connection_id]
            
            # 从用户连接映射中移除
            if user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            logger.info(f"WebSocket连接断开: connection_id={connection_id}, user_id={user_id}")
    
    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            connection_info = self.active_connections[connection_id]
            websocket = connection_info["websocket"]
            
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(message, ensure_ascii=False))
                    return True
                else:
                    # 连接已断开，清理
                    self.disconnect(connection_id)
                    return False
            except Exception as e:
                logger.error(f"发送WebSocket消息失败: connection_id={connection_id}, error={e}")
                self.disconnect(connection_id)
                return False
        return False
    
    async def send_to_user(self, message: dict, user_id: int):
        """发送消息给指定用户的所有连接"""
        if user_id in self.user_connections:
            connection_ids = list(self.user_connections[user_id])
            success_count = 0
            
            for connection_id in connection_ids:
                if await self.send_personal_message(message, connection_id):
                    success_count += 1
            
            logger.debug(f"消息发送给用户 {user_id}: {success_count}/{len(connection_ids)} 连接成功")
            return success_count > 0
        return False
    
    def get_connection_stats(self) -> dict:
        """获取连接统计"""
        return {
            "total_connections": len(self.active_connections),
            "total_users": len(self.user_connections),
            "connections_per_user": {
                user_id: len(connections) 
                for user_id, connections in self.user_connections.items()
            }
        }


# 全局连接管理器
manager = ConnectionManager()


def authenticate_websocket(token: str) -> Optional[int]:
    """WebSocket认证"""
    try:
        payload = decode_token(token)
        if payload and "user_id" in payload:
            return payload["user_id"]
        return None
    except Exception as e:
        logger.warning(f"WebSocket认证失败: {e}")
        return None


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(..., description="认证Token")
):
    """
    WebSocket端点
    
    支持实时通信功能：
    - 任务状态更新
    - 文档处理进度
    - 系统通知
    - 心跳检测
    """
    connection_id = f"ws_{int(time.time() * 1000)}_{id(websocket)}"
    
    # 认证
    user_id = authenticate_websocket(token)
    if not user_id:
        await websocket.close(code=1008, reason="Authentication failed")
        logger.warning(f"WebSocket认证失败: connection_id={connection_id}")
        return
    
    try:
        # 建立连接
        await manager.connect(websocket, connection_id, user_id)
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 更新心跳时间
                if connection_id in manager.active_connections:
                    manager.active_connections[connection_id]["last_ping"] = time.time()
                
                # 处理消息
                await handle_websocket_message(message, connection_id, user_id)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端主动断开: connection_id={connection_id}")
                break
            except json.JSONDecodeError:
                logger.warning(f"WebSocket消息格式错误: connection_id={connection_id}")
                await manager.send_personal_message({
                    "type": "error",
                    "data": {"message": "消息格式错误"},
                    "timestamp": int(time.time() * 1000)
                }, connection_id)
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: connection_id={connection_id}, error={e}")
                break
    
    except Exception as e:
        logger.error(f"WebSocket连接错误: connection_id={connection_id}, error={e}")
    
    finally:
        # 清理连接
        manager.disconnect(connection_id)


async def handle_websocket_message(message: dict, connection_id: str, user_id: int):
    """处理WebSocket消息"""
    message_type = message.get("type", "")
    
    if message_type == "ping":
        # 心跳响应
        await manager.send_personal_message({
            "type": "pong",
            "data": {"server_time": datetime.now().isoformat()},
            "timestamp": int(time.time() * 1000)
        }, connection_id)
    
    elif message_type == "auth":
        # 认证确认
        await manager.send_personal_message({
            "type": "auth_confirmed",
            "data": {
                "user_id": user_id,
                "message": "认证成功"
            },
            "timestamp": int(time.time() * 1000)
        }, connection_id)
    
    elif message_type == "test":
        # 测试消息
        await manager.send_personal_message({
            "type": "test_response",
            "data": {
                "message": "测试消息收到",
                "original_message": message.get("data", {})
            },
            "timestamp": int(time.time() * 1000)
        }, connection_id)
    
    else:
        logger.warning(f"未知消息类型: {message_type}")


# 获取连接统计的API端点
@router.get("/ws/stats")
async def get_websocket_stats():
    """获取WebSocket连接统计"""
    stats = manager.get_connection_stats()
    return {
        "success": True,
        "data": stats,
        "message": "WebSocket统计信息"
    }
