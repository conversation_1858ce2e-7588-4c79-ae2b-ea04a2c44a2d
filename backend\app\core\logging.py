"""
结构化日志系统

提供统一的日志记录功能，支持：
- 结构化日志输出 (JSON格式)
- 日志级别控制
- 文件和控制台输出
- 日志文件轮转
- 性能监控日志
- 异常跟踪
"""

import sys
import json
import logging
import traceback
from typing import Any, Dict, Optional, Union
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from contextlib import contextmanager

import structlog
from structlog.stdlib import LoggerFactory
from structlog.processors import J<PERSON><PERSON>enderer, TimeStamper, add_log_level, StackInfoRenderer

from .config import settings


class JsonFormatter(logging.Formatter):
    """JSON格式日志格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
            'process': record.process,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # 添加自定义字段
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


class TextFormatter(logging.Formatter):
    """文本格式日志格式化器"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s [%(filename)s:%(lineno)d]',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path(settings.logging.file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, settings.logging.level.upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加文件处理器
        if settings.logging.file:
            file_handler = self._create_file_handler()
            root_logger.addHandler(file_handler)
        
        # 添加控制台处理器
        if settings.logging.console_output:
            console_handler = self._create_console_handler()
            root_logger.addHandler(console_handler)
        
        # 配置structlog
        self._setup_structlog()
    
    def _create_file_handler(self) -> RotatingFileHandler:
        """创建文件处理器"""
        file_handler = RotatingFileHandler(
            filename=settings.logging.file,
            maxBytes=settings.logging.max_size,
            backupCount=settings.logging.backup_count,
            encoding='utf-8'
        )
        
        if settings.logging.format.lower() == 'json':
            file_handler.setFormatter(JsonFormatter())
        else:
            file_handler.setFormatter(TextFormatter())
        
        return file_handler
    
    def _create_console_handler(self) -> logging.StreamHandler:
        """创建控制台处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        
        if settings.logging.format.lower() == 'json':
            console_handler.setFormatter(JsonFormatter())
        else:
            console_handler.setFormatter(TextFormatter())
        
        return console_handler
    
    def _setup_structlog(self) -> None:
        """设置structlog"""
        processors = [
            structlog.contextvars.merge_contextvars,
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
        ]
        
        # 只在最后添加一个渲染器，避免双重编码
        if settings.logging.format.lower() == 'json':
            # 使用stdlib处理器，让JsonFormatter处理最终的JSON格式化
            processors.append(structlog.stdlib.ProcessorFormatter.wrap_for_formatter)
        else:
            processors.append(structlog.dev.ConsoleRenderer())
        
        structlog.configure(
            processors=processors,
            logger_factory=LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def get_logger(self, name: str) -> structlog.stdlib.BoundLogger:
        """获取指定名称的日志记录器"""
        if name not in self._loggers:
            self._loggers[name] = structlog.get_logger(name)
        return self._loggers[name]


# 全局日志管理器实例
_logger_manager = LoggerManager()


def get_logger(name: str = __name__) -> structlog.stdlib.BoundLogger:
    """获取日志记录器"""
    return _logger_manager.get_logger(name)


# 创建主应用日志记录器
logger = get_logger("word_service")


class LoggerMixin:
    """日志记录器混入类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = get_logger(self.__class__.__name__)
    
    def log_info(self, message: str, **kwargs) -> None:
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs) -> None:
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def log_exception(self, message: str, exc: Optional[Exception] = None, **kwargs) -> None:
        """记录异常日志"""
        if exc:
            kwargs['exception_type'] = type(exc).__name__
            kwargs['exception_message'] = str(exc)
        self.logger.exception(message, **kwargs)


@contextmanager
def log_execution_time(operation_name: str, logger_instance: Optional[structlog.stdlib.BoundLogger] = None):
    """记录操作执行时间的上下文管理器"""
    if logger_instance is None:
        logger_instance = logger
    
    start_time = datetime.now()
    logger_instance.info(f"开始执行: {operation_name}")
    
    try:
        yield
        execution_time = (datetime.now() - start_time).total_seconds()
        logger_instance.info(
            f"完成执行: {operation_name}",
            execution_time=execution_time,
            status="success"
        )
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger_instance.error(
            f"执行失败: {operation_name}",
            execution_time=execution_time,
            error=str(e),
            status="failed"
        )
        raise


def log_function_call(func_name: str = None):
    """函数调用日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            function_name = func_name or f"{func.__module__}.{func.__name__}"
            func_logger = get_logger(func.__module__)
            
            with log_execution_time(function_name, func_logger):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, name: str = "performance"):
        self.logger = get_logger(name)
    
    def log_request(self, method: str, path: str, status_code: int, 
                   response_time: float, **kwargs) -> None:
        """记录HTTP请求日志"""
        self.logger.info(
            "HTTP请求",
            method=method,
            path=path,
            status_code=status_code,
            response_time=response_time,
            **kwargs
        )
    
    def log_task_execution(self, task_name: str, status: str, 
                          execution_time: float, **kwargs) -> None:
        """记录任务执行日志"""
        self.logger.info(
            "任务执行",
            task_name=task_name,
            status=status,
            execution_time=execution_time,
            **kwargs
        )
    
    def log_database_query(self, query_type: str, table: str, 
                          execution_time: float, **kwargs) -> None:
        """记录数据库查询日志"""
        self.logger.info(
            "数据库查询",
            query_type=query_type,
            table=table,
            execution_time=execution_time,
            **kwargs
        )
    
    def log_file_operation(self, operation: str, file_path: str, 
                          file_size: Optional[int] = None, 
                          processing_time: float = 0, **kwargs) -> None:
        """记录文件操作日志"""
        self.logger.info(
            "文件操作",
            operation=operation,
            file_path=file_path,
            file_size=file_size,
            processing_time=processing_time,
            **kwargs
        )


class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self, name: str = "security"):
        self.logger = get_logger(name)
    
    def log_authentication(self, user_id: str, success: bool, 
                          ip_address: str, user_agent: str, **kwargs) -> None:
        """记录认证日志"""
        self.logger.info(
            "用户认证",
            user_id=user_id,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            **kwargs
        )
    
    def log_authorization(self, user_id: str, resource: str, 
                         action: str, granted: bool, **kwargs) -> None:
        """记录授权日志"""
        self.logger.info(
            "权限检查",
            user_id=user_id,
            resource=resource,
            action=action,
            granted=granted,
            **kwargs
        )
    
    def log_suspicious_activity(self, activity_type: str, details: str, 
                               ip_address: str, **kwargs) -> None:
        """记录可疑活动"""
        self.logger.warning(
            "可疑活动",
            activity_type=activity_type,
            details=details,
            ip_address=ip_address,
            **kwargs
        )


# 创建全局日志记录器实例
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()


def setup_logging() -> None:
    """设置日志系统 - 应用启动时调用"""
    global _logger_manager
    _logger_manager = LoggerManager()
    logger.info("日志系统初始化完成", log_level=settings.logging.level)


if __name__ == "__main__":
    # 测试日志系统
    setup_logging()
    
    test_logger = get_logger("test")
    test_logger.info("这是一条测试信息")
    test_logger.warning("这是一条警告信息")
    test_logger.error("这是一条错误信息")
    
    # 测试性能日志
    performance_logger.log_request("GET", "/api/v1/test", 200, 0.123)
    
    # 测试安全日志
    security_logger.log_authentication("user123", True, "***********", "Mozilla/5.0")
    
    print("日志系统测试完成") 