# Word文档分析服务 - 生产环境部署指南

## 🎯 部署概述

本指南提供Word文档分析服务在生产环境的完整部署方案，包括容器化部署、负载均衡、SSL配置、监控告警等企业级功能。

## 🏗️ 架构设计

### 生产架构图
```
[Internet] 
    ↓
[Load Balancer/CDN]
    ↓
[Nginx Reverse Proxy] 
    ↓
[FastAPI App Cluster] ← → [Redis Cluster]
    ↓
[PostgreSQL HA] ← → [File Storage]
    ↓
[Monitoring & Logging]
```

### 服务组件
- **应用层**: FastAPI应用集群
- **代理层**: Nginx反向代理 + SSL
- **数据层**: PostgreSQL主从 + Redis集群
- **存储层**: 文件存储系统
- **监控层**: Prometheus + Grafana + 日志收集

## 🔧 环境准备

### 服务器要求

#### 最小配置
- **CPU**: 4核心
- **内存**: 8GB
- **存储**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **存储**: 200GB SSD
- **网络**: 1Gbps

### 操作系统要求
- **Windows Server 2019/2022** (推荐)
- **Container Runtime**: Docker Desktop for Windows
- **PowerShell**: 5.1+

## 📦 Docker生产部署

### 1. 项目结构准备
```bash
# 创建生产目录结构
mkdir -p /opt/word-service/{config,data,logs,ssl,scripts}
cd /opt/word-service

# 拷贝项目文件
cp -r backend/* .
```

### 2. 生产环境配置

#### 创建生产环境配置文件
```yaml
# deploy/config/production.yaml
server:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  max_connections: 2000
  keepalive_timeout: 10
  graceful_timeout: 60

database:
  url: "postgresql+asyncpg://word_user:${DB_PASSWORD}@postgres-master:5432/word_service"
  pool_size: 20
  max_overflow: 40
  pool_timeout: 30
  pool_recycle: 3600

redis:
  url: "redis://redis-cluster:6379/0"
  max_connections: 20
  timeout: 10

security:
  secret_key: "${JWT_SECRET_KEY}"
  bcrypt_rounds: 14

logging:
  level: "WARNING"
  format: "json"
  console_output: false

performance:
  enable_compression: true
  compression_level: 6
  enable_caching: true
  
rate_limit:
  per_user_minute: 200
  per_ip_minute: 500
  daily_task_limit: 100
```

#### 创建环境变量文件
```bash
# .env.production
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# 数据库配置
DB_PASSWORD=your-super-secure-db-password
DATABASE_URL=postgresql+asyncpg://word_user:${DB_PASSWORD}@postgres:5432/word_service

# Redis配置
REDIS_PASSWORD=your-redis-password
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0

# 安全配置
JWT_SECRET_KEY=your-super-secret-jwt-key-min-32-characters
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# 文件配置
MAX_FILE_SIZE=104857600
UPLOAD_PATH=/app/data/uploads

# SSL配置
SSL_ENABLED=true
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# 监控配置
PROMETHEUS_ENABLED=true
GRAFANA_PASSWORD=your-grafana-password
```

### 3. Docker镜像构建

#### 优化的生产Dockerfile
```dockerfile
# deploy/docker/Dockerfile.production
FROM python:3.12-windowsservercore-1809 as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN powershell -Command \
    Install-WindowsFeature -Name IIS-WebServerRole; \
    Install-WindowsFeature -Name IIS-WebServer

# 创建应用用户
RUN net user appuser /add /passwordreq:no
RUN net localgroup "Users" appuser /add

# 复制依赖文件
COPY requirements-prod.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements-prod.txt

# 复制应用代码
COPY app/ ./app/
COPY config/ ./config/
COPY scripts/ ./scripts/

# 创建数据目录
RUN mkdir data logs ssl
RUN icacls data /grant appuser:F
RUN icacls logs /grant appuser:F

# 复制启动脚本
COPY deploy/scripts/start.ps1 .
COPY deploy/scripts/healthcheck.ps1 .

# 设置权限
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD powershell -File healthcheck.ps1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["powershell", "-File", "start.ps1"]
```

#### 构建生产镜像
```bash
# 构建镜像
docker build -f deploy/docker/Dockerfile.production -t word-service:latest .

# 推送到私有仓库（可选）
docker tag word-service:latest your-registry.com/word-service:latest
docker push your-registry.com/word-service:latest
```

### 4. Docker Compose生产配置

#### 完整的docker-compose.production.yml
```yaml
version: '3.8'

services:
  # 应用服务集群
  word-service-1:
    image: word-service:latest
    container_name: word-service-1
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=1
    env_file:
      - .env.production
    volumes:
      - word_data:/app/data
      - word_logs:/app/logs
      - ssl_certs:/app/ssl:ro
      - ./config/production.yaml:/app/config/production.yaml:ro
    networks:
      - word-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "powershell", "-File", "healthcheck.ps1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G

  word-service-2:
    image: word-service:latest
    container_name: word-service-2
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - INSTANCE_ID=2
    env_file:
      - .env.production
    volumes:
      - word_data:/app/data
      - word_logs:/app/logs
      - ssl_certs:/app/ssl:ro
      - ./config/production.yaml:/app/config/production.yaml:ro
    networks:
      - word-network
    depends_on:
      - postgres
      - redis
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G

  # 数据库主从配置
  postgres:
    image: postgres:17.5
    container_name: word-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: word_service
      POSTGRES_USER: word_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deploy/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./deploy/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - word-network
    ports:
      - "127.0.0.1:5432:5432"
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U word_user -d word_service"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G

  # Redis集群
  redis:
    image: redis:7-alpine
    container_name: word-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./deploy/redis/redis.conf:/etc/redis/redis.conf
    networks:
      - word-network
    ports:
      - "127.0.0.1:6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    container_name: word-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d:ro
      - ssl_certs:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - word-network
    depends_on:
      - word-service-1
      - word-service-2
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "https://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: word-prometheus
    restart: unless-stopped
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./deploy/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - word-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: word-grafana
    restart: unless-stopped
    ports:
      - "127.0.0.1:3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deploy/monitoring/grafana:/etc/grafana/provisioning
    networks:
      - word-network

networks:
  word-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  word_data:
    driver: local
  word_logs:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ssl_certs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
```

## 🔒 SSL/TLS配置

### 1. 证书获取

#### 使用Let's Encrypt
```bash
# 安装Certbot
pip install certbot

# 获取证书
certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
```

### 2. Nginx SSL配置

#### SSL配置文件
```nginx
# deploy/nginx/conf.d/ssl.conf
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # 负载均衡
    upstream word_service {
        server word-service-1:8000 weight=1 max_fails=3 fail_timeout=30s;
        server word-service-2:8000 weight=1 max_fails=3 fail_timeout=30s;
    }

    location / {
        proxy_pass http://word_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 文件上传大小限制
    client_max_body_size 100M;
}
```

## 📊 监控和日志

### 1. Prometheus配置

#### 监控配置
```yaml
# deploy/monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'word-service'
    static_configs:
      - targets: ['word-service-1:8000', 'word-service-2:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 2. 告警规则

#### 创建告警规则
```yaml
# deploy/monitoring/alert_rules.yml
groups:
  - name: word-service-alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务实例宕机"
          description: "{{ $labels.instance }} 已宕机超过1分钟"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "错误率过高"
          description: "错误率超过10%"

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
```

### 3. 日志收集

#### 创建日志配置
```yaml
# deploy/logging/filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /var/log/nginx/*.log
      - /app/logs/*.log
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]

setup.kibana:
  host: "kibana:5601"
```

## 🚀 部署脚本

### 1. 自动化部署脚本

#### 创建部署脚本
```powershell
# deploy/scripts/deploy.ps1
param(
    [string]$Environment = "production",
    [string]$Version = "latest",
    [switch]$Force = $false
)

Write-Host "开始部署Word文档分析服务..." -ForegroundColor Green

# 1. 检查Docker环境
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Error "Docker未安装或未添加到PATH"
    exit 1
}

# 2. 拉取最新镜像
Write-Host "拉取最新镜像..." -ForegroundColor Yellow
docker-compose -f docker-compose.production.yml pull

# 3. 备份数据库
Write-Host "备份数据库..." -ForegroundColor Yellow
$BackupPath = "backups/db_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"
docker exec word-postgres pg_dump -U word_user word_service > $BackupPath

# 4. 停止现有服务
if ($Force) {
    Write-Host "强制停止现有服务..." -ForegroundColor Red
    docker-compose -f docker-compose.production.yml down --remove-orphans
} else {
    Write-Host "优雅停止现有服务..." -ForegroundColor Yellow
    docker-compose -f docker-compose.production.yml stop
}

# 5. 启动新服务
Write-Host "启动新服务..." -ForegroundColor Green
docker-compose -f docker-compose.production.yml up -d

# 6. 等待服务就绪
Write-Host "等待服务就绪..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 7. 健康检查
Write-Host "执行健康检查..." -ForegroundColor Yellow
$HealthUrl = "http://localhost/health"
try {
    $Response = Invoke-RestMethod -Uri $HealthUrl -TimeoutSec 30
    if ($Response.status -eq "healthy") {
        Write-Host "部署成功！服务运行正常。" -ForegroundColor Green
    } else {
        Write-Warning "服务启动但健康检查失败"
    }
} catch {
    Write-Error "健康检查失败: $($_.Exception.Message)"
}

Write-Host "部署完成！" -ForegroundColor Green
```

### 2. 滚动更新脚本

#### 无停机更新
```powershell
# deploy/scripts/rolling-update.ps1
Write-Host "开始滚动更新..." -ForegroundColor Green

# 更新服务实例1
Write-Host "更新服务实例1..." -ForegroundColor Yellow
docker-compose -f docker-compose.production.yml stop word-service-1
docker-compose -f docker-compose.production.yml up -d word-service-1

# 等待实例1就绪
Start-Sleep -Seconds 30

# 更新服务实例2
Write-Host "更新服务实例2..." -ForegroundColor Yellow
docker-compose -f docker-compose.production.yml stop word-service-2
docker-compose -f docker-compose.production.yml up -d word-service-2

Write-Host "滚动更新完成！" -ForegroundColor Green
```

## 🔍 性能优化

### 1. 数据库优化

#### PostgreSQL配置优化
```ini
# deploy/postgres/postgresql.conf
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 256MB

# 连接配置
max_connections = 200
superuser_reserved_connections = 3

# 检查点配置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# 日志配置
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000
```

### 2. 应用优化

#### 创建性能配置
```yaml
# config/performance.yaml
uvicorn:
  workers: 4
  worker_class: "uvicorn.workers.UvicornWorker"
  max_requests: 1000
  max_requests_jitter: 100
  preload_app: true
  keepalive: 5

database:
  pool_size: 20
  max_overflow: 40
  pool_pre_ping: true
  pool_recycle: 3600

redis:
  connection_pool_size: 20
  max_connections: 100
  socket_keepalive: true
  socket_keepalive_options:
    TCP_KEEPIDLE: 1
    TCP_KEEPINTVL: 3
    TCP_KEEPCNT: 5
```

## 🛡️ 安全加固

### 1. 防火墙配置

#### Windows防火墙规则
```powershell
# 允许HTTP/HTTPS流量
New-NetFirewallRule -DisplayName "Word Service HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "Word Service HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow

# 限制管理端口访问
New-NetFirewallRule -DisplayName "Word Service Admin" -Direction Inbound -Protocol TCP -LocalPort 9090,3000 -RemoteAddress ***********/24 -Action Allow
```

### 2. 用户权限配置

#### 创建专用用户
```powershell
# 创建服务账户
$Password = ConvertTo-SecureString "SecurePassword123!" -AsPlainText -Force
New-LocalUser -Name "WordServiceUser" -Password $Password -Description "Word Service Account"

# 设置目录权限
icacls "C:\opt\word-service" /grant "WordServiceUser:(OI)(CI)F"
```

## 📋 运维检查清单

### 部署前检查
- [ ] 服务器资源充足（CPU、内存、磁盘）
- [ ] Docker环境配置正确
- [ ] 数据库连接测试通过
- [ ] SSL证书配置正确
- [ ] 防火墙规则配置完成
- [ ] 备份策略制定并测试

### 部署后检查
- [ ] 应用服务正常启动
- [ ] 数据库连接正常
- [ ] 缓存服务运行正常
- [ ] 负载均衡工作正常
- [ ] SSL证书生效
- [ ] 监控告警配置完成
- [ ] 日志收集正常
- [ ] 性能指标正常

### 日常运维
- [ ] 监控服务状态
- [ ] 检查系统资源使用
- [ ] 查看错误日志
- [ ] 数据库定期备份
- [ ] 证书到期提醒
- [ ] 安全补丁更新

---

🎉 **生产环境部署完成！**

访问您的服务：https://your-domain.com
监控面板：http://your-domain.com:3000 