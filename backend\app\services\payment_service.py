import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.payment import Order, OrderCreate, PaymentStatus, PaymentMethod
from app.database import crud
from app.core.logging import logger

# 套餐对应的检测次数
PLAN_CHECKS: Dict[str, int] = {
    "basic": 3,
    "standard": 10,
    "professional": 50,
}

class PaymentService:
    """
    处理支付相关业务逻辑的服务 - 使用真实数据库
    """
    
    async def create_order(self, session: AsyncSession, user_id: str, plan_id: str, amount: float, payment_method: PaymentMethod) -> Order:
        """
        创建一个新的支付订单
        """
        try:
            # 生成订单ID
            order_id = f"order_{uuid.uuid4().hex[:12]}"
            
            # 准备订单数据
            order_data = {
                "order_id": order_id,
                "user_id": user_id,
                "plan_id": plan_id,
                "amount": amount,
                "payment_method": payment_method.value,
                "status": PaymentStatus.PENDING.value,
                "created_at": datetime.utcnow()
            }
            
            # 保存到数据库
            db_order = await crud.create_order(session, order_data)
            if not db_order:
                raise Exception("订单创建失败")
            
            # 转换为Pydantic模型
            order = Order(
                order_id=db_order["order_id"],
                user_id=db_order["user_id"],
                plan_id=db_order["plan_id"],
                amount=db_order["amount"],
                payment_method=PaymentMethod(db_order["payment_method"]),
                status=PaymentStatus(db_order["status"]),
                created_at=db_order["created_at"],
                paid_at=db_order.get("paid_at")
            )
            
            # 异步启动支付处理任务
            asyncio.create_task(self.simulate_payment_processing(order_id))
            
            logger.info(f"订单创建成功: {order_id}, 用户: {user_id}, 套餐: {plan_id}")
            return order
            
        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            raise

    async def get_order_status(self, session: AsyncSession, order_id: str) -> Optional[Order]:
        """
        获取订单的状态
        """
        try:
            db_order = await crud.get_order_by_id(session, order_id)
            if not db_order:
                return None
            
            # 转换为Pydantic模型
            order = Order(
                order_id=db_order["order_id"],
                user_id=db_order["user_id"],
                plan_id=db_order["plan_id"],
                amount=db_order["amount"],
                payment_method=PaymentMethod(db_order["payment_method"]),
                status=PaymentStatus(db_order["status"]),
                created_at=db_order["created_at"],
                paid_at=db_order.get("paid_at")
            )
            
            return order
            
        except Exception as e:
            logger.error(f"获取订单状态失败: {str(e)}")
            return None

    async def simulate_payment_processing(self, order_id: str):
        """
        模拟真实的支付处理过程
        """
        try:
            # 模拟支付处理时间（8秒）
            await asyncio.sleep(8)
            
            # 在新的数据库会话中处理支付完成
            from app.database.connection import get_db_session
            
            async for session in get_db_session():
                # 获取订单信息
                db_order = await crud.get_order_by_id(session, order_id)
                if not db_order or db_order["status"] != PaymentStatus.PENDING.value:
                    logger.warning(f"订单 {order_id} 状态异常，跳过支付处理")
                    break
                
                # 更新订单状态为已支付
                paid_at = datetime.utcnow()
                updated_order = await crud.update_order_status(
                    session, order_id, PaymentStatus.PAID.value, paid_at
                )
                
                if updated_order:
                    # 更新用户检测次数余额
                    checks_to_add = PLAN_CHECKS.get(db_order["plan_id"], 0)
                    if checks_to_add > 0:
                        updated_user = await crud.update_user_check_balance(
                            session, db_order["user_id"], checks_to_add
                        )
                        
                        if updated_user:
                            logger.info(f"✅ 订单 {order_id} 支付成功！用户 {db_order['user_id']} 增加 {checks_to_add} 次检测余额")
                        else:
                            logger.error(f"⚠️ 订单 {order_id} 支付成功，但更新用户余额失败")
                    else:
                        logger.warning(f"套餐 {db_order['plan_id']} 未配置检测次数")
                else:
                    logger.error(f"更新订单 {order_id} 支付状态失败")
                break
                    
        except Exception as e:
            logger.error(f"支付处理失败 {order_id}: {str(e)}")

    async def get_user_orders(self, session: AsyncSession, user_id: str, skip: int = 0, limit: int = 20,
                              status: Optional[str] = None, search: Optional[str] = None, 
                              date_range: Optional[str] = None) -> List[Order]:
        """
        获取用户的订单历史（支持筛选）
        """
        try:
            db_orders = await crud.get_user_orders(session, user_id, skip, limit, status, search, date_range)
            
            orders = []
            for db_order in db_orders:
                order = Order(
                    order_id=db_order["order_id"],
                    user_id=db_order["user_id"],
                    plan_id=db_order["plan_id"],
                    amount=db_order["amount"],
                    payment_method=PaymentMethod(db_order["payment_method"]),
                    status=PaymentStatus(db_order["status"]),
                    created_at=db_order["created_at"],
                    paid_at=db_order.get("paid_at")
                )
                orders.append(order)
            
            return orders
            
        except Exception as e:
            logger.error(f"获取用户订单历史失败: {str(e)}")
            return []
            
    async def get_user_orders_count(self, session: AsyncSession, user_id: str, status: Optional[str] = None, 
                                   search: Optional[str] = None, date_range: Optional[str] = None) -> int:
        """
        获取用户订单总数（支持筛选）
        """
        try:
            return await crud.count_user_orders(session, user_id, status, search, date_range)
        except Exception as e:
            logger.error(f"获取用户订单总数失败: {str(e)}")
            return 0
    
    async def cancel_order(self, session: AsyncSession, order_id: str, user_id: str) -> Optional[Order]:
        """
        取消订单（仅限待支付状态）
        """
        try:
            # 获取订单信息
            db_order = await crud.get_order_by_id(session, order_id)
            if not db_order:
                return None
            
            # 验证订单所有权
            if db_order["user_id"] != user_id:
                raise Exception("无权取消此订单")
            
            # 只能取消待支付的订单
            if db_order["status"] != PaymentStatus.PENDING.value:
                raise Exception("只能取消待支付的订单")
            
            # 更新订单状态
            updated_order = await crud.update_order_status(
                session, order_id, PaymentStatus.CANCELLED.value
            )
            
            if updated_order:
                order = Order(
                    order_id=updated_order["order_id"],
                    user_id=updated_order["user_id"],
                    plan_id=updated_order["plan_id"],
                    amount=updated_order["amount"],
                    payment_method=PaymentMethod(updated_order["payment_method"]),
                    status=PaymentStatus(updated_order["status"]),
                    created_at=updated_order["created_at"],
                    paid_at=updated_order.get("paid_at")
                )
                
                logger.info(f"订单 {order_id} 已取消")
                return order
            
            return None
            
        except Exception as e:
            logger.error(f"取消订单失败: {str(e)}")
            raise

payment_service = PaymentService() 