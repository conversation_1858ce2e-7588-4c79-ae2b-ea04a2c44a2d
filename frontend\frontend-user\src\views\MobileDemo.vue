<template>
  <BaseLayout
    title="移动端演示"
    description="展示移动端适配效果和组件"
    :breadcrumbs="[
      { text: '首页', to: '/' },
      { text: '移动端演示' }
    ]"
  >
    <!-- 设备信息卡片 -->
    <div class="mb-6">
      <BaseCard>
        <template #header>
          <h3 class="text-lg font-medium">设备信息</h3>
        </template>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">设备类型</p>
            <p class="font-medium">
              <span v-if="deviceInfo.isMobile" class="text-green-600">📱 移动设备</span>
              <span v-else-if="deviceInfo.isTablet" class="text-blue-600">📱 平板设备</span>
              <span v-else class="text-gray-600">🖥️ 桌面设备</span>
            </p>
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">屏幕尺寸</p>
            <p class="font-medium">{{ deviceInfo.width }} × {{ deviceInfo.height }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">屏幕方向</p>
            <p class="font-medium">
              <span v-if="deviceInfo.orientation === 'portrait'">📱 竖屏</span>
              <span v-else>📱 横屏</span>
            </p>
          </div>
          <div>
            <p class="text-sm text-gray-600 dark:text-gray-400">触摸支持</p>
            <p class="font-medium">
              <span v-if="deviceInfo.isTouch" class="text-green-600">✅ 支持触摸</span>
              <span v-else class="text-gray-600">❌ 不支持触摸</span>
            </p>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 移动端组件演示 -->
    <div class="space-y-6">
      <!-- 按钮演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端按钮</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <button class="mobile-btn mobile-btn-primary w-full touch-feedback">
              主要按钮
            </button>
            <button class="mobile-btn mobile-btn-secondary w-full touch-feedback">
              次要按钮
            </button>
          </div>
          <div class="space-y-3">
            <button class="mobile-btn mobile-btn-primary w-full touch-feedback" disabled>
              禁用按钮
            </button>
            <button class="mobile-btn mobile-btn-secondary w-full touch-feedback">
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12" />
              </svg>
              图标按钮
            </button>
          </div>
        </div>
      </div>

      <!-- 输入框演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端输入框</h3>
        <div class="space-y-4">
          <div class="mobile-form-group">
            <label class="mobile-form-label">普通输入框</label>
            <input 
              type="text" 
              class="mobile-input" 
              placeholder="请输入内容"
              v-model="demoText"
            >
          </div>
          <div class="mobile-form-group">
            <label class="mobile-form-label">搜索输入框</label>
            <div class="relative">
              <input 
                type="search" 
                class="mobile-input pr-10" 
                placeholder="搜索..."
                v-model="searchText"
              >
              <button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 卡片演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端卡片</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="mobile-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <div class="mobile-heading">卡片标题</div>
            <div class="mobile-subtitle mb-2">副标题信息</div>
            <div class="mobile-body">
              这是一个移动端优化的卡片组件，具有适当的内边距和触摸友好的设计。
            </div>
            <div class="flex justify-between items-center mt-4">
              <span class="text-sm text-gray-500">2024-12-19</span>
              <button class="mobile-btn mobile-btn-primary text-sm px-3 py-1">
                查看
              </button>
            </div>
          </div>
          <div class="mobile-card bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <div class="mobile-heading">统计卡片</div>
            <div class="mobile-subtitle mb-4">数据概览</div>
            <div class="grid grid-cols-2 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">1,234</div>
                <div class="text-sm text-gray-500">总数量</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">89%</div>
                <div class="text-sm text-gray-500">成功率</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端列表</h3>
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <ul class="mobile-list">
            <li class="mobile-list-item touch-feedback" @click="showAlert('点击了列表项 1')">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span class="text-blue-600 dark:text-blue-400 font-medium">1</span>
                  </div>
                  <div>
                    <div class="mobile-body font-medium">列表项标题 1</div>
                    <div class="mobile-subtitle">这是列表项的描述信息</div>
                  </div>
                </div>
                <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </li>
            <li class="mobile-list-item touch-feedback" @click="showAlert('点击了列表项 2')">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <span class="text-green-600 dark:text-green-400 font-medium">2</span>
                  </div>
                  <div>
                    <div class="mobile-body font-medium">列表项标题 2</div>
                    <div class="mobile-subtitle">这是另一个列表项的描述</div>
                  </div>
                </div>
                <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </li>
            <li class="mobile-list-item touch-feedback" @click="showAlert('点击了列表项 3')">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                    <span class="text-purple-600 dark:text-purple-400 font-medium">3</span>
                  </div>
                  <div>
                    <div class="mobile-body font-medium">列表项标题 3</div>
                    <div class="mobile-subtitle">最后一个列表项的描述</div>
                  </div>
                </div>
                <svg class="w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 网格演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端网格</h3>
        <div class="mobile-grid mobile-grid-2 md:mobile-grid-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="mobile-card text-center">
            <div class="text-3xl mb-2">📊</div>
            <div class="mobile-body font-medium">数据分析</div>
          </div>
          <div class="mobile-card text-center">
            <div class="text-3xl mb-2">📈</div>
            <div class="mobile-body font-medium">趋势图表</div>
          </div>
          <div class="mobile-card text-center">
            <div class="text-3xl mb-2">⚙️</div>
            <div class="mobile-body font-medium">设置</div>
          </div>
          <div class="mobile-card text-center">
            <div class="text-3xl mb-2">👤</div>
            <div class="mobile-body font-medium">用户中心</div>
          </div>
        </div>
      </div>

      <!-- 交互演示 -->
      <div>
        <h3 class="text-lg font-medium mb-4">移动端交互</h3>
        <div class="space-y-4">
          <button 
            @click="showNotification"
            class="mobile-btn mobile-btn-primary w-full touch-feedback"
          >
            显示通知
          </button>
          <button 
            @click="showConfirm"
            class="mobile-btn mobile-btn-secondary w-full touch-feedback"
          >
            确认对话框
          </button>
          <button 
            @click="toggleModal"
            class="mobile-btn mobile-btn-primary w-full touch-feedback"
          >
            显示模态框
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端模态框演示 -->
    <div v-if="showMobileModal" class="mobile-modal">
      <div class="mobile-modal-content">
        <div class="flex justify-between items-center mb-4">
          <h3 class="mobile-heading">移动端模态框</h3>
          <button 
            @click="toggleModal"
            class="text-gray-500 hover:text-gray-700"
          >
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="mobile-body mb-6">
          这是一个专为移动端优化的模态框组件，从底部滑入，具有安全区域适配。
        </div>
        <div class="flex space-x-3">
          <button 
            @click="toggleModal"
            class="mobile-btn mobile-btn-secondary flex-1 touch-feedback"
          >
            取消
          </button>
          <button 
            @click="toggleModal"
            class="mobile-btn mobile-btn-primary flex-1 touch-feedback"
          >
            确定
          </button>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useResponsive } from '@/utils/useResponsive'
import { useNotifications } from '@/utils/useNotifications'
import { useConfirm } from '@/utils/useConfirm'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'

// 响应式检测
const { deviceInfo } = useResponsive()

// 通知和确认
const { showNotification: showNotificationUtil } = useNotifications()
const { showConfirm: showConfirmUtil } = useConfirm()

// 演示数据
const demoText = ref('')
const searchText = ref('')
const showMobileModal = ref(false)

// 方法
const showAlert = (message: string) => {
  showNotificationUtil({
    type: 'info',
    title: '列表点击',
    message: message
  })
}

const showNotification = () => {
  showNotificationUtil({
    type: 'success',
    title: '移动端通知',
    message: '这是一个移动端优化的通知消息'
  })
}

const showConfirm = async () => {
  const result = await showConfirmUtil({
    title: '移动端确认',
    message: '这是一个移动端优化的确认对话框，是否继续？'
  })
  
  if (result) {
    showNotificationUtil({
      type: 'success',
      title: '确认成功',
      message: '您点击了确认按钮'
    })
  }
}

const toggleModal = () => {
  showMobileModal.value = !showMobileModal.value
}
</script>

<style scoped>
/* 组件特定样式 */
.mobile-demo {
  padding: 1rem;
}

/* 移动端专用样式 */
@media (max-width: 768px) {
  .mobile-demo {
    padding: 0.5rem;
  }
}
</style> 