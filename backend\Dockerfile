# ==================================================
# Word文档分析服务 - 生产级Dockerfile
# ==================================================

# 使用官方Python 3.8 Windows Server Core镜像
FROM mcr.microsoft.com/windows/servercore:ltsc2019 as base

# 设置工作目录
WORKDIR /app

# 安装Python 3.8
ADD https://www.python.org/ftp/python/3.8.10/python-3.8.10-amd64.exe python-installer.exe
RUN python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 && \
    del python-installer.exe

# 验证Python安装
RUN python --version && pip --version

# ==================================================
# 构建阶段
# ==================================================
FROM base as builder

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 升级pip并安装构建工具
RUN python -m pip install --upgrade pip setuptools wheel

# 复制依赖文件
COPY requirements.txt requirements-prod.txt ./

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-prod.txt

# ==================================================
# 生产阶段
# ==================================================
FROM base as production

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    ENVIRONMENT=production \
    LOG_LEVEL=INFO

# 创建应用用户（Windows容器中的用户管理）
RUN net user appuser /add && \
    net localgroup "Users" appuser /add

# 创建必要的目录
RUN mkdir C:\app\data && \
    mkdir C:\app\logs && \
    mkdir C:\app\config && \
    mkdir C:\app\data\uploads && \
    mkdir C:\app\data\temp && \
    mkdir C:\app\data\images && \
    mkdir C:\app\data\reports && \
    mkdir C:\app\data\backups

# 从构建阶段复制Python环境
COPY --from=builder C:\Python38 C:\Python38

# 设置Python路径
ENV PATH="C:\Python38;C:\Python38\Scripts;${PATH}"

# 复制应用代码
COPY app/ ./app/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY config.yaml ./
COPY pyproject.toml ./

# 复制启动脚本
COPY deploy/docker/start.ps1 ./
COPY deploy/docker/healthcheck.ps1 ./

# 设置权限
RUN icacls C:\app /grant appuser:F /T

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD powershell -File C:\app\healthcheck.ps1

# 暴露端口
EXPOSE 8000

# 设置用户
USER appuser

# 启动命令
CMD ["powershell", "-File", "C:\\app\\start.ps1"]

# ==================================================
# 开发阶段（可选）
# ==================================================
FROM builder as development

# 安装开发依赖
COPY requirements-dev.txt ./
RUN pip install --no-cache-dir -r requirements-dev.txt

# 复制所有代码（包括测试）
COPY . .

# 开发模式启动
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# ==================================================
# 元数据标签
# ==================================================
LABEL maintainer="Word Service Team" \
      version="1.0.0" \
      description="Word文档分析服务 - 生产容器" \
      org.opencontainers.image.title="Word Document Analysis Service" \
      org.opencontainers.image.description="基于FastAPI的Word文档智能分析服务" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="Word Service Team" \
      org.opencontainers.image.licenses="MIT" 