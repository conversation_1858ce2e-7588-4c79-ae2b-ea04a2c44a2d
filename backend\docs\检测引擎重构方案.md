# 检测引擎重构方案

> **版本**: 1.0
> **日期**: 2025-07-16
> **状态**: 方案草案

## 1. 项目目标

当前检测引擎的规则（存放于 `*.json` 文件）采用扁平的数组结构。虽然该结构在项目初期运行良好，但随着规则数量的增加，其在可读性、可维护性和可扩展性方面的缺点逐渐暴露。

本次重构的核心目标是，将现有的扁平化规则结构升级为一种**分层的、带引用机制的配置对象结构**。

旨在解决以下痛点：
- **可读性差**：所有规则混合在同一个列表中，难以快速定位和理解。
- **维护成本高**：样式（如字体、字号）等参数在多个规则中重复定义，修改时容易遗漏，不符合DRY（Don't Repeat Yourself）原则。
- **扩展性受限**：难以实现规则依赖、规则组合等更复杂的逻辑。

## 2. 方案设计：新旧结构对比

### 2.1 旧结构（扁平列表）

```json
// backend/config/rules/hbkj_bachelor_2024.json (旧)
[
    {
        "rule_id": "hbkj_format_003_level_1_title",
        "name": "一级标题格式检查",
        "parameters": {
            "font_family": "黑体",
            "font_size": 16,
            "alignment": "center",
            "bold": true
        },
        "check_function_name": "check_headings_by_level"
    },
    {
        "rule_id": "hbkj_format_004_level_2_title",
        "name": "二级标题格式检查",
        "parameters": {
            "font_family": "黑体", // <-- 重复
            "font_size": 14,
            "alignment": "left",
            "bold": true // <-- 重复
        },
        "check_function_name": "check_headings_by_level"
    }
]
```

### 2.2 新结构（分层对象，带引用）

新结构引入了`metadata`, `definitions`, `rules`三个顶级节点，并将规则按类型分组，通过`$ref`机制实现参数复用。

```json
// backend/config/rules/hbkj_bachelor_2024.json (新)
{
  "metadata": {
    "standard_id": "hbkj_bachelor_2024",
    "name": "河北科技学院学士学位论文检测标准",
    "version": "1.0.0",
    "description": "基于河北科技学院学士学位论文格式要求的检测标准。"
  },
  "definitions": {
    "properties": {
      "page_sizes": {
        "A4": { "value": "A4", "required": true, "errorMessage": "论文应使用A4纸张规格", "severity": "critical" }
      },
      "margins": {
        "top_default":    { "value": 2.5, "unit": "cm", "tolerance": 0.2, "required": true, "errorMessage": "上边距应为2.5cm", "severity": "high" },
        "bottom_default": { "value": 2.5, "unit": "cm", "tolerance": 0.2, "required": true, "errorMessage": "下边距应为2.5cm", "severity": "high" },
        "left_default":   { "value": 3.0, "unit": "cm", "tolerance": 0.2, "required": true, "errorMessage": "左边距应为3.0cm", "severity": "high" },
        "right_default":  { "value": 2.0, "unit": "cm", "tolerance": 0.2, "required": true, "errorMessage": "右边距应为2.0cm", "severity": "high" }
      },
      "font_families": {
        "chinese_body": { "value": "宋体", "alternatives": ["SimSun"], "required": true, "errorMessage": "正文中文字体应使用宋体" },
        "english_body": { "value": "Times New Roman", "required": true, "errorMessage": "正文英文字体应使用Times New Roman" },
        "heading_default": { "value": "黑体", "required": true, "errorMessage": "标题应使用黑体" }
      },
      "font_sizes": {
        "body_text": { "value": "小四", "point_size": 12, "tolerance": 0.5, "required": true, "errorMessage": "正文字号应为小四号(12pt)" },
        "level_1_title": { "value": "三号", "point_size": 16, "required": true, "errorMessage": "一级标题字号应为三号(16pt)" },
        "level_2_title": { "value": "四号", "point_size": 14, "required": true, "errorMessage": "二级标题字号应为四号(14pt)" }
      },
      "line_spacings": {
        "body_text": { "value": 1.5, "tolerance": 0.1, "required": true, "errorMessage": "正文行距应为1.5倍行距" }
      },
      "indents": {
        "body_text_first_line": { "value": 2.0, "unit": "字符", "required": true, "errorMessage": "段落首行应缩进2个字符" }
      }
    },
    "styles": {
      "page_setup_default": {
        "paper_size": { "$ref": "#/definitions/properties/page_sizes/A4" },
        "top": { "$ref": "#/definitions/properties/margins/top_default" },
        "bottom": { "$ref": "#/definitions/properties/margins/bottom_default" },
        "left": { "$ref": "#/definitions/properties/margins/left_default" },
        "right": { "$ref": "#/definitions/properties/margins/right_default" }
      },
      "body_text_default": {
        "font_family_chinese": { "$ref": "#/definitions/properties/font_families/chinese_body" },
        "font_family_english": { "$ref": "#/definitions/properties/font_families/english_body" },
        "font_size": { "$ref": "#/definitions/properties/font_sizes/body_text" },
        "line_spacing": { "$ref": "#/definitions/properties/line_spacings/body_text" },
        "first_line_indent": { "$ref": "#/definitions/properties/indents/body_text_first_line" }
      },
      "level_1_title": {
        "font_family": { "$ref": "#/definitions/properties/font_families/heading_default" },
        "font_size": { "$ref": "#/definitions/properties/font_sizes/level_1_title" },
        "alignment": { "value": "center" },
        "bold": { "value": true }
      },
      "level_2_title": {
        "font_family": { "$ref": "#/definitions/properties/font_families/heading_default" },
        "font_size": { "$ref": "#/definitions/properties/font_sizes/level_2_title" },
        "alignment": { "value": "left" },
        "bold": { "value": true }
      },
      "abstract_title": {
        "font_family": { "$ref": "#/definitions/properties/font_families/english_body" },
        "font_size": { "$ref": "#/definitions/properties/font_sizes/level_1_title" },
        "alignment": { "value": "center" },
        "bold": { "value": true }
      }
    },
    "document_structure": [
      { 
        "name": "封面", 
        "description": "论文的封面，包含标题、作者、导师等核心信息。",
        "required": true,
        "identifiers": ["河北科技学院", "学士学位论文", "题目", "学生", "姓名", "指导教师"],
        "error_messages": {
          "missing": "检测到论文缺失【封面】部分，请确保论文包含封面页。",
          "out_of_order": "检测到【封面】不在文档的起始位置。"
        }
      },
      { "name": "任务书", "required": true, "identifiers": ["任务书"] },
      { "name": "开题报告", "required": true, "identifiers": ["开题报告"] },
      { "name": "诚信声明", "required": true, "identifiers": ["诚信声明", "承诺书", "原创性声明"] },
      { "name": "版权声明", "required": true, "identifiers": ["版权声明", "版权", "知识产权", "使用授权书", "版权使用授权", "学位论文版权使用授权书"] },
      { "name": "中文摘要", "required": true, "identifiers": ["摘要", "概要"], "content_rules": [
        { "$ref": "#/rules/content/abstract_word_count" }
      ] },
      { "name": "中文关键词", "required": true, "identifiers": ["关键词", "关键字"] },
      { 
        "name": "英文摘要", 
        "required": true, 
        "identifiers": ["abstract"],
        "format_rule": { "$ref": "#/rules/format/english_abstract_title_format" }
      },
      { "name": "英文关键词", "required": true, "identifiers": ["keywords", "key words"] },
      { 
        "name": "目录", 
        "description": "论文的目录部分，通常在正文之前，列出章节标题和对应页码。",
        "required": true,
        "identifiers": ["目录", "目 录", "CONTENTS", "Contents"],
        "error_messages": {
          "missing": "检测到论文缺失【目录】部分，请根据学校要求添加。",
          "out_of_order": "检测到【目录】的顺序不正确，它应该位于【英文关键词】之后，【正文】之前。"
        }
      },
      { "name": "正文", "required": true, "identifiers": ["第", "章", "正文", "引言", "绪论"] },
      { 
        "name": "参考文献", 
        "required": true, 
        "identifiers": ["参考文献", "reference", "参考资料"],
        "content_rules": [
          { "$ref": "#/rules/content/references_item_count" }
        ]
      },
      { "name": "致谢", "required": false, "identifiers": ["致谢", "谢辞"] },
      { "name": "附录", "required": false, "identifiers": ["附录", "appendix"] }
    ]
  },
  "rules": {
    "structure": {
      "section_order": {
        "name": "论文章节结构与顺序检查",
        "description": "检查论文章节是否完整，且遵循标准定义的顺序。",
        "severity": "error",
        "check_function": "check_section_order",
        "parameters": {
          "standard_structure": { "$ref": "#/definitions/document_structure" }
        }
      },
      "cover_check": {
        "name": "封面信息检查",
        "description": "检查论文封面是否包含必需的信息。",
        "severity": "error",
        "check_function": "check_cover_elements",
        "parameters": {
          "required_keywords": ["河北科技学院", "学士学位论文", "题目", "学生", "姓名", "指导教师"]
        }
      }
    },
    "format": {
      "english_abstract_title_format": {
        "name": "英文摘要标题格式检查",
        "severity": "error",
        "check_function": "check_paragraph_format",
        "parameters": {
          "style": { "$ref": "#/definitions/styles/abstract_title" },
          "exact_text": "ABSTRACT"
        }
      },
      "level_1_title_format": {
        "name": "一级标题格式检查",
        "severity": "error",
        "check_function": "check_headings_by_level",
        "parameters": {
          "style": { "$ref": "#/definitions/styles/level_1_title" },
          "pattern": "^第[一二三四五六七八九十]+章\\s.*"
        }
      },
      "level_2_title_format": {
        "name": "二级标题格式检查",
        "severity": "error",
        "check_function": "check_headings_by_level",
        "parameters": {
          "style": { "$ref": "#/definitions/styles/level_2_title" },
          "pattern": "^\\d+\\.\\d+\\s.*"
        }
      }
    },
    "content": {
      "abstract_word_count": {
        "name": "中文摘要字数检查",
        "description": "要求中文摘要字数在300到500字之间。",
        "check_function": "check_content_length",
        "severity": "error",
        "parameters": {
          "min": 300,
          "max": 500,
          "unit": "字", // '字' or 'char'
          "errorMessage": "中文摘要字数应在300-500字之间。当前：{current_count}字。"
        }
      },
      "references_item_count": {
        "name": "参考文献条目数量检查",
        "description": "要求参考文献数量不少于10条。",
        "check_function": "check_content_length",
        "severity": "warning",
        "parameters": {
          "min": 10,
          "unit": "条",
          "errorMessage": "参考文献数量不足，应至少包含10条。当前数量：{current_count}条。"
        }
      }
    }
  }
}
```

## 3. 实施步骤

我们将分阶段进行重构，以确保平稳过渡和风险可控。

### **阶段一：规则文件迁移**
- **任务**: 将 `backend/config/rules/hbkj_bachelor_2024.json` 文件从旧的扁平数组格式完全转换为新的分层对象格式。
- **目标**: 创建一个结构清晰、包含 `definitions` 和 `$ref` 引用的新版规则文件。
- **验证**: 确保所有原始规则都已迁移，并且逻辑上等价。

### **阶段二：升级规则引擎 (`rule_engine.py`)**
- **任务**: 修改 `RuleEngine` 类，使其能够解析新的规则结构。
- **关键逻辑点**:
    1. **修改加载逻辑**: `load_rules_from_directory` 方法需要修改。不再是直接遍历JSON数组，而是加载整个JSON对象，然后递归地解析 `rules` 节点下的内容。
    2. **实现引用解析器**: 需要创建一个内部方法（例如 `_resolve_ref`），当遍历到包含`$ref`的键时，该方法能根据路径（如 `#/definitions/styles/level_1_title`）查找到 `definitions` 中对应的值，并将其替换或合并到当前参数中。
    3. **规则注册**: `register_rule` 方法需要调整，以适应从分层结构中提取规则。`rule_id` 可以由其在JSON树中的路径动态生成（如 `format.level_1_title_format`）。
- **验证**: 单元测试需要跟进，确保引擎能正确加载新规则，并能正确解析引用。

### **阶段三：适配与联调**
- **任务**: 确保分析服务 (`document_analyzer.py`) 调用规则引擎的方式保持兼容，并对可能受影响的 `check_...` 函数进行微调。
- **分析**:
    - `DocumentAnalyzer` 调用 `RuleEngine` 的方式可能需要从“执行单个规则”调整为“执行一个规则集”。
    - 检测函数接收到的 `parameters` 参数将是已经解析和合并了引用之后的结果，函数本身的逻辑可能无需大的改动。
- **验证**: 执行完整的端到端测试，使用 `test.docx` 和新的规则文件，验证分析结果与重构前完全一致。

### **阶段四：清理与文档更新**
- **任务**: 移除旧的、不再需要的代码，并更新相关文档。
- **具体操作**:
    - 清理 `RuleEngine` 中只为旧结构服务的代码。
    - 更新 `README.md` 或其他开发者文档中关于“如何添加新规则”的说明，使其反映新的结构。

## 4. 方案细化与决策

本章节旨在明确重构过程中的关键技术决策，为具体的代码实现提供清晰的指导。

### 4.1 检查函数接口契约 (Check Function Contract)

**核心原则**: **检查函数应与规则的配置结构完全解耦。**

这意味着，`RuleEngine` 作为调用方，必须承担所有 `$ref` 引用的解析工作。检查函数作为最终执行者，其接收到的 `parameters` 参数不应包含任何 `$ref` 字段，而必须是已经解析和合并完成后的、可以直接使用的Python对象。

**示例**:

对于以下的新版规则定义：
```json
// backend/config/rules/hbkj_bachelor_2024.json (新)
"level_1_title_format": {
    "name": "一级标题格式检查",
    "check_function": "check_headings_by_level",
    "parameters": {
      "style": { "$ref": "#/definitions/styles/level_1_title" },
      "pattern": "^第[一二三四五六七八九十]+章\\s.*"
    }
}
```

当 `RuleEngine` 调用 `check_headings_by_level` 函数时，传递给该函数的 `parameters` 参数**必须是**如下形态的Python字典：

```python
{
    "style": {
        "font_family": "黑体",
        "font_size": 16,
        "font_size_name": "三号",
        "alignment": "center",
        "bold": true,
        "spacing_before": 24,
        "spacing_after": 18
    },
    "pattern": "^第[一二三四五六七八九十]+章\\s.*"
}
```

**实现影响**:
- **简化检查函数**: 检查函数的开发者无需关心复杂的引用逻辑，只需聚焦于业务检查本身，降低了新规则的开发门槛。
- **强化规则引擎**: 对 `RuleEngine` 的要求更高，其内部的 `_resolve_ref` 方法需要能够递归地遍历整个 `parameters` 结构，并准确地替换所有引用。

**函数返回值规范**:
- **原则**: 为支持丰富的报告和精确的状态反馈，所有检查函数（`check_function`）的返回值**不应**是一个简单的布尔值，而必须是一个结构化的**结果对象**。
- **核心字段**:
    - `passed` (bool): 检查是否通过。
    - `message` (str): 最终呈现给用户的、完整的人性化提示信息。
    - `details` (dict): 包含检查过程中的详细数据，用于程序化处理和生成丰富的UI。
- **状态表达**: `details` 对象中应包含一个 `status` 字段，用以明确表达检查结果的状态。例如：`"达标"`, `"不足"`, `"过多"`, `"格式不符"` 等。

**示例 (`check_content_length` 返回值)**:
当检测到字数不足时，函数应返回：
```python
{
  "passed": False,
  "message": "中文摘要字数不符合要求，应在300-500字之间。当前字数：280字。",
  "details": {
    "status": "不足",  # 清晰的状态标识
    "expected": { "min": 300, "max": 500 },
    "actual": 280,
    "unit": "字"
  }
}
```
当检测通过时，函数应返回：
```python
{
  "passed": True,
  "message": "字数符合要求。",
  "details": {
    "status": "达标",
    "expected": { "min": 300, "max": 500 },
    "actual": 350,
    "unit": "字"
  }
}
```

### 4.2 引用解析器 (`$ref` Resolver) 行为规范

为确保规则引擎的健壮性和可预测性，`$ref` 解析器在加载和解析规则时必须遵循以下行为规范。

**1. 无效路径处理 (Invalid Path Handling)**
- **决策**: **快速失败 (Fail-Fast)**。
- **规范**: 如果规则文件中的 `$ref` 指向了一个不存在的路径（如 `#/definitions/styles/non_existent_style`），`RuleEngine` 必须在**加载阶段**立即中断，并抛出一个明确的配置错误（`ConfigurationError`）。引擎不应在规则配置有误的情况下启动。
- **理由**: 这是一种严格但安全的设计。静默地忽略一个损坏的规则比直接报错更危险，因为它会产生“检查已在执行”的假象。强制修复错误的配置可以从根本上保证检测结果的准确性。

**2. 循环引用检测 (Circular Reference Detection)**
- **决策**: **必须检测并禁止循环引用。**
- **规范**: 解析器在解析一个引用路径时，必须维护一个当前解析链的“堆栈”。如果在解析过程中，遇到了一个已经存在于当前堆栈中的路径，则必须认定为循环引用，并立即中断加载，抛出配置错误。
- **示例**:
  ```json
  "style_A": { "$ref": "#/definitions/styles/style_B" },
  "style_B": { "$ref": "#/definitions/styles/style_A" }
  ```
  解析器在解析 `style_A` 时，若发现其依赖链中再次出现了 `style_A`，则判定为循环引用。
- **理由**: 这是防止因配置错误导致无限递归和堆栈溢出的关键措施，能确保引擎的稳定性。

**3. 引用合并策略 (Reference Merging Strategy)**
- **决策**: **本地值覆盖引用值 (Local values override referenced values)**。
- **规范**: 当一个对象中同时包含 `$ref` 和其他本地键值对时，解析器应先加载 `$ref` 指向的完整对象，然后将本地的键值对应用（或覆盖）到这个对象上。
- **示例**:
  ```json
  "definitions": {
    "styles": {
      "base_title": { "font_family": "黑体", "font_size": 14, "bold": true }
    }
  },
  "rules": {
    "format": {
      "special_title": {
        "parameters": {
          "style": {
            "$ref": "#/definitions/styles/base_title",
            "font_family": "宋体", // 本地值，将覆盖引用的 "黑体"
            "italic": true       // 本地值，将新增到对象中
          }
        }
      }
    }
  }
  ```
  最终解析出的 `style` 参数应为：`{ "font_family": "宋体", "font_size": 14, "bold": true, "italic": true }`。
- **理由**: 此策略提供了最大的灵活性。它允许在 `definitions` 中创建可复用的“基础模板”，同时又能在具体的规则中进行微调和扩展，符合配置的特异性原则。

### 4.3 高级结构检查：识别与校验分离

您提出的“识别要松，校验要严”的需求，揭示了当前方案的一个核心挑战。为了完美解决这个问题，我们引入**“识别与校验分离”**的两步走高级方案。

**核心思想**: 将“找到章节”和“检查章节格式”这两个完全不同的任务，解耦到两个独立的机制中。

**第一步：使用 `identifiers` 进行大小写不敏感的“识别”**
- **目标**: 定位章节，不关心其具体格式。
- **规范**:
    1. `check_section_order` 等结构检查函数，在使用 `identifiers` 列表进行匹配时，必须采用**大小写不敏感**的比较方式。
    2. 为此，`identifiers` 列表中的字符串应简化为小写形式，以增强配置的可读性（如 `"abstract"` 而非 `["Abstract", "ABSTRACT"]`）。
- **作用**: 确保无论用户在文档中输入 `ABSTRACT` 还是 `abstract`，引擎都能准确地识别出这是“英文摘要”章节。

**第二步：使用 `format_rule` 进行严格的“校验”**
- **目标**: 对第一步定位到的章节，进行精确、严格的格式校验。
- **规范**:
    1. 在 `document_structure` 的条目中，引入一个可选的 `format_rule` 字段。
    2. 该字段的值必须是一个 `$ref`，指向 `rules.format` 中定义的一条具体的格式检查规则。
    3. 这条被引用的格式检查规则，可以引入 `exact_text` 之类的参数，用于进行**大小写敏感**的精确文本内容匹配。

**工作流程示例**:
1. 引擎执行 `section_order` 规则，其 `check_section_order` 函数开始工作。
2. 它读取到“英文摘要”的配置，使用其 `identifiers: ["abstract"]` ，通过大小写不敏感匹配，在文档中成功定位到了一个内容为 `Abstract` 的标题段落。
3. 接着，它发现该配置项下存在 `format_rule: { "$ref": "#/rules/format/english_abstract_title_format" }`。
4. 引擎立即触发 `english_abstract_title_format` 这条规则，并将刚刚定位到的 `Abstract` 标题段落作为检查目标。
5. 该规则的检查函数 (`check_paragraph_format`) 开始执行，它会：
    - 检查标题段落的样式是否符合 `abstract_title_style` 的定义（字体、字号、居中等）。
    - 检查标题段落的文本内容是否与参数 `exact_text: "ABSTRACT"` **完全相等**。
6. 由于 `Abstract` 不等于 `ABSTRACT`，函数将生成一个精确的格式错误：“英文摘要标题必须为全大写‘ABSTRACT’”。

**方案优势**:
- **职责单一**: “识别”和“校验”的关注点被彻底分离。
- **功能强大**: 不仅能校验大小写，还能同时校验字体、间距、对齐等所有格式属性。
- **配置清晰**: 在 `document_structure` 中，`identifiers` 回答了“它是什么”，`format_rule` 回答了“它应该长什么样”，一目了然。

### 4.4 基础格式属性的原子化定义

您提供的详细格式数据，启发我们对 `definitions` 的设计进行最终升级，引入**三层分级结构**，实现终极的配置复用（DRY）。

**核心思想**: 将最基础、不可再分的格式定义为“原子属性”（Properties），可复用的样式组合（Styles）通过引用这些原子来构建，最终规则（Rules）再引用这些样式。

**设计分层**:
1.  **`definitions.properties` (原子层)**: 定义最细粒度的、全局可复用的格式属性。每个属性都是一个自包含的对象，封装了值、错误信息、严重性等。
    ```json
    // in definitions.properties
    "font_sizes": {
      "body_text": {
        "value": "小四",
        "point_size": 12,
        "errorMessage": "字号应为小四号(12pt)"
      }
    }
    ```
2.  **`definitions.styles` (组合层)**: 通过 `$ref` 引用 `properties` 中的原子属性，将它们组合成一个有意义的、完整的样式集，如“正文样式”或“一级标题样式”。
    ```json

    // in definitions.styles
    "body_text_default": {
      "font_family_chinese": { "$ref": "#/definitions/properties/font_families/chinese_body" },
      "font_size":           { "$ref": "#/definitions/properties/font_sizes/body_text" }
      // ... more property refs
    }
    ```
3.  **`rules` (应用层)**: 最终的规则，通过引用 `styles` 中定义好的样式集来执行检查。此层的结构保持不变。
    ```json
    // in rules.format
    "body_text": {
      "check_function": "check_text_format",
      "parameters": { "$ref": "#/definitions/styles/body_text_default" }
    }
    ```

**方案优势**:
- **终极的DRY**: 例如，“宋体”这个字体只需在 `properties` 中定义一次，就可以被正文、页眉、参考文献等多个样式引用，维护成本降至最低。
- **职责清晰**: `properties` 负责“是什么”，`styles` 负责“怎么组合”，`rules` 负责“何时检查”，结构优雅，逻辑清晰。
- **强大的表达力**: 完全采纳您提供的详细格式定义，让每一条基础格式要求都包含了丰富的元数据，使引擎的反馈能力大大增强。

### 4.5 内容属性检查 (`content_rules`)

为满足对特定章节内容（如字数、条目数）的检查需求，我们引入 `content_rules` 机制。

**核心思想**: 复用 `format_rule` 的链接思想，将对章节**内容本身**的检查，解耦为一系列可复用的、独立的“内容规则”。

**设计方案**:
1.  **在 `document_structure` 中引入 `content_rules` 字段**:
    - 这是一个**数组**，允许一个章节关联多条不同的内容检查规则。
    - 数组中的每个元素都是一个 `$ref`，指向一条定义在 `rules.content` 中的具体规则。

    ```json
    // in definitions.document_structure
    "name": "中文摘要",
    "required": true,
    "identifiers": ["摘要", "概要"],
    "content_rules": [
      { "$ref": "#/rules/content/abstract_word_count" }
    ]
    ```

2.  **创建新的 `rules.content` 规则类别**:
    - 用于统一定义所有与内容相关的检查规则。
    - 这些规则可以由通用的 `check_function` 实现，通过参数来驱动具体逻辑。

    ```json
    // in rules.content
    "abstract_word_count": {
      "name": "中文摘要字数检查",
      "check_function": "check_content_length",
      "parameters": {
        "min": 300,
        "max": 500,
        "unit": "字", // '字' or 'char'
        "errorMessage": "中文摘要字数应在300-500字之间。当前：{current_count}字。"
      }
    }
    ```

**工作机制**:
- 引擎在定位到一个章节后，会检查其 `content_rules` 数组。
- 引擎会触发数组中引用的所有规则，并将当前章节的**内容**作为检查对象。
- 以 `check_content_length` 函数为例，它会根据 `parameters.unit` 的值，决定是执行“字数统计”还是“条目统计”，然后将结果与 `min`/`max` 比较，并使用 `errorMessage` 生成人性化的反馈。

**方案优势**:
- **职责清晰**: 实现了 `结构定义`、`格式规则`、`内容规则` 的完美分离。
- **极致的复用**: 一个通用的 `check_content_length` 函数就能处理所有关于数量的检查。
- **强大的扩展性**: 未来可以轻松添加如“关键词密度”、“公式数量”等更复杂的内容检查规则，而无需改动现有架构。
- **人性化反馈**: `errorMessage` 支持模板变量，能给用户提供更精确的提示。

## 5. 高级特性与架构目标

本次重构的目标不仅是优化现有结构，更是要构建一个具备下一代检测能力和高度可扩展性的引擎框架。因此，以下高级特性是本次设计的核心组成部分，需要在实现中予以支持。

### 5.1 核心检测能力扩展

- **图表和公式检测**: 引擎必须能够处理图、表、公式等非文本元素。需要设计通用的 `check_figure_properties`, `check_table_style` 等规则函数，支持检查标题位置、格式、编号连续性及正文引用。
- **参考文献与引用深度检测**: 这是学术规范检测的核心。引擎需支持 `check_citation_consistency` 规则，通过交叉比对，发现“正文已引用但未列出”和“已列出但未引用”的问题。同时，检查函数需具备 `style_guide` 参数，以支持对 `GBT-7714` 等不同引用格式的精确校验。
- **内容一致性与逻辑检测**: 引擎应支持 `check_terminology_consistency` 等规则，检查关键术语的使用是否一致、首次使用是否包含全称定义等。

### 5.2 规则的继承与覆盖 (`extends`)

为提升多标准之间的复用性，规则引擎必须支持 `extends` 机制。一个标准文件可以继承另一个“基础”标准文件，并只定义其差异化部分。引擎在加载时，需要实现递归合并逻辑。

```json
// graduate_standard.json
{
  "metadata": { "extends": "./bachelor_standard.json" },
  "definitions": { /* 只定义与本科生标准不同的部分 */ }
}
```

### 5.3 规则文件的模块化与导入 (`$import`)

为支持规则库的规模化，引擎必须支持 `$import` 机制。允许将 `definitions` 或部分规则拆分到独立的模块文件中，再按需导入。引擎加载时需处理文件依赖，构建完整的规则树。

```json
"definitions": {
  "properties": { "$import": "./common_properties.json" },
  "styles": { "$import": "./common_styles.json" }
}
```

### 5.4 条件化规则 (`conditions`)

为实现更高维度的检测逻辑，引擎必须支持条件化规则。规则中可包含 `conditions` 字段，允许引擎根据文档的元数据（如 `document.metadata.discipline`）动态判断是否执行该条规则。

## 6. 预期收益
- **高可维护性**: 通用样式和参数在 `definitions` 中统一定义，修改一处即可全局生效。
- **高可读性**: 规则按类型分组，结构清晰，易于理解和查找。
- **强扩展性**: 新结构为未来引入更复杂的功能（如规则依赖、条件执行、规则集继承等）打下了坚实的基础。
- **声明式配置**: 规则文件本身成为一份自描述的、易于人类阅读的配置文件。

---

请审阅以上方案。我们可以基于此进行讨论，确认每一个细节。 