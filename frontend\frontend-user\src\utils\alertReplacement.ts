/**
 * Alert函数替换指南
 * 使用现代化的Toast通知替代原生alert()函数
 */

import { $notify } from './useNotifications'

// ===========================================
// 🚀 基础使用方法
// ===========================================

/**
 * 基础通知方法
 * 替代原生 alert() 函数
 */

// ✅ 新的写法
export const showNotification = {
  // 成功通知
  success: (message: string) => $notify.success(message),
  
  // 错误通知  
  error: (message: string) => $notify.error(message),
  
  // 警告通知
  warning: (message: string) => $notify.warning(message),
  
  // 信息通知
  info: (message: string) => $notify.info(message),
  
  // 通用alert替代
  alert: (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => 
    $notify.alert(message, type)
}

// ===========================================
// 📝 替换示例对照表
// ===========================================

/**
 * 原生alert替换示例
 */

// ❌ 原来的写法
// alert('登录成功！')
// alert('登录失败，请检查用户名和密码')
// alert('请填写完整信息')
// alert('正在处理，请稍候...')

// ✅ 新的写法
// showNotification.success('登录成功！')
// showNotification.error('登录失败，请检查用户名和密码')
// showNotification.warning('请填写完整信息')
// showNotification.info('正在处理，请稍候...')

// ===========================================
// 🎯 高级用法示例
// ===========================================

/**
 * 带标题的通知
 */
export const showAdvancedNotification = {
  success: (message: string, title = '操作成功') => 
    $notify.success(message, { title }),
    
  error: (message: string, title = '操作失败') => 
    $notify.error(message, { title }),
    
  warning: (message: string, title = '注意') => 
    $notify.warning(message, { title }),
    
  info: (message: string, title = '提示') => 
    $notify.info(message, { title })
}

/**
 * 自定义持续时间的通知
 */
export const showTimedNotification = {
  // 短暂通知 (2秒)
  brief: (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
    $notify[type](message, { duration: 2000 }),
    
  // 长时间通知 (8秒)
  long: (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
    $notify[type](message, { duration: 8000 }),
    
  // 永久通知 (需要手动关闭)
  persistent: (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') =>
    $notify[type](message, { duration: 0, persistent: true })
}

// ===========================================
// 🔄 批量替换脚本
// ===========================================

/**
 * 页面级别的alert替换方法
 * 在Vue组件中使用
 */
export const useAlertReplacement = () => {
  // 在组件的setup中使用
  const notify = {
    success: (message: string) => showNotification.success(message),
    error: (message: string) => showNotification.error(message),
    warning: (message: string) => showNotification.warning(message),
    info: (message: string) => showNotification.info(message),
    
    // 兼容原有alert用法
    alert: (message: string) => showNotification.info(message)
  }
  
  return notify
}

// ===========================================
// 📋 具体页面替换示例
// ===========================================

/**
 * 登录页面替换示例
 */
export const loginPageExample = {
  // ❌ 原来的写法
  oldWay: () => {
    // alert('登录失败：' + (error as Error).message)
    // alert('请联系系统管理员重置密码')
    // alert('技术支持：<EMAIL>')
  },
  
  // ✅ 新的写法
  newWay: (error?: Error) => {
    showNotification.error(`登录失败：${error?.message || '未知错误'}`)
    showNotification.warning('请联系系统管理员重置密码')
    showNotification.info('技术支持：<EMAIL>')
  }
}

/**
 * 订单页面替换示例
 */
export const orderPageExample = {
  // ❌ 原来的写法
  oldWay: () => {
    // alert('正在跳转到支付页面...')
    // alert('支付成功！订单已完成')
    // alert(`订单 ${orderNumber} 已取消`)
  },
  
  // ✅ 新的写法  
  newWay: (orderNumber?: string) => {
    showNotification.info('正在跳转到支付页面...', { duration: 2000 })
    showNotification.success('支付成功！订单已完成')
    if (orderNumber) {
      showNotification.warning(`订单 ${orderNumber} 已取消`)
    }
  }
}

/**
 * 用户管理页面替换示例
 */
export const userManagementExample = {
  // ❌ 原来的写法
  oldWay: () => {
    // alert('请填写完整信息')
    // alert('批量操作已完成')
    // alert('正在导出选中用户数据...')
  },
  
  // ✅ 新的写法
  newWay: () => {
    showNotification.warning('请填写完整信息')
    showNotification.success('批量操作已完成')
    showNotification.info('正在导出选中用户数据...', { duration: 6000 })
  }
}

// ===========================================
// 🛠️ 实用工具函数
// ===========================================

/**
 * API请求结果通知
 */
export const apiNotification = {
  success: (message = '操作成功') => showNotification.success(message),
  error: (error: any) => {
    const message = error?.response?.data?.message || error?.message || '操作失败'
    showNotification.error(message)
  },
  loading: (message = '正在处理...') => showNotification.info(message, { duration: 1000 })
}

/**
 * 表单验证通知
 */
export const formNotification = {
  required: (field: string) => showNotification.warning(`${field}不能为空`),
  invalid: (field: string) => showNotification.warning(`${field}格式不正确`),
  success: (action = '保存') => showNotification.success(`${action}成功`)
}

/**
 * 文件操作通知
 */
export const fileNotification = {
  uploading: () => showNotification.info('正在上传文件...', { duration: 2000 }),
  uploaded: () => showNotification.success('文件上传成功'),
  uploadError: () => showNotification.error('文件上传失败'),
  downloading: () => showNotification.info('正在下载文件...', { duration: 2000 }),
  downloaded: () => showNotification.success('文件下载完成')
}

// ===========================================
// 📖 使用指南
// ===========================================

/**
 * 在Vue组件中的使用方法：
 * 
 * 1. 导入替换函数：
 *    import { showNotification } from '@/utils/alertReplacement'
 * 
 * 2. 替换原有alert：
 *    // alert('成功') → showNotification.success('成功')
 *    // alert('错误') → showNotification.error('错误')
 * 
 * 3. 使用composable：
 *    const notify = useAlertReplacement()
 *    notify.success('操作成功')
 * 
 * 4. 高级用法：
 *    showAdvancedNotification.success('保存成功', '数据更新')
 *    showTimedNotification.brief('临时提示')
 */ 