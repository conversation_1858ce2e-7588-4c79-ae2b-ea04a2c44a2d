/**
 * Word文档分析服务 - 主要JavaScript文件
 * 包含工具函数、组件库和基础功能
 */

// 全局应用对象
window.WordService = {
    // 配置信息
    config: {
        apiBaseUrl: 'http://localhost:8000/api/v1',
        version: '1.0.0',
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedFileTypes: ['doc', 'docx'],
        notificationDuration: 3000
    },
    
    // 工具函数
    utils: {},
    
    // 组件
    components: {},
    
    // 数据模拟
    mockData: {},
    
    // 状态管理
    state: {
        user: null,
        currentPage: '',
        documents: [],
        tasks: []
    }
};

// ============= 工具函数 =============
WordService.utils = {
    /**
     * 格式化日期
     */
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 获取文件扩展名
     */
    getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
    },
    
    /**
     * 验证文件类型
     */
    validateFileType(file) {
        const extension = this.getFileExtension(file.name);
        return WordService.config.allowedFileTypes.includes(extension);
    },
    
    /**
     * 验证文件大小
     */
    validateFileSize(file) {
        return file.size <= WordService.config.maxFileSize;
    },
    
    /**
     * 生成随机ID
     */
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    },
    
    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 深拷贝对象
     */
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    },
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = WordService.config.notificationDuration) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, duration);
    }
};

// ============= 组件库 =============
WordService.components = {
    /**
     * 模态框组件
     */
    Modal: {
        show(content, options = {}) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${options.title || '提示'}</h3>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="WordService.components.Modal.hide()">取消</button>
                        <button class="btn btn-primary" onclick="WordService.components.Modal.confirm()">${options.confirmText || '确定'}</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            setTimeout(() => modal.classList.add('show'), 100);
            
            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hide();
                }
            });
            
            this.currentModal = modal;
            this.onConfirm = options.onConfirm;
        },
        
        hide() {
            if (this.currentModal) {
                this.currentModal.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(this.currentModal);
                    this.currentModal = null;
                }, 200);
            }
        },
        
        confirm() {
            if (this.onConfirm) {
                this.onConfirm();
            }
            this.hide();
        }
    },
    
    /**
     * 文件上传组件
     */
    FileUpload: {
        init(element, options = {}) {
            const dropZone = element;
            const fileInput = dropZone.querySelector('input[type="file"]');
            
            // 拖拽事件
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });
            
            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });
            
            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                this.handleFiles(files, options);
            });
            
            // 点击选择文件
            dropZone.addEventListener('click', () => {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                this.handleFiles(files, options);
            });
        },
        
        handleFiles(files, options) {
            const validFiles = files.filter(file => {
                if (!WordService.utils.validateFileType(file)) {
                    WordService.utils.showNotification(`文件 ${file.name} 格式不支持，请选择Word文档`, 'error');
                    return false;
                }
                if (!WordService.utils.validateFileSize(file)) {
                    WordService.utils.showNotification(`文件 ${file.name} 超过大小限制（${WordService.utils.formatFileSize(WordService.config.maxFileSize)}）`, 'error');
                    return false;
                }
                return true;
            });
            
            if (validFiles.length > 0 && options.onFileSelect) {
                options.onFileSelect(validFiles);
            }
        }
    },
    
    /**
     * 数据表格组件
     */
    DataTable: {
        render(container, data, columns, options = {}) {
            const table = document.createElement('table');
            table.className = 'data-table';
            
            // 表头
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            columns.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col.title;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);
            
            // 表体
            const tbody = document.createElement('tbody');
            if (data.length === 0) {
                const emptyRow = document.createElement('tr');
                const emptyCell = document.createElement('td');
                emptyCell.colSpan = columns.length;
                emptyCell.className = 'empty-state';
                emptyCell.innerHTML = '<div class="empty-state-icon">📄</div><p>暂无数据</p>';
                emptyRow.appendChild(emptyCell);
                tbody.appendChild(emptyRow);
            } else {
                data.forEach(item => {
                    const row = document.createElement('tr');
                    columns.forEach(col => {
                        const td = document.createElement('td');
                        if (col.render) {
                            td.innerHTML = col.render(item[col.key], item);
                        } else {
                            td.textContent = item[col.key] || '';
                        }
                        row.appendChild(td);
                    });
                    tbody.appendChild(row);
                });
            }
            table.appendChild(tbody);
            
            container.innerHTML = '';
            container.appendChild(table);
        }
    },
    
    /**
     * 图表组件
     */
    Charts: {
        renderLineChart(canvas, data, options = {}) {
            const ctx = canvas.getContext('2d');
            const { width, height } = canvas;
            const padding = 40;
            
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            
            if (!data.length) return;
            
            // 计算数据范围
            const maxValue = Math.max(...data.map(d => d.value));
            const minValue = Math.min(...data.map(d => d.value));
            const valueRange = maxValue - minValue || 1;
            
            // 绘制坐标轴
            ctx.strokeStyle = '#e5e7eb';
            ctx.lineWidth = 1;
            
            // Y轴
            ctx.beginPath();
            ctx.moveTo(padding, padding);
            ctx.lineTo(padding, height - padding);
            ctx.stroke();
            
            // X轴
            ctx.beginPath();
            ctx.moveTo(padding, height - padding);
            ctx.lineTo(width - padding, height - padding);
            ctx.stroke();
            
            // 绘制数据线
            if (data.length > 1) {
                ctx.strokeStyle = '#3b82f6';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                data.forEach((point, index) => {
                    const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
                    const y = height - padding - ((point.value - minValue) / valueRange) * (height - 2 * padding);
                    
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                ctx.stroke();
                
                // 绘制数据点
                ctx.fillStyle = '#3b82f6';
                data.forEach((point, index) => {
                    const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
                    const y = height - padding - ((point.value - minValue) / valueRange) * (height - 2 * padding);
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    ctx.fill();
                });
            }
        },
        
        renderPieChart(canvas, data, options = {}) {
            const ctx = canvas.getContext('2d');
            const { width, height } = canvas;
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) / 2 - 20;
            
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            
            if (!data.length) return;
            
            const total = data.reduce((sum, item) => sum + item.value, 0);
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
            
            let currentAngle = -Math.PI / 2;
            
            data.forEach((item, index) => {
                const sliceAngle = (item.value / total) * 2 * Math.PI;
                
                // 绘制扇形
                ctx.fillStyle = colors[index % colors.length];
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fill();
                
                // 绘制标签
                const labelAngle = currentAngle + sliceAngle / 2;
                const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
                const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${item.label}`, labelX, labelY);
                
                currentAngle += sliceAngle;
            });
        }
    }
};

// ============= 模拟数据 =============
WordService.mockData = {
    user: {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        registerTime: '2024-01-01T10:00:00Z',
        documentsCount: 15,
        tasksCount: 8
    },
    
    documents: [
        {
            id: 1,
            filename: '学术论文格式检查.docx',
            fileSize: 2048576,
            uploadTime: '2024-01-15T14:30:00Z',
            status: 'completed',
            analysisType: 'paper_check',
            progress: 100,
            issues: 3
        },
        {
            id: 2,
            filename: '研究报告.doc',
            fileSize: 1536000,
            uploadTime: '2024-01-15T15:45:00Z',
            status: 'processing',
            analysisType: 'format_check',
            progress: 65,
            issues: 0
        },
        {
            id: 3,
            filename: '技术文档.docx',
            fileSize: 3072000,
            uploadTime: '2024-01-15T16:20:00Z',
            status: 'pending',
            analysisType: 'structure_check',
            progress: 0,
            issues: 0
        }
    ],
    
    tasks: [
        {
            id: 101,
            documentId: 1,
            type: 'paper_check',
            status: 'completed',
            createTime: '2024-01-15T14:30:00Z',
            completeTime: '2024-01-15T14:35:00Z',
            progress: 100
        },
        {
            id: 102,
            documentId: 2,
            type: 'format_check',
            status: 'processing',
            createTime: '2024-01-15T15:45:00Z',
            completeTime: null,
            progress: 65
        }
    ],
    
    dashboardStats: {
        totalDocuments: 15,
        newDocumentsThisMonth: 5,
        completedTasks: 12,
        processingTasks: 2,
        severgeIssues: 2,
        generalIssues: 8,
        warningIssues: 15,
        storageUsed: 25.6, // MB
        storageTotal: 100 // MB
    },
    
    chartData: {
        uploadTrend: [
            { label: '01-01', value: 2 },
            { label: '01-08', value: 3 },
            { label: '01-15', value: 5 },
            { label: '01-22', value: 4 },
            { label: '01-29', value: 6 }
        ],
        analysisTypes: [
            { label: '论文检测', value: 8 },
            { label: '格式检查', value: 5 },
            { label: '结构分析', value: 2 }
        ],
        issueTypes: [
            { label: '严重', value: 2 },
            { label: '一般', value: 8 },
            { label: '提示', value: 15 }
        ]
    }
};

// ============= 路由和页面管理 =============
WordService.router = {
    currentRoute: '',
    
    init() {
        // 监听浏览器前进后退
        window.addEventListener('popstate', this.handleRouteChange.bind(this));
        
        // 初始路由
        this.handleRouteChange();
    },
    
    navigateTo(route) {
        history.pushState(null, '', route);
        this.handleRouteChange();
    },
    
    handleRouteChange() {
        const path = window.location.pathname;
        this.currentRoute = path;
        WordService.state.currentPage = path;
        
        // 这里可以添加页面特定的初始化逻辑
        console.log('当前页面:', path);
    }
};

// ============= 页面特定功能 =============
WordService.pages = {
    // 文档上传页面
    upload: {
        init() {
            const uploadZone = document.querySelector('.upload-zone');
            if (uploadZone) {
                WordService.components.FileUpload.init(uploadZone, {
                    onFileSelect: this.handleFileUpload.bind(this)
                });
            }
        },
        
        handleFileUpload(files) {
            files.forEach(file => {
                console.log('上传文件:', file.name);
                WordService.utils.showNotification(`开始上传 ${file.name}`, 'info');
                
                // 模拟上传进度
                this.simulateUpload(file);
            });
        },
        
        simulateUpload(file) {
            let progress = 0;
            const progressBar = document.querySelector('.progress-bar');
            
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    WordService.utils.showNotification(`${file.name} 上传完成`, 'success');
                }
                
                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
            }, 200);
        }
    },
    
    // 仪表盘页面
    dashboard: {
        init() {
            this.renderStats();
            this.renderCharts();
        },
        
        renderStats() {
            const stats = WordService.mockData.dashboardStats;
            
            // 更新统计卡片
            this.updateStatCard('total-documents', stats.totalDocuments);
            this.updateStatCard('new-documents', stats.newDocumentsThisMonth);
            this.updateStatCard('completed-tasks', stats.completedTasks);
            this.updateStatCard('processing-tasks', stats.processingTasks);
        },
        
        updateStatCard(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        },
        
        renderCharts() {
            // 上传趋势图
            const trendCanvas = document.getElementById('upload-trend-chart');
            if (trendCanvas) {
                WordService.components.Charts.renderLineChart(
                    trendCanvas, 
                    WordService.mockData.chartData.uploadTrend
                );
            }
            
            // 分析类型饼图
            const typeCanvas = document.getElementById('analysis-type-chart');
            if (typeCanvas) {
                WordService.components.Charts.renderPieChart(
                    typeCanvas,
                    WordService.mockData.chartData.analysisTypes
                );
            }
        }
    },
    
    // 文档列表页面
    documents: {
        init() {
            this.renderDocumentList();
        },
        
        renderDocumentList() {
            const container = document.getElementById('document-list');
            if (!container) return;
            
            const columns = [
                { key: 'filename', title: '文件名' },
                { 
                    key: 'fileSize', 
                    title: '文件大小',
                    render: (value) => WordService.utils.formatFileSize(value)
                },
                {
                    key: 'uploadTime',
                    title: '上传时间',
                    render: (value) => WordService.utils.formatDate(value, 'MM-DD HH:mm')
                },
                {
                    key: 'status',
                    title: '状态',
                    render: (value) => {
                        const statusMap = {
                            'pending': '<span class="status-badge status-pending">待处理</span>',
                            'processing': '<span class="status-badge status-processing">处理中</span>',
                            'completed': '<span class="status-badge status-completed">已完成</span>',
                            'failed': '<span class="status-badge status-failed">失败</span>'
                        };
                        return statusMap[value] || value;
                    }
                },
                {
                    key: 'id',
                    title: '操作',
                    render: (value, item) => `
                        <button class="btn btn-sm btn-primary" onclick="WordService.pages.documents.viewDocument(${value})">查看</button>
                        <button class="btn btn-sm btn-danger" onclick="WordService.pages.documents.deleteDocument(${value})">删除</button>
                    `
                }
            ];
            
            WordService.components.DataTable.render(
                container,
                WordService.mockData.documents,
                columns
            );
        },
        
        viewDocument(id) {
            WordService.utils.showNotification(`查看文档 ${id}`, 'info');
            // 这里可以跳转到文档详情页面
        },
        
        deleteDocument(id) {
            WordService.components.Modal.show(
                '确定要删除这个文档吗？删除后无法恢复。',
                {
                    title: '确认删除',
                    confirmText: '删除',
                    onConfirm: () => {
                        WordService.utils.showNotification('文档已删除', 'success');
                        // 这里可以从数据中移除文档并重新渲染
                    }
                }
            );
        }
    }
};

// ============= 初始化 =============
document.addEventListener('DOMContentLoaded', function() {
    // 初始化路由
    WordService.router.init();
    
    // 根据当前页面初始化对应功能
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
    
    if (WordService.pages[currentPage] && WordService.pages[currentPage].init) {
        WordService.pages[currentPage].init();
    }
    
    console.log('WordService initialized for page:', currentPage);
});

// 导出到全局
window.WordService = WordService; 