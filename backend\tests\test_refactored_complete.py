"""
重构后检测引擎的完整测试用例

该测试文件验证重构后的检测引擎与重构前的结果保持一致，
并验证新功能的正确性。
"""

import pytest
import asyncio
import time
from pathlib import Path

from app.checkers.rule_engine import RuleEngine
from app.services.document_analyzer import CHECK_FUNCTIONS
from app.services.document_processor import DocumentData
from app.models.check_result import CheckResult, CheckSeverity


class TestRefactoredEngineComplete:
    """重构后检测引擎的完整测试"""

    @pytest.fixture
    def rule_engine(self):
        """规则引擎fixture"""
        engine = RuleEngine(CHECK_FUNCTIONS)
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        if Path(rule_file).exists():
            engine.load_rules_from_file(rule_file)
        return engine

    @pytest.fixture
    def comprehensive_document(self):
        """全面的测试文档"""
        return DocumentData(
            file_path="test_comprehensive.docx",
            doc_info={
                "page_count": 25,
                "word_count": 15000,
                "creation_time": "2024-01-01T00:00:00",
                "page_setup": {
                    "paper_size": "A4",
                    "margin_top": "2.5cm",
                    "margin_bottom": "2.5cm"
                }
            },
            content_stats={
                "total_paragraphs": 100,
                "total_headings": 15,
                "total_tables": 8,
                "total_images": 5
            },
            elements=[
                # 完整的文档结构
                {"type": "paragraph", "text": "河北科技学院", "style": {"font_family": "宋体", "font_size": 22}},
                {"type": "paragraph", "text": "学士学位论文", "style": {"font_family": "宋体", "font_size": 22}},
                {"type": "paragraph", "text": "任务书", "style": {"font_family": "黑体", "font_size": 16}},
                {"type": "paragraph", "text": "开题报告", "style": {"font_family": "黑体", "font_size": 16}},
                {"type": "paragraph", "text": "诚信声明", "style": {"font_family": "黑体", "font_size": 16}},
                {"type": "paragraph", "text": "版权声明", "style": {"font_family": "黑体", "font_size": 16}},
                
                # 摘要部分
                {"type": "heading", "text": "摘要", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True, "spacing_before": 24, "spacing_after": 18}},
                {"type": "paragraph", "text": "本文研究了基于深度学习的图像识别技术。通过分析卷积神经网络的结构特点，提出了一种改进的图像识别算法。实验结果表明，该算法在准确率和效率方面都有显著提升。本研究为图像识别技术的发展提供了新的思路和方法，具有重要的理论意义和实用价值。该研究不仅在理论上有所突破，在实际应用中也具有广阔的前景。通过大量的实验验证，证明了所提出算法的有效性和可靠性。本文的主要贡献包括：提出了新的网络结构、改进了训练方法、优化了参数设置等。这些改进使得算法在多个数据集上都取得了优异的性能表现。", "style": {"font_family": "宋体", "font_size": 12, "alignment": "justify", "line_spacing": 1.5, "first_line_indent": 24}},
                {"type": "paragraph", "text": "关键词：深度学习；图像识别；卷积神经网络；算法优化；计算机视觉", "style": {"font_family": "宋体", "font_size": 12}},
                
                # 英文摘要
                {"type": "paragraph", "text": "ABSTRACT", "style": {"font_family": "Times New Roman", "font_size": 16, "bold": True, "alignment": "center"}},
                {"type": "paragraph", "text": "This paper studies image recognition technology based on deep learning.", "style": {"font_family": "Times New Roman", "font_size": 12}},
                {"type": "paragraph", "text": "Keywords: Deep Learning; Image Recognition; Convolutional Neural Network", "style": {"font_family": "Times New Roman", "font_size": 12}},
                
                # 目录
                {"type": "heading", "text": "目录", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True, "spacing_before": 24, "spacing_after": 18}},
                
                # 正文章节
                {"type": "heading", "text": "第一章 绪论", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True, "spacing_before": 24, "spacing_after": 18}},
                {"type": "heading", "text": "1.1 研究背景", "level": 2, "style": {"font_family": "黑体", "font_size": 14, "alignment": "left", "bold": True, "spacing_before": 12, "spacing_after": 6}},
                {"type": "paragraph", "text": "随着人工智能技术的快速发展，图像识别技术已经成为计算机视觉领域的重要研究方向。", "style": {"font_family": "宋体", "font_size": 12, "alignment": "justify", "line_spacing": 1.5, "first_line_indent": 24}},
                
                {"type": "heading", "text": "第二章 相关技术综述", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True, "spacing_before": 24, "spacing_after": 18}},
                {"type": "heading", "text": "2.1 深度学习基础", "level": 2, "style": {"font_family": "黑体", "font_size": 14, "alignment": "left", "bold": True, "spacing_before": 12, "spacing_after": 6}},
                
                # 参考文献（10条以上）
                {"type": "heading", "text": "参考文献", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True}},
                {"type": "paragraph", "text": "[1] LeCun Y, Bengio Y, Hinton G. Deep learning[J]. Nature, 2015, 521(7553): 436-444.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[2] Krizhevsky A, Sutskever I, Hinton G E. ImageNet classification with deep convolutional neural networks[J]. Communications of the ACM, 2017, 60(6): 84-90.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[3] He K, Zhang X, Ren S, et al. Deep residual learning for image recognition[C]//Proceedings of the IEEE conference on computer vision and pattern recognition. 2016: 770-778.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[4] Simonyan K, Zisserman A. Very deep convolutional networks for large-scale image recognition[J]. arXiv preprint arXiv:1409.1556, 2014.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[5] Szegedy C, Liu W, Jia Y, et al. Going deeper with convolutions[C]//Proceedings of the IEEE conference on computer vision and pattern recognition. 2015: 1-9.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[6] Goodfellow I, Bengio Y, Courville A. Deep learning[M]. MIT press, 2016.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[7] Russakovsky O, Deng J, Su H, et al. Imagenet large scale visual recognition challenge[J]. International journal of computer vision, 2015, 115(3): 211-252.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[8] Redmon J, Divvala S, Girshick R, et al. You only look once: Unified, real-time object detection[C]//Proceedings of the IEEE conference on computer vision and pattern recognition. 2016: 779-788.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[9] Long J, Shelhamer E, Darrell T. Fully convolutional networks for semantic segmentation[C]//Proceedings of the IEEE conference on computer vision and pattern recognition. 2015: 3431-3440.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[10] Ronneberger O, Fischer P, Brox T. U-net: Convolutional networks for biomedical image segmentation[C]//International Conference on Medical image computing and computer-assisted intervention. Springer, 2015: 234-241.", "style": {"font_family": "宋体", "font_size": 12}},
                {"type": "paragraph", "text": "[11] Huang G, Liu Z, Van Der Maaten L, et al. Densely connected convolutional networks[C]//Proceedings of the IEEE conference on computer vision and pattern recognition. 2017: 4700-4708.", "style": {"font_family": "宋体", "font_size": 12}},
                
                # 致谢
                {"type": "heading", "text": "致谢", "level": 1, "style": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": True}},
                {"type": "paragraph", "text": "感谢导师的悉心指导和同学们的帮助支持。", "style": {"font_family": "宋体", "font_size": 12, "alignment": "justify", "line_spacing": 1.5, "first_line_indent": 24}}
            ]
        )

    @pytest.mark.asyncio
    async def test_complete_detection_pipeline(self, rule_engine, comprehensive_document):
        """测试完整的检测流程"""
        if not rule_engine.rules:
            pytest.skip("规则文件未加载")
        
        # 执行检测
        start_time = time.perf_counter()
        results = await rule_engine.execute_check(comprehensive_document)
        execution_time = time.perf_counter() - start_time
        
        # 验证基本结果
        assert isinstance(results, list)
        assert len(results) > 0
        assert all(isinstance(r, CheckResult) for r in results)
        
        # 验证性能
        assert execution_time < 1.0, f"检测执行时间过长: {execution_time:.3f}秒"
        
        # 统计结果
        passed_count = sum(1 for r in results if r.passed)
        failed_count = len(results) - passed_count
        
        print(f"\n完整检测结果:")
        print(f"  总规则数: {len(results)}")
        print(f"  通过数: {passed_count}")
        print(f"  失败数: {failed_count}")
        print(f"  通过率: {passed_count/len(results)*100:.1f}%")
        print(f"  执行时间: {execution_time:.3f}秒")

    def test_structure_check_functionality(self, rule_engine, comprehensive_document):
        """测试结构检查功能"""
        if not rule_engine.rules:
            pytest.skip("规则文件未加载")
        
        # 测试章节顺序检查
        from app.services.document_analyzer import check_section_order
        
        params = {
            "rule_id": "test.structure",
            "rule_name": "测试结构检查",
            "standard_structure": [
                {"name": "封面", "required": True, "identifiers": ["河北科技学院", "学士学位论文"]},
                {"name": "摘要", "required": True, "identifiers": ["摘要"]},
                {"name": "目录", "required": True, "identifiers": ["目录"]},
                {"name": "正文", "required": True, "identifiers": ["第", "章"]},
                {"name": "参考文献", "required": True, "identifiers": ["参考文献"]},
                {"name": "致谢", "required": False, "identifiers": ["致谢"]}
            ]
        }
        
        result = check_section_order(comprehensive_document, params)
        
        assert isinstance(result, CheckResult)
        assert result.rule_id == "test.structure"
        assert result.rule_name == "测试结构检查"
        assert isinstance(result.passed, bool)
        
        # 验证详细信息
        if hasattr(result, 'details') and result.details:
            assert "identified_sections" in result.details
            identified = result.details["identified_sections"]
            assert isinstance(identified, list)
            print(f"\n识别到的章节: {identified}")

    def test_content_check_functionality(self, rule_engine, comprehensive_document):
        """测试内容检查功能"""
        from app.services.document_analyzer import check_content_length, check_abstract_and_keywords
        
        # 测试字数统计
        params = {
            "rule_id": "test.content.length",
            "rule_name": "测试字数检查",
            "min": 300,
            "max": 500,
            "unit": "字",
            "errorMessage": "字数应在{min}-{max}字之间。当前：{current_count}字。"
        }
        
        result = check_content_length(comprehensive_document, params)
        
        assert isinstance(result, CheckResult)
        assert hasattr(result, 'details')
        if result.details:
            assert "current_count" in result.details
            assert "unit" in result.details
            print(f"\n字数统计结果: {result.details['current_count']}{result.details['unit']}")
        
        # 测试摘要关键词检查
        abstract_params = {
            "rule_id": "test.abstract",
            "rule_name": "测试摘要检查",
            "min_keywords": 3,
            "max_keywords": 8
        }
        
        abstract_result = check_abstract_and_keywords(comprehensive_document, abstract_params)
        
        assert isinstance(abstract_result, CheckResult)
        print(f"\n摘要检查结果: {abstract_result.message}")

    def test_format_check_functionality(self, rule_engine, comprehensive_document):
        """测试格式检查功能"""
        from app.services.document_analyzer import (
            check_headings_by_level, 
            check_text_format, 
            check_paragraph_format,
            check_references_format
        )
        
        # 测试标题格式检查
        heading_params = {
            "rule_id": "test.format.heading",
            "rule_name": "测试标题格式",
            "level": 1,
            "style": {
                "font_family": {"value": "黑体", "errorMessage": "一级标题字体应为黑体"},
                "font_size": {"value": 16, "tolerance": 0.5, "errorMessage": "一级标题字号应为16pt"},
                "alignment": {"value": "center", "errorMessage": "一级标题应居中对齐"},
                "bold": {"value": True, "errorMessage": "一级标题应加粗"}
            }
        }
        
        heading_result = check_headings_by_level(comprehensive_document, heading_params)
        
        assert isinstance(heading_result, CheckResult)
        print(f"\n标题格式检查结果: {heading_result.message}")
        
        # 测试正文格式检查
        text_params = {
            "rule_id": "test.format.text",
            "rule_name": "测试正文格式",
            "style": {
                "font_family": {"value": "宋体"},
                "font_size": {"value": 12},
                "line_spacing": {"value": 1.5}
            }
        }
        
        text_result = check_text_format(comprehensive_document, text_params)
        
        assert isinstance(text_result, CheckResult)
        print(f"\n正文格式检查结果: {text_result.message}")
        
        # 测试参考文献格式检查
        ref_result = check_references_format(comprehensive_document, {})
        
        assert isinstance(ref_result, CheckResult)
        print(f"\n参考文献检查结果: {ref_result.message}")

    def test_error_handling_robustness(self, rule_engine):
        """测试错误处理的健壮性"""
        from app.services.document_analyzer import CHECK_FUNCTIONS
        
        # 测试空文档数据
        empty_doc = DocumentData(
            file_path="empty.docx",
            doc_info={},
            content_stats={},
            elements=[]
        )
        
        # 所有检查函数都应该能处理空文档而不崩溃
        for func_name, func in CHECK_FUNCTIONS.items():
            try:
                result = func(empty_doc, {})
                assert isinstance(result, CheckResult)
                print(f"✅ {func_name}: 正确处理空文档")
            except Exception as e:
                pytest.fail(f"函数 {func_name} 处理空文档时出错: {e}")

    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, rule_engine, comprehensive_document):
        """测试性能基准"""
        if not rule_engine.rules:
            pytest.skip("规则文件未加载")
        
        # 多次执行测试性能稳定性
        execution_times = []
        
        for i in range(5):
            start_time = time.perf_counter()
            results = await rule_engine.execute_check(comprehensive_document)
            execution_time = time.perf_counter() - start_time
            execution_times.append(execution_time)
        
        avg_time = sum(execution_times) / len(execution_times)
        max_time = max(execution_times)
        min_time = min(execution_times)
        
        print(f"\n性能基准测试结果:")
        print(f"  平均执行时间: {avg_time:.3f}秒")
        print(f"  最大执行时间: {max_time:.3f}秒")
        print(f"  最小执行时间: {min_time:.3f}秒")
        print(f"  时间变异系数: {(max_time - min_time) / avg_time * 100:.1f}%")
        
        # 性能要求
        assert avg_time < 0.5, f"平均执行时间过长: {avg_time:.3f}秒"
        assert max_time < 1.0, f"最大执行时间过长: {max_time:.3f}秒"

    def test_backward_compatibility(self, rule_engine, comprehensive_document):
        """测试向后兼容性"""
        if not rule_engine.rules:
            pytest.skip("规则文件未加载")
        
        # 验证结果结构与重构前保持一致
        import asyncio
        results = asyncio.run(rule_engine.execute_check(comprehensive_document))
        
        # 验证结果类型和结构
        assert isinstance(results, list)
        
        for result in results:
            assert isinstance(result, CheckResult)
            assert hasattr(result, 'rule_id')
            assert hasattr(result, 'rule_name')
            assert hasattr(result, 'passed')
            assert hasattr(result, 'severity')
            assert hasattr(result, 'message')
            assert isinstance(result.passed, bool)
            assert isinstance(result.message, str)
        
        # 验证关键规则类型存在
        rule_types = set()
        for result in results:
            if "structure" in result.rule_id:
                rule_types.add("structure")
            elif "format" in result.rule_id:
                rule_types.add("format")
            elif "content" in result.rule_id:
                rule_types.add("content")
        
        assert "structure" in rule_types or "format" in rule_types or "content" in rule_types, \
            "缺少基本的规则类型"
        
        print(f"\n向后兼容性验证通过，包含规则类型: {rule_types}")
