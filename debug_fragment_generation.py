"""
调试问题片段生成过程
"""

import sys
import os
import json
sys.path.append('backend')

from app.services.problem_fragment_service import ProblemFragmentService
from app.services.problem_fragment_generator import ProblemFragmentGenerator
from app.services.document_processor import DocumentData
from app.models.check_result import CheckResult, CheckSeverity

def debug_fragment_generation_from_task():
    """从任务结果调试问题片段生成"""
    
    print("🔍 调试问题片段生成过程")
    print("=" * 60)
    
    # 读取任务结果
    task_id = "task_a19610aa74844e70b590db5d2ecd11b6"
    result_file = f"task_result_{task_id}.json"
    
    if not os.path.exists(result_file):
        print(f"❌ 任务结果文件不存在: {result_file}")
        return
    
    with open(result_file, 'r', encoding='utf-8') as f:
        task_result = json.load(f)
    
    # 检查检测结果
    check_result = task_result.get('check_result', [])
    print(f"📊 检测结果数量: {len(check_result)}")
    
    if not check_result:
        print("❌ 没有检测结果")
        return
    
    # 转换为CheckResult对象
    check_results = []
    
    for i, result_item in enumerate(check_result):
        print(f"\n📌 检测结果 {i+1}:")
        print(f"   规则ID: {result_item.get('rule_id', 'N/A')}")
        print(f"   规则名称: {result_item.get('rule_name', 'N/A')}")
        print(f"   通过: {result_item.get('passed', False)}")
        print(f"   严重程度: {result_item.get('severity', 'N/A')}")
        print(f"   消息: {result_item.get('message', 'N/A')[:100]}...")
        
        # 只处理失败的检测结果
        if not result_item.get('passed', True):
            try:
                # 映射严重程度
                severity_map = {
                    'error': CheckSeverity.ERROR,
                    'warning': CheckSeverity.WARNING,
                    'critical': CheckSeverity.CRITICAL,
                    'info': CheckSeverity.INFO
                }
                
                severity = severity_map.get(result_item.get('severity', 'warning'), CheckSeverity.WARNING)
                
                check_result_obj = CheckResult(
                    rule_id=result_item.get('rule_id', 'unknown'),
                    rule_name=result_item.get('rule_name', '未知规则'),
                    passed=result_item.get('passed', False),
                    severity=severity,
                    message=result_item.get('message', ''),
                    details=result_item.get('details', {}),
                    position=result_item.get('position'),
                    metadata=result_item.get('metadata', {})
                )
                
                check_results.append(check_result_obj)
                print(f"   ✅ 转换为CheckResult对象成功")
                
            except Exception as e:
                print(f"   ❌ 转换失败: {str(e)}")
    
    print(f"\n📊 转换后的CheckResult对象数量: {len(check_results)}")
    
    # 创建模拟的文档数据
    document_data = DocumentData(
        file_path="test.docx",
        doc_info={
            "title": "新媒体技术对舞蹈编导创作手法的影响研究",
            "author": "李岩",
            "pages": 36,
            "words": 18806
        },
        content_stats={
            "page_count": 36,
            "word_count": 18806,
            "paragraph_count": 589
        },
        elements=[
            {"id": "elem_1", "text": "新媒体技术对舞蹈编导创作手法的影响研究", "type": "title"},
            {"id": "elem_2", "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "type": "keywords"},
            {"id": "elem_3", "text": "Key words: dance creation; new media technology; choreographic techniques", "type": "keywords"},
            {"id": "elem_4", "text": "ABSTRACT", "type": "heading"},
            {"id": "elem_5", "text": "正文内容段落...", "type": "paragraph"}
        ],
        paragraphs=[],
        tables=[],
        images=[]
    )
    
    # 测试问题片段生成
    try:
        print(f"\n🔧 开始生成问题片段...")
        
        generator = ProblemFragmentGenerator()
        fragments = generator.generate_fragments_from_results(check_results, document_data)
        
        print(f"✅ 生成问题片段数量: {len(fragments)}")
        
        # 显示生成的问题片段
        for i, fragment in enumerate(fragments):
            print(f"\n📌 问题片段 {i+1}:")
            print(f"   ID: {fragment.fragment_id}")
            print(f"   结构: {fragment.structure}")
            print(f"   类别: {fragment.category}")
            print(f"   严重程度: {fragment.severity}")
            print(f"   位置: {fragment.position}")
            print(f"   原文: {fragment.original_text[:50]}...")
            print(f"   问题描述: {fragment.problem_description}")
            print(f"   标准参考: {fragment.standard_reference}")
            print(f"   可自动修复: {fragment.auto_fixable}")
            print(f"   规则ID: {fragment.rule_id}")
        
        # 转换为字典格式
        fragment_dicts = [fragment.to_dict() for fragment in fragments]
        
        print(f"\n📋 转换为字典格式成功，数量: {len(fragment_dicts)}")
        
        # 按结构分组统计
        structure_stats = {}
        severity_stats = {}
        
        for fragment_dict in fragment_dicts:
            structure = fragment_dict.get('structure', '未知')
            severity = fragment_dict.get('severity', '未知')
            
            structure_stats[structure] = structure_stats.get(structure, 0) + 1
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        print(f"\n📊 按结构分组统计:")
        for structure, count in structure_stats.items():
            print(f"   {structure}: {count} 个")
        
        print(f"\n📊 按严重程度统计:")
        for severity, count in severity_stats.items():
            print(f"   {severity}: {count} 个")
        
        # 查找中文关键词相关的问题片段
        keywords_fragments = [f for f in fragment_dicts if "关键词" in f.get('original_text', '') or "keywords" in f.get('rule_id', '').lower()]
        
        if keywords_fragments:
            print(f"\n🎯 中文关键词相关问题片段: {len(keywords_fragments)} 个")
            for fragment in keywords_fragments:
                print(f"   - {fragment.get('fragment_id')}: {fragment.get('problem_description')}")
        else:
            print(f"\n⚠️ 未找到中文关键词相关的问题片段")
        
        return fragment_dicts
        
    except Exception as e:
        print(f"❌ 生成问题片段失败: {str(e)}")
        import traceback
        print(f"❌ 详细错误信息: {traceback.format_exc()}")
        return []

if __name__ == "__main__":
    fragments = debug_fragment_generation_from_task()
    
    print("\n" + "=" * 60)
    if fragments:
        print(f"🎉 调试成功！生成了 {len(fragments)} 个问题片段。")
    else:
        print("💥 调试失败！未能生成问题片段。")
