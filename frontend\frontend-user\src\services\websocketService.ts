import { ref, type Ref } from 'vue'

// WebSocket连接状态
export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

// 消息类型定义
export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
  id?: string
}

// 任务状态更新消息
export interface TaskStatusMessage extends WebSocketMessage {
  type: 'task_status'
  data: {
    task_id: string
    status: 'pending' | 'running' | 'completed' | 'failed'
    progress: number
    message?: string
    result?: any
    error?: string
  }
}

// 系统通知消息
export interface SystemNotificationMessage extends WebSocketMessage {
  type: 'system_notification'
  data: {
    title: string
    message: string
    level: 'info' | 'success' | 'warning' | 'error'
    action?: {
      text: string
      url: string
    }
  }
}

// 文档状态更新消息
export interface DocumentStatusMessage extends WebSocketMessage {
  type: 'document_status'
  data: {
    document_id: number
    status: 'uploaded' | 'processing' | 'completed' | 'failed'
    analysis_progress?: number
    analysis_result?: any
  }
}

// 事件监听器类型
type EventListener = (message: WebSocketMessage) => void

class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private token: string | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: number | null = null
  private heartbeatTimer = 30000 // 30秒心跳
  
  // 响应式状态
  public status: Ref<WebSocketStatus> = ref(WebSocketStatus.DISCONNECTED)
  public isConnected: Ref<boolean> = ref(false)
  public lastError: Ref<string | null> = ref(null)
  
  // 事件监听器
  private listeners = new Map<string, EventListener[]>()
  
  constructor() {
    // 根据环境确定WebSocket URL
    // 开发环境连接到后端服务器，生产环境使用当前域名
    const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    
    if (isDev) {
      // 开发环境：连接到后端服务器
      this.url = 'ws://127.0.0.1:8000/ws'
    } else {
      // 生产环境：使用当前域名
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      this.url = `${protocol}//${host}/ws`
    }
    
    console.debug('WebSocket URL:', this.url)
  }

  /**
   * 连接WebSocket
   */
  connect(token?: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected')
      return
    }

    if (token) {
      this.token = token
    } else {
      // 从localStorage获取token
      this.token = localStorage.getItem('access_token')
    }

    if (!this.token) {
      // 用户未登录时，不显示警告，静默等待
      console.debug('WebSocket connection deferred: waiting for user authentication')
      this.status.value = WebSocketStatus.DISCONNECTED
      return
    }

    console.log('Attempting WebSocket connection to:', this.url)
    this.status.value = WebSocketStatus.CONNECTING
    this.lastError.value = null

    try {
      // 建立WebSocket连接，携带认证token
      const wsUrl = `${this.url}?token=${encodeURIComponent(this.token)}`
      console.debug('WebSocket connection URL:', wsUrl.replace(/token=[^&]+/, 'token=***'))
      
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = this.onOpen.bind(this)
      this.ws.onmessage = this.onMessage.bind(this)
      this.ws.onclose = this.onClose.bind(this)
      this.ws.onerror = this.onError.bind(this)

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      this.status.value = WebSocketStatus.ERROR
      this.lastError.value = error instanceof Error ? error.message : 'Connection failed'
    }
  }

  /**
   * 用户登录后调用此方法建立WebSocket连接
   */
  connectAfterLogin(token: string): void {
    this.token = token
    this.connect(token)
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.reconnectAttempts = this.maxReconnectAttempts // 阻止自动重连
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }

    this.status.value = WebSocketStatus.DISCONNECTED
    this.isConnected.value = false
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, cannot send message')
      return false
    }

    try {
      this.ws.send(JSON.stringify(message))
      return true
    } catch (error) {
      console.error('Failed to send WebSocket message:', error)
      return false
    }
  }

  /**
   * 订阅特定类型的消息
   */
  subscribe(messageType: string, listener: EventListener): () => void {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, [])
    }
    
    this.listeners.get(messageType)!.push(listener)

    // 返回取消订阅函数
    return () => {
      const listeners = this.listeners.get(messageType)
      if (listeners) {
        const index = listeners.indexOf(listener)
        if (index > -1) {
          listeners.splice(index, 1)
        }
      }
    }
  }

  /**
   * 订阅任务状态更新
   */
  subscribeToTaskStatus(taskId: string, callback: (status: TaskStatusMessage['data']) => void): () => void {
    return this.subscribe('task_status', (message: WebSocketMessage) => {
      if (message.type === 'task_status') {
        const taskMessage = message as TaskStatusMessage
        if (taskMessage.data.task_id === taskId) {
          callback(taskMessage.data)
        }
      }
    })
  }

  /**
   * 订阅文档状态更新
   */
  subscribeToDocumentStatus(documentId: number, callback: (status: DocumentStatusMessage['data']) => void): () => void {
    return this.subscribe('document_status', (message: WebSocketMessage) => {
      if (message.type === 'document_status') {
        const docMessage = message as DocumentStatusMessage
        if (docMessage.data.document_id === documentId) {
          callback(docMessage.data)
        }
      }
    })
  }

  /**
   * 订阅系统通知
   */
  subscribeToNotifications(callback: (notification: SystemNotificationMessage['data']) => void): () => void {
    return this.subscribe('system_notification', (message: WebSocketMessage) => {
      if (message.type === 'system_notification') {
        const notificationMessage = message as SystemNotificationMessage
        callback(notificationMessage.data)
      }
    })
  }

  // WebSocket事件处理器
  private onOpen(): void {
    console.log('WebSocket connected')
    this.status.value = WebSocketStatus.CONNECTED
    this.isConnected.value = true
    this.reconnectAttempts = 0
    this.lastError.value = null

    // 启动心跳
    this.startHeartbeat()

    // 发送认证确认消息
    this.send({
      type: 'auth',
      data: { token: this.token },
      timestamp: Date.now()
    })
  }

  private onMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // 处理心跳响应
      if (message.type === 'pong') {
        return
      }

      // 分发消息给订阅者
      const listeners = this.listeners.get(message.type) || []
      listeners.forEach(listener => {
        try {
          listener(message)
        } catch (error) {
          console.error('Error in message listener:', error)
        }
      })

      // 分发给通用监听器
      const allListeners = this.listeners.get('*') || []
      allListeners.forEach(listener => {
        try {
          listener(message)
        } catch (error) {
          console.error('Error in general message listener:', error)
        }
      })

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  private onClose(event: CloseEvent): void {
    console.log('WebSocket disconnected. Code:', event.code, 'Reason:', event.reason)
    console.debug('Close event details:', {
      code: event.code,
      reason: event.reason,
      wasClean: event.wasClean
    })
    
    this.status.value = WebSocketStatus.DISCONNECTED
    this.isConnected.value = false
    this.ws = null

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // 根据关闭代码设置错误信息
    if (event.code === 1006) {
      this.lastError.value = 'Connection failed: Unable to connect to server'
    } else if (event.code === 1011) {
      this.lastError.value = 'Server error: Internal server error'
    } else if (event.code === 1008) {
      this.lastError.value = 'Authentication failed: Invalid token'
    } else if (event.code !== 1000) {
      this.lastError.value = `Connection closed unexpectedly (${event.code}): ${event.reason || 'Unknown reason'}`
    }

    // 自动重连（除非是主动断开）
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect()
    }
  }

  private onError(error: Event): void {
    console.error('WebSocket error occurred:', error)
    console.debug('WebSocket error details:', {
      type: error.type,
      target: error.target,
      currentState: this.ws?.readyState
    })
    
    this.status.value = WebSocketStatus.ERROR
    this.lastError.value = 'Connection error: Failed to establish WebSocket connection'
  }

  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    this.status.value = WebSocketStatus.RECONNECTING

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // 指数退避
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`)

    setTimeout(() => {
      if (this.status.value === WebSocketStatus.RECONNECTING) {
        this.connect()
      }
    }, delay)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    this.heartbeatInterval = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({
          type: 'ping',
          data: {},
          timestamp: Date.now()
        })
      }
    }, this.heartbeatTimer)
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats(): {
    status: WebSocketStatus
    isConnected: boolean
    reconnectAttempts: number
    lastError: string | null
  } {
    return {
      status: this.status.value,
      isConnected: this.isConnected.value,
      reconnectAttempts: this.reconnectAttempts,
      lastError: this.lastError.value
    }
  }
}

// 创建全局WebSocket服务实例
export const websocketService = new WebSocketService()

export default websocketService 