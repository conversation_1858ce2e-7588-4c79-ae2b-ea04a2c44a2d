# Word图片属性提取技术说明

## 概述

通过pywin32的COM接口，我们可以精确提取Word文档中图片的所有属性信息，包括尺寸、位置、格式、视觉效果等，确保外部程序能够完美还原原始文档的图片效果。

## COM接口核心对象

### 1. InlineShapes 集合
处理嵌入在文本行中的图片：
```python
import win32com.client

# 获取Word应用程序
word_app = win32com.client.Dispatch("Word.Application")
doc = word_app.Documents.Open(file_path)

# 遍历所有内嵌图片
for i, shape in enumerate(doc.InlineShapes):
    if shape.Type == 3:  # wdInlineShapePicture
        image_info = extract_inline_image_properties(shape, i)
```

### 2. Shapes 集合
处理浮动图片（可自由定位的图片）：
```python
# 遍历所有浮动图片
for i, shape in enumerate(doc.Shapes):
    if shape.Type == 13:  # msoPicture
        image_info = extract_floating_image_properties(shape, i)
```

## 图片属性完整提取

### 1. 基础属性提取
```python
def extract_basic_properties(shape):
    """提取图片基础属性"""
    properties = {
        # 尺寸信息
        "original_width": shape.Width,
        "original_height": shape.Height,
        "scale_width": getattr(shape, 'ScaleWidth', 100),
        "scale_height": getattr(shape, 'ScaleHeight', 100),
        
        # 格式信息
        "type": shape.Type,
        "name": getattr(shape, 'Name', ''),
        "alt_text": getattr(shape, 'AlternativeText', ''),
        "title": getattr(shape, 'Title', ''),
    }
    
    # 计算显示尺寸
    properties["display_width"] = properties["original_width"] * properties["scale_width"] / 100
    properties["display_height"] = properties["original_height"] * properties["scale_height"] / 100
    
    return properties
```

### 2. 位置信息提取
```python
def extract_position_info(shape, is_inline=True):
    """提取图片位置信息"""
    if is_inline:
        # 内嵌图片位置信息
        position_info = {
            "anchor_type": "inline",
            "range_start": shape.Range.Start,
            "range_end": shape.Range.End,
            "paragraph_index": get_paragraph_index(shape.Range),
            "page_number": shape.Range.Information[7],  # wdActiveEndPageNumber
        }
    else:
        # 浮动图片位置信息
        position_info = {
            "anchor_type": "floating",
            "left": shape.Left,
            "top": shape.Top,
            "anchor_range_start": shape.Anchor.Start,
            "anchor_range_end": shape.Anchor.End,
            "relative_horizontal_position": shape.RelativeHorizontalPosition,
            "relative_vertical_position": shape.RelativeVerticalPosition,
            "page_number": shape.Anchor.Information[7],
        }
    
    return position_info
```

### 3. 环绕属性提取
```python
def extract_wrap_properties(shape):
    """提取文字环绕属性"""
    wrap_properties = {
        "wrap_type": "none",  # 默认值
        "distance_top": 0,
        "distance_bottom": 0,
        "distance_left": 0,
        "distance_right": 0,
    }
    
    # 只有浮动图片才有环绕属性
    if hasattr(shape, 'WrapFormat'):
        wrap_format = shape.WrapFormat
        wrap_types = {
            0: "inline",
            1: "square",
            2: "tight",
            3: "behind",
            4: "front",
            5: "through",
            7: "top_bottom"
        }
        
        wrap_properties.update({
            "wrap_type": wrap_types.get(wrap_format.Type, "unknown"),
            "distance_top": wrap_format.DistanceTop,
            "distance_bottom": wrap_format.DistanceBottom,
            "distance_left": wrap_format.DistanceLeft,
            "distance_right": wrap_format.DistanceRight,
        })
    
    return wrap_properties
```

### 4. 视觉效果属性提取
```python
def extract_visual_properties(shape):
    """提取图片视觉效果属性"""
    visual_properties = {
        "brightness": 0,
        "contrast": 0,
        "rotation": 0,
        "flip_horizontal": False,
        "flip_vertical": False,
        "transparency": 0,
    }
    
    # 获取图片格式对象
    if hasattr(shape, 'PictureFormat'):
        pic_format = shape.PictureFormat
        visual_properties.update({
            "brightness": getattr(pic_format, 'Brightness', 0),
            "contrast": getattr(pic_format, 'Contrast', 0),
            "transparency": getattr(pic_format, 'TransparencyColor', 0),
        })
    
    # 获取旋转信息
    if hasattr(shape, 'Rotation'):
        visual_properties["rotation"] = shape.Rotation
    
    # 获取翻转信息
    if hasattr(shape, 'HorizontalFlip'):
        visual_properties["flip_horizontal"] = shape.HorizontalFlip
    if hasattr(shape, 'VerticalFlip'):
        visual_properties["flip_vertical"] = shape.VerticalFlip
    
    return visual_properties
```

### 5. 图片数据提取
```python
def extract_image_data(shape, output_path):
    """提取图片二进制数据"""
    import tempfile
    import hashlib
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # 导出图片到临时文件
        shape.Range.Export(temp_path, FilterName="PNG")
        
        # 读取图片数据
        with open(temp_path, 'rb') as f:
            image_data = f.read()
        
        # 计算哈希值
        image_hash = hashlib.sha256(image_data).hexdigest()
        
        # 保存到指定路径
        final_path = f"{output_path}/{image_hash}.png"
        with open(final_path, 'wb') as f:
            f.write(image_data)
        
        return {
            "hash": image_hash,
            "file_path": final_path,
            "file_size": len(image_data),
            "format": "png"
        }
    
    finally:
        # 清理临时文件
        import os
        if os.path.exists(temp_path):
            os.unlink(temp_path)
```

## 完整的图片信息提取函数

```python
def extract_complete_image_info(shape, position_index, doc_id, is_inline=True):
    """提取图片的完整信息"""
    
    # 基础属性
    basic_props = extract_basic_properties(shape)
    
    # 位置信息
    position_info = extract_position_info(shape, is_inline)
    
    # 环绕属性
    wrap_props = extract_wrap_properties(shape)
    
    # 视觉效果
    visual_props = extract_visual_properties(shape)
    
    # 图片数据
    image_data = extract_image_data(shape, f"images/{doc_id}")
    
    # 组合完整信息
    complete_info = {
        "type": "image",
        "position": position_index,
        "image_id": f"img_{doc_id}_{position_index:03d}",
        "image_hash": image_data["hash"],
        "url": f"/api/v1/images/img_{doc_id}_{position_index:03d}.png",
        
        # 尺寸信息
        "original_width": basic_props["original_width"],
        "original_height": basic_props["original_height"],
        "display_width": basic_props["display_width"],
        "display_height": basic_props["display_height"],
        "scale_width": basic_props["scale_width"],
        "scale_height": basic_props["scale_height"],
        
        # 文件信息
        "format": image_data["format"],
        "file_size": image_data["file_size"],
        "file_path": image_data["file_path"],
        "alt_text": basic_props["alt_text"],
        "title": basic_props["title"],
        
        # 位置信息
        "position_info": {
            **position_info,
            **wrap_props
        },
        
        # 视觉效果
        "properties": visual_props,
        
        # 元数据
        "metadata": {
            "extraction_time": datetime.now().isoformat(),
            "shape_name": basic_props["name"],
            "shape_type": basic_props["type"],
            "is_inline": is_inline
        }
    }
    
    return complete_info
```

## 批量处理流程

```python
def process_all_images_in_document(doc_path, doc_id):
    """处理文档中的所有图片"""
    
    word_app = win32com.client.Dispatch("Word.Application")
    word_app.Visible = False
    
    try:
        doc = word_app.Documents.Open(doc_path)
        all_images = []
        position_counter = 0
        
        # 处理内嵌图片
        for shape in doc.InlineShapes:
            if shape.Type == 3:  # wdInlineShapePicture
                position_counter += 1
                image_info = extract_complete_image_info(
                    shape, position_counter, doc_id, is_inline=True
                )
                all_images.append(image_info)
        
        # 处理浮动图片
        for shape in doc.Shapes:
            if shape.Type == 13:  # msoPicture
                position_counter += 1
                image_info = extract_complete_image_info(
                    shape, position_counter, doc_id, is_inline=False
                )
                all_images.append(image_info)
        
        # 按位置排序
        all_images.sort(key=lambda x: x["position"])
        
        return all_images
    
    finally:
        doc.Close()
        word_app.Quit()
```

## 性能优化建议

### 1. 并发处理
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def process_images_concurrent(doc_path, doc_id, max_workers=3):
    """并发处理图片提取"""
    loop = asyncio.get_event_loop()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交图片处理任务
        tasks = []
        # ... 创建任务
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks)
    
    return results
```

### 2. 缓存优化
```python
import redis
import json

class ImageCache:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    def get_cached_image(self, image_hash):
        """获取缓存的图片信息"""
        cached = self.redis_client.get(f"image:{image_hash}")
        return json.loads(cached) if cached else None
    
    def cache_image(self, image_hash, image_info):
        """缓存图片信息"""
        self.redis_client.setex(
            f"image:{image_hash}", 
            3600 * 24,  # 24小时过期
            json.dumps(image_info)
        )
```

通过这些详细的技术实现，您可以精确提取Word文档中图片的所有属性信息，确保外部程序能够完美还原原始文档的图片效果和布局。 