# Word文档分析服务 - 用户使用指南

## 📖 服务介绍

Word文档分析服务是一个专业的文档分析平台，为用户提供：
- **文档内容提取**：自动提取Word文档的文本内容
- **结构分析**：分析文档的标题层次和段落结构  
- **格式检测**：检查文档格式是否符合论文标准
- **图片处理**：提取和分析文档中的图片
- **报告生成**：生成详细的分析报告

## 🚀 快速开始

### 1. 访问服务
打开浏览器访问：
- **Web界面**：http://your-domain.com
- **API文档**：http://your-domain.com/docs

### 2. 用户注册
#### 通过Web界面注册
1. 点击页面右上角"注册"按钮
2. 填写注册信息：
   - 用户名：3-20个字符，只能包含字母和数字
   - 邮箱：有效的邮箱地址
   - 密码：至少8位，包含大写字母和数字
3. 点击"注册"完成账户创建

#### 通过API注册
```bash
curl -X POST "http://your-domain.com/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "email": "<EMAIL>",
    "password": "your_password
  }'
```

### 3. 用户登录
#### 通过Web界面登录
1. 点击"登录"按钮
2. 输入用户名和密码
3. 点击"登录"进入系统

#### 通过API登录
```bash
curl -X POST "http://your-domain.com/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=your_username&password=your_password"
```

## 📄 文档管理

### 文档上传

#### 支持的文件格式
- **Word文档**：.docx格式（Microsoft Word 2007及以上版本）
- **文件大小**：最大100MB
- **内容要求**：支持中英文混合文档

#### 上传步骤
1. **选择文件**：点击"上传文档"按钮
2. **选择文件**：从本地选择.docx文件
3. **确认上传**：系统会显示文件信息，确认无误后点击"开始分析"
4. **等待处理**：系统会自动处理文档，显示进度条

#### API上传
```bash
curl -X POST "http://your-domain.com/api/v1/documents/upload" \
  -H "Authorization: Bearer your_token" \
  -F "file=@your_document.docx"
```

### 文档列表管理

#### 查看文档列表
在"我的文档"页面可以看到：
- 文档名称和上传时间
- 处理状态（处理中/已完成/失败）
- 文档大小和页数
- 操作按钮（查看/下载/删除）

#### 文档状态说明
- **🟡 等待中**：文档已上传，等待系统处理
- **🔵 处理中**：系统正在分析文档内容
- **🟢 已完成**：分析完成，可查看结果
- **🔴 失败**：处理过程中出现错误

## 📊 分析结果

### 查看分析报告

#### 内容分析结果
1. **文档概览**
   - 总字数、段落数、页数
   - 文档标题和作者信息
   - 创建时间和修改时间

2. **结构分析**
   - 标题层次结构图
   - 章节划分和编号
   - 目录结构检查

3. **内容提取**
   - 完整文本内容
   - 段落分段显示
   - 表格内容提取

#### 格式检测结果
1. **格式符合性**
   - 字体设置检查
   - 行间距和段间距
   - 页边距设置
   - 页眉页脚格式

2. **论文标准检查**
   - 标题格式规范
   - 参考文献格式
   - 图表标注规范
   - 学术写作规范

#### 图片分析结果
1. **图片信息**
   - 图片数量和尺寸
   - 图片格式和质量
   - 图片在文档中的位置

2. **图片问题检测**
   - 分辨率是否足够
   - 图片说明是否完整
   - 图片编号是否规范

### 下载报告

#### 支持的报告格式
- **在线查看**：网页格式，实时浏览
- **PDF报告**：完整的分析报告，包含所有结果
- **JSON数据**：结构化数据，便于程序处理
- **Excel表格**：表格形式的统计数据

#### 下载步骤
1. 在分析结果页面点击"下载报告"
2. 选择所需的报告格式
3. 点击"生成报告"
4. 等待报告生成完成
5. 点击"下载"保存到本地

## 🔧 高级功能

### 批量处理

#### 批量上传文档
1. 选择"批量上传"模式
2. 一次最多可上传10个文档
3. 系统会依次处理每个文档
4. 在任务列表中查看所有处理进度

#### 批量下载报告
1. 在文档列表中选择多个已完成的文档
2. 点击"批量下载"
3. 选择报告格式
4. 系统会打包所有报告供下载

### 自定义分析配置

#### 分析类型选择
- **快速分析**：仅提取基本内容和结构
- **标准分析**：包含格式检测和基本论文检查
- **深度分析**：完整的论文标准检查和图片分析
- **自定义**：根据需要选择特定的分析模块

#### 论文标准配置
- **本科论文**：适用于本科毕业论文
- **硕士论文**：适用于硕士学位论文
- **博士论文**：适用于博士学位论文
- **期刊论文**：适用于学术期刊投稿
- **会议论文**：适用于学术会议论文

### 分析历史管理

#### 查看历史记录
在"分析历史"页面可以：
- 查看所有分析任务的历史记录
- 按时间、状态筛选记录
- 重新下载历史报告
- 删除不需要的记录

#### 数据统计
- 每月分析文档数量
- 各类型文档比例
- 分析成功率统计
- 平均处理时间

## 📱 API使用指南

### 认证方式
所有API请求都需要在请求头中包含访问令牌：
```
Authorization: Bearer your_access_token
```

### 常用API端点

#### 1. 获取文档列表
```bash
curl -X GET "http://your-domain.com/api/v1/documents/" \
  -H "Authorization: Bearer your_token"
```

#### 2. 查看任务状态
```bash
curl -X GET "http://your-domain.com/api/v1/tasks/task_id" \
  -H "Authorization: Bearer your_token"
```

#### 3. 获取分析结果
```bash
curl -X GET "http://your-domain.com/api/v1/documents/doc_id/analysis" \
  -H "Authorization: Bearer your_token"
```

#### 4. 下载报告
```bash
curl -X GET "http://your-domain.com/api/v1/documents/doc_id/report?format=pdf" \
  -H "Authorization: Bearer your_token" \
  -o "report.pdf"
```

### API响应格式

#### 成功响应
```json
{
  "data": {
    "id": 123,
    "filename": "document.docx",
    "status": "completed",
    "created_at": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "v1"
  }
}
```

#### 错误响应
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "文件格式不支持",
    "details": {
      "field": "file",
      "supported_formats": [".docx"]
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## 💡 使用技巧

### 文档准备建议
1. **文档格式**：使用标准的Word .docx格式
2. **文件命名**：使用有意义的文件名，避免特殊字符
3. **内容完整**：确保文档内容完整，包含所有必要章节
4. **图片质量**：使用高清图片，避免模糊不清的图像

### 提高分析质量
1. **标准格式**：按照学术写作规范设置文档格式
2. **清晰结构**：使用标准的标题样式建立文档层次
3. **完整信息**：包含作者、标题、摘要等基本信息
4. **规范引用**：按照标准格式添加参考文献

### 优化处理速度
1. **文件大小**：尽量控制文档大小在50MB以内
2. **图片优化**：压缩图片大小，但保持清晰度
3. **避免复杂元素**：减少嵌入对象和复杂表格
4. **错峰使用**：避在高峰时段上传大量文档

## 🔧 常见问题

### Q1: 上传失败怎么办？
**可能原因：**
- 文件格式不支持（只支持.docx）
- 文件大小超过限制（100MB）
- 网络连接问题
- 服务器暂时繁忙

**解决方法：**
1. 检查文件格式和大小
2. 尝试重新上传
3. 联系技术支持

### Q2: 分析结果不准确？
**可能原因：**
- 文档格式不规范
- 内容过于复杂
- 图片质量问题

**解决方法：**
1. 按照规范整理文档格式
2. 简化复杂的表格和图表
3. 使用"深度分析"模式

### Q3: 处理时间很长？
**可能原因：**
- 文档内容复杂
- 系统负载较高
- 文件大小过大

**解决方法：**
1. 耐心等待处理完成
2. 尝试在非高峰时段使用
3. 联系客服了解处理进度

### Q4: 忘记密码怎么办？
**解决方法：**
1. 点击登录页面的"忘记密码"
2. 输入注册邮箱
3. 查收重置密码邮件
4. 按照邮件指示重设密码

## 📞 技术支持

### 联系方式
- **客服邮箱**：<EMAIL>
- **技术支持**：<EMAIL>
- **在线客服**：访问网站点击右下角客服图标
- **服务时间**：工作日 9:00-18:00

### 反馈建议
我们欢迎您的意见和建议：
1. 通过网站"意见反馈"功能提交
2. 发送邮件到************************
3. 在用户社区发布讨论

### 服务状态
- **服务状态页面**：status.wordservice.com
- **维护通知**：会通过邮件和网站公告
- **紧急联系**：<EMAIL>

---

🎯 **开始您的文档分析之旅！**
📊 **让专业分析助力您的学术研究** 