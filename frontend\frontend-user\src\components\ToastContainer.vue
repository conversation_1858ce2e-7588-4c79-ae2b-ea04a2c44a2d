<template>
  <div class="toast-container">
    <TransitionGroup
      name="toast"
      tag="div"
      class="fixed top-4 right-4 z-50 space-y-2 w-80"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="toastClasses(notification.type)"
        @click="notification.closable && closeNotification(notification.id)"
      >
        <!-- 状态图标 -->
        <div class="flex-shrink-0">
          <svg :class="iconClasses(notification.type)" fill="currentColor" viewBox="0 0 20 20">
            <path v-if="notification.type === 'success'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            <path v-else-if="notification.type === 'error'" fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            <path v-else-if="notification.type === 'warning'" fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            <path v-else-if="notification.type === 'info'" fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>

        <!-- 内容 -->
        <div class="ml-3 flex-1">
          <!-- 标题 -->
          <p v-if="notification.title" :class="titleClasses(notification.type)">
            {{ notification.title }}
          </p>
          
          <!-- 消息 -->
          <p :class="messageClasses(notification.type, !!notification.title)">
            {{ notification.message }}
          </p>
        </div>

        <!-- 关闭按钮 -->
        <div v-if="notification.closable" class="ml-4 flex-shrink-0">
          <button
            :class="closeButtonClasses(notification.type)"
            @click.stop="closeNotification(notification.id)"
            aria-label="关闭"
          >
            <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useNotifications } from '@/utils/useNotifications'

const { notifications, closeNotification } = useNotifications()

// Toast样式类
const toastClasses = (type: string) => {
  const baseClasses = 'flex items-start p-4 rounded-lg shadow-lg border backdrop-blur-sm cursor-pointer hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]'
  
  const typeClasses = {
    success: 'bg-green-50/95 dark:bg-green-900/80 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200',
    error: 'bg-red-50/95 dark:bg-red-900/80 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200',
    warning: 'bg-yellow-50/95 dark:bg-yellow-900/80 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200',
    info: 'bg-blue-50/95 dark:bg-blue-900/80 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200'
  }
  
  return `${baseClasses} ${typeClasses[type as keyof typeof typeClasses] || typeClasses.info}`
}

// 图标样式类
const iconClasses = (type: string) => {
  const baseClasses = 'h-5 w-5 mt-0.5'
  
  const typeClasses = {
    success: 'text-green-500 dark:text-green-400',
    error: 'text-red-500 dark:text-red-400',
    warning: 'text-yellow-500 dark:text-yellow-400',
    info: 'text-blue-500 dark:text-blue-400'
  }
  
  return `${baseClasses} ${typeClasses[type as keyof typeof typeClasses] || typeClasses.info}`
}

// 标题样式类
const titleClasses = (type: string) => {
  const typeClasses = {
    success: 'text-green-900 dark:text-green-100',
    error: 'text-red-900 dark:text-red-100',
    warning: 'text-yellow-900 dark:text-yellow-100',
    info: 'text-blue-900 dark:text-blue-100'
  }
  
  return `text-sm font-semibold ${typeClasses[type as keyof typeof typeClasses] || typeClasses.info}`
}

// 消息样式类  
const messageClasses = (type: string, hasTitle: boolean) => {
  const typeClasses = {
    success: 'text-green-800 dark:text-green-200',
    error: 'text-red-800 dark:text-red-200',
    warning: 'text-yellow-800 dark:text-yellow-200',
    info: 'text-blue-800 dark:text-blue-200'
  }
  
  const marginClass = hasTitle ? 'mt-1' : ''
  
  return `text-sm ${typeClasses[type as keyof typeof typeClasses] || typeClasses.info} ${marginClass}`
}

// 关闭按钮样式类
const closeButtonClasses = (type: string) => {
  const typeClasses = {
    success: 'text-green-500 dark:text-green-400 hover:text-green-600 dark:hover:text-green-300',
    error: 'text-red-500 dark:text-red-400 hover:text-red-600 dark:hover:text-red-300',
    warning: 'text-yellow-500 dark:text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-300',
    info: 'text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300'
  }
  
  return `rounded-md p-1 hover:bg-white/20 dark:hover:bg-black/20 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 ${typeClasses[type as keyof typeof typeClasses] || typeClasses.info}`
}
</script>

<style scoped>
/* Toast动画 */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.2s ease-in;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toast-move {
  transition: transform 0.3s ease;
}

/* 确保容器在所有元素之上 */
.toast-container {
  pointer-events: none;
}

.toast-container > div {
  pointer-events: auto;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .toast-container > div {
    @apply w-72 mr-2;
  }
}
</style> 