"""
Word文档分析服务 - 健康检查API
"""

import os
import time
import psutil
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from typing import Dict, Any

from app.core.config import settings
from app.core.logging import logger
from app.core.response import success_response, error_response

router = APIRouter()


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    message: str
    timestamp: int
    version: str
    environment: str
    uptime: float
    system_info: Dict[str, Any]


@router.get("/")
async def health_check():
    """基础健康检查"""
    try:
        current_time = time.time()
        
        # 获取系统信息
        try:
            disk_path = 'C:\\' if os.name == 'nt' else '/'
            disk_usage = psutil.disk_usage(disk_path)
            disk_percent = disk_usage.percent
        except:
            disk_percent = 0
            
        system_info = {
            "cpu_percent": psutil.cpu_percent(interval=0.1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": disk_percent
        }
        
        health_data = {
            "status": "healthy",
            "message": "服务运行正常",
            "timestamp": int(current_time),
            "version": "1.0.0",
            "environment": settings.environment,
            "uptime": current_time - getattr(settings, 'start_time', current_time),
            "system_info": system_info
        }
        
        return success_response(data=health_data, message="服务运行正常")
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return error_response(message="健康检查失败", code=500)


@router.get("/detailed")
async def detailed_health_check():
    """详细健康检查"""
    try:
        current_time = time.time()
        
        # 系统资源检查
        cpu_info = {
            "count": psutil.cpu_count(),
            "percent": psutil.cpu_percent(interval=0.1),
            "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
        }
        
        memory_info = psutil.virtual_memory()._asdict()
        
        try:
            disk_path = 'C:\\' if os.name == 'nt' else '/'
            disk_info = psutil.disk_usage(disk_path)._asdict()
        except:
            disk_info = {"total": 0, "used": 0, "free": 0, "percent": 0}
        
        # 数据库连接检查
        database_status = await check_database_connection()
        
        # Redis连接检查  
        redis_status = await check_redis_connection()
        
        # Word COM检查
        word_status = await check_word_com_status()
        
        # 任务队列检查
        queue_status = await check_task_queue_status()
        
        # 计算整体健康状态
        all_checks_passed = all([
            database_status["status"] == "healthy",
            redis_status["status"] in ["healthy", "disabled"],  # Redis可选
            word_status["status"] == "healthy",
            queue_status["status"] == "healthy"
        ])
        
        overall_status = "healthy" if all_checks_passed else "degraded"
        
        detailed_data = {
            "status": overall_status,
            "timestamp": int(current_time),
            "version": "1.0.0",
            "environment": settings.environment,
            "uptime": current_time - getattr(settings, 'start_time', current_time),
            "database": database_status,
            "redis": redis_status,
            "word_com": word_status,
            "task_queue": queue_status,
            "system": {
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info
            }
        }
        
        return success_response(data=detailed_data, message="详细健康检查完成")
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {str(e)}")
        return error_response(message="详细健康检查失败", code=500)


async def check_database_connection():
    """检查数据库连接"""
    try:
        # 这里将实际检查数据库连接
        from app.database.connection import get_database_status
        return await get_database_status()
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "数据库连接失败"
        }


async def check_redis_connection():
    """检查Redis连接"""
    try:
        # 这里将实际检查Redis连接
        from app.core.cache import get_redis_status
        return await get_redis_status()
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e),
            "message": "Redis连接失败"
        }


async def check_word_com_status():
    """检查Word COM接口状态"""
    try:
        # 这里将实际检查Word COM状态
        from app.services.word_com import get_com_status
        return await get_com_status()
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e), 
            "message": "Word COM接口检查失败"
        }


async def check_task_queue_status():
    """检查任务队列状态"""
    try:
        # 这里将实际检查任务队列状态
        from app.tasks.queue import get_queue_status
        return await get_queue_status()
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "任务队列检查失败"
        } 