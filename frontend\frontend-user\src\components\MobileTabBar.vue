<template>
  <div class="mobile-tab-bar md:hidden">
    <!-- 底部导航栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-40 safe-area-pb">
      <div class="grid grid-cols-5 h-16">
        <!-- 仪表盘 -->
        <router-link
          to="/dashboard"
          class="tab-item"
          :class="{ 'tab-active': isActiveRoute('/dashboard') }"
        >
          <div class="tab-icon">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
          </div>
          <span class="tab-label">仪表盘</span>
        </router-link>

        <!-- 上传文档 -->
        <router-link
          to="/upload"
          class="tab-item"
          :class="{ 'tab-active': isActiveRoute('/upload') }"
        >
          <div class="tab-icon">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <span class="tab-label">上传</span>
        </router-link>

        <!-- 中心按钮 - 快速操作 -->
        <button
          @click="showQuickActions = !showQuickActions"
          class="tab-item tab-center"
          :class="{ 'tab-active': showQuickActions }"
        >
          <div class="tab-center-icon">
            <svg class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        </button>

        <!-- 我的文档 -->
        <router-link
          to="/documents"
          class="tab-item"
          :class="{ 'tab-active': isActiveRoute('/documents') }"
        >
          <div class="tab-icon">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <span class="tab-label">文档</span>
        </router-link>

        <!-- 任务中心 -->
        <router-link
          to="/tasks"
          class="tab-item"
          :class="{ 'tab-active': isActiveRoute('/tasks') }"
        >
          <div class="tab-icon relative">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <!-- 任务数量徽章 -->
            <span 
              v-if="taskCount > 0" 
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ taskCount > 99 ? '99+' : taskCount }}
            </span>
          </div>
          <span class="tab-label">任务</span>
        </router-link>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="quick-actions-enter"
      enter-to-class="quick-actions-enter-to"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="quick-actions-leave-from"
      leave-to-class="quick-actions-leave-to"
    >
      <div v-if="showQuickActions" class="quick-actions-panel">
        <div class="quick-actions-backdrop" @click="showQuickActions = false"></div>
        <div class="quick-actions-content">
          <div class="quick-actions-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">快速操作</h3>
            <button
              @click="showQuickActions = false"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="quick-actions-grid">
            <router-link
              to="/upload"
              @click="closeQuickActions"
              class="quick-action-item"
            >
              <div class="quick-action-icon bg-blue-100 text-blue-600">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <span>上传文档</span>
            </router-link>

            <router-link
              to="/profile"
              @click="closeQuickActions"
              class="quick-action-item"
            >
              <div class="quick-action-icon bg-green-100 text-green-600">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <span>个人中心</span>
            </router-link>

            <router-link
              to="/orders"
              @click="closeQuickActions"
              class="quick-action-item"
            >
              <div class="quick-action-icon bg-purple-100 text-purple-600">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L6 10H3m4 3v6a1 1 0 001 1h1m0 0h10a1 1 0 001-1v-6m-10 0V10a1 1 0 011-1h2m8 4h2l.4 2" />
                </svg>
              </div>
              <span>订单管理</span>
            </router-link>

            <button
              @click="openHelp"
              class="quick-action-item"
            >
              <div class="quick-action-icon bg-yellow-100 text-yellow-600">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span>帮助中心</span>
            </button>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 状态管理
const showQuickActions = ref(false)
const taskCount = ref(3) // 模拟任务数量，实际应该从store获取

// 检查当前路由是否激活
const isActiveRoute = (path: string) => {
  return route.path === path || route.path.startsWith(path + '/')
}

// 关闭快速操作面板
const closeQuickActions = () => {
  showQuickActions.value = false
}

// 打开帮助中心
const openHelp = () => {
  closeQuickActions()
  router.push('/help')
}
</script>

<style scoped>
/* 底部安全区域适配 */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 标签项样式 */
.tab-item {
  @apply flex flex-col items-center justify-center space-y-1 text-gray-500 dark:text-gray-400 transition-all duration-200 relative;
}

.tab-item:not(.tab-center):hover {
  @apply text-blue-600 dark:text-blue-400 bg-gray-50 dark:bg-gray-700/50;
}

.tab-active {
  @apply text-blue-600 dark:text-blue-400;
}

.tab-active .tab-icon {
  @apply transform scale-110;
}

/* 图标样式 */
.tab-icon {
  @apply transition-transform duration-200;
}

.tab-label {
  @apply text-xs font-medium leading-none;
}

/* 中心按钮样式 */
.tab-center {
  @apply relative;
}

.tab-center-icon {
  @apply w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center shadow-lg transform transition-all duration-200;
}

.tab-center:hover .tab-center-icon {
  @apply scale-105 bg-blue-700;
}

.tab-center.tab-active .tab-center-icon {
  @apply rotate-45 bg-blue-700;
}

/* 快速操作面板样式 */
.quick-actions-panel {
  @apply fixed inset-0 z-50;
}

.quick-actions-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
  will-change: opacity;
}

.quick-actions-content {
  @apply absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 rounded-t-3xl shadow-2xl;
  padding-bottom: calc(4rem + env(safe-area-inset-bottom));
  will-change: transform;
  transform-origin: bottom;
  backface-visibility: hidden;
}

/* 自定义动画类 */
.quick-actions-enter {
  @apply opacity-0;
}

.quick-actions-enter .quick-actions-backdrop {
  @apply opacity-0;
}

.quick-actions-enter .quick-actions-content {
  transform: translateY(100%) translateZ(0);
}

.quick-actions-enter-to {
  @apply opacity-100;
}

.quick-actions-enter-to .quick-actions-backdrop {
  @apply opacity-100;
}

.quick-actions-enter-to .quick-actions-content {
  transform: translateY(0) translateZ(0);
}

.quick-actions-leave-from {
  @apply opacity-100;
}

.quick-actions-leave-from .quick-actions-backdrop {
  @apply opacity-100;
}

.quick-actions-leave-from .quick-actions-content {
  transform: translateY(0) translateZ(0);
}

.quick-actions-leave-to {
  @apply opacity-0;
}

.quick-actions-leave-to .quick-actions-backdrop {
  @apply opacity-0;
}

.quick-actions-leave-to .quick-actions-content {
  transform: translateY(100%) translateZ(0);
}

/* 确保过渡效果应用到子元素 - 使用弹性缓动 */
.quick-actions-backdrop {
  transition: opacity 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.quick-actions-content {
  transition: transform 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.quick-actions-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700;
}

.quick-actions-grid {
  @apply grid grid-cols-2 gap-4 p-6;
}

.quick-action-item {
  @apply flex flex-col items-center space-y-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.quick-action-icon {
  @apply w-12 h-12 rounded-full flex items-center justify-center;
}

.quick-action-item span {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

/* 活动指示器 */
.tab-item.tab-active::before {
  content: '';
  @apply absolute top-0 left-1/2 transform -translate-x-1/2 w-6 h-0.5 bg-blue-600 dark:bg-blue-400 rounded-full;
}

/* 动画优化 */
.tab-icon, .tab-center-icon {
  transform-origin: center;
}

/* 触摸反馈 */
.tab-item:active {
  @apply transform scale-95;
}

.quick-action-item:active {
  @apply transform scale-95;
}
</style> 