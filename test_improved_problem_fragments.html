<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进后的问题片段显示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .before, .after {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .before { border-color: #ef4444; }
        .after { border-color: #10b981; }
        .header {
            padding: 15px;
            font-weight: bold;
            color: white;
        }
        .before .header { background: #ef4444; }
        .after .header { background: #10b981; }
        .content {
            padding: 20px;
        }
        .fragment-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .fragment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .severity-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        .severity-severe { background: #ef4444; }
        .original-text {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
        .detail-row {
            display: flex;
            margin: 8px 0;
        }
        .detail-label {
            font-weight: bold;
            width: 80px;
            color: #666;
        }
        .detail-value {
            flex: 1;
        }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .improvement {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .improvement h3 {
            color: #10b981;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 改进后的问题片段显示效果</h1>
        <p>展示修复后的中文关键词问题片段显示，对比改进前后的效果。</p>

        <div class="improvement">
            <h3>🚀 主要改进点</h3>
            <ul class="feature-list">
                <li><strong>原文片段准确</strong>：显示实际的关键词文本而不是"文档内容"</li>
                <li><strong>问题描述具体</strong>：明确说明检测到的具体问题</li>
                <li><strong>标准参考清晰</strong>：显示规则文件中定义的标准要求</li>
                <li><strong>分类更精确</strong>：按问题类型进行分类（对齐、标点、分隔符等）</li>
                <li><strong>配置驱动</strong>：通过规则文件配置问题模板，无需修改代码</li>
            </ul>
        </div>

        <h2>📊 改进前后对比</h2>
        <div class="comparison">
            <div class="before">
                <div class="header">❌ 改进前</div>
                <div class="content">
                    <div class="fragment-item">
                        <div class="fragment-header">
                            <span>中文关键词问题</span>
                            <span class="severity-badge severity-severe">严重</span>
                        </div>
                        <div class="original-text">文档内容</div>
                        <div class="detail-row">
                            <span class="detail-label">问题详情:</span>
                            <span class="detail-value">中文关键词格式检查失败：关键词段落应左对齐，当前：3</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">标准:</span>
                            <span class="detail-value">请参考相关格式标准</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">问题描述:</span>
                            <span class="detail-value">格式检查失败</span>
                        </div>
                        <div class="error">❌ 信息不够具体，用户难以理解和修复</div>
                    </div>
                </div>
            </div>

            <div class="after">
                <div class="header">✅ 改进后</div>
                <div class="content">
                    <div class="fragment-item">
                        <div class="fragment-header">
                            <span>中文关键词问题</span>
                            <span class="severity-badge severity-severe">严重</span>
                        </div>
                        <div class="original-text">关键词:人工智能;机器学习</div>
                        <div class="detail-row">
                            <span class="detail-label">问题详情:</span>
                            <span class="detail-value">对齐方式问题</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">标准:</span>
                            <span class="detail-value">左对齐</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">问题描述:</span>
                            <span class="detail-value">当前为两端对齐，应为左对齐</span>
                        </div>
                        <div class="success">✅ 信息具体明确，用户可以快速理解和修复</div>
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 多种问题类型展示</h2>
        <div id="problem-types-demo"></div>

        <h2>📋 技术实现说明</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
            <h3>1. 规则文件配置</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 4px; overflow-x: auto;">
"problem_templates": {
  "alignment_error": {
    "category": "对齐方式问题",
    "standard": "左对齐", 
    "description_template": "当前为{actual_alignment}，应为左对齐"
  },
  "colon_error": {
    "category": "标点符号问题",
    "standard": "中文冒号（：）",
    "description_template": "使用了{actual_colon}，应使用中文冒号（：）"
  }
}</pre>

            <h3>2. 检测函数改进</h3>
            <ul>
                <li>使用规则文件中的问题模板</li>
                <li>生成详细的问题信息结构</li>
                <li>包含原文、位置、类别等完整信息</li>
            </ul>

            <h3>3. 问题片段生成器优化</h3>
            <ul>
                <li>从检测结果中提取详细信息</li>
                <li>使用实际的原文而不是占位符</li>
                <li>生成准确的问题描述和标准参考</li>
            </ul>
        </div>
    </div>

    <script>
        // 展示多种问题类型
        function showProblemTypes() {
            const problemTypes = [
                {
                    type: "对齐方式问题",
                    original: "关键词:人工智能;机器学习",
                    standard: "左对齐",
                    description: "当前为两端对齐，应为左对齐"
                },
                {
                    type: "标点符号问题", 
                    original: "关键词:深度学习;神经网络;算法",
                    standard: "中文冒号（：）",
                    description: "使用了英文冒号(:)，应使用中文冒号（：）"
                },
                {
                    type: "分隔符问题",
                    original: "关键词：机器学习;数据挖掘;人工智能",
                    standard: "中文分号（；）", 
                    description: "使用了英文分号(;)，应使用中文分号（；）"
                },
                {
                    type: "数量问题",
                    original: "关键词：计算机科学；软件工程",
                    standard: "3-5个关键词",
                    description: "当前2个关键词，建议3-5个"
                },
                {
                    type: "格式问题",
                    original: "关键词：算法设计；数据结构；",
                    standard: "最后一个关键词后不加分号",
                    description: "最后一个关键词后不应有分号"
                }
            ]

            let html = ''
            problemTypes.forEach((problem, index) => {
                html += `
                <div class="fragment-item">
                    <div class="fragment-header">
                        <span>问题类型 ${index + 1}: ${problem.type}</span>
                        <span class="severity-badge severity-severe">严重</span>
                    </div>
                    <div class="original-text">${problem.original}</div>
                    <div class="detail-row">
                        <span class="detail-label">标准:</span>
                        <span class="detail-value">${problem.standard}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">问题:</span>
                        <span class="detail-value">${problem.description}</span>
                    </div>
                </div>
                `
            })

            document.getElementById('problem-types-demo').innerHTML = html
        }

        // 页面加载时显示问题类型
        document.addEventListener('DOMContentLoaded', showProblemTypes)
    </script>
</body>
</html>
