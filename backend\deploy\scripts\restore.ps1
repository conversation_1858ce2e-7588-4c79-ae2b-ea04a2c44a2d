# ==================================================
# Word文档分析服务 - 数据恢复脚本
# ==================================================

param(
    [string]$BackupFile = "",               # 备份文件或目录路径
    [string]$RestoreType = "full",          # full, data, config, logs
    [string]$RestorePath = ".",             # 恢复目标路径
    [switch]$Force = $false,                # 强制覆盖现有文件
    [switch]$CreateBackup = $true,          # 恢复前创建当前状态备份
    [switch]$Verify = $true,                # 验证恢复完整性
    [switch]$DryRun = $false,               # 试运行模式(不实际恢复)
    [switch]$List = $false,                 # 仅列出可用备份
    [switch]$Info = $false,                 # 显示备份信息
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
Word文档分析服务 - 数据恢复脚本

用法:
    .\restore.ps1 [参数]

参数:
    -BackupFile     备份文件或目录路径 (必需，除非使用-List)
    -RestoreType    恢复类型 (full/data/config/logs) [默认: full]
                   full:   完整恢复(数据+配置+日志)
                   data:   仅恢复数据文件
                   config: 仅恢复配置文件  
                   logs:   仅恢复日志文件
    -RestorePath    恢复目标路径 [默认: 当前目录]
    -Force          强制覆盖现有文件 [默认: false]
    -CreateBackup   恢复前备份当前状态 [默认: true]
    -Verify         验证恢复完整性 [默认: true]
    -DryRun         试运行模式(不实际恢复) [默认: false]
    -List           列出可用备份文件
    -Info           显示指定备份文件的信息
    -Help           显示此帮助信息

示例:
    .\restore.ps1 -List                                 # 列出可用备份
    .\restore.ps1 -BackupFile "backup_full_20241219.zip" -Info  # 查看备份信息
    .\restore.ps1 -BackupFile "backup_full_20241219.zip"        # 完整恢复
    .\restore.ps1 -BackupFile "backup_data_20241219.zip" -RestoreType data  # 仅恢复数据
    .\restore.ps1 -BackupFile "backup.zip" -DryRun             # 试运行恢复
    .\restore.ps1 -BackupFile "backup.zip" -Force              # 强制恢复
"@ -ForegroundColor Green
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO"  { Write-Host $logMessage -ForegroundColor Green }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        default { Write-Host $logMessage }
    }
}

# 辅助函数：格式化文件大小
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size Bytes"
    }
}

# 列出可用备份
function List-Backups {
    $backupPaths = @(".\data\backups", ".\backups")
    $foundBackups = @()
    
    foreach ($path in $backupPaths) {
        if (Test-Path $path) {
            $backups = Get-ChildItem -Path $path -Filter "backup_*" | Sort-Object CreationTime -Descending
            foreach ($backup in $backups) {
                $foundBackups += @{
                    Path = $backup.FullName
                    Name = $backup.Name
                    Size = $backup.Length
                    Created = $backup.CreationTime
                    Type = if ($backup.Extension -eq ".zip") { "压缩" } else { "目录" }
                }
            }
        }
    }
    
    if ($foundBackups.Count -eq 0) {
        Write-Host "未找到备份文件" -ForegroundColor Yellow
        return
    }
    
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "可用备份列表" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    $index = 1
    foreach ($backup in $foundBackups) {
        $sizeStr = Format-FileSize $backup.Size
        Write-Host "$index. $($backup.Name)" -ForegroundColor Cyan
        Write-Host "   路径: $($backup.Path)" -ForegroundColor Gray
        Write-Host "   大小: $sizeStr" -ForegroundColor Gray
        Write-Host "   类型: $($backup.Type)" -ForegroundColor Gray
        Write-Host "   创建: $($backup.Created.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
        Write-Host ""
        $index++
    }
}

# 显示备份信息
function Show-BackupInfo {
    param([string]$BackupPath)
    
    if (!(Test-Path $BackupPath)) {
        Write-Error "备份文件不存在: $BackupPath"
        return
    }
    
    $item = Get-Item $BackupPath
    
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "备份文件信息" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "文件名: $($item.Name)" -ForegroundColor Cyan
    Write-Host "路径: $($item.FullName)" -ForegroundColor Gray
    Write-Host "大小: $(Format-FileSize $item.Length)" -ForegroundColor Gray
    Write-Host "创建时间: $($item.CreationTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
    Write-Host "修改时间: $($item.LastWriteTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
    Write-Host "类型: $(if ($item.Extension -eq '.zip') { '压缩文件' } else { '目录' })" -ForegroundColor Gray
    
    # 如果是ZIP文件，显示内容信息
    if ($item.Extension -eq ".zip") {
        try {
            Add-Type -AssemblyName "System.IO.Compression.FileSystem"
            $zip = [System.IO.Compression.ZipFile]::OpenRead($BackupPath)
            
            Write-Host "文件数量: $($zip.Entries.Count)" -ForegroundColor Gray
            
            # 查找元数据文件
            $metadataEntry = $zip.Entries | Where-Object { $_.Name -eq "backup_metadata.json" }
            if ($metadataEntry) {
                $stream = $metadataEntry.Open()
                $reader = New-Object System.IO.StreamReader($stream)
                $metadataJson = $reader.ReadToEnd()
                $reader.Close()
                $stream.Close()
                
                $metadata = $metadataJson | ConvertFrom-Json
                
                Write-Host "========================================" -ForegroundColor Green
                Write-Host "备份元数据" -ForegroundColor Green
                Write-Host "========================================" -ForegroundColor Green
                Write-Host "备份时间: $($metadata.timestamp)" -ForegroundColor Cyan
                Write-Host "备份类型: $($metadata.type)" -ForegroundColor Gray
                Write-Host "备份大小: $(Format-FileSize $metadata.total_size)" -ForegroundColor Gray
                Write-Host "文件数量: $($metadata.items_count)" -ForegroundColor Gray
                Write-Host "创建用户: $($metadata.created_by)" -ForegroundColor Gray
                Write-Host "创建机器: $($metadata.machine)" -ForegroundColor Gray
                Write-Host "备份版本: $($metadata.version)" -ForegroundColor Gray
            }
            
            $zip.Dispose()
        } catch {
            Write-Warning "无法读取ZIP文件内容: $($_.Exception.Message)"
        }
    }
}

# 处理特殊操作
if ($List) {
    List-Backups
    exit 0
}

if ($Info) {
    if ([string]::IsNullOrEmpty($BackupFile)) {
        Write-Error "使用 -Info 参数时必须指定 -BackupFile"
        exit 1
    }
    Show-BackupInfo $BackupFile
    exit 0
}

# 验证参数
if ([string]::IsNullOrEmpty($BackupFile)) {
    Write-Error "必须指定备份文件路径，使用 -BackupFile 参数"
    Write-Host "使用 -List 参数查看可用备份" -ForegroundColor Yellow
    exit 1
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "Word文档分析服务 - 数据恢复" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "备份文件: $BackupFile" -ForegroundColor Yellow
Write-Host "恢复类型: $RestoreType" -ForegroundColor Yellow
if ($DryRun) {
    Write-Host "模式: 试运行 (不实际恢复)" -ForegroundColor Magenta
}
Write-Host "========================================" -ForegroundColor Green

exit 0 