/**
 * 统一错误处理工具类
 * 提供错误分类、格式化和处理方式
 */

import { logger } from './logger'

export type ErrorType = 
  | 'network'
  | 'validation'
  | 'authentication'
  | 'authorization'
  | 'business'
  | 'unknown'

export interface AppError {
  type: ErrorType
  message: string
  code?: string | number
  details?: any
  timestamp: number
  context?: string
}

interface ErrorConfig {
  showToUser: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  reportToRemote: boolean
}

class ErrorHandler {
  private errorConfigs: Record<ErrorType, ErrorConfig> = {
    network: { showToUser: true, logLevel: 'error', reportToRemote: true },
    validation: { showToUser: true, logLevel: 'warn', reportToRemote: false },
    authentication: { showToUser: true, logLevel: 'warn', reportToRemote: true },
    authorization: { showToUser: true, logLevel: 'warn', reportToRemote: true },
    business: { showToUser: true, logLevel: 'error', reportToRemote: true },
    unknown: { showToUser: true, logLevel: 'error', reportToRemote: true }
  }

  /**
   * 处理错误
   */
  handle(error: Error | AppError | any, context?: string): void {
    const appError = this.normalizeError(error, context)
    const config = this.errorConfigs[appError.type]

    // 记录日志
    logger[config.logLevel](appError.message, {
      type: appError.type,
      code: appError.code,
      details: appError.details,
      context: appError.context
    })

    // 显示给用户
    if (config.showToUser) {
      this.showToUser(appError)
    }

    // 上报到远程服务器
    if (config.reportToRemote) {
      this.reportToRemote(appError)
    }
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: any, context?: string): AppError {
    if (this.isAppError(error)) {
      return error
    }

    if (error instanceof Error) {
      return {
        type: this.determineErrorType(error),
        message: error.message,
        details: { stack: error.stack },
        timestamp: Date.now(),
        context
      }
    }

    return {
      type: 'unknown',
      message: String(error),
      timestamp: Date.now(),
      context
    }
  }

  /**
   * 判断错误类型
   */
  private determineErrorType(error: any): ErrorType {
    // 🔥 修复：优先检查HTTP状态码
    if (error.response?.status) {
      const status = error.response.status;
      
      if (status === 400) {
        return 'business'  // 400业务错误，显示原始错误消息
      }
      if (status === 401) {
        return 'authentication'
      }
      if (status === 403) {
        return 'authorization'
      }
      if (status === 422) {
        return 'validation'
      }
      if (status >= 500) {
        return 'unknown'  // 服务器内部错误
      }
    }
    
    // 回退到消息内容检查
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network'
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return 'validation'
    }
    
    if (message.includes('unauthorized') || message.includes('401')) {
      return 'authentication'
    }
    
    if (message.includes('forbidden') || message.includes('403')) {
      return 'authorization'
    }
    
    return 'unknown'
  }

  /**
   * 检查是否为应用错误
   */
  private isAppError(error: any): error is AppError {
    return error && typeof error === 'object' && 'type' in error && 'message' in error
  }

  /**
   * 显示错误给用户
   */
  private showToUser(error: AppError): void {
    const userMessage = this.getUserFriendlyMessage(error)
    
    // 使用通知系统显示错误
    if (window.$notify) {
      window.$notify.error(userMessage, {
        title: '操作失败',
        duration: 5000
      })
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(error: AppError): string {
    const messages: Record<ErrorType, string> = {
      network: '网络连接失败，请检查网络连接后重试',
      validation: '输入信息有误，请检查后重新提交',
      authentication: '登录信息已过期，请重新登录',
      authorization: '您没有权限执行此操作',
      business: error.message,
      unknown: '系统异常，请稍后重试'
    }

    return messages[error.type] || error.message
  }

  /**
   * 上报错误到远程服务器
   */
  private async reportToRemote(error: AppError): Promise<void> {
    try {
      const reportUrl = import.meta.env.VITE_ERROR_REPORT_URL
      if (!reportUrl) return

      await fetch(reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...error,
          userAgent: navigator.userAgent,
          url: window.location.href,
          userId: localStorage.getItem('wms_user_id')
        })
      })
    } catch (reportError) {
      logger.warn('Failed to report error to remote server', reportError)
    }
  }

  /**
   * 创建应用错误
   */
  createError(
    type: ErrorType,
    message: string,
    options?: {
      code?: string | number
      details?: any
      context?: string
    }
  ): AppError {
    return {
      type,
      message,
      code: options?.code,
      details: options?.details,
      timestamp: Date.now(),
      context: options?.context
    }
  }

  /**
   * 便捷方法
   */
  networkError(message: string, details?: any): AppError {
    return this.createError('network', message, { details })
  }

  validationError(message: string, details?: any): AppError {
    return this.createError('validation', message, { details })
  }

  authError(message: string): AppError {
    return this.createError('authentication', message)
  }

  businessError(message: string, code?: string | number): AppError {
    return this.createError('business', message, { code })
  }

  /**
   * 公开的用户友好错误消息获取方法
   */
  getFriendlyErrorMessage(error: any): string {
    const appError = this.normalizeError(error)
    return this.getUserFriendlyMessage(appError)
  }
}

// 创建全局错误处理实例
export const errorHandler = new ErrorHandler()

// 便捷方法
export const handleError = errorHandler.handle.bind(errorHandler)
export const createError = errorHandler.createError.bind(errorHandler)
export const networkError = errorHandler.networkError.bind(errorHandler)
export const validationError = errorHandler.validationError.bind(errorHandler)
export const authError = errorHandler.authError.bind(errorHandler)
export const businessError = errorHandler.businessError.bind(errorHandler)
export const getFriendlyErrorMessage = errorHandler.getFriendlyErrorMessage.bind(errorHandler)

// 全局错误处理器
window.addEventListener('error', (event) => {
  handleError(event.error, 'Global Error Handler')
})

window.addEventListener('unhandledrejection', (event) => {
  handleError(event.reason, 'Unhandled Promise Rejection')
})

export default errorHandler 