#!/bin/bash

# ==================================================
# Word文档分析服务 - 自动化部署脚本
# ==================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
DOCKER_COMPOSE_FILE="$PROJECT_ROOT/docker-compose.production.yml"
ENV_FILE="$PROJECT_ROOT/.env.production"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_FILE="/var/log/word-service-deploy.log"

# 默认参数
ENVIRONMENT="production"
VERSION="latest"
FORCE=false
SKIP_BACKUP=false
HEALTH_CHECK_TIMEOUT=300

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}" | tee -a "$LOG_FILE"
}

print_info() {
    print_message "$BLUE" "INFO: $1"
}

print_success() {
    print_message "$GREEN" "SUCCESS: $1"
}

print_warning() {
    print_message "$YELLOW" "WARNING: $1"
}

print_error() {
    print_message "$RED" "ERROR: $1"
}

# 函数：显示帮助信息
show_help() {
    cat << EOF
Word文档分析服务部署脚本

用法: $0 [选项]

选项:
    -e, --environment ENVIRONMENT    部署环境 (development|staging|production) [默认: production]
    -v, --version VERSION           Docker镜像版本 [默认: latest]
    -f, --force                     强制重新部署，停止现有服务
    -s, --skip-backup               跳过数据库备份
    -t, --timeout SECONDS          健康检查超时时间 [默认: 300]
    -h, --help                      显示此帮助信息

示例:
    $0                              # 使用默认配置部署
    $0 -e staging -v v1.2.0         # 部署特定版本到staging环境
    $0 -f -s                        # 强制部署并跳过备份

EOF
}

# 函数：解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -v|--version)
                VERSION="$2"
                shift 2
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            -s|--skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            -t|--timeout)
                HEALTH_CHECK_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 函数：检查必需的依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    local deps=("docker" "docker-compose" "curl" "jq")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            print_error "未找到必需的依赖: $dep"
            exit 1
        fi
    done
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行"
        exit 1
    fi
    
    print_success "所有依赖检查通过"
}

# 函数：验证环境文件
validate_environment() {
    print_info "验证环境配置..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        print_error "环境配置文件不存在: $ENV_FILE"
        exit 1
    fi
    
    # 检查必需的环境变量
    local required_vars=("DATABASE_URL" "REDIS_URL" "SECRET_KEY")
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$ENV_FILE"; then
            print_error "环境文件中缺少必需变量: $var"
            exit 1
        fi
    done
    
    print_success "环境配置验证通过"
}

# 函数：创建必需的目录
create_directories() {
    print_info "创建必需的目录..."
    
    local dirs=("$BACKUP_DIR" "$PROJECT_ROOT/logs" "$PROJECT_ROOT/data/uploads" "$PROJECT_ROOT/data/temp")
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_info "创建目录: $dir"
        fi
    done
    
    print_success "目录创建完成"
}

# 函数：备份数据库
backup_database() {
    if [[ "$SKIP_BACKUP" == true ]]; then
        print_warning "跳过数据库备份"
        return 0
    fi
    
    print_info "开始数据库备份..."
    
    local backup_file="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # 检查PostgreSQL容器是否运行
    if docker ps --format "table {{.Names}}" | grep -q "word-postgres"; then
        if docker exec word-postgres pg_dump -U word_user word_service > "$backup_file"; then
            print_success "数据库备份完成: $backup_file"
        else
            print_error "数据库备份失败"
            exit 1
        fi
    else
        print_warning "PostgreSQL容器未运行，跳过备份"
    fi
}

# 函数：拉取最新镜像
pull_images() {
    print_info "拉取Docker镜像..."
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" pull; then
        print_success "镜像拉取完成"
    else
        print_error "镜像拉取失败"
        exit 1
    fi
}

# 函数：停止现有服务
stop_services() {
    print_info "停止现有服务..."
    
    if [[ "$FORCE" == true ]]; then
        print_warning "强制停止所有服务..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" down --remove-orphans --volumes
    else
        print_info "优雅停止服务..."
        docker-compose -f "$DOCKER_COMPOSE_FILE" stop
    fi
    
    print_success "服务停止完成"
}

# 函数：启动服务
start_services() {
    print_info "启动服务..."
    
    # 设置环境变量
    export ENVIRONMENT="$ENVIRONMENT"
    export VERSION="$VERSION"
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" up -d; then
        print_success "服务启动完成"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 函数：等待服务就绪
wait_for_services() {
    print_info "等待服务就绪..."
    
    local timeout=$HEALTH_CHECK_TIMEOUT
    local interval=10
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        # 检查容器状态
        local unhealthy_containers=$(docker-compose -f "$DOCKER_COMPOSE_FILE" ps --filter "health=unhealthy" -q)
        if [[ -z "$unhealthy_containers" ]]; then
            print_success "所有服务已就绪"
            return 0
        fi
        
        print_info "等待服务启动... ($elapsed/$timeout 秒)"
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    print_error "服务启动超时"
    return 1
}

# 函数：健康检查
health_check() {
    print_info "执行健康检查..."
    
    local health_url="http://localhost/health"
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        print_info "健康检查尝试 $attempt/$max_attempts..."
        
        if response=$(curl -s -f "$health_url" 2>/dev/null); then
            if echo "$response" | jq -e '.status == "healthy"' >/dev/null 2>&1; then
                print_success "健康检查通过"
                echo "$response" | jq '.'
                return 0
            fi
        fi
        
        sleep 10
        attempt=$((attempt + 1))
    done
    
    print_error "健康检查失败"
    return 1
}

# 函数：显示服务状态
show_service_status() {
    print_info "显示服务状态..."
    
    echo ""
    print_info "=== Docker容器状态 ==="
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    
    echo ""
    print_info "=== 服务日志（最后20行）==="
    docker-compose -f "$DOCKER_COMPOSE_FILE" logs --tail=20 word-service-1
    
    echo ""
    print_info "=== 磁盘使用情况 ==="
    df -h
    
    echo ""
    print_info "=== 内存使用情况 ==="
    free -h
}

# 函数：清理旧的备份文件
cleanup_old_backups() {
    print_info "清理旧备份文件..."
    
    # 保留最近30天的备份
    find "$BACKUP_DIR" -name "db_backup_*.sql" -mtime +30 -delete 2>/dev/null || true
    
    # 保留最近10个备份文件
    ls -t "$BACKUP_DIR"/db_backup_*.sql 2>/dev/null | tail -n +11 | xargs rm -f 2>/dev/null || true
    
    print_success "备份清理完成"
}

# 函数：发送通知（可选）
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以添加邮件、Slack、企业微信等通知逻辑
    print_info "通知: $message"
}

# 函数：回滚到上一个版本
rollback() {
    print_warning "开始回滚操作..."
    
    # 这里可以实现回滚逻辑
    # 例如：使用备份恢复数据库，启动上一个版本的容器等
    
    print_error "回滚功能待实现"
}

# 主部署流程
main() {
    print_info "开始部署Word文档分析服务..."
    print_info "环境: $ENVIRONMENT, 版本: $VERSION"
    
    # 创建日志文件
    touch "$LOG_FILE"
    
    # 执行部署步骤
    check_dependencies
    validate_environment
    create_directories
    
    # 如果服务正在运行，先备份
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "Up"; then
        backup_database
    fi
    
    pull_images
    stop_services
    start_services
    
    if wait_for_services && health_check; then
        show_service_status
        cleanup_old_backups
        
        print_success "部署成功完成！"
        print_info "访问地址: http://localhost"
        print_info "API文档: http://localhost/docs"
        print_info "监控面板: http://localhost:3000"
        
        send_notification "success" "Word文档分析服务部署成功"
        exit 0
    else
        print_error "部署失败"
        print_warning "可以查看日志: docker-compose -f $DOCKER_COMPOSE_FILE logs"
        print_warning "或执行回滚操作"
        
        send_notification "failure" "Word文档分析服务部署失败"
        exit 1
    fi
}

# 信号处理
trap 'print_error "部署被中断"; exit 1' INT TERM

# 解析参数并执行主流程
parse_arguments "$@"
main 