# Word文档分析服务 - 开发指南

## 🎯 开发概述

本指南为Word文档分析服务的开发人员提供完整的开发规范、环境配置和最佳实践。

## 🛠️ 开发环境设置

### 必需环境
- **Python 3.12+**
- **PostgreSQL 17.5+**
- **Redis** (推荐)
- **Microsoft Word** (用于COM接口)
- **Git**
- **Visual Studio Code** (推荐IDE)

### 开发工具安装
```bash
# 克隆项目
git clone <repository-url>
cd paper-check-win/backend

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install
```

### IDE配置

#### VS Code配置 (.vscode/settings.json)
```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "files.associations": {
        "*.yaml": "yaml",
        "*.yml": "yaml"
    }
}
```

#### VS Code扩展推荐
```json
{
    "recommendations": [
        "ms-python.python",
        "ms-python.vscode-pylance",
        "ms-python.black-formatter",
        "ms-python.flake8",
        "ms-python.mypy-type-checker",
        "redhat.vscode-yaml",
        "ms-vscode.powershell"
    ]
}
```

## 📐 代码规范

### Python代码风格

#### PEP 8 + Black格式化
```python
# 良好的代码示例
from typing import List, Optional, Dict, Any
import asyncio
from datetime import datetime

class UserService:
    """用户服务类
    
    提供用户相关的业务逻辑处理
    """
    
    def __init__(self, db: AsyncSession) -> None:
        self.db = db
    
    async def create_user(
        self, 
        user_data: Dict[str, Any]
    ) -> Optional[User]:
        """创建新用户
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            创建的用户对象，失败返回None
            
        Raises:
            ValueError: 用户数据无效时抛出
        """
        try:
            # 验证数据
            if not self._validate_user_data(user_data):
                raise ValueError("用户数据无效")
            
            # 创建用户
            user = User(**user_data)
            self.db.add(user)
            await self.db.commit()
            
            return user
        
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建用户失败: {e}")
            return None
```

#### 命名规范
```python
# 变量和函数 - snake_case
user_name = "test_user"
file_path = "/path/to/file"

async def process_document(file_path: str) -> Dict[str, Any]:
    pass

# 类名 - PascalCase
class DocumentProcessor:
    pass

class TaskManager:
    pass

# 常量 - UPPER_CASE
MAX_FILE_SIZE = 104857600
DEFAULT_TIMEOUT = 30

# 私有方法 - 前缀下划线
def _validate_input(data: Dict) -> bool:
    pass
```

### 类型注解规范
```python
from typing import List, Dict, Optional, Union, Tuple, Any
from datetime import datetime

# 函数类型注解
async def get_user_by_id(user_id: int) -> Optional[User]:
    pass

def process_tasks(
    tasks: List[Dict[str, Any]],
    timeout: int = 30
) -> Tuple[List[str], List[str]]:
    pass

# 变量类型注解
users: List[User] = []
config: Dict[str, Any] = {}
result: Optional[str] = None
```

### 文档字符串规范
```python
def analyze_document(
    file_path: str,
    analysis_type: str = "full",
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """分析Word文档
    
    对指定的Word文档进行内容分析和格式检测
    
    Args:
        file_path: Word文档的完整路径
        analysis_type: 分析类型，可选值: 'full', 'structure', 'content'
        options: 可选的分析参数配置
        
    Returns:
        包含分析结果的字典，包含以下键:
        - content: 文档内容文本
        - structure: 文档结构信息  
        - images: 图片信息列表
        - metadata: 文档元数据
        
    Raises:
        FileNotFoundError: 文档文件不存在
        ValueError: 分析类型无效
        COMError: Word COM接口错误
        
    Example:
        >>> result = analyze_document("test.docx", "full")
        >>> print(result["content"])
        "文档内容..."
    """
    pass
```

## 🏗️ 项目架构

### 目录结构说明
```
backend/
├── app/                    # 应用核心
│   ├── api/               # API路由层
│   │   └── v1/           # API版本1
│   │       ├── auth.py   # 认证端点
│   │       ├── documents.py # 文档处理端点
│   │       ├── tasks.py  # 任务管理端点
│   │       ├── images.py # 图片处理端点
│   │       └── system.py # 系统管理端点
│   ├── core/              # 核心功能模块
│   │   ├── config.py     # 配置管理
│   │   ├── security.py   # 安全认证
│   │   ├── exceptions.py # 异常定义
│   │   └── logging.py    # 日志配置
│   ├── database/          # 数据库层
│   │   ├── connection.py # 数据库连接
│   │   ├── crud.py       # CRUD操作
│   │   └── init_db.py    # 数据库初始化
│   ├── services/          # 业务服务层
│   │   ├── auth_service.py # 认证服务
│   │   ├── document_processor.py # 文档处理服务
│   │   ├── task_manager.py # 任务管理服务
│   │   └── file_handler.py # 文件处理服务
│   ├── models/            # 数据模型
│   │   ├── user.py       # 用户模型
│   │   ├── task.py       # 任务模型
│   │   └── document.py   # 文档模型
│   ├── analyzers/         # 分析器模块
│   │   ├── content_parser.py # 内容解析器
│   │   └── format_analyzer.py # 格式分析器
│   ├── checkers/          # 检测器模块
│   │   ├── structure_checker.py # 结构检测
│   │   └── format_checker.py # 格式检测
│   └── main.py           # 应用入口
├── tests/                 # 测试代码
├── config/               # 配置文件
├── docs/                 # 项目文档
└── requirements.txt      # 依赖管理
```

### 分层架构原则

#### 1. API层 (app/api/)
- 负责HTTP请求处理
- 参数验证和响应格式化
- 路由定义和端点管理
- 依赖注入配置

```python
# app/api/v1/documents.py
from fastapi import APIRouter, Depends, HTTPException, UploadFile
from app.services.document_processor import DocumentProcessor
from app.core.security import get_current_user

router = APIRouter(prefix="/documents", tags=["documents"])

@router.post("/upload")
async def upload_document(
    file: UploadFile,
    current_user: User = Depends(get_current_user),
    processor: DocumentProcessor = Depends()
) -> Dict[str, Any]:
    """上传文档进行分析"""
    if not file.filename.endswith('.docx'):
        raise HTTPException(400, "只支持.docx格式文件")
    
    result = await processor.process_document(file, current_user.id)
    return {"task_id": result.task_id, "status": "processing"}
```

#### 2. 服务层 (app/services/)
- 实现核心业务逻辑
- 协调各个组件
- 事务管理和错误处理

```python
# app/services/document_processor.py
class DocumentProcessor:
    """文档处理服务"""
    
    def __init__(self, db: AsyncSession, task_manager: TaskManager):
        self.db = db
        self.task_manager = task_manager
    
    async def process_document(
        self, 
        file: UploadFile, 
        user_id: int
    ) -> Task:
        """处理上传的文档"""
        try:
            # 保存文件
            file_path = await self._save_file(file)
            
            # 创建任务
            task = await self.task_manager.create_task({
                "user_id": user_id,
                "file_path": file_path,
                "task_type": "document_analysis"
            })
            
            # 异步处理
            asyncio.create_task(self._analyze_document(task.id, file_path))
            
            return task
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise
```

#### 3. 数据层 (app/database/)
- 数据库操作封装
- CRUD函数实现
- 数据模型映射

```python
# app/database/crud.py
class UserCRUD:
    """用户数据访问对象"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """创建用户"""
        query = """
        INSERT INTO users (username, email, hashed_password, created_at)
        VALUES (:username, :email, :hashed_password, :created_at)
        RETURNING id, username, email, created_at
        """
        
        result = await self.db.execute(text(query), {
            "username": user_data["username"],
            "email": user_data["email"],
            "hashed_password": user_data["hashed_password"],
            "created_at": datetime.utcnow()
        })
        
        return result.fetchone()
```

## 🔌 API开发规范

### RESTful API设计
```python
# 资源命名规范
GET    /api/v1/users          # 获取用户列表
POST   /api/v1/users          # 创建用户
GET    /api/v1/users/{id}     # 获取特定用户
PUT    /api/v1/users/{id}     # 更新用户
DELETE /api/v1/users/{id}     # 删除用户

# 嵌套资源
GET    /api/v1/users/{id}/tasks    # 获取用户的任务列表
POST   /api/v1/users/{id}/tasks    # 为用户创建任务
```

### 响应格式标准
```python
# 成功响应
{
    "data": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>"
    },
    "meta": {
        "timestamp": "2024-01-01T12:00:00Z",
        "version": "v1"
    }
}

# 列表响应
{
    "data": [
        {"id": 1, "name": "item1"},
        {"id": 2, "name": "item2"}
    ],
    "meta": {
        "total": 2,
        "page": 1,
        "per_page": 10,
        "pages": 1
    }
}

# 错误响应
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "输入数据无效",
        "details": {
            "field": "email",
            "issue": "格式不正确"
        }
    },
    "meta": {
        "timestamp": "2024-01-01T12:00:00Z",
        "request_id": "req_123456"
    }
}
```

### 错误处理规范
```python
# app/core/exceptions.py
class WordServiceException(Exception):
    """服务基础异常"""
    def __init__(self, message: str, code: str = "INTERNAL_ERROR"):
        self.message = message
        self.code = code
        super().__init__(message)

class ValidationError(WordServiceException):
    """数据验证错误"""
    def __init__(self, message: str, field: str = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field = field

class DocumentProcessingError(WordServiceException):
    """文档处理错误"""
    def __init__(self, message: str, file_path: str = None):
        super().__init__(message, "DOCUMENT_ERROR")
        self.file_path = file_path

# 全局异常处理器
@app.exception_handler(WordServiceException)
async def word_service_exception_handler(request: Request, exc: WordServiceException):
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.code,
                "message": exc.message
            },
            "meta": {
                "timestamp": datetime.utcnow().isoformat(),
                "request_id": getattr(request.state, "request_id", None)
            }
        }
    )
```

## 🔒 安全开发规范

### 认证和授权
```python
# JWT令牌处理
from app.core.security import create_access_token, verify_token

# 生成令牌
def create_user_token(user: User) -> str:
    data = {
        "sub": str(user.id),
        "username": user.username,
        "exp": datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    }
    return create_access_token(data)

# 权限检查装饰器
def require_permission(permission: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, current_user: User = Depends(get_current_user), **kwargs):
            if not user_has_permission(current_user, permission):
                raise HTTPException(403, "权限不足")
            return await func(*args, current_user=current_user, **kwargs)
        return wrapper
    return decorator
```

### 输入验证
```python
# 使用Pydantic进行数据验证
from pydantic import BaseModel, validator, EmailStr

class UserCreateModel(BaseModel):
    username: str
    email: EmailStr
    password: str
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3 or len(v) > 20:
            raise ValueError('用户名长度必须在3-20字符之间')
        if not v.isalnum():
            raise ValueError('用户名只能包含字母和数字')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('密码至少8位')
        if not any(c.isupper() for c in v):
            raise ValueError('密码必须包含大写字母')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含数字')
        return v
```

### SQL注入防护
```python
# 使用参数化查询
async def get_user_by_username(username: str) -> Optional[User]:
    query = """
    SELECT id, username, email, created_at 
    FROM users 
    WHERE username = :username
    """
    result = await db.execute(text(query), {"username": username})
    return result.fetchone()

# 避免字符串拼接
# ❌ 错误示例
query = f"SELECT * FROM users WHERE username = '{username}'"

# ✅ 正确示例  
query = "SELECT * FROM users WHERE username = :username"
result = await db.execute(text(query), {"username": username})
```

## 🐛 调试和日志

### 日志配置
```python
# app/core/logging.py
import logging
import structlog
from datetime import datetime

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# 使用示例
logger = structlog.get_logger(__name__)

async def process_document(file_path: str, user_id: int):
    logger.info(
        "开始处理文档",
        file_path=file_path,
        user_id=user_id,
        action="document_processing_start"
    )
    
    try:
        result = await analyze_document(file_path)
        logger.info(
            "文档处理完成",
            file_path=file_path,
            user_id=user_id,
            result_size=len(result),
            action="document_processing_success"
        )
        return result
    except Exception as e:
        logger.error(
            "文档处理失败",
            file_path=file_path,
            user_id=user_id,
            error=str(e),
            action="document_processing_error",
            exc_info=True
        )
        raise
```

### 调试技巧
```python
# 使用pdb进行调试
import pdb; pdb.set_trace()

# 异步调试
import asyncio
import pdb

async def debug_async_function():
    await asyncio.sleep(0)  # 让出控制权
    pdb.set_trace()  # 设置断点

# 性能分析
import cProfile
import pstats

def profile_function(func):
    """性能分析装饰器"""
    def wrapper(*args, **kwargs):
        pr = cProfile.Profile()
        pr.enable()
        result = func(*args, **kwargs)
        pr.disable()
        
        stats = pstats.Stats(pr)
        stats.sort_stats('cumulative')
        stats.print_stats()
        
        return result
    return wrapper
```

## 🧪 开发测试

### 单元测试编写
```python
# 为每个模块编写测试
# tests/unit/test_user_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.user_service import UserService

@pytest.fixture
def user_service():
    mock_db = Mock()
    return UserService(mock_db)

async def test_create_user_success(user_service):
    """测试成功创建用户"""
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    with patch.object(user_service, '_validate_user_data', return_value=True):
        user = await user_service.create_user(user_data)
        assert user.username == "testuser"

async def test_create_user_validation_error(user_service):
    """测试用户数据验证失败"""
    invalid_data = {"username": "a"}  # 用户名太短
    
    with pytest.raises(ValueError, match="用户名长度"):
        await user_service.create_user(invalid_data)
```

### API测试
```python
# tests/integration/test_api.py
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_user_registration_flow():
    """测试用户注册流程"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 注册用户
        response = await client.post("/api/v1/auth/register", json={
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpass123"
        })
        assert response.status_code == 200
        
        # 验证响应格式
        data = response.json()
        assert "token" in data
        assert data["user"]["username"] == "newuser"
```

## 📊 性能优化

### 数据库优化
```python
# 使用连接池
from sqlalchemy.ext.asyncio import create_async_engine

engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=40,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False
)

# 批量操作
async def create_multiple_tasks(task_data_list: List[Dict]) -> List[Task]:
    """批量创建任务"""
    query = """
    INSERT INTO tasks (user_id, filename, file_path, task_type, status, created_at)
    VALUES (:user_id, :filename, :file_path, :task_type, :status, :created_at)
    """
    
    await db.execute_many(text(query), task_data_list)
    await db.commit()

# 查询优化
async def get_user_tasks_optimized(user_id: int, limit: int = 10) -> List[Task]:
    """优化的用户任务查询"""
    query = """
    SELECT t.id, t.filename, t.status, t.created_at,
           COUNT(*) OVER() as total_count
    FROM tasks t
    WHERE t.user_id = :user_id
    ORDER BY t.created_at DESC
    LIMIT :limit
    """
    
    result = await db.execute(text(query), {"user_id": user_id, "limit": limit})
    return result.fetchall()
```

### 缓存策略
```python
# Redis缓存使用
import redis.asyncio as redis
from functools import wraps

redis_client = redis.Redis.from_url(REDIS_URL)

def cache_result(expire_time: int = 3600):
    """缓存结果装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            await redis_client.setex(
                cache_key, 
                expire_time, 
                json.dumps(result, default=str)
            )
            
            return result
        return wrapper
    return decorator

# 使用示例
@cache_result(expire_time=1800)
async def get_system_statistics() -> Dict[str, Any]:
    """获取系统统计信息（缓存30分钟）"""
    # 复杂的统计查询
    pass
```

## 🚀 部署准备

### 环境变量管理
```python
# app/core/config.py
from pydantic import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "Word Document Analysis Service"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    
    # 数据库配置
    DATABASE_URL: str
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 安全配置
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    
    # 文件配置
    MAX_FILE_SIZE: int = 104857600  # 100MB
    UPLOAD_PATH: str = "data/uploads"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 健康检查
```python
# app/api/v1/health.py
@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """系统健康检查"""
    checks = {
        "database": await check_database_connection(),
        "redis": await check_redis_connection(),
        "disk_space": check_disk_space(),
        "memory": check_memory_usage()
    }
    
    all_healthy = all(check["status"] == "ok" for check in checks.values())
    
    return {
        "status": "healthy" if all_healthy else "unhealthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "checks": checks
    }
```

## 📚 最佳实践总结

### 1. 代码质量
- 遵循PEP 8和类型注解
- 编写全面的单元测试
- 使用代码检查工具(flake8, mypy)
- 定期代码审查

### 2. 安全考虑
- 所有输入都要验证
- 使用参数化查询防止SQL注入
- 实施适当的认证和授权
- 记录安全相关事件

### 3. 性能优化
- 使用数据库连接池
- 实施适当的缓存策略
- 优化数据库查询
- 监控应用性能

### 4. 错误处理
- 定义清晰的异常层次
- 记录详细的错误日志
- 提供用户友好的错误信息
- 实施全局异常处理

---

🎯 **开发目标：高质量、高性能、高可用的企业级服务** 