import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types'

// 扩展Axios配置类型
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  metadata?: {
    startTime: Date
  }
  _retry?: boolean
}

interface RetryConfig {
  retries: number
  retryDelay: number
  retryCondition?: (error: any) => boolean
}

class ApiService {
  private instance: AxiosInstance
  private refreshTokenPromise: Promise<string | null> | null = null

  constructor() {
    this.instance = axios.create({
      baseURL: (window as any).__API_BASE_URL__ || 'http://localhost:8000/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        
        // 添加请求时间戳用于超时处理
        ;(config as any).metadata = { startTime: new Date() }
        
        return config
      },
      (error) => {
        console.error('Request interceptor error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 记录响应时间
        const config = response.config as any
        if (config.metadata) {
          const responseTime = new Date().getTime() - config.metadata.startTime.getTime()
          console.debug(`API ${config.method?.toUpperCase()} ${config.url} took ${responseTime}ms`)
        }
        
        return response
      },
      async (error) => {
        const originalRequest = error.config
        
        // Token过期处理
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true
          
          try {
            const newToken = await this.refreshAccessToken()
            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`
              return this.instance(originalRequest)
            }
          } catch (refreshError) {
            this.handleAuthError()
            return Promise.reject(refreshError)
          }
        }
        
        // 网络错误和服务器错误处理
        if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          console.error('Network error:', error.message)
        }
        
        return Promise.reject(error)
      }
    )
  }

  // 获取存储的Token
  private getToken(): string | null {
    return localStorage.getItem('access_token')
  }

  // 刷新访问令牌
  private async refreshAccessToken(): Promise<string | null> {
    if (this.refreshTokenPromise) {
      return this.refreshTokenPromise
    }

    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      return null
    }

    this.refreshTokenPromise = (async () => {
      try {
        const response = await this.instance.post<ApiResponse<{ access_token: string }>>('/v1/auth/refresh', {
          refresh_token: refreshToken
        })

        const newToken = response.data.data?.access_token
        if (newToken) {
          localStorage.setItem('access_token', newToken)
          return newToken
        }
        return null
      } catch (error) {
        this.handleAuthError()
        return null
      } finally {
        this.refreshTokenPromise = null
      }
    })()

    return this.refreshTokenPromise
  }

  // 处理认证错误
  private handleAuthError(): void {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
    
    // 触发登出事件
    window.dispatchEvent(new Event('auth:logout'))
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.instance.get('/health', { timeout: 5000 })
      return response.status === 200
    } catch (error) {
      console.error('Health check failed:', error)
      return false
    }
  }

  // 自动重试机制
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    config: RetryConfig = { retries: 3, retryDelay: 1000 }
  ): Promise<T> {
    let lastError: any
    
    for (let attempt = 0; attempt <= config.retries; attempt++) {
      try {
        return await requestFn()
      } catch (error: any) {
        lastError = error
        
        // 检查是否应该重试
        const status = error.response?.status;
        if (
          attempt === config.retries ||
          (config.retryCondition && !config.retryCondition(error)) ||
          (status && status >= 400 && status < 500) // 不重试所有4xx客户端错误
        ) {
          break
        }
        
        // 等待重试
        await new Promise(resolve => 
          setTimeout(resolve, config.retryDelay * Math.pow(2, attempt))
        )
      }
    }
    
    throw lastError
  }

  // GET请求
  async get<T = any>(
    url: string, 
    config?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<T> {
    const requestFn = async () => {
      const response = await this.instance.get<ApiResponse<T>>(url, config)
      // 处理后端的统一响应格式 {success: true, data: T, message: string}
      return (response.data.data ?? response.data) as T
    }

    if (retryConfig) {
      return this.retryRequest(requestFn, retryConfig)
    }
    
    return requestFn()
  }

  // POST请求
  async post<T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<T> {
    const requestFn = async () => {
      const response = await this.instance.post<ApiResponse<T>>(url, data, config)
      // 处理后端的统一响应格式 {success: true, data: T, message: string}
      return (response.data.data ?? response.data) as T
    }

    if (retryConfig) {
      return this.retryRequest(requestFn, retryConfig)
    }
    
    return requestFn()
  }

  // PUT请求
  async put<T = any>(
    url: string, 
    data?: any, 
    config?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<T> {
    const requestFn = async () => {
      const response = await this.instance.put<ApiResponse<T>>(url, data, config)
      return (response.data.data ?? response.data) as T
    }

    if (retryConfig) {
      return this.retryRequest(requestFn, retryConfig)
    }
    
    return requestFn()
  }

  // DELETE请求
  async delete<T = any>(
    url: string, 
    config?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<T> {
    const requestFn = async () => {
      const response = await this.instance.delete<ApiResponse<T>>(url, config)
      return (response.data.data ?? response.data) as T
    }

    if (retryConfig) {
      return this.retryRequest(requestFn, retryConfig)
    }
    
    return requestFn()
  }

  // 文件上传（支持分片上传）
  async upload<T = any>(
    url: string, 
    file: File, 
    options?: {
      onProgress?: (progress: number) => void
      chunkSize?: number
      maxRetries?: number
    }
  ): Promise<T> {
    const { onProgress, chunkSize = 5 * 1024 * 1024, maxRetries = 3 } = options || {}
    
    // 小文件直接上传
    if (file.size <= chunkSize) {
      return this.uploadFile(url, file, onProgress, maxRetries)
    }
    
    // 大文件分片上传
    return this.uploadFileInChunks(url, file, chunkSize, onProgress, maxRetries)
  }

  // 普通文件上传
  private async uploadFile<T>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    maxRetries: number = 3
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }

    return this.retryRequest(async () => {
      const response = await this.instance.post<ApiResponse<T>>(url, formData, config)
      return (response.data.data ?? response.data) as T
    }, { retries: maxRetries, retryDelay: 1000 })
  }

  // 分片上传实现
  private async uploadFileInChunks<T>(
    url: string,
    file: File,
    chunkSize: number,
    onProgress?: (progress: number) => void,
    maxRetries: number = 3
  ): Promise<T> {
    const totalChunks = Math.ceil(file.size / chunkSize)
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    let uploadedBytes = 0

    // 1. 初始化分片上传
    await this.post(`${url}/init`, {
      filename: file.name,
      fileSize: file.size,
      totalChunks,
      uploadId
    })

    // 2. 上传每个分片
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize
      const end = Math.min(start + chunkSize, file.size)
      const chunk = file.slice(start, end)

      const chunkFormData = new FormData()
      chunkFormData.append('chunk', chunk)
      chunkFormData.append('chunkIndex', chunkIndex.toString())
      chunkFormData.append('uploadId', uploadId)

      await this.retryRequest(async () => {
        await this.instance.post(`${url}/chunk`, chunkFormData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
      }, { retries: maxRetries, retryDelay: 1000 })

      uploadedBytes += chunk.size
      if (onProgress) {
        onProgress(Math.round((uploadedBytes / file.size) * 100))
      }
    }

    // 3. 完成分片上传
    const response = await this.post<T>(`${url}/complete`, { uploadId })
    return response
  }

  // 下载文件
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, {
      responseType: 'blob'
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 公开访问axios实例的方法
  getAxiosInstance(): AxiosInstance {
    return this.instance
  }

  // 公开获取Token的方法
  getStoredToken(): string | null {
    return this.getToken()
  }
}

export const apiService = new ApiService()
export default apiService 