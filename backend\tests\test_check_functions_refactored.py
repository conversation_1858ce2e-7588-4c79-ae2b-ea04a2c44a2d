"""
检查函数重构后的测试

该测试文件验证重构后的检查函数功能，包括：
1. 检查函数的返回值格式
2. 参数解析的正确性
3. 错误处理机制
4. 结果的一致性
"""

import pytest
from unittest.mock import Mock, patch

from app.services.document_analyzer import (
    check_section_order,
    # 🗑️ check_content_length 已移除，内容检查已整合到 check_section_order 中
    check_headings_by_level,
    check_paragraph_format,
    check_text_format,
    check_page_setup,
    check_header_footer_format,
    check_references_format,
    check_abstract_and_keywords
)
from app.models.check_result import CheckResult, CheckSeverity
from app.services.document_processor import DocumentData


class TestCheckFunctionReturnFormat:
    """测试检查函数返回值格式"""

    def test_check_section_order_return_format(self, sample_document_data):
        """测试章节顺序检查函数返回格式"""
        params = {
            "standard_structure": [
                {"name": "封面", "required": True, "identifiers": ["封面"]},
                {"name": "摘要", "required": True, "identifiers": ["摘要"]}
            ]
        }
        
        result = check_section_order(sample_document_data, params)
        
        # 验证返回值类型和基本结构
        assert isinstance(result, CheckResult)
        assert hasattr(result, 'rule_id')
        assert hasattr(result, 'rule_name')
        assert hasattr(result, 'passed')
        assert hasattr(result, 'severity')
        assert hasattr(result, 'message')
        assert isinstance(result.passed, bool)
        assert isinstance(result.severity, CheckSeverity)
        assert isinstance(result.message, str)

    # 🗑️ test_check_content_length_return_format 已移除
    # 内容检查现在已整合到 check_section_order 函数中

    def test_check_headings_by_level_return_format(self, sample_document_data):
        """测试标题格式检查函数返回格式"""
        params = {
            "style": {
                "font_family": {"value": "黑体"},
                "font_size": {"value": "三号", "point_size": 16},
                "alignment": {"value": "center"},
                "bold": {"value": True}
            },
            "pattern": "^第[一二三四五六七八九十]+章\\s.*"
        }
        
        result = check_headings_by_level(sample_document_data, params)
        
        # 验证返回值格式
        assert isinstance(result, CheckResult)
        assert result.rule_name is not None
        assert result.message is not None

    def test_check_paragraph_format_return_format(self, sample_document_data):
        """测试段落格式检查函数返回格式"""
        params = {
            "style": {
                "font_family": {"value": "Times New Roman"},
                "font_size": {"value": "三号", "point_size": 16},
                "alignment": {"value": "center"},
                "bold": {"value": True}
            },
            "exact_text": "ABSTRACT"
        }
        
        result = check_paragraph_format(sample_document_data, params)
        
        # 验证返回值格式
        assert isinstance(result, CheckResult)
        assert result.severity in [CheckSeverity.INFO, CheckSeverity.WARNING, CheckSeverity.ERROR, CheckSeverity.CRITICAL]


class TestCheckFunctionParameterHandling:
    """测试检查函数参数处理"""

    def test_check_functions_handle_resolved_params(self, sample_document_data):
        """测试检查函数能正确处理已解析的参数"""
        # 模拟已解析的参数（不包含$ref）
        resolved_params = {
            "style": {
                "font_family": "黑体",
                "font_size": 16,
                "font_size_name": "三号",
                "alignment": "center",
                "bold": True,
                "spacing_before": 24,
                "spacing_after": 18
            },
            "pattern": "^第[一二三四五六七八九十]+章\\s.*"
        }
        
        # 所有检查函数都应该能处理已解析的参数而不抛出异常
        functions_to_test = [
            check_section_order,
            # 🗑️ check_content_length 已移除
            check_headings_by_level,
            check_paragraph_format,
            check_text_format,
            check_page_setup,
            check_header_footer_format,
            check_references_format,
            check_abstract_and_keywords
        ]
        
        for func in functions_to_test:
            try:
                result = func(sample_document_data, resolved_params)
                assert isinstance(result, CheckResult)
            except Exception as e:
                pytest.fail(f"函数 {func.__name__} 处理已解析参数时出错: {e}")

    def test_check_functions_handle_empty_params(self, sample_document_data):
        """测试检查函数处理空参数"""
        empty_params = {}
        
        functions_to_test = [
            check_section_order,
            # 🗑️ check_content_length 已移除
            check_headings_by_level,
            check_paragraph_format,
            check_text_format,
            check_page_setup,
            check_header_footer_format,
            check_references_format,
            check_abstract_and_keywords
        ]
        
        for func in functions_to_test:
            try:
                result = func(sample_document_data, empty_params)
                assert isinstance(result, CheckResult)
                # 空参数可能导致检查失败，但不应该抛出异常
            except Exception as e:
                pytest.fail(f"函数 {func.__name__} 处理空参数时出错: {e}")


class TestCheckFunctionConsistency:
    """测试检查函数一致性"""

    def test_all_check_functions_return_check_result(self, sample_document_data):
        """测试所有检查函数都返回CheckResult对象"""
        from app.services.document_analyzer import CHECK_FUNCTIONS
        
        test_params = {"test": "value"}
        
        for func_name, func in CHECK_FUNCTIONS.items():
            result = func(sample_document_data, test_params)
            assert isinstance(result, CheckResult), f"函数 {func_name} 没有返回CheckResult对象"

    def test_check_result_has_required_fields(self, sample_document_data):
        """测试CheckResult对象包含必需字段"""
        from app.services.document_analyzer import CHECK_FUNCTIONS
        
        test_params = {"test": "value"}
        
        for func_name, func in CHECK_FUNCTIONS.items():
            result = func(sample_document_data, test_params)
            
            # 验证必需字段
            assert hasattr(result, 'rule_id'), f"函数 {func_name} 返回的结果缺少rule_id"
            assert hasattr(result, 'rule_name'), f"函数 {func_name} 返回的结果缺少rule_name"
            assert hasattr(result, 'passed'), f"函数 {func_name} 返回的结果缺少passed"
            assert hasattr(result, 'severity'), f"函数 {func_name} 返回的结果缺少severity"
            assert hasattr(result, 'message'), f"函数 {func_name} 返回的结果缺少message"
            
            # 验证字段类型
            assert isinstance(result.passed, bool), f"函数 {func_name} 的passed字段不是布尔值"
            assert isinstance(result.severity, CheckSeverity), f"函数 {func_name} 的severity字段不是CheckSeverity枚举"
            assert isinstance(result.message, str), f"函数 {func_name} 的message字段不是字符串"


class TestSpecificCheckFunctions:
    """测试特定检查函数的逻辑"""

    # 🗑️ 已移除 test_check_content_length_with_word_count 和 test_check_content_length_with_item_count
    # 内容检查功能现在已整合到 check_section_order 函数中，通过 content_requirements 参数处理

    def test_check_section_order_with_content_requirements(self, sample_document_data):
        """测试章节顺序检查函数的内容要求功能"""
        params = {
            "standard_structure": [
                {
                    "name": "中文摘要",
                    "required": True,
                    "identifiers": ["摘要"],
                    "content_requirements": {
                        "min": 300,
                        "max": 500,
                        "unit": "字",
                        "count_method": "characters",
                        "severity": "warning",
                        "errorMessage": "中文摘要字数建议在300-500字之间。当前：{current_count}字。"
                    }
                }
            ]
        }

        result = check_section_order(sample_document_data, params)

        # 验证返回值格式
        assert isinstance(result, CheckResult)
        # 在真实实现中，应该检查实际字数和内容要求

    def test_check_headings_pattern_matching(self, sample_document_data):
        """测试标题模式匹配"""
        params = {
            "style": {
                "font_family": {"value": "黑体"},
                "font_size": {"value": "三号", "point_size": 16}
            },
            "pattern": "^第[一二三四五六七八九十]+章\\s.*"
        }
        
        result = check_headings_by_level(sample_document_data, params)
        
        # 当前是存根实现，应该返回通过
        assert isinstance(result, CheckResult)
        # 在真实实现中，应该检查标题是否匹配模式

    def test_check_paragraph_exact_text(self, sample_document_data):
        """测试段落精确文本匹配"""
        params = {
            "style": {
                "font_family": {"value": "Times New Roman"},
                "font_size": {"value": "三号", "point_size": 16},
                "alignment": {"value": "center"},
                "bold": {"value": True}
            },
            "exact_text": "ABSTRACT"
        }
        
        result = check_paragraph_format(sample_document_data, params)
        
        # 当前是存根实现，应该返回通过
        assert isinstance(result, CheckResult)
        # 在真实实现中，应该检查段落文本是否完全匹配


class TestErrorHandling:
    """测试错误处理"""

    def test_check_functions_handle_malformed_params(self, sample_document_data):
        """测试检查函数处理格式错误的参数"""
        malformed_params = {
            "style": "not_a_dict",  # 应该是字典
            "pattern": 123  # 应该是字符串
        }
        
        # 检查函数应该优雅地处理错误参数
        result = check_headings_by_level(sample_document_data, malformed_params)
        assert isinstance(result, CheckResult)
        # 可能返回失败，但不应该抛出异常

    def test_check_functions_handle_none_document_data(self):
        """测试检查函数处理None文档数据"""
        params = {"test": "value"}
        
        # 大多数检查函数应该能处理None输入而不崩溃
        try:
            result = check_text_format(None, params)
            assert isinstance(result, CheckResult)
        except Exception:
            # 如果抛出异常，应该是可预期的异常类型
            pass
