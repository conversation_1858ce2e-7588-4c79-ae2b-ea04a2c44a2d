import requests
import json
import time
import os

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heiba<PERSON>n5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def upload_document(token):
    """上传测试文档"""
    try:
        upload_url = "http://localhost:8000/api/v1/documents/upload"
        headers = {"Authorization": f"Bearer {token}"}
        
        # 检查测试文档是否存在
        test_doc_path = "docs/test.docx"
        if not os.path.exists(test_doc_path):
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return None
        
        with open(test_doc_path, 'rb') as f:
            files = {"file": ("test.docx", f, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")}
            data = {"document_type": "thesis"}
            
            response = requests.post(upload_url, headers=headers, files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            document_id = result.get("data", {}).get("document_id")
            print(f"✅ 文档上传成功: {document_id}")
            return document_id
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上传文档失败: {str(e)}")
        return None

def create_task(token, document_id):
    """创建检测任务"""
    try:
        task_url = "http://localhost:8000/api/v1/tasks"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        
        task_data = {
            "document_id": document_id,
            "task_type": "format_check",
            "config": {
                "check_format": True,
                "check_citation": True,
                "check_structure": True
            }
        }
        
        response = requests.post(task_url, headers=headers, json=task_data)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("data", {}).get("task_id")
            print(f"✅ 任务创建成功: {task_id}")
            return task_id
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建任务失败: {str(e)}")
        return None

def wait_for_task_completion(token, task_id, max_wait_time=60):
    """等待任务完成"""
    try:
        task_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            response = requests.get(task_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                task_data = result.get("data", {})
                status = task_data.get("status")
                
                print(f"📊 任务状态: {status}")
                
                if status == "completed":
                    print("✅ 任务完成")
                    return True
                elif status == "failed":
                    print("❌ 任务失败")
                    return False
                else:
                    print("⏳ 等待任务完成...")
                    time.sleep(5)
            else:
                print(f"❌ 获取任务状态失败: {response.status_code}")
                return False
        
        print("⏰ 等待超时")
        return False
        
    except Exception as e:
        print(f"❌ 等待任务完成失败: {str(e)}")
        return False

def test_problem_fragments(token, task_id):
    """测试问题片段API"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 20
        })
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 问题片段API测试成功!")
            print(f"📋 问题片段总数: {data['data']['total_count']}")
            
            fragments = data['data']['fragments']
            for i, fragment in enumerate(fragments[:3]):
                print(f"\n📌 问题片段 {i+1}:")
                print(f"   结构: {fragment['structure']}")
                print(f"   严重程度: {fragment['severity']}")
                print(f"   原文: {fragment['original_text'][:50]}...")
                print(f"   问题描述: {fragment['problem_description']}")
            
            return True
        else:
            print(f"❌ 问题片段API测试失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试问题片段失败: {str(e)}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始完整工作流测试")
    print("=" * 60)
    
    # 1. 获取认证令牌
    print("🔐 步骤1: 获取认证令牌...")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        return
    
    # 2. 上传文档
    print("\n📄 步骤2: 上传测试文档...")
    document_id = upload_document(token)
    if not document_id:
        print("❌ 无法上传文档，测试终止")
        return
    
    # 3. 创建任务
    print("\n⚙️ 步骤3: 创建检测任务...")
    task_id = create_task(token, document_id)
    if not task_id:
        print("❌ 无法创建任务，测试终止")
        return
    
    # 4. 等待任务完成
    print("\n⏳ 步骤4: 等待任务完成...")
    if not wait_for_task_completion(token, task_id):
        print("❌ 任务未完成，继续测试问题片段API...")
    
    # 5. 测试问题片段API
    print("\n🔍 步骤5: 测试问题片段API...")
    if test_problem_fragments(token, task_id):
        print("\n✅ 所有测试通过!")
        print(f"🌐 前端统计页面: http://localhost:3000/document/{task_id}/statistics")
    else:
        print("\n❌ 问题片段API测试失败")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
