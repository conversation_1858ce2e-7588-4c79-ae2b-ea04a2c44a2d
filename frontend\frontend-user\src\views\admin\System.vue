<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">仪表盘</router-link>
            <router-link to="/admin/users" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">用户管理</router-link>
            <router-link to="/admin/documents" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">任务监控</router-link>
            <router-link to="/admin/system" class="text-red-600 dark:text-red-400 font-medium">系统设置</router-link>
            <router-link to="/admin/reports" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">报告中心</router-link>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
          系统设置
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          配置系统参数、安全设置和性能监控
        </p>
      </div>

      <!-- 设置导航 -->
      <div class="mb-8">
        <div class="border-b border-gray-200 dark:border-gray-700">
          <nav class="-mb-px tab-navigation">
            <button 
              @click="activeTab = 'general'"
              :class="[
                'tab-btn',
                activeTab === 'general' ? 'active border-red-500 text-red-600' : ''
              ]"
            >
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
              </svg>
              通用设置
            </button>
            <button 
              @click="activeTab = 'security'"
              :class="[
                'tab-btn',
                activeTab === 'security' ? 'active border-red-500 text-red-600' : ''
              ]"
            >
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
              安全设置
            </button>
            <button 
              @click="activeTab = 'performance'"
              :class="[
                'tab-btn',
                activeTab === 'performance' ? 'active border-red-500 text-red-600' : ''
              ]"
            >
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              性能监控
            </button>
            <button 
              @click="activeTab = 'maintenance'"
              :class="[
                'tab-btn',
                activeTab === 'maintenance' ? 'active border-red-500 text-red-600' : ''
              ]"
            >
              <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 011-1h3a1 1 0 001-1V4z"/>
              </svg>
              维护管理
            </button>
          </nav>
        </div>
      </div>

      <!-- 通用设置 -->
      <div v-if="activeTab === 'general'" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 基本配置 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">基本配置</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">系统基础参数设置</p>
            </div>
            <div class="card-body space-y-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">系统名称</label>
                <input v-model="generalSettings.systemName" type="text" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">系统版本</label>
                <input v-model="generalSettings.systemVersion" type="text" class="form-input" readonly>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">服务域名</label>
                <input v-model="generalSettings.domain" type="text" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">联系邮箱</label>
                <input v-model="generalSettings.contactEmail" type="email" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时区设置</label>
                <select v-model="generalSettings.timezone" class="form-input">
                  <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                  <option value="UTC">UTC (UTC+0)</option>
                  <option value="America/New_York">America/New_York (UTC-5)</option>
                </select>
              </div>
              <div class="flex items-center">
                <label class="custom-checkbox">
                  <input v-model="generalSettings.maintenanceMode" type="checkbox">
                  <span class="checkbox-visual"></span>
                  <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">维护模式</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 文件配置 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">文件配置</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">文档处理和存储设置</p>
            </div>
            <div class="card-body space-y-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大文件大小 (MB)</label>
                <input v-model.number="generalSettings.maxFileSize" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">支持的文件类型</label>
                <div class="space-y-3">
                  <div v-for="format in fileFormats" :key="format.key" class="block w-full">
                    <label class="custom-checkbox flex items-center w-full">
                      <input v-model="format.enabled" type="checkbox">
                      <span class="checkbox-visual"></span>
                      <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ format.label }}</span>
                    </label>
                  </div>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">文件保存天数</label>
                <input v-model.number="generalSettings.retentionDays" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">存储路径</label>
                <input v-model="generalSettings.storagePath" type="text" class="form-input">
              </div>
            </div>
          </div>
        </div>

        <!-- 保存按钮 -->
        <div class="mt-8 flex justify-end">
          <button @click="saveGeneralSettings" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            保存设置
          </button>
        </div>
      </div>

      <!-- 安全设置 -->
      <div v-if="activeTab === 'security'" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 访问控制 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">访问控制</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">用户认证和权限管理</p>
            </div>
            <div class="card-body space-y-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">密码最小长度</label>
                <input v-model.number="securitySettings.minPasswordLength" type="number" min="6" max="20" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">会话超时 (分钟)</label>
                <input v-model.number="securitySettings.sessionTimeout" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">登录失败锁定阈值</label>
                <input v-model.number="securitySettings.maxLoginAttempts" type="number" class="form-input">
              </div>
              <div class="space-y-3">
                <div v-for="option in securityOptions" :key="option.key" class="block w-full">
                  <label class="custom-checkbox flex items-center w-full">
                    <input v-model="option.enabled" type="checkbox">
                    <span class="checkbox-visual"></span>
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ option.label }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据安全 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">数据安全</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">数据加密和备份设置</p>
            </div>
            <div class="card-body space-y-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">加密算法</label>
                <select v-model="securitySettings.encryptionAlgorithm" class="form-input">
                  <option value="AES-256">AES-256</option>
                  <option value="AES-128">AES-128</option>
                  <option value="RSA-2048">RSA-2048</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">备份频率</label>
                <select v-model="securitySettings.backupFrequency" class="form-input">
                  <option value="daily">每日备份</option>
                  <option value="weekly">每周备份</option>
                  <option value="monthly">每月备份</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">备份保存天数</label>
                <input v-model.number="securitySettings.backupRetentionDays" type="number" class="form-input">
              </div>
              <div class="space-y-3">
                <div v-for="option in dataSecurityOptions" :key="option.key" class="block w-full">
                  <label class="custom-checkbox flex items-center w-full">
                    <input v-model="option.enabled" type="checkbox">
                    <span class="checkbox-visual"></span>
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ option.label }}</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API安全 -->
        <div class="card mt-8">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API安全</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">API访问控制和速率限制</p>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API调用速率限制 (次/分钟)</label>
                <input v-model.number="securitySettings.apiRateLimit" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">单用户速率限制 (次/分钟)</label>
                <input v-model.number="securitySettings.userRateLimit" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API密钥有效期 (天)</label>
                <input v-model.number="securitySettings.apiKeyValidity" type="number" class="form-input">
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8 flex justify-end">
          <button @click="saveSecuritySettings" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            保存安全设置
          </button>
        </div>
      </div>

      <!-- 性能监控 -->
      <div v-if="activeTab === 'performance'" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 系统状态 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统状态</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">实时系统性能监控</p>
            </div>
            <div class="card-body space-y-6">
              <div v-for="metric in systemMetrics" :key="metric.name">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ metric.name }}</span>
                  <span class="text-sm text-gray-900 dark:text-white">{{ metric.value }}{{ metric.unit }}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    :class="[
                      'h-2 rounded-full transition-all duration-300',
                      metric.level === 'low' ? 'bg-green-600 dark:bg-green-400' :
                      metric.level === 'medium' ? 'bg-yellow-600 dark:bg-yellow-400' :
                      'bg-red-600 dark:bg-red-400'
                    ]"
                    :style="{ width: metric.percentage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 服务状态 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">服务状态</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">各项服务运行状态</p>
            </div>
            <div class="card-body space-y-4">
              <div v-for="service in serviceStatus" :key="service.name" class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ service.name }}</span>
                <span :class="[
                  'status-badge text-xs',
                  service.status === 'running' ? 'status-completed' :
                  service.status === 'warning' ? 'status-pending' :
                  'status-failed'
                ]">
                  {{ service.statusText }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 性能设置 -->
        <div class="card mt-8">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">性能配置</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">系统性能优化参数</p>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大并发任务数</label>
                <input v-model.number="performanceSettings.maxConcurrentTasks" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">任务超时时间 (分钟)</label>
                <input v-model.number="performanceSettings.taskTimeout" type="number" class="form-input">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">缓存过期时间 (小时)</label>
                <input v-model.number="performanceSettings.cacheExpiry" type="number" class="form-input">
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8 flex justify-end">
          <button @click="savePerformanceSettings" class="btn btn-primary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            保存性能设置
          </button>
        </div>
      </div>

      <!-- 维护管理 -->
      <div v-if="activeTab === 'maintenance'" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 系统维护 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统维护</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">系统清理和维护操作</p>
            </div>
            <div class="card-body space-y-4">
              <button @click="clearCache" class="w-full btn btn-secondary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                </svg>
                清理缓存
              </button>
              <button @click="clearLogs" class="w-full btn btn-secondary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                清理日志
              </button>
              <button @click="optimizeDatabase" class="w-full btn btn-secondary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
                </svg>
                优化数据库
              </button>
              <button @click="restartServices" class="w-full btn btn-warning">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                重启服务
              </button>
            </div>
          </div>

          <!-- 备份恢复 -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">备份恢复</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">数据备份和恢复管理</p>
            </div>
            <div class="card-body space-y-4">
              <button @click="createBackup" class="w-full btn btn-primary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                </svg>
                创建备份
              </button>
              <button @click="showBackupList" class="w-full btn btn-secondary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                备份列表
              </button>
              <button @click="scheduleBackup" class="w-full btn btn-secondary">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                定时备份
              </button>
              <button @click="restoreFromBackup" class="w-full btn btn-warning">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"/>
                </svg>
                从备份恢复
              </button>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="card mt-8">
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统信息</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">当前系统详细信息</p>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-sm">
              <div v-for="info in systemInfo" :key="info.label">
                <span class="text-gray-600 dark:text-gray-400">{{ info.label }}:</span>
                <p class="font-medium text-gray-900 dark:text-white">{{ info.value }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'
import { $notify } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'

const router = useRouter()
const themeStore = useThemeStore()

// 组件状态
const showAdminMenu = ref(false)
const activeTab = ref('general')

// 通用设置
const generalSettings = reactive({
  systemName: 'Word文档分析服务',
  systemVersion: 'v2.1.0',
  domain: 'doc-analyzer.com',
  contactEmail: '<EMAIL>',
  timezone: 'Asia/Shanghai',
  maintenanceMode: false,
  maxFileSize: 50,
  retentionDays: 30,
  storagePath: '/var/data/documents'
})

// 文件格式配置
const fileFormats = ref([
  { key: 'docx', label: 'Microsoft Word (.docx)', enabled: true },
  { key: 'doc', label: 'Microsoft Word 97-2003 (.doc)', enabled: true },
  { key: 'rtf', label: 'Rich Text Format (.rtf)', enabled: false },
  { key: 'pdf', label: 'PDF 文档 (.pdf)', enabled: false }
])

// 安全设置
const securitySettings = reactive({
  minPasswordLength: 8,
  sessionTimeout: 120,
  maxLoginAttempts: 5,
  encryptionAlgorithm: 'AES-256',
  backupFrequency: 'daily',
  backupRetentionDays: 7,
  apiRateLimit: 1000,
  userRateLimit: 60,
  apiKeyValidity: 365
})

// 安全选项
const securityOptions = ref([
  { key: 'twoFactor', label: '启用双因子认证', enabled: true },
  { key: 'httpsOnly', label: '强制HTTPS访问', enabled: true },
  { key: 'ipWhitelist', label: 'IP白名单验证', enabled: false }
])

// 数据安全选项
const dataSecurityOptions = ref([
  { key: 'fileEncryption', label: '文件内容加密存储', enabled: true },
  { key: 'dbEncryption', label: '数据库连接加密', enabled: true },
  { key: 'backupVerification', label: '自动备份验证', enabled: true }
])

// 系统监控指标
const systemMetrics = ref([
  { name: 'CPU使用率', value: 32, unit: '%', percentage: 32, level: 'low' },
  { name: '内存使用率', value: 68, unit: '%', percentage: 68, level: 'medium' },
  { name: '磁盘使用率', value: 45, unit: '%', percentage: 45, level: 'low' },
  { name: '网络I/O', value: 1.2, unit: ' MB/s', percentage: 60, level: 'low' }
])

// 服务状态
const serviceStatus = ref([
  { name: 'Web服务', status: 'running', statusText: '运行中' },
  { name: '数据库', status: 'running', statusText: '运行中' },
  { name: 'Redis缓存', status: 'running', statusText: '运行中' },
  { name: '文件处理服务', status: 'running', statusText: '运行中' },
  { name: '任务队列', status: 'warning', statusText: '部分异常' },
  { name: '邮件服务', status: 'running', statusText: '运行中' }
])

// 性能设置
const performanceSettings = reactive({
  maxConcurrentTasks: 10,
  taskTimeout: 30,
  cacheExpiry: 24
})

// 备份列表
const recentBackups = ref([
  { id: 1, name: '系统完整备份', date: '2024-01-20 02:00:00', size: '2.5GB' },
  { id: 2, name: '数据库备份', date: '2024-01-19 02:00:00', size: '850MB' },
  { id: 3, name: '配置文件备份', date: '2024-01-18 02:00:00', size: '15MB' }
])

// 系统信息
const systemInfo = ref([
  { label: '操作系统', value: 'Windows Server 2019' },
  { label: 'Python版本', value: '3.12.0' },
  { label: '数据库版本', value: 'PostgreSQL 17.5' },
  { label: '系统启动时间', value: '2024-01-15 09:30:00' },
  { label: '总内存', value: '16 GB' },
  { label: '可用磁盘', value: '500 GB' },
  { label: 'CPU核心', value: '8 核' },
  { label: '系统负载', value: '1.2 / 8.0' }
])

// 方法
const saveGeneralSettings = () => {
  $notify.success('通用设置已保存')
}

const saveSecuritySettings = () => {
  $notify.success('安全设置已保存')
}

const savePerformanceSettings = () => {
  $notify.success('性能设置已保存')
}

const clearCache = async () => {
  const result = await $confirm.warning('确定要清理系统缓存吗？这可能会影响系统性能。', {
    title: '清理系统缓存',
    confirmText: '确定清理',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在清理系统缓存...')
  }
}

const clearLogs = async () => {
  const result = await $confirm.warning('确定要清理系统日志吗？此操作不可恢复。', {
    title: '清理系统日志',
    confirmText: '确定清理',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在清理系统日志...')
  }
}

const optimizeDatabase = async () => {
  const result = await $confirm.info('确定要优化数据库吗？这可能需要一些时间。', {
    title: '优化数据库',
    confirmText: '开始优化',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在优化数据库...')
  }
}

const restartServices = async () => {
  const result = await $confirm.danger('确定要重启系统服务吗？这可能会影响在线用户。', {
    title: '重启系统服务',
    confirmText: '确定重启',
    cancelText: '取消'
  })
  if (result) {
    $notify.warning('正在重启系统服务...')
  }
}

const createBackup = async () => {
  const result = await $confirm.info('确定要创建系统备份吗？这可能需要一些时间。', {
    title: '创建系统备份',
    confirmText: '创建备份',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在创建系统备份...')
  }
}

const showBackupList = () => {
  // 实现备份列表显示逻辑
  $notify.info('正在显示备份列表...')
}

const scheduleBackup = () => {
  // 实现定时备份逻辑
  $notify.info('正在设置定时备份...')
}

const restoreFromBackup = () => {
  // 实现从备份恢复逻辑
  $notify.info('正在从备份恢复...')
}

// 管理员登出
const handleAdminLogout = () => {
  adminLogout(router)
}

onMounted(() => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/system')) {
    return
  }
})
</script>

<style scoped>
/* 系统设置特有样式 */
</style> 