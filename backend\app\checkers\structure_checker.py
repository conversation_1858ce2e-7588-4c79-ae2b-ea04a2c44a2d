"""
论文结构检测器

该模块实现了论文结构检查功能，包括：
- 标题结构检查（层级、编号、格式等）
- 章节结构检查（完整性、顺序等）
- 目录结构检查（存在性、准确性等）
- 引用结构检查（格式、完整性等）
"""

import asyncio
import re
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Set
import structlog

from .rule_engine import CheckResult, CheckSeverity
from ..core.exceptions import DocumentAnalysisException


logger = structlog.get_logger(__name__)


class StructureCheckType(Enum):
    """结构检查类型"""
    HEADING_STRUCTURE = "heading_structure"     # 标题结构
    CHAPTER_STRUCTURE = "chapter_structure"     # 章节结构
    REFERENCE_STRUCTURE = "reference_structure" # 引用结构


@dataclass
class StructureIssue:
    """结构问题数据类"""
    issue_type: StructureCheckType      # 问题类型
    severity: CheckSeverity             # 严重程度
    message: str                        # 问题描述
    position: Optional[int] = None      # 问题位置
    suggestions: List[str] = field(default_factory=list)  # 修复建议
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "issue_type": self.issue_type.value,
            "severity": self.severity.value,
            "message": self.message,
            "position": self.position,
            "suggestions": self.suggestions
        }


@dataclass
class StructureCheckResult:
    """结构检查结果数据类"""
    check_type: StructureCheckType      # 检查类型
    passed: bool                        # 是否通过
    issues: List[StructureIssue] = field(default_factory=list)  # 问题列表
    statistics: Dict[str, Any] = field(default_factory=dict)    # 统计信息
    execution_time: float = 0.0         # 执行时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "check_type": self.check_type.value,
            "passed": self.passed,
            "issues": [issue.to_dict() for issue in self.issues],
            "statistics": self.statistics,
            "execution_time": self.execution_time
        }


class StructureChecker:
    """论文结构检测器"""
    
    def __init__(self, structure_config: Optional[Dict[str, Any]] = None):
        """
        初始化结构检测器
        
        Args:
            structure_config: 结构标准配置
        """
        self.logger = structlog.get_logger(__name__)
        self.config = structure_config or self._get_default_config()
        
        # 检查统计
        self.check_stats = {
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "total_issues": 0
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认结构配置"""
        return {
            "heading_structure": {
                "max_levels": 6,
                "require_numbering": True,
                "skip_levels_allowed": False
            },
            "chapter_structure": {
                "required_sections": [
                    "摘要", "abstract", "引言", "introduction",
                    "结论", "conclusion", "参考文献", "references"
                ]
            },
            "reference_structure": {
                "required": True,
                "min_references": 5,
                "in_text_citation_required": True
            }
        }
    
    async def check_heading_structure(self, analysis_data: Dict[str, Any]) -> StructureCheckResult:
        """
        检查标题结构
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            标题结构检查结果
        """
        start_time = asyncio.get_event_loop().time()
        issues = []
        
        try:
            # 获取文档结构信息
            document_structure = analysis_data.get("document_structure", {})
            headings = document_structure.get("headings", [])
            config = self.config["heading_structure"]
            
            if not headings:
                return StructureCheckResult(
                    check_type=StructureCheckType.HEADING_STRUCTURE,
                    passed=True,
                    issues=[],
                    statistics={"no_headings": True},
                    execution_time=asyncio.get_event_loop().time() - start_time
                )
            
            # 检查标题层级
            levels = []
            for heading in headings:
                level = heading.get("level", 1) if isinstance(heading, dict) else 1
                levels.append(level)
                
                # 检查层级是否超出限制
                if level > config["max_levels"]:
                    issues.append(StructureIssue(
                        issue_type=StructureCheckType.HEADING_STRUCTURE,
                        severity=CheckSeverity.WARNING,
                        message=f"标题层级过深: 第{level}级",
                        suggestions=[f"建议将标题层级控制在{config['max_levels']}级以内"]
                    ))
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return StructureCheckResult(
                check_type=StructureCheckType.HEADING_STRUCTURE,
                passed=len(issues) == 0,
                issues=issues,
                statistics={
                    "total_issues": len(issues),
                    "total_headings": len(headings),
                    "max_level": max(levels) if levels else 0
                },
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error("标题结构检查失败", error=str(e))
            
            return StructureCheckResult(
                check_type=StructureCheckType.HEADING_STRUCTURE,
                passed=False,
                issues=[StructureIssue(
                    issue_type=StructureCheckType.HEADING_STRUCTURE,
                    severity=CheckSeverity.ERROR,
                    message=f"标题结构检查失败: {str(e)}"
                )],
                execution_time=execution_time
            )
    
    async def check_chapter_structure(self, analysis_data: Dict[str, Any]) -> StructureCheckResult:
        """
        检查章节结构
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            章节结构检查结果
        """
        start_time = asyncio.get_event_loop().time()
        issues = []
        
        try:
            # 获取文档结构信息
            document_structure = analysis_data.get("document_structure", {})
            chapters = document_structure.get("chapters", [])
            config = self.config["chapter_structure"]
            
            # 提取章节标题
            chapter_titles = []
            for chapter in chapters:
                if isinstance(chapter, dict):
                    title = chapter.get("title", "").lower().strip()
                else:
                    title = str(chapter).lower().strip()
                chapter_titles.append(title)
            
            # 检查必需章节
            required_sections = config.get("required_sections", [])
            missing_sections = []
            
            for required in required_sections:
                required_lower = required.lower()
                found = any(required_lower in title for title in chapter_titles)
                if not found:
                    missing_sections.append(required)
            
            if missing_sections:
                issues.append(StructureIssue(
                    issue_type=StructureCheckType.CHAPTER_STRUCTURE,
                    severity=CheckSeverity.WARNING,
                    message=f"缺少必需章节: {', '.join(missing_sections)}",
                    suggestions=[f"建议添加缺少的章节: {', '.join(missing_sections)}"]
                ))
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return StructureCheckResult(
                check_type=StructureCheckType.CHAPTER_STRUCTURE,
                passed=len(issues) == 0,
                issues=issues,
                statistics={
                    "total_issues": len(issues),
                    "total_chapters": len(chapters),
                    "missing_sections": missing_sections
                },
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error("章节结构检查失败", error=str(e))
            
            return StructureCheckResult(
                check_type=StructureCheckType.CHAPTER_STRUCTURE,
                passed=False,
                issues=[StructureIssue(
                    issue_type=StructureCheckType.CHAPTER_STRUCTURE,
                    severity=CheckSeverity.ERROR,
                    message=f"章节结构检查失败: {str(e)}"
                )],
                execution_time=execution_time
            )
    
    def get_check_stats(self) -> Dict[str, Any]:
        """获取检查统计"""
        stats = self.check_stats.copy()
        
        # 计算通过率
        if stats["total_checks"] > 0:
            stats["pass_rate"] = stats["passed_checks"] / stats["total_checks"]
        else:
            stats["pass_rate"] = 0.0
        
        return stats 