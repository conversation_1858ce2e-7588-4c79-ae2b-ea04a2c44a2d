<template>
  <BaseLayout 
    title="订单管理" 
    description="查看您的订单历史和付款状态"
  >
    <template #header-actions>
      <BaseButton to="/pricing" variant="primary" prepend-icon="M12 6v6m0 0v6m0-6h6m-6 0H6">
        购买套餐
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <BaseCard class="text-center">
        <div class="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mb-3">
          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div v-if="isStatisticsLoading" class="animate-pulse">
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-12 mx-auto mb-2"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mx-auto"></div>
        </div>
        <div v-else>
          <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ statistics.completed_orders }}</p>
          <p class="text-sm text-gray-600 dark:text-gray-300">已完成订单</p>
        </div>
      </BaseCard>
      
      <BaseCard class="text-center">
        <div class="mx-auto h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3">
          <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div v-if="isStatisticsLoading" class="animate-pulse">
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-12 mx-auto mb-2"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
        </div>
        <div v-else>
          <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ statistics.pending_orders }}</p>
          <p class="text-sm text-gray-600 dark:text-gray-300">待支付</p>
        </div>
      </BaseCard>
      
      <BaseCard class="text-center">
        <div class="mx-auto h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
          <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
        </div>
        <div v-if="isStatisticsLoading" class="animate-pulse">
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20 mx-auto mb-2"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12 mx-auto"></div>
        </div>
        <div v-else>
          <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">¥{{ statistics.total_spent.toLocaleString() }}</p>
          <p class="text-sm text-gray-600 dark:text-gray-300">总消费</p>
        </div>
      </BaseCard>
      
      <BaseCard class="text-center">
        <div class="mx-auto h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mb-3">
          <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a1 1 0 011-1h6a1 1 0 011 1v2M7 7h10"/>
          </svg>
        </div>
        <div v-if="isStatisticsLoading" class="animate-pulse">
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto mb-2"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
        </div>
        <div v-else>
          <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ statistics.current_plan ? getPlanName(statistics.current_plan) : '暂无' }}</p>
          <p class="text-sm text-gray-600 dark:text-gray-300">当前套餐</p>
        </div>
      </BaseCard>
    </div>

    <!-- 筛选器 -->
    <BaseCard class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BaseInput
          v-model="filters.search"
          placeholder="搜索订单号或套餐名称..."
          prepend-icon="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          :disabled="isLoading"
          @input="filterOrders"
        />
        
        <BaseInput
          v-model="filters.status"
          type="select"
          :disabled="isLoading"
          :options="[
            { value: '', label: '所有状态' },
            { value: 'paid', label: '已支付' },
            { value: 'pending', label: '待支付' },
            { value: 'failed', label: '支付失败' },
            { value: 'cancelled', label: '已取消' }
          ]"
          @change="filterOrdersImmediate"
        />
        
        <BaseInput
          v-model="filters.dateRange"
          type="select"
          :disabled="isLoading"
          :options="[
            { value: '', label: '全部时间' },
            { value: 'today', label: '今天' },
            { value: 'week', label: '本周' },
            { value: 'month', label: '本月' },
            { value: 'year', label: '今年' }
          ]"
          @change="filterOrdersImmediate"
        />
      </div>
    </BaseCard>

    <!-- 订单列表 -->
    <div v-if="isLoading" class="text-center py-16">
      <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-4 text-gray-600 dark:text-gray-400">正在加载订单列表...</p>
    </div>

    <div v-else-if="error" class="text-center py-16 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <p class="text-red-600 dark:text-red-400">{{ error }}</p>
      <BaseButton @click="loadOrders" class="mt-4" variant="primary">重试</BaseButton>
    </div>

    <div v-else-if="paginatedOrders.length === 0" class="text-center py-16">
      <div class="text-6xl mb-4">📋</div>
      <p class="text-gray-600 dark:text-gray-400">暂无订单记录</p>
      <BaseButton to="/pricing" class="mt-4" variant="primary">购买套餐</BaseButton>
    </div>

    <div v-else class="space-y-4">
      <div v-for="order in paginatedOrders" :key="order.order_id" 
           :class="['card', 'order-item', order.status]">
        <div class="card-body">
          <div class="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-4 mb-3">
                <div :class="['h-12 w-12 rounded-lg flex items-center justify-center', getOrderIconBg(order.status)]">
                  <svg :class="['h-6 w-6', getOrderIconColor(order.status)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path v-if="order.status === 'paid'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                    <path v-else-if="order.status === 'pending'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ getPlanName(order.plan_id) }}</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-300">订单号: {{ getOrderNumber(order.order_id) }}</p>
                </div>
              </div>
              
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-300">
                <div>
                  <span class="font-medium">订单金额：</span>
                  <p class="text-lg font-bold text-gray-900 dark:text-white">¥{{ order.amount.toFixed(2) }}</p>
                </div>
                <div>
                  <span class="font-medium">{{ getTimeLabel(order.status) }}：</span>
                  <p>{{ formatDate(order.status === 'paid' && order.paid_at ? order.paid_at : order.created_at) }}</p>
                </div>
                <div>
                  <span class="font-medium">有效期：</span>
                  <p>1年</p>
                </div>
                <div>
                  <span class="font-medium">{{ order.status === 'pending' ? '订单状态' : '支付方式' }}：</span>
                  <p :class="{ 'text-orange-600 dark:text-orange-400': order.status === 'pending' }">
                    {{ getStatusText(order) }}
                  </p>
                </div>
              </div>
            </div>
            
            <div class="flex flex-col space-y-2 min-w-0 flex-shrink-0 items-end">
              <span :class="['status-badge', getStatusBadgeClass(order.status)]">
                {{ getStatusText(order, true) }}
              </span>
              <div class="flex space-x-2 flex-wrap justify-end">
                <BaseButton v-if="order.status === 'pending'" @click="payOrder(order)" variant="primary" size="sm">立即支付</BaseButton>
                <BaseButton v-if="order.status === 'pending'" @click="cancelOrder(order)" variant="danger" size="sm">取消订单</BaseButton>
                <BaseButton v-if="order.status === 'paid'" @click="downloadInvoice(order)" variant="secondary" size="sm" prepend-icon="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                  下载发票
                </BaseButton>
                <BaseButton @click="viewOrderDetail(order)" variant="secondary" size="sm">查看详情</BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <BasePagination 
      v-if="!isLoading"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="totalOrders"
      :disabled="isLoading"
      @page-change="changePage"
    />

    <!-- 支付模态框 -->
    <div v-if="showPaymentModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex min-h-screen items-center justify-center p-4">
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="hidePaymentModal"></div>
        <BaseCard class="relative w-full max-w-md">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">订单支付</h3>
          </template>
          
          <div class="space-y-4">
            <div class="text-center">
              <p class="text-lg font-medium text-gray-900 dark:text-white">订单号: {{ getOrderNumber(selectedOrder?.order_id || '') }}</p>
              <p class="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-2">¥{{ selectedOrder?.amount.toFixed(2) }}</p>
            </div>
            <div class="space-y-3">
              <BaseCard variant="border" class="p-4">
                <label class="flex items-center space-x-3 cursor-pointer">
                  <input v-model="paymentMethod" type="radio" value="wechat" class="form-radio text-blue-600 focus:ring-blue-500">
                  <div class="h-8 w-8 bg-green-100 dark:bg-green-900/50 rounded flex items-center justify-center">
                    <svg class="h-5 w-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8.5 2A5.5 5.5 0 003 7.5v9A5.5 5.5 0 008.5 22h7a5.5 5.5 0 005.5-5.5v-9A5.5 5.5 0 0015.5 2h-7zm0 2h7A3.5 3.5 0 0119 7.5v9a3.5 3.5 0 01-3.5 3.5h-7A3.5 3.5 0 015 16.5v-9A3.5 3.5 0 018.5 4z"/>
                    </svg>
                  </div>
                  <span class="font-medium text-gray-900 dark:text-white">微信支付</span>
                </label>
              </BaseCard>
              <BaseCard variant="border" class="p-4">
                <label class="flex items-center space-x-3 cursor-pointer">
                  <input v-model="paymentMethod" type="radio" value="alipay" class="form-radio text-blue-600 focus:ring-blue-500">
                  <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/50 rounded flex items-center justify-center">
                    <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                    </svg>
                  </div>
                  <span class="font-medium text-gray-900 dark:text-white">支付宝</span>
                </label>
              </BaseCard>
            </div>
          </div>
          
          <template #footer>
            <div class="flex space-x-3 justify-end">
              <BaseButton @click="hidePaymentModal" variant="secondary">取消</BaseButton>
              <BaseButton @click="processPayment" variant="primary">确认支付</BaseButton>
            </div>
          </template>
        </BaseCard>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import BaseButton from '@/components/BaseButton.vue'
import BasePagination from '@/components/BasePagination.vue'
import { $notify } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'
import { paymentApi, type OrderStatus, type PaymentStatistics } from '@/services/paymentApi'

const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()

// 用户菜单相关
const userMenuRef = ref<HTMLElement>()
const userMenuOpen = ref(false)

// 数据加载状态
const isLoading = ref(false)
const isStatisticsLoading = ref(false)
const error = ref<string | null>(null)

// 支付模态框
const showPaymentModal = ref(false)
const selectedOrder = ref<OrderStatus | null>(null)
const paymentMethod = ref('wechat')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalOrders = ref(0)

// 筛选器
const filters = reactive({
  search: '',
  status: '',
  dateRange: ''
})

// 统计数据
const statistics = ref<PaymentStatistics>({
  completed_orders: 0,
  pending_orders: 0,
  total_spent: 0,
  current_plan: ''
})

// 订单数据
const orders = ref<OrderStatus[]>([])

// 过滤后的订单 (已通过API筛选)
const filteredOrders = computed(() => {
  return orders.value
})

// 当前页的订单 (后端已分页，直接使用)
const paginatedOrders = computed(() => {
  return orders.value
})



// 切换用户菜单
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    userMenuOpen.value = false
  }
}

// 防抖搜索
let searchTimeout: number | null = null

// 过滤订单
const filterOrders = () => {
  currentPage.value = 1
  
  // 如果是搜索输入，使用防抖
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    loadOrders()
  }, 300)
}

// 立即过滤（用于下拉选择器）
const filterOrdersImmediate = () => {
  currentPage.value = 1
  loadOrders()
}

// 获取订单图标背景色
const getOrderIconBg = (status: string) => {
  const colors = {
    paid: 'bg-purple-100',
    pending: 'bg-orange-100',
    failed: 'bg-red-100',
    cancelled: 'bg-gray-100'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100'
}

// 获取订单图标颜色
const getOrderIconColor = (status: string) => {
  const colors = {
    paid: 'text-purple-600',
    pending: 'text-orange-600',
    failed: 'text-red-600',
    cancelled: 'text-gray-600'
  }
  return colors[status as keyof typeof colors] || 'text-gray-600'
}

// 获取时间标签
const getTimeLabel = (status: string) => {
  const labels = {
    paid: '支付时间',
    pending: '创建时间',
    failed: '失败时间',
    cancelled: '取消时间'
  }
  return labels[status as keyof typeof labels] || '创建时间'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取套餐名称
const getPlanName = (planId: string) => {
  const planNames: Record<string, string> = {
    'basic': '基础套餐',
    'standard': '标准套餐',
    'professional': '专业套餐'
  }
  return planNames[planId] || planId
}

// 获取订单号显示格式
const getOrderNumber = (orderId: string) => {
  // 如果订单号以 "order_" 开头，去掉前缀并显示完整的12位编号
  if (orderId.startsWith('order_')) {
    return `#${orderId.substring(6).toUpperCase()}`
  }
  // 兼容其他格式的订单号
  return `#${orderId.slice(-12).toUpperCase()}`
}

// 获取支付方式中文显示
const getPaymentMethodText = (paymentMethod: string) => {
  const paymentMethodMap: Record<string, string> = {
    'wechat': '微信支付',
    'alipay': '支付宝',
    'card': '银行卡支付',
    'unionpay': '银联支付',
    'bank': '银行转账'
  }
  
  // 处理可能的大小写问题和空值
  if (!paymentMethod) {
    return '未知支付方式'
  }
  
  const method = paymentMethod.toLowerCase()
  const result = paymentMethodMap[method]
  
  return result || '未知支付方式'
}

// 获取状态文本
const getStatusText = (order: OrderStatus, isBadge = false) => {
  const statusTexts = {
    paid: isBadge ? '已支付' : getPaymentMethodText(order.payment_method),
    pending: isBadge ? '待支付' : '等待支付中',
    failed: isBadge ? '支付失败' : '支付失败',
    cancelled: isBadge ? '已取消' : '已取消'
  }
  return statusTexts[order.status as keyof typeof statusTexts] || '未知'
}

// 获取状态徽章样式
const getStatusBadgeClass = (status: string) => {
  const classes = {
    paid: 'status-completed',
    pending: 'status-pending',
    failed: 'status-cancelled',
    cancelled: 'status-cancelled'
  }
  return classes[status as keyof typeof classes] || 'status-cancelled'
}

// 支付订单
const payOrder = (order: OrderStatus) => {
  selectedOrder.value = order
  showPaymentModal.value = true
}

// 隐藏支付模态框
const hidePaymentModal = () => {
  showPaymentModal.value = false
  selectedOrder.value = null
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    isStatisticsLoading.value = true
    const stats = await paymentApi.getPaymentStatistics()
    statistics.value = stats
  } catch (err) {
    console.error('Failed to load statistics:', err)
    $notify.error('加载统计数据失败')
  } finally {
    isStatisticsLoading.value = false
  }
}

// 加载订单列表
const loadOrders = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      ...(filters.status && { status: filters.status }),
      ...(filters.search && { search: filters.search }),
      ...(filters.dateRange && { date_range: filters.dateRange })
    }
    
    const response = await paymentApi.getPaymentHistory(params)
    orders.value = response.orders
    totalOrders.value = response.total
  } catch (err) {
    console.error('Failed to load orders:', err)
    error.value = '加载订单列表失败'
    $notify.error('加载订单列表失败')
  } finally {
    isLoading.value = false
  }
}

// 处理支付
const processPayment = async () => {
  const order = selectedOrder.value
  if (!order) return

  try {
    $notify.info('正在处理支付...')
    
    const paymentInfo = await paymentApi.processPayment(order.order_id, paymentMethod.value)
    $notify.success('支付成功！')
    
    // 重新加载数据
    await loadOrders()
    await loadStatistics()
    hidePaymentModal()
  } catch (err) {
    console.error('Payment failed:', err)
    $notify.error('支付失败，请重试')
  }
}

// 取消订单
const cancelOrder = async (order: OrderStatus) => {
  const result = await $confirm.warning('确定要取消这个订单吗？取消后无法恢复。', {
    title: '取消订单',
    confirmText: '确定取消',
    cancelText: '保留订单'
  })
  if (result) {
    try {
      await paymentApi.cancelOrder(order.order_id)
      $notify.warning(`订单 ${order.order_id} 已取消`)
      
      // 重新加载数据
      await loadOrders()
      await loadStatistics()
    } catch (err) {
      console.error('Cancel order failed:', err)
      $notify.error('取消订单失败，请重试')
    }
  }
}

// 下载发票
const downloadInvoice = async (order: OrderStatus) => {
  try {
    $notify.info(`正在下载订单 ${order.order_id} 的发票...`)
    
    const blob = await paymentApi.downloadInvoice(order.order_id)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `invoice_${order.order_id}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    $notify.success('发票下载完成')
  } catch (err) {
    console.error('Download invoice failed:', err)
    $notify.error('发票下载失败')
  }
}

// 查看订单详情
const viewOrderDetail = (order: OrderStatus) => {
  $notify.info(`查看订单详情：${order.order_id}`)
  // 这里可以跳转到订单详情页面
  // router.push(`/orders/${order.order_id}`)
}

// 切换页码
const changePage = (page: number) => {
  if (page >= 1) {
    currentPage.value = page
    loadOrders()
  }
}

// 退出登录
const logout = () => {
  userStore.logout()
  router.push('/auth')
}

onMounted(async () => {
  // 检查登录状态
  const user = localStorage.getItem('user')
  if (!user) {
    router.push('/auth')
    return
  }
  
  // 加载数据
  await Promise.all([
    loadStatistics(),
    loadOrders()
  ])
  
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  
  // 清理定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})
</script>
<style scoped>
:deep(.form-group) {
  @apply mb-0;
}
</style>