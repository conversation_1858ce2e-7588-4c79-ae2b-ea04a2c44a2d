<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文关键词格式检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .success { color: #10b981; background: #ecfdf5; padding: 15px; border-radius: 6px; }
        .error { color: #ef4444; background: #fef2f2; padding: 15px; border-radius: 6px; }
        .warning { color: #f59e0b; background: #fffbeb; padding: 15px; border-radius: 6px; }
        .info { color: #3b82f6; background: #eff6ff; padding: 15px; border-radius: 6px; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #2563eb; }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #3b82f6;
            background: white;
        }
        .test-input {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result-details {
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 中文关键词格式检测测试</h1>
        <p>测试新增的中文关键词格式检查功能，验证程序能否正确检测各种格式问题。</p>

        <div class="test-section">
            <h2>📋 测试用例</h2>
            <div id="test-cases">
                <div class="test-case">
                    <h3>✅ 正确格式</h3>
                    <div class="test-input">关键词：人工智能；机器学习；深度学习；神经网络</div>
                    <p>期望结果：通过所有检查</p>
                </div>

                <div class="test-case">
                    <h3>❌ 英文冒号错误</h3>
                    <div class="test-input">关键词:人工智能；机器学习；深度学习</div>
                    <p>期望结果：标签格式检查失败</p>
                </div>

                <div class="test-case">
                    <h3>❌ 英文分号错误</h3>
                    <div class="test-input">关键词：人工智能;机器学习;深度学习</div>
                    <p>期望结果：分隔符检查失败</p>
                </div>

                <div class="test-case">
                    <h3>❌ 末尾分号错误</h3>
                    <div class="test-input">关键词：人工智能；机器学习；深度学习；</div>
                    <p>期望结果：分隔符检查失败</p>
                </div>

                <div class="test-case">
                    <h3>❌ 关键词数量不足</h3>
                    <div class="test-input">关键词：人工智能；机器学习</div>
                    <p>期望结果：格式检查失败（数量不足）</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 检测结果</h2>
            <button onclick="runDetectionTest()">开始检测测试</button>
            <button onclick="testFrontendDisplay()">测试前端显示</button>
            <div id="detection-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 前端问题片段显示测试</h2>
            <div id="frontend-test-results"></div>
        </div>
    </div>

    <script>
        // API服务类
        class ApiService {
            constructor() {
                this.baseURL = 'http://localhost:8000/api'
            }

            async get(url) {
                const response = await fetch(`${this.baseURL}${url}`)
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                return response.json()
            }

            async post(url, data) {
                const response = await fetch(`${this.baseURL}${url}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }
                return response.json()
            }
        }

        const apiService = new ApiService()

        // 测试用例数据
        const testCases = [
            {
                name: '正确格式',
                text: '关键词：人工智能；机器学习；深度学习；神经网络',
                expected: 'pass'
            },
            {
                name: '英文冒号错误',
                text: '关键词:人工智能；机器学习；深度学习',
                expected: 'fail'
            },
            {
                name: '英文分号错误',
                text: '关键词：人工智能;机器学习;深度学习',
                expected: 'fail'
            },
            {
                name: '末尾分号错误',
                text: '关键词：人工智能；机器学习；深度学习；',
                expected: 'fail'
            },
            {
                name: '关键词数量不足',
                text: '关键词：人工智能；机器学习',
                expected: 'fail'
            }
        ]

        // 运行检测测试
        async function runDetectionTest() {
            const resultsDiv = document.getElementById('detection-results')
            resultsDiv.innerHTML = '<div class="info">🔄 正在运行检测测试...</div>'

            try {
                let html = '<h3>📋 检测结果汇总</h3>'

                for (const testCase of testCases) {
                    html += `<div class="test-case">`
                    html += `<h4>测试：${testCase.name}</h4>`
                    html += `<div class="test-input">${testCase.text}</div>`

                    // 模拟检测结果（实际应该调用后端API）
                    const result = await simulateDetection(testCase.text)
                    
                    const statusClass = result.passed ? 'success' : 'error'
                    html += `<div class="${statusClass}">`
                    html += `<strong>检测结果：</strong>${result.passed ? '✅ 通过' : '❌ 失败'}<br>`
                    html += `<strong>消息：</strong>${result.message}<br>`
                    if (result.details) {
                        html += `<strong>详情：</strong>${JSON.stringify(result.details)}`
                    }
                    html += `</div>`
                    html += `</div>`
                }

                resultsDiv.innerHTML = html
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`
            }
        }

        // 模拟检测逻辑
        async function simulateDetection(text) {
            const errors = []

            // 检查冒号
            if (text.includes('关键词:') && !text.includes('关键词：')) {
                errors.push('应使用中文冒号"："而不是英文冒号":"')
            }

            // 检查分号
            if (text.includes(';') && !text.includes('；')) {
                errors.push('应使用中文分号"；"而不是英文分号";"')
            }

            // 检查末尾分号
            if (text.endsWith('；')) {
                errors.push('最后一个关键词后不应有分号')
            }

            // 检查关键词数量
            const keywordsMatch = text.match(/关键词[：:](.*)/);
            if (keywordsMatch) {
                const keywordsText = keywordsMatch[1];
                const keywords = keywordsText.split(/[；;]/).filter(k => k.trim());
                if (keywords.length < 3) {
                    errors.push(`关键词数量不足，建议3-5个，当前：${keywords.length}个`);
                }
            }

            return {
                passed: errors.length === 0,
                message: errors.length === 0 ? '中文关键词格式检查通过' : errors.join('；'),
                details: {
                    error_count: errors.length,
                    error_list: errors
                }
            }
        }

        // 测试前端显示
        function testFrontendDisplay() {
            const resultsDiv = document.getElementById('frontend-test-results')
            
            // 模拟问题片段数据
            const mockProblemFragments = [
                {
                    fragment_id: 'test_001',
                    structure: '中文关键词',
                    category: 'format',
                    severity: 'severe',
                    original_text: '关键词:人工智能;机器学习',
                    problem_description: '使用了英文冒号和英文分号，关键词数量不足',
                    standard_reference: '关键词：人工智能；机器学习；深度学习',
                    auto_fixable: true
                },
                {
                    fragment_id: 'test_002',
                    structure: '中文关键词',
                    category: 'format',
                    severity: 'general',
                    original_text: '关键词：算法；数据结构；',
                    problem_description: '最后一个关键词后不应有分号',
                    standard_reference: '关键词：算法；数据结构',
                    auto_fixable: true
                }
            ]

            let html = '<h3>📊 问题片段显示效果</h3>'
            html += '<div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">'
            html += '<div style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd;">'
            html += '<h4 style="margin: 0;">中文关键词格式问题</h4>'
            html += `<span style="color: #666;">发现 ${mockProblemFragments.length} 个问题</span>`
            html += '</div>'

            mockProblemFragments.forEach((fragment, index) => {
                const severityColor = fragment.severity === 'severe' ? '#ef4444' : '#f59e0b'
                html += '<div style="padding: 15px; border-bottom: 1px solid #eee;">'
                html += `<div style="display: flex; justify-between; align-items: start; margin-bottom: 10px;">`
                html += `<span style="font-weight: bold;">问题 ${index + 1}</span>`
                html += `<span style="background: ${severityColor}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">${fragment.severity}</span>`
                html += '</div>'
                html += `<div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace;">${fragment.original_text}</div>`
                html += `<div style="color: #666; margin: 5px 0;"><strong>问题：</strong>${fragment.problem_description}</div>`
                html += `<div style="color: #666; margin: 5px 0;"><strong>标准：</strong>${fragment.standard_reference}</div>`
                html += `<div style="color: ${fragment.auto_fixable ? '#10b981' : '#ef4444'}; font-size: 14px;">${fragment.auto_fixable ? '✅ 可自动修复' : '❌ 需手动修复'}</div>`
                html += '</div>'
            })

            html += '</div>'
            html += '<div class="success" style="margin-top: 15px;">✅ 前端问题片段显示功能正常，能够正确展示中文关键词格式问题</div>'

            resultsDiv.innerHTML = html
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 中文关键词格式检测测试页面已加载')
        })
    </script>
</body>
</html>
