import apiService from './api'
import type { Document, PaginationParams, PaginatedResponse, Task, TaskProgress } from '@/types'

export interface DocumentUploadOptions {
  onProgress?: (progress: number) => void
  analysisType?: 'paper_check' | 'format_check' | 'content_analysis'
  options?: {
    check_format?: boolean
    check_structure?: boolean
    check_references?: boolean
    check_citations?: boolean
    extract_images?: boolean
    detection_standard?: string  // 新增：检测标准
    standard_name?: string       // 新增：标准名称
  }
}

export interface DocumentListParams extends PaginationParams {
  status?: 'uploaded' | 'processing' | 'completed' | 'failed'
  search?: string
  sort_by?: 'upload_time' | 'filename' | 'file_size'
  sort_order?: 'asc' | 'desc'
}

export interface AnalysisResult {
  id: string
  document_id: number
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  result?: {
    overall_score?: number
    total_problems?: number
    categories?: Record<string, number>
    details?: any
  }
  created_at: string
  completed_at?: string
  error_message?: string
}

// 新增类型定义
export interface DocumentProblem {
  problem_id: string
  category: string
  problem_type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  position?: number
  range_start?: number
  range_end?: number
  page_number?: number
  suggestion?: string
  auto_fixable: boolean
  element_id?: string
}

export interface DocumentContent {
  document_id: string
  title?: string
  author?: string
  abstract?: string
  pages: number
  words: number
  tables: number
  images: number
  content_elements?: Array<{
    element_id: string
    element_type: string
    content: string
    position: number
    page_number?: number
  }>
}

export interface DocumentImage {
  image_id: string
  file_path: string
  original_width: number
  original_height: number
  display_width: number
  display_height: number
  position: number
  page_number?: number
  caption?: string
}

export interface PaperCheckResult {
  result_id: string
  paper_standard: string
  overall_score: number
  compliance_status: 'compliant' | 'partially_compliant' | 'non_compliant' | 'unknown'
  total_problems: number
  major_problems: number
  minor_problems: number
  detailed_results: any
  checked_at: string
}

export class DocumentApi {
  /**
   * 上传文档
   */
  async uploadDocument(
    file: File,
    options?: DocumentUploadOptions
  ): Promise<{ document: Document; task_id?: string }> {
    const { onProgress, analysisType = 'paper_check', options: analysisOptions } = options || {}

    try {
      // 创建FormData并添加分析类型参数
      const formData = new FormData();
      formData.append('file', file);
      formData.append('analysis_type', analysisType);
      
      // 如果有其他选项，添加到FormData中
      if (analysisOptions && Object.keys(analysisOptions).length > 0) {
        formData.append('options', JSON.stringify(analysisOptions));
      }
      
      // 使用原生fetch API上传，确保FormData正确传输
      const authToken = localStorage.getItem('access_token');
      const response = await fetch('http://localhost:8000/api/v1/documents/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          // 不设置Content-Type，让浏览器自动设置boundary
        },
        body: formData
      });
      
      const responseData = await response.json();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${responseData.message || 'Upload failed'}`);
      }

      // 处理后端的包装响应格式
      return responseData.data || responseData;
    } catch (error: any) {
      // 如果上传失败，处理不同的错误类型
      if (error?.response?.status === 413 || error?.code === 'FILE_TOO_LARGE') {
        throw new Error('文件过大，请选择小于 50MB 的文件')
      }
      
      if (error?.response?.status === 415) {
        throw new Error('不支持的文件格式，请上传 .docx 文件')
      }
      
      if (error?.response?.status === 402) {
        throw new Error('检测次数不足，请充值后再试')
      }

      throw error
    }
  }

  /**
   * 获取文档列表 - 修复后端数据格式不匹配问题
   */
  async getDocuments(page: number = 1, limit: number = 10, status?: string): Promise<Document[]> {
    const params: any = { page, limit };
    if (status) {
      params.status = status;
    }
    
    try {
      // 后端返回格式: {documents: Document[], pagination: {}}
      const response = await apiService.get<{documents: Document[], pagination: any}>('/v1/documents/', { params });
      
      // 确保返回documents数组，如果没有则返回空数组
      return response.documents || [];
    } catch (error) {
      console.error('获取文档列表失败:', error);
      return [];
    }
  }

  /**
   * 获取文档详情
   */
  async getDocument(documentId: number): Promise<Document> {
    return apiService.get<Document>(`/v1/documents/${documentId}`)
  }

  /**
   * 根据任务ID获取文档
   */
  async getDocumentByTask(taskId: string): Promise<Document> {
    return apiService.get<Document>(`/v1/documents/task/${taskId}`)
  }

  /**
   * 获取文档内容
   */
  async getDocumentContent(
    documentId: string,
    includeText: boolean = true,
    includeImages: boolean = true,
    elementType?: string
  ): Promise<DocumentContent> {
    const params = new URLSearchParams({
      include_text: includeText.toString(),
      include_images: includeImages.toString()
    })
    
    if (elementType) {
      params.append('element_type', elementType)
    }
    
    return apiService.get<DocumentContent>(`/v1/documents/${documentId}/content?${params.toString()}`)
  }

  /**
   * 获取文档分析结果
   */
  async getDocumentAnalysis(documentId: string): Promise<PaperCheckResult> {
    return apiService.get<PaperCheckResult>(`/v1/documents/${documentId}/analysis`)
  }

  /**
   * 获取文档问题列表
   */
  async getDocumentProblems(
    documentId: string,
    severity?: string,
    category?: string,
    page: number = 1,
    limit: number = 50
  ): Promise<{problems: DocumentProblem[], pagination: any}> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    })
    
    if (severity) params.append('severity', severity)
    if (category) params.append('category', category)
    
    return apiService.get<{problems: DocumentProblem[], pagination: any}>(`/v1/documents/${documentId}/problems?${params.toString()}`)
  }

  /**
   * 获取文档图片列表
   */
  async getDocumentImages(documentId: string): Promise<DocumentImage[]> {
    return apiService.get<DocumentImage[]>(`/v1/documents/${documentId}/images`)
  }

  /**
   * 获取文档内容元素
   */
  async getDocumentElements(
    documentId: string,
    elementType?: string,
    page: number = 1,
    limit: number = 50
  ): Promise<{elements: any[], pagination: any}> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    })
    
    if (elementType) params.append('element_type', elementType)
    
    return apiService.get<{elements: any[], pagination: any}>(`/v1/documents/${documentId}/elements?${params.toString()}`)
  }

  /**
   * 删除文档
   */
  async deleteDocument(documentId: string): Promise<void> {
    return apiService.delete(`/v1/documents/${documentId}`)
  }

  /**
   * 批量删除文档
   */
  async deleteDocuments(documentIds: number[]): Promise<void> {
    return apiService.post('/v1/documents/batch-delete', { document_ids: documentIds })
  }

  /**
   * 启动文档分析
   */
  async startAnalysis(
    documentId: number,
    analysisType: string = 'paper_check',
    options?: Record<string, any>
  ): Promise<{ task_id: string }> {
    return apiService.post<{ task_id: string }>(`/v1/documents/${documentId}/analyze`, {
      analysis_type: analysisType,
      options: options || {}
    })
  }

  /**
   * 获取分析结果
   */
  async getAnalysisResult(documentId: number): Promise<AnalysisResult> {
    return apiService.get<AnalysisResult>(`/v1/documents/${documentId}/analysis`)
  }

  /**
   * 获取检测报告
   */
  async getReport(
    documentId: number,
    format: 'json' | 'html' | 'pdf' | 'text' = 'json'
  ): Promise<any> {
    const url = `/v1/documents/${documentId}/report?format=${format}`
    
    if (format === 'pdf' || format === 'html') {
      // 对于二进制或HTML格式，直接下载
      const response = await apiService.getAxiosInstance().get(url, {
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${apiService.getStoredToken()}`
        }
      })
      return response.data
    }
    
    return apiService.get(url)
  }

  /**
   * 下载报告文件
   */
  async downloadReport(
    documentId: number,
    format: 'pdf' | 'html' | 'json' | 'text' = 'pdf',
    filename?: string
  ): Promise<void> {
    const url = `/v1/documents/${documentId}/report?format=${format}`
    const actualFilename = filename || `report_${documentId}.${format}`
    
    return apiService.download(url, actualFilename)
  }

  /**
   * 下载原文档
   */
  async downloadDocument(documentId: number, filename?: string): Promise<void> {
    const url = `/v1/documents/${documentId}/download`
    return apiService.download(url, filename)
  }

  /**
   * 重新分析文档
   */
  async reanalyzeDocument(
    documentId: string,
    analysisType?: string,
    options?: Record<string, any>
  ): Promise<{ 
    task_id: string; 
    original_task_id?: string;
    remaining_balance?: number; 
    message?: string; 
    status?: string; 
    estimated_time?: string;
  }> {
    return apiService.post<{ 
      task_id: string; 
      original_task_id?: string;
      remaining_balance?: number; 
      message?: string; 
      status?: string; 
      estimated_time?: string;
    }>(`/v1/documents/${documentId}/reanalyze`, {
      analysis_type: analysisType,
      options: options || {}
    })
  }

  /**
   * 获取文档统计信息
   */
  async getDocumentStats(): Promise<{
    total_documents: number
    processing_documents: number
    completed_documents: number
    failed_documents: number
    total_size: number
  }> {
    return apiService.get<{
      total_documents: number
      processing_documents: number
      completed_documents: number
      failed_documents: number
      total_size: number
    }>('/v1/documents/stats')
  }

  /**
   * 搜索文档
   */
  async searchDocuments(
    query: string,
    filters?: {
      status?: string
      date_from?: string
      date_to?: string
      file_type?: string
    }
  ): Promise<PaginatedResponse<Document>> {
    const params = {
      q: query,
      ...filters
    }
    
    const queryParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString())
      }
    })

    return apiService.get<PaginatedResponse<Document>>(`/v1/documents/search?${queryParams.toString()}`)
  }

  /**
   * 预览文档内容
   */
  async previewDocument(documentId: number): Promise<{
    content: string
    pages: number
    word_count: number
    images: number
  }> {
    return apiService.get<{
      content: string
      pages: number
      word_count: number
      images: number
    }>(`/v1/documents/${documentId}/preview`)
  }

  /**
   * 获取支持的文件类型
   */
  async getSupportedFileTypes(): Promise<{
    types: string[]
    max_size: number
    restrictions: Record<string, any>
  }> {
    return apiService.get<{
      types: string[]
      max_size: number
      restrictions: Record<string, any>
    }>('/v1/documents/supported-types')
  }

  getRecentDocuments(limit: number = 5): Promise<Document[]> {
    return this.getDocuments(1, limit);
  }

  /**
   * 导出文档分析报告 (支持Task ID格式)
   */
  async exportDocumentReport(
    documentId: string,
    format: 'json' | 'html' | 'pdf' = 'pdf',
    includeProblems: boolean = true,
    includeSuggestions: boolean = true
  ): Promise<void> {
    const params = new URLSearchParams({
      format,
      include_problems: includeProblems.toString(),
      include_suggestions: includeSuggestions.toString()
    })
    
    const filename = `report_${documentId}.${format}`
    return apiService.download(`/v1/documents/${documentId}/export?${params.toString()}`, filename)
  }
}

// 导出默认实例
export default new DocumentApi() 