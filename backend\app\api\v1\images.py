"""
Word文档分析服务 - 图片资源API
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Response, Depends
from fastapi.responses import FileResponse
import structlog
import os
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.response import success_response, error_response
from app.database import crud
from app.database.session import get_db
from app.models import Image
from app.services.image_processor import get_image_processor
from app.security import get_current_user_id

router = APIRouter()
logger = structlog.get_logger()


@router.get("/document/{document_id}", response_model=List[Image])
async def get_document_images(
    document_id: str,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取指定文档的图片列表
    
    - **document_id**: 文档ID
    - **page**: 页码
    - **limit**: 每页数量
    """
    try:
        # 验证文档是否存在
        document = await crud.get_document(session, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档未找到")
        
        # 获取图片列表
        skip = (page - 1) * limit
        images = await crud.get_images_by_document(
            session,
            document_id, 
            skip=skip, 
            limit=limit
        )
        
        logger.info(f"获取文档图片列表: {document_id}", count=len(images))
        
        return images
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档图片列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")


@router.get("/{image_id}")
async def get_image_info(
    image_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取图片详细信息
    
    - **image_id**: 图片ID
    """
    try:
        image = await crud.get_image(session, image_id)
        if not image:
            raise HTTPException(status_code=404, detail="图片未找到")
        
        logger.info(f"获取图片信息: {image_id}")
        
        return success_response(
            data=image.model_dump(),
            message="获取图片信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片信息失败: {str(e)}")
        return error_response(message=f"获取图片信息失败: {str(e)}", code=500)


@router.get("/{image_id}/download")
async def download_image(
    image_id: str,
    session: AsyncSession = Depends(get_db)
):
    """
    下载图片文件
    
    - **image_id**: 图片ID
    """
    try:
        image = await crud.get_image(session, image_id)
        if not image:
            raise HTTPException(status_code=404, detail="图片未找到")
        
        # 检查文件是否存在
        file_path = Path(image.storage_path)
        if not file_path.exists():
            logger.error(f"图片文件不存在: {image.storage_path}")
            raise HTTPException(status_code=404, detail="图片文件不存在")
        
        logger.info(f"下载图片: {image_id}")
        
        # 返回文件响应
        return FileResponse(
            path=str(file_path),
            filename=image.original_filename or f"image_{image_id}.{image.format.lower()}",
            media_type=f"image/{image.format.lower()}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载图片失败: {str(e)}")


@router.get("/{image_id}/thumbnail")
async def get_image_thumbnail(
    image_id: str,
    size: int = Query(200, ge=50, le=500, description="缩略图大小(像素)"),
    session: AsyncSession = Depends(get_db)
):
    """
    获取图片缩略图
    
    - **image_id**: 图片ID
    - **size**: 缩略图大小(像素)
    """
    try:
        image = await crud.get_image(session, image_id)
        if not image:
            raise HTTPException(status_code=404, detail="图片未找到")
        
        # 检查原始文件是否存在
        file_path = Path(image.storage_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="图片文件不存在")
        
        # 生成缩略图
        processor = get_image_processor()
        thumbnail_data = await processor.generate_thumbnail(
            str(file_path), 
            size=size
        )
        
        logger.info(f"生成图片缩略图: {image_id}", size=size)
        
        # 返回缩略图数据
        return Response(
            content=thumbnail_data,
            media_type=f"image/{image.format.lower()}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图片缩略图失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缩略图失败: {str(e)}")


@router.get("/task/{task_id}")
async def get_task_images(
    task_id: str,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取指定任务相关文档的所有图片
    
    - **task_id**: 任务ID
    - **page**: 页码
    - **limit**: 每页数量
    """
    try:
        # 验证任务是否存在
        task = await crud.get_task(session, task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务未找到")
        
        # 获取任务相关的文档
        documents = await crud.get_documents_by_task(session, task_id)
        if not documents:
            return success_response(data=[], message="任务下无文档")
        
        # 获取所有文档的图片
        all_images = []
        for document in documents:
            images = await crud.get_images_by_document(session, document.document_id)
            all_images.extend(images)
        
        # 分页处理
        skip = (page - 1) * limit
        paginated_images = all_images[skip:skip + limit]
        
        logger.info(f"获取任务图片列表: {task_id}", count=len(paginated_images))
        
        return success_response(
            data={
                "images": [img.model_dump() for img in paginated_images],
                "total": len(all_images),
                "page": page,
                "limit": limit
            },
            message="获取任务图片列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务图片列表失败: {str(e)}")
        return error_response(message=f"获取图片列表失败: {str(e)}", code=500)


@router.get("/")
async def get_images(
    format: Optional[str] = Query(None, description="图片格式筛选"),
    min_width: Optional[int] = Query(None, description="最小宽度筛选"),
    min_height: Optional[int] = Query(None, description="最小高度筛选"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取图片列表，支持筛选和分页
    
    - **format**: 图片格式筛选 (png, jpg, jpeg, gif等)
    - **min_width**: 最小宽度筛选
    - **min_height**: 最小高度筛选
    - **page**: 页码
    - **limit**: 每页数量
    """
    try:
        skip = (page - 1) * limit
        
        # 构建筛选条件
        filters = {}
        if format:
            filters['format'] = format.upper()
        if min_width:
            filters['min_width'] = min_width
        if min_height:
            filters['min_height'] = min_height
        
        # 获取图片列表
        images = await crud.get_images(
            session,
            skip=skip, 
            limit=limit, 
            filters=filters
        )
        
        # 获取总数
        total_images = await crud.count_images(session, filters=filters)
        
        logger.info("获取图片列表", filters=filters, count=len(images))
        
        return success_response(
            data={
                "images": [img.model_dump() for img in images],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_images,
                    "total_pages": (total_images + limit - 1) // limit
                }
            },
            message="获取图片列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取图片列表失败: {str(e)}")
        return error_response(message=f"获取图片列表失败: {str(e)}", code=500)


@router.delete("/{image_id}")
async def delete_image(
    image_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    删除指定图片
    
    - **image_id**: 图片ID
    """
    try:
        image = await crud.get_image(session, image_id)
        if not image:
            raise HTTPException(status_code=404, detail="图片未找到")
        
        # 删除物理文件
        file_path = Path(image.storage_path)
        if file_path.exists():
            file_path.unlink()
            logger.info(f"删除图片文件: {image.storage_path}")
        
        # 删除数据库记录
        await crud.delete_image(session, image_id)
        
        logger.info(f"删除图片: {image_id}")
        
        return success_response(message="删除图片成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除图片失败: {str(e)}")
        return error_response(message=f"删除图片失败: {str(e)}", code=500) 