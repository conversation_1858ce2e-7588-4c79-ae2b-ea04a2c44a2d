<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问题详情显示修复</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .before, .after {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .before { border-color: #ef4444; }
        .after { border-color: #10b981; }
        .header {
            padding: 15px;
            font-weight: bold;
            color: white;
        }
        .before .header { background: #ef4444; }
        .after .header { background: #10b981; }
        .content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            font-size: 14px;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .severity-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }
        .severity-severe { background: #ef4444; }
        .severity-general { background: #f59e0b; }
        .original-text {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .improvement {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .improvement h3 {
            color: #10b981;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .problem-category {
            font-weight: bold;
            color: #1f2937;
        }
        .technical-category {
            color: #6b7280;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 问题详情显示修复</h1>
        <p>修复"问题详情"列显示严重程度而不是具体问题类别的问题。</p>

        <div class="improvement">
            <h3>🔧 修复内容</h3>
            <ul class="feature-list">
                <li><strong>问题详情列</strong>：从显示"严重"、"一般"改为显示"对齐方式问题"、"标点符号问题"等</li>
                <li><strong>信息层次</strong>：问题类别作为主要信息，严重程度作为辅助徽章</li>
                <li><strong>用户体验</strong>：用户可以快速了解具体是什么类型的问题</li>
                <li><strong>配置驱动</strong>：问题类别来自规则文件的problem_templates配置</li>
            </ul>
        </div>

        <h2>📊 修复前后对比</h2>
        <div class="comparison">
            <div class="before">
                <div class="header">❌ 修复前</div>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>原文片段</th>
                                <th>问题详情</th>
                                <th>标准</th>
                                <th>问题描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <div class="original-text">关键词:人工智能;机器学习</div>
                                </td>
                                <td>
                                    <span class="severity-badge severity-severe">严重</span>
                                    <div class="technical-category">format</div>
                                </td>
                                <td>左对齐</td>
                                <td>当前为两端对齐，应为左对齐</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="color: #ef4444; margin-top: 10px;">
                        ❌ 问题：用户看到"严重"但不知道具体是什么问题
                    </div>
                </div>
            </div>

            <div class="after">
                <div class="header">✅ 修复后</div>
                <div class="content">
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>原文片段</th>
                                <th>问题详情</th>
                                <th>标准</th>
                                <th>问题描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>
                                    <div class="original-text">关键词:人工智能;机器学习</div>
                                </td>
                                <td>
                                    <div class="problem-category">对齐方式问题</div>
                                    <span class="severity-badge severity-severe">严重</span>
                                </td>
                                <td>左对齐</td>
                                <td>当前为两端对齐，应为左对齐</td>
                            </tr>
                        </tbody>
                    </table>
                    <div style="color: #10b981; margin-top: 10px;">
                        ✅ 改进：用户立即知道是"对齐方式问题"
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 多种问题类型展示</h2>
        <div id="problem-types-table"></div>

        <h2>📋 技术实现</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
            <h3>1. 后端修改</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 4px; overflow-x: auto;">
// 问题片段生成器修改
def _determine_category_from_rule(self, result: CheckResult) -> str:
    # 🔥 优先使用检测结果中的详细问题类别
    if result.details and isinstance(result.details, dict):
        problems = result.details.get("problems", [])
        if problems and len(problems) > 0:
            first_problem = problems[0]
            category = first_problem.get("category")
            if category:
                return category  # 返回"对齐方式问题"而不是"format"
    
    # 回退到技术分类
    return "format"</pre>

            <h3>2. 前端修改</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 4px; overflow-x: auto;">
<!-- 问题详情列 -->
&lt;td class="px-4 py-3"&gt;
  &lt;div class="flex flex-col space-y-1"&gt;
    &lt;!-- 显示具体的问题类别 --&gt;
    &lt;span class="text-sm font-medium"&gt;
      {{ fragment.category }}  &lt;!-- "对齐方式问题" --&gt;
    &lt;/span&gt;
    &lt;!-- 严重程度作为辅助徽章 --&gt;
    &lt;span class="severity-badge"&gt;
      {{ getSeverityDisplayName(fragment.severity) }}
    &lt;/span&gt;
  &lt;/div&gt;
&lt;/td&gt;</pre>

            <h3>3. 规则文件配置</h3>
            <pre style="background: #fff; padding: 15px; border-radius: 4px; overflow-x: auto;">
"problem_templates": {
  "alignment_error": {
    "category": "对齐方式问题",    // 这个值会显示在"问题详情"列
    "standard": "左对齐",
    "description_template": "当前为{actual_alignment}，应为左对齐"
  }
}</pre>
        </div>
    </div>

    <script>
        // 展示多种问题类型
        function showProblemTypesTable() {
            const problemTypes = [
                {
                    original: "关键词:人工智能;机器学习",
                    category: "对齐方式问题",
                    severity: "严重",
                    standard: "左对齐",
                    description: "当前为两端对齐，应为左对齐"
                },
                {
                    original: "关键词:深度学习;神经网络;算法",
                    category: "标点符号问题",
                    severity: "严重", 
                    standard: "中文冒号（：）",
                    description: "使用了英文冒号(:)，应使用中文冒号（：）"
                },
                {
                    original: "关键词：机器学习;数据挖掘;人工智能",
                    category: "分隔符问题",
                    severity: "一般",
                    standard: "中文分号（；）",
                    description: "使用了英文分号(;)，应使用中文分号（；）"
                },
                {
                    original: "关键词：计算机科学；软件工程",
                    category: "数量问题",
                    severity: "一般",
                    standard: "3-5个关键词",
                    description: "当前2个关键词，建议3-5个"
                },
                {
                    original: "关键词：算法设计；数据结构；",
                    category: "格式问题",
                    severity: "一般",
                    standard: "最后一个关键词后不加分号",
                    description: "最后一个关键词后不应有分号"
                }
            ]

            let html = `
            <table>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>原文片段</th>
                        <th>问题详情</th>
                        <th>标准</th>
                        <th>问题描述</th>
                    </tr>
                </thead>
                <tbody>
            `

            problemTypes.forEach((problem, index) => {
                const severityClass = problem.severity === '严重' ? 'severity-severe' : 'severity-general'
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>
                        <div class="original-text">${problem.original}</div>
                    </td>
                    <td>
                        <div class="problem-category">${problem.category}</div>
                        <span class="severity-badge ${severityClass}">${problem.severity}</span>
                    </td>
                    <td>${problem.standard}</td>
                    <td>${problem.description}</td>
                </tr>
                `
            })

            html += `
                </tbody>
            </table>
            <div style="color: #10b981; margin-top: 15px;">
                ✅ 现在用户可以清楚地看到每个问题的具体类型！
            </div>
            `

            document.getElementById('problem-types-table').innerHTML = html
        }

        // 页面加载时显示表格
        document.addEventListener('DOMContentLoaded', showProblemTypesTable)
    </script>
</body>
</html>
