import json
import requests

# 测试API接口
response = requests.get("http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024")
data = response.json()

doc_structure = data['data']['document_structure']
print('=== 检测标准结构 ===')
for i, section in enumerate(doc_structure):
    status = "必需" if section["required"] else "可选"
    print(f'{i+1:2d}. {section["name"]:8s} - {status}')

# 特别检查致谢和附录
print('\n=== 重点检查 ===')
for section in doc_structure:
    if section["name"] in ["致谢", "附录"]:
        print(f'{section["name"]}: required = {section["required"]}')
