"""
Word实例池管理器

提供Word应用程序实例的池化管理：
- Word实例池管理
- 实例复用和生命周期管理
- 资源监控和统计
- 自动清理和回收
"""

import threading
import time
import queue
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from contextlib import contextmanager
import structlog

from app.services.word_com import WordApplication, WordCOMError
from app.core.config import settings

logger = structlog.get_logger()


class ResourceError(Exception):
    """资源管理异常"""
    pass


class WordInstanceInfo:
    """Word实例信息"""
    
    def __init__(self, instance: WordApplication, instance_id: str):
        self.instance = instance
        self.instance_id = instance_id
        self.created_at = datetime.utcnow()
        self.last_used_at = datetime.utcnow()
        self.usage_count = 0
        self.is_busy = False
        self.is_healthy = True
        self.error_count = 0
        self.max_errors = 5
    
    def mark_used(self):
        """标记实例被使用"""
        self.last_used_at = datetime.utcnow()
        self.usage_count += 1
    
    def mark_error(self):
        """标记实例出错"""
        self.error_count += 1
        if self.error_count >= self.max_errors:
            self.is_healthy = False
            logger.warning(f"Word实例 {self.instance_id} 错误次数过多，标记为不健康")
    
    def reset_errors(self):
        """重置错误计数"""
        self.error_count = 0
        self.is_healthy = True
    
    def get_age_seconds(self) -> float:
        """获取实例年龄（秒）"""
        return (datetime.utcnow() - self.created_at).total_seconds()
    
    def get_idle_seconds(self) -> float:
        """获取实例空闲时间（秒）"""
        return (datetime.utcnow() - self.last_used_at).total_seconds()
    
    def should_retire(self, max_age: float = 3600, max_usage: int = 100) -> bool:
        """检查实例是否应该退休"""
        return (
            not self.is_healthy or
            self.get_age_seconds() > max_age or
            self.usage_count > max_usage
        )
    
    def get_info(self) -> Dict[str, Any]:
        """获取实例信息"""
        return {
            'instance_id': self.instance_id,
            'created_at': self.created_at.isoformat(),
            'last_used_at': self.last_used_at.isoformat(),
            'usage_count': self.usage_count,
            'is_busy': self.is_busy,
            'is_healthy': self.is_healthy,
            'error_count': self.error_count,
            'age_seconds': self.get_age_seconds(),
            'idle_seconds': self.get_idle_seconds(),
            'should_retire': self.should_retire()
        }


class WordInstancePool:
    """Word实例池"""
    
    def __init__(
        self,
        min_size: int = 1,
        max_size: int = 5,
        max_idle_time: float = 300,  # 5分钟
        max_instance_age: float = 3600,  # 1小时
        max_instance_usage: int = 100,
        health_check_interval: float = 60  # 1分钟
    ):
        self.min_size = min_size
        self.max_size = max_size
        self.max_idle_time = max_idle_time
        self.max_instance_age = max_instance_age
        self.max_instance_usage = max_instance_usage
        self.health_check_interval = health_check_interval
        
        self.instances: Dict[str, WordInstanceInfo] = {}
        self.available_queue = queue.Queue()
        self.lock = threading.RLock()
        self.next_instance_id = 1
        
        # 统计信息
        self.stats = {
            'created_instances': 0,
            'destroyed_instances': 0,
            'borrowed_instances': 0,
            'returned_instances': 0,
            'health_checks': 0,
            'failed_health_checks': 0
        }
        
        # 健康检查线程
        self.health_check_thread = None
        self.shutdown_event = threading.Event()
        
        # 初始化池
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化实例池"""
        try:
            logger.info(f"正在初始化Word实例池，最小大小: {self.min_size}, 最大大小: {self.max_size}")
            
            # 创建最小数量的实例
            for _ in range(self.min_size):
                self._create_instance()
            
            # 启动健康检查线程
            self._start_health_check()
            
            logger.info(f"Word实例池初始化完成，当前实例数: {len(self.instances)}")
            
        except Exception as e:
            logger.error(f"初始化Word实例池失败: {str(e)}")
            raise ResourceError(f"初始化Word实例池失败: {str(e)}")
    
    def _create_instance(self) -> Optional[WordInstanceInfo]:
        """创建新的Word实例"""
        try:
            with self.lock:
                if len(self.instances) >= self.max_size:
                    logger.warning(f"Word实例池已达到最大大小: {self.max_size}")
                    return None
                
                instance_id = f"word-{self.next_instance_id}"
                self.next_instance_id += 1
                
                logger.debug(f"正在创建Word实例: {instance_id}")
                
                # 创建Word应用程序实例
                word_app = WordApplication(visible=False)
                if not word_app.start():
                    raise WordCOMError("无法启动Word应用程序")
                
                # 创建实例信息
                instance_info = WordInstanceInfo(word_app, instance_id)
                self.instances[instance_id] = instance_info
                
                # 添加到可用队列
                self.available_queue.put(instance_id)
                
                # 更新统计
                self.stats['created_instances'] += 1
                
                logger.info(f"Word实例创建成功: {instance_id}")
                return instance_info
                
        except Exception as e:
            logger.error(f"创建Word实例失败: {str(e)}")
            return None
    
    def _destroy_instance(self, instance_id: str) -> bool:
        """销毁Word实例"""
        try:
            with self.lock:
                instance_info = self.instances.get(instance_id)
                if instance_info is None:
                    logger.warning(f"Word实例不存在: {instance_id}")
                    return False
                
                if instance_info.is_busy:
                    logger.warning(f"Word实例正在使用中，无法销毁: {instance_id}")
                    return False
                
                logger.debug(f"正在销毁Word实例: {instance_id}")
                
                # 停止Word应用程序
                instance_info.instance.stop()
                
                # 从池中移除
                del self.instances[instance_id]
                
                # 更新统计
                self.stats['destroyed_instances'] += 1
                
                logger.info(f"Word实例已销毁: {instance_id}")
                return True
                
        except Exception as e:
            logger.error(f"销毁Word实例失败: {instance_id}, 错误: {str(e)}")
            return False
    
    def borrow_instance(self, timeout: float = 30.0) -> Optional[WordInstanceInfo]:
        """借用Word实例"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 尝试从队列获取可用实例
                    instance_id = self.available_queue.get(timeout=1.0)
                    
                    with self.lock:
                        instance_info = self.instances.get(instance_id)
                        if instance_info is None:
                            continue
                        
                        # 检查实例健康状态
                        if not instance_info.is_healthy or instance_info.should_retire():
                            # 销毁不健康的实例
                            self._destroy_instance(instance_id)
                            # 尝试创建新实例
                            self._create_instance()
                            continue
                        
                        # 标记为忙碌
                        instance_info.is_busy = True
                        instance_info.mark_used()
                        
                        # 更新统计
                        self.stats['borrowed_instances'] += 1
                        
                        logger.debug(f"借用Word实例: {instance_id}")
                        return instance_info
                        
                except queue.Empty:
                    # 队列为空，尝试创建新实例
                    with self.lock:
                        if len(self.instances) < self.max_size:
                            new_instance = self._create_instance()
                            if new_instance:
                                new_instance.is_busy = True
                                new_instance.mark_used()
                                self.stats['borrowed_instances'] += 1
                                logger.debug(f"借用新创建的Word实例: {new_instance.instance_id}")
                                return new_instance
            
            logger.warning(f"借用Word实例超时: {timeout}秒")
            return None
            
        except Exception as e:
            logger.error(f"借用Word实例失败: {str(e)}")
            return None
    
    def return_instance(self, instance_info: WordInstanceInfo, has_error: bool = False):
        """归还Word实例"""
        try:
            with self.lock:
                if instance_info.instance_id not in self.instances:
                    logger.warning(f"归还的Word实例不在池中: {instance_info.instance_id}")
                    return
                
                # 标记错误
                if has_error:
                    instance_info.mark_error()
                else:
                    instance_info.reset_errors()
                
                # 检查是否应该退休
                if instance_info.should_retire():
                    logger.info(f"Word实例需要退休: {instance_info.instance_id}")
                    self._destroy_instance(instance_info.instance_id)
                    # 如果实例数量低于最小值，创建新实例
                    if len(self.instances) < self.min_size:
                        self._create_instance()
                else:
                    # 标记为可用
                    instance_info.is_busy = False
                    self.available_queue.put(instance_info.instance_id)
                
                # 更新统计
                self.stats['returned_instances'] += 1
                
                logger.debug(f"归还Word实例: {instance_info.instance_id}")
                
        except Exception as e:
            logger.error(f"归还Word实例失败: {str(e)}")
    
    @contextmanager
    def get_instance(self, timeout: float = 30.0):
        """获取Word实例的上下文管理器"""
        instance_info = None
        has_error = False
        
        try:
            instance_info = self.borrow_instance(timeout)
            if instance_info is None:
                raise ResourceError(f"无法获取Word实例，超时: {timeout}秒")
            
            yield instance_info.instance
            
        except Exception as e:
            has_error = True
            logger.error(f"使用Word实例时出错: {str(e)}")
            raise e
            
        finally:
            if instance_info is not None:
                self.return_instance(instance_info, has_error)
    
    def _start_health_check(self):
        """启动健康检查线程"""
        if self.health_check_thread is not None:
            return
        
        def health_check_worker():
            while not self.shutdown_event.wait(self.health_check_interval):
                try:
                    self._perform_health_check()
                except Exception as e:
                    logger.error(f"健康检查失败: {str(e)}")
        
        self.health_check_thread = threading.Thread(
            target=health_check_worker,
            name="WordPool-HealthCheck",
            daemon=True
        )
        self.health_check_thread.start()
        logger.info("Word实例池健康检查线程已启动")
    
    def _perform_health_check(self):
        """执行健康检查"""
        try:
            with self.lock:
                current_time = datetime.utcnow()
                instances_to_remove = []
                
                for instance_id, instance_info in self.instances.items():
                    if instance_info.is_busy:
                        continue
                    
                    try:
                        # 检查实例是否仍在运行
                        if not instance_info.instance.is_running():
                            logger.warning(f"Word实例已停止运行: {instance_id}")
                            instance_info.is_healthy = False
                        
                        # 检查空闲时间
                        if instance_info.get_idle_seconds() > self.max_idle_time:
                            if len(self.instances) > self.min_size:
                                logger.info(f"Word实例空闲时间过长，准备销毁: {instance_id}")
                                instances_to_remove.append(instance_id)
                                continue
                        
                        # 检查是否需要退休
                        if instance_info.should_retire():
                            logger.info(f"Word实例需要退休: {instance_id}")
                            instances_to_remove.append(instance_id)
                            continue
                        
                        self.stats['health_checks'] += 1
                        
                    except Exception as e:
                        logger.error(f"检查Word实例健康状态失败: {instance_id}, 错误: {str(e)}")
                        instance_info.mark_error()
                        self.stats['failed_health_checks'] += 1
                
                # 移除不健康的实例
                for instance_id in instances_to_remove:
                    self._destroy_instance(instance_id)
                
                # 确保最小实例数量
                while len(self.instances) < self.min_size:
                    if not self._create_instance():
                        break
                        
        except Exception as e:
            logger.error(f"执行健康检查失败: {str(e)}")
    
    def shutdown(self, timeout: float = 30.0):
        """关闭实例池"""
        try:
            logger.info("正在关闭Word实例池...")
            
            # 停止健康检查线程
            self.shutdown_event.set()
            if self.health_check_thread:
                self.health_check_thread.join(timeout=5.0)
            
            # 销毁所有实例
            with self.lock:
                instance_ids = list(self.instances.keys())
                for instance_id in instance_ids:
                    self._destroy_instance(instance_id)
                
                # 清空队列
                while not self.available_queue.empty():
                    try:
                        self.available_queue.get_nowait()
                    except queue.Empty:
                        break
            
            logger.info("Word实例池已关闭")
            
        except Exception as e:
            logger.error(f"关闭Word实例池失败: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取实例池状态"""
        with self.lock:
            total_instances = len(self.instances)
            busy_instances = sum(1 for info in self.instances.values() if info.is_busy)
            healthy_instances = sum(1 for info in self.instances.values() if info.is_healthy)
            
            instance_details = [info.get_info() for info in self.instances.values()]
            
            return {
                'pool_config': {
                    'min_size': self.min_size,
                    'max_size': self.max_size,
                    'max_idle_time': self.max_idle_time,
                    'max_instance_age': self.max_instance_age,
                    'max_instance_usage': self.max_instance_usage
                },
                'current_status': {
                    'total_instances': total_instances,
                    'busy_instances': busy_instances,
                    'available_instances': total_instances - busy_instances,
                    'healthy_instances': healthy_instances,
                    'unhealthy_instances': total_instances - healthy_instances
                },
                'statistics': dict(self.stats),
                'instances': instance_details
            }


# 全局Word实例池
_word_pool = None
_pool_lock = threading.Lock()


def get_word_pool() -> WordInstancePool:
    """
    获取全局Word实例池
    
    Returns:
        WordInstancePool: Word实例池
    """
    global _word_pool
    
    with _pool_lock:
        if _word_pool is None:
            min_size = getattr(settings, 'WORD_POOL_MIN_SIZE', 1)
            max_size = getattr(settings, 'WORD_POOL_MAX_SIZE', 5)
            max_idle_time = getattr(settings, 'WORD_POOL_MAX_IDLE_TIME', 300)
            max_instance_age = getattr(settings, 'WORD_POOL_MAX_INSTANCE_AGE', 3600)
            max_instance_usage = getattr(settings, 'WORD_POOL_MAX_INSTANCE_USAGE', 100)
            
            _word_pool = WordInstancePool(
                min_size=min_size,
                max_size=max_size,
                max_idle_time=max_idle_time,
                max_instance_age=max_instance_age,
                max_instance_usage=max_instance_usage
            )
        
        return _word_pool


def cleanup_word_pool():
    """清理全局Word实例池"""
    global _word_pool
    
    with _pool_lock:
        if _word_pool is not None:
            _word_pool.shutdown()
            _word_pool = None
            logger.info("全局Word实例池已清理") 