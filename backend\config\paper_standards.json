{"metadata": {"name": "论文标准检测配置", "version": "1.0.0", "description": "学术论文格式和结构检测标准配置", "created_at": "2024-12-19", "author": "Word Document Analysis Service"}, "format_standards": {"page_format": {"paper_size": "A4", "orientation": "portrait", "margins": {"top": 25.4, "bottom": 25.4, "left": 31.8, "right": 31.8, "unit": "mm"}, "margin_tolerance": 2.0}, "font_format": {"body_font": {"name": "宋体", "size": 12, "size_tolerance": 0.5}, "heading_fonts": {"h1": {"name": "黑体", "size": 16, "bold": true}, "h2": {"name": "黑体", "size": 14, "bold": true}, "h3": {"name": "黑体", "size": 12, "bold": true}}, "allowed_fonts": ["宋体", "黑体", "Times New Roman", "<PERSON><PERSON>", "Calib<PERSON>"], "max_font_types": 4}, "paragraph_format": {"line_spacing": 1.5, "line_spacing_tolerance": 0.1, "first_line_indent": 24, "indent_tolerance": 2, "alignment": "justify", "paragraph_spacing": {"before": 0, "after": 0, "unit": "pt"}}, "style_format": {"heading_numbering": true, "consistent_heading_format": true, "toc_required": true, "page_numbers": true, "header_footer_consistent": true}}, "structure_standards": {"heading_structure": {"max_levels": 6, "require_numbering": true, "numbering_patterns": ["^\\d+$", "^\\d+\\.\\d+$", "^\\d+\\.\\d+\\.\\d+$", "^\\d+\\.\\d+\\.\\d+\\.\\d+$", "^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$", "^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$"], "skip_levels_allowed": false, "numbering_consistency": true}, "chapter_structure": {"required_sections": ["摘要", "abstract", "引言", "introduction", "结论", "conclusion", "参考文献", "references"], "section_order": ["摘要", "abstract", "关键词", "keywords", "目录", "contents", "引言", "introduction", "文献综述", "literature review", "研究方法", "methodology", "结果与分析", "results", "讨论", "discussion", "结论", "conclusion", "参考文献", "references", "附录", "appendix"], "min_sections": 5, "max_sections": 15}, "toc_structure": {"required": true, "max_depth": 3, "page_numbers_required": true, "dot_leaders": true, "consistent_formatting": true}, "reference_structure": {"required": true, "min_references": 5, "max_references": 100, "citation_formats": ["apa", "mla", "gb7714", "ieee"], "in_text_citation_required": true, "citation_consistency": true, "reference_completeness": true}}, "content_standards": {"word_count": {"total_words": {"min": 8000, "max": 50000}, "abstract_words": {"min": 200, "max": 500}, "introduction_words": {"min": 800, "max": 2000}, "conclusion_words": {"min": 500, "max": 1500}}, "language_quality": {"spelling_check": true, "grammar_check": true, "terminology_consistency": true, "academic_tone": true}, "figure_table": {"caption_required": true, "numbering_required": true, "in_text_reference": true, "quality_standards": {"resolution_min": 300, "format_allowed": ["png", "jpg", "eps", "pdf"]}}}, "rules": [{"rule_id": "page_margin_check", "name": "页边距检查", "description": "检查页面边距是否符合标准要求", "rule_type": "layout", "severity": "warning", "status": "active", "conditions": {"top_margin_min": 20.0, "bottom_margin_min": 20.0, "left_margin_min": 25.0, "right_margin_min": 20.0, "tolerance": 2.0}, "parameters": {"unit": "mm", "strict_mode": false}, "tags": ["layout", "margin", "basic"], "version": "1.0.0", "author": "system"}, {"rule_id": "font_consistency_check", "name": "字体一致性检查", "description": "检查文档字体使用的一致性", "rule_type": "format", "severity": "warning", "status": "active", "conditions": {"allowed_fonts": ["宋体", "Times New Roman", "<PERSON><PERSON>"], "max_font_types": 3, "body_font_required": "宋体"}, "parameters": {"tolerance_percentage": 10}, "tags": ["format", "font", "consistency"], "version": "1.0.0", "author": "system"}, {"rule_id": "heading_structure_check", "name": "标题结构检查", "description": "检查标题层级和编号的规范性", "rule_type": "structure", "severity": "error", "status": "active", "conditions": {"max_levels": 6, "require_numbering": true, "skip_levels_allowed": false}, "parameters": {"numbering_patterns": ["^\\d+$", "^\\d+\\.\\d+$", "^\\d+\\.\\d+\\.\\d+$"]}, "tags": ["structure", "heading", "numbering"], "version": "1.0.0", "author": "system"}, {"rule_id": "reference_completeness_check", "name": "参考文献完整性检查", "description": "检查参考文献的完整性和格式", "rule_type": "reference", "severity": "error", "status": "active", "conditions": {"min_references": 5, "citation_required": true, "format_consistency": true}, "parameters": {"allowed_formats": ["apa", "gb7714"], "check_urls": true}, "tags": ["reference", "citation", "completeness"], "version": "1.0.0", "author": "system"}, {"rule_id": "abstract_word_count_check", "name": "摘要字数检查", "description": "检查摘要字数是否在合理范围内", "rule_type": "content", "severity": "warning", "status": "active", "conditions": {"min_words": 200, "max_words": 500, "language_specific": true}, "parameters": {"chinese_char_as_word": true, "exclude_punctuation": true}, "tags": ["content", "word_count", "abstract"], "version": "1.0.0", "author": "system"}], "rule_groups": {"basic_format": ["page_margin_check", "font_consistency_check"], "structure_check": ["heading_structure_check", "reference_completeness_check"], "content_check": ["abstract_word_count_check"], "comprehensive": ["page_margin_check", "font_consistency_check", "heading_structure_check", "reference_completeness_check", "abstract_word_count_check"], "hbkj_structure": ["hbkj_structure_001_cover", "hbkj_structure_002_task_book", "hbkj_structure_003_proposal_report", "hbkj_structure_004_integrity_statement", "hbkj_structure_005_copyright_statement", "hbkj_structure_006_abstract_complete", "hbkj_structure_007_table_of_contents", "hbkj_structure_008_main_body", "hbkj_structure_009_references", "hbkj_structure_010_acknowledgments", "hbkj_structure_011_appendix"], "hbkj_format": ["hbkj_format_001_page_setup", "hbkj_format_002_body_text", "hbkj_format_003_level_1_title", "hbkj_format_004_level_2_title", "hbkj_format_005_level_3_title", "hbkj_format_006_header_footer", "hbkj_format_007_chinese_abstract", "hbkj_format_008_english_abstract", "hbkj_format_009_references_format"], "hbkj_order": ["hbkj_order_001_section_order"]}, "check_profiles": {"undergraduate": {"name": "本科论文检查", "description": "适用于本科毕业论文的检查标准", "rule_groups": ["basic_format", "structure_check"], "word_count": {"min": 8000, "max": 15000}, "reference_count": {"min": 10, "max": 30}}, "graduate": {"name": "研究生论文检查", "description": "适用于硕士研究生论文的检查标准", "rule_groups": ["comprehensive"], "word_count": {"min": 20000, "max": 50000}, "reference_count": {"min": 30, "max": 80}}, "doctoral": {"name": "博士论文检查", "description": "适用于博士学位论文的检查标准", "rule_groups": ["comprehensive"], "word_count": {"min": 50000, "max": 150000}, "reference_count": {"min": 80, "max": 200}}, "journal": {"name": "期刊论文检查", "description": "适用于学术期刊投稿论文的检查标准", "rule_groups": ["basic_format", "content_check"], "word_count": {"min": 3000, "max": 8000}, "reference_count": {"min": 15, "max": 50}}, "hbkj_bachelor": {"name": "河北科技学院本科论文检查", "description": "适用于河北科技学院学士学位论文的检查标准", "rule_file": "hbkj_bachelor_2024.json", "rule_groups": ["hbkj_structure", "hbkj_format", "hbkj_order"], "word_count": {"min": 8000, "max": 15000}, "reference_count": {"min": 10, "max": 30}, "required_sections": ["封面", "任务书", "开题报告", "诚信声明", "版权声明", "中文摘要", "中文关键词", "英文摘要", "英文关键词", "目录", "正文", "参考文献", "致谢", "附录"], "format_requirements": {"page_setup": {"paper_size": "A4", "margins": {"top": 2.5, "bottom": 2.5, "left": 3.0, "right": 2.0, "unit": "cm"}}, "text_format": {"body_font_chinese": "宋体", "body_font_english": "Times New Roman", "body_font_size": 12, "line_spacing": 1.5, "first_line_indent": 2.0}, "heading_format": {"level_1": {"font_family": "黑体", "font_size": 16, "alignment": "center", "bold": true}, "level_2": {"font_family": "黑体", "font_size": 14, "alignment": "left", "bold": true}, "level_3": {"font_family": "黑体", "font_size": 12, "alignment": "left", "bold": true}}}}}}