"""
问题片段生成器

将检测结果(CheckResult)转换为前端需要的问题片段(ProblemFragment)格式，
提供详细的问题定位、描述和修复建议。
"""

import re
import uuid
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..models.check_result import CheckResult, CheckIssue, CheckSeverity
from ..services.document_processor import DocumentData
from ..utils.error_formatter import ErrorDetail, ErrorCategory, ErrorSeverity


class FragmentSeverity(Enum):
    """问题片段严重程度"""
    SEVERE = "severe"      # 严重错误
    GENERAL = "general"    # 一般错误  
    SUGGESTION = "suggestion"  # 建议


@dataclass
class ProblemFragment:
    """问题片段数据类"""
    fragment_id: str
    structure: str
    category: str
    severity: str
    position: int
    original_text: str
    range_start: int
    range_end: int
    context_before: str
    context_after: str
    problem_description: str
    standard_reference: str
    auto_fixable: bool
    rule_id: str
    rule_name: str
    position_description: str = ""  # 🔥 新增：用户友好的位置描述
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "fragment_id": self.fragment_id,
            "structure": self.structure,
            "category": self.category,
            "severity": self.severity,
            "position": self.position,
            "original_text": self.original_text,
            "range_start": self.range_start,
            "range_end": self.range_end,
            "context_before": self.context_before,
            "context_after": self.context_after,
            "problem_description": self.problem_description,
            "standard_reference": self.standard_reference,
            "auto_fixable": self.auto_fixable,
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "position_description": self.position_description  # 🔥 新增字段
        }


class ProblemFragmentGenerator:
    """问题片段生成器"""
    
    def __init__(self):
        self.structure_mapping = self._init_structure_mapping()
        self.category_mapping = self._init_category_mapping()
        self.severity_mapping = self._init_severity_mapping()
    
    def generate_fragments_from_results(
        self, 
        check_results: List[CheckResult], 
        document_data: DocumentData
    ) -> List[ProblemFragment]:
        """
        从检测结果生成问题片段列表
        
        Args:
            check_results: 检测结果列表
            document_data: 文档数据
            
        Returns:
            问题片段列表
        """
        fragments = []
        
        for result in check_results:
            if not result.passed:  # 只处理未通过的检测结果
                # 🔥 新增：过滤掉文档结构相关的检测结果
                if self._should_exclude_from_fragments(result):
                    continue

                fragment_list = self._convert_result_to_fragments(result, document_data)
                fragments.extend(fragment_list)
        
        return fragments

    def _should_exclude_from_fragments(self, result: CheckResult) -> bool:
        """
        判断检测结果是否应该从问题片段中排除

        Args:
            result: 检测结果

        Returns:
            True表示应该排除，False表示应该包含
        """
        # 🔥 新方案：从规则配置中读取exclude_from_fragments字段
        return self._check_rule_exclude_config(result.rule_id)

    def _check_rule_exclude_config(self, rule_id: str) -> bool:
        """
        检查规则配置中是否设置了exclude_from_fragments

        Args:
            rule_id: 规则ID，格式如 "structure.section_order"

        Returns:
            True表示应该排除，False表示应该包含
        """
        try:
            # 加载规则配置
            import json
            import os

            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'config', 'rules', 'hbkj_bachelor_2024.json'
            )

            if not os.path.exists(config_path):
                logger.warning(f"规则配置文件不存在: {config_path}")
                return False

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 解析规则ID，如 "structure.section_order" -> ["structure", "section_order"]
            rule_parts = rule_id.split('.')
            if len(rule_parts) < 2:
                return False

            category = rule_parts[0]  # 如 "structure"
            rule_name = rule_parts[1]  # 如 "section_order"

            # 在配置中查找规则
            rules = config.get('rules', {})
            category_rules = rules.get(category, {})
            rule_config = category_rules.get(rule_name, {})

            # 检查是否设置了exclude_from_fragments
            exclude_flag = rule_config.get('exclude_from_fragments', False)

            if exclude_flag:
                logger.info(f"规则 {rule_id} 配置为排除问题片段")

            return exclude_flag

        except Exception as e:
            logger.error(f"检查规则排除配置失败: {str(e)}")
            return False

    def _convert_result_to_fragments(
        self, 
        result: CheckResult, 
        document_data: DocumentData
    ) -> List[ProblemFragment]:
        """
        将单个检测结果转换为问题片段
        
        Args:
            result: 检测结果
            document_data: 文档数据
            
        Returns:
            问题片段列表
        """
        fragments = []
        
        # 如果有具体的issues，为每个issue创建片段
        if result.issues:
            for issue in result.issues:
                fragment = self._create_fragment_from_issue(issue, result, document_data)
                if fragment:
                    fragments.append(fragment)
        else:
            # 如果没有具体issues，从result本身创建片段
            fragment = self._create_fragment_from_result(result, document_data)
            if fragment:
                fragments.append(fragment)
        
        return fragments
    
    def _create_fragment_from_issue(
        self, 
        issue: CheckIssue, 
        result: CheckResult, 
        document_data: DocumentData
    ) -> Optional[ProblemFragment]:
        """从CheckIssue创建问题片段"""
        
        # 确定文档结构
        structure = self._determine_structure(issue, result, document_data)
        
        # 确定问题类别
        category = self._determine_category(issue, result)
        
        # 确定严重程度
        severity = self._map_severity(issue.severity)
        
        # 获取位置信息
        position, range_start, range_end = self._get_position_info(issue, document_data)
        
        # 获取原文和上下文
        original_text, context_before, context_after = self._extract_text_context(
            position, range_start, range_end, document_data
        )
        
        # 生成问题描述和标准参考
        problem_description = self._generate_problem_description(issue, result)
        standard_reference = self._generate_standard_reference(issue, result)
        
        # 判断是否可自动修复
        auto_fixable = self._is_auto_fixable(issue, result)
        
        return ProblemFragment(
            fragment_id=f"frag_{uuid.uuid4().hex[:8]}",
            structure=structure,
            category=category,
            severity=severity,
            position=position,
            original_text=original_text,
            range_start=range_start,
            range_end=range_end,
            context_before=context_before,
            context_after=context_after,
            problem_description=problem_description,
            standard_reference=standard_reference,
            auto_fixable=auto_fixable,
            rule_id=result.rule_id,
            rule_name=result.rule_name
        )
    
    def _create_fragment_from_result(
        self, 
        result: CheckResult, 
        document_data: DocumentData
    ) -> Optional[ProblemFragment]:
        """从CheckResult直接创建问题片段"""
        
        # 确定文档结构
        structure = self._determine_structure_from_rule(result, document_data)
        
        # 确定问题类别
        category = self._determine_category_from_rule(result)
        
        # 确定严重程度
        severity = self._map_severity(result.severity)
        
        # 获取位置信息
        position, range_start, range_end = self._get_position_from_result(result, document_data)
        
        # 获取原文和上下文
        original_text, context_before, context_after = self._extract_text_context_from_result(
            result, position, range_start, range_end, document_data
        )
        
        # 生成问题描述和标准参考
        problem_description = self._generate_problem_description_from_result(result)
        standard_reference = self._generate_standard_reference_from_result(result)

        # 判断是否可自动修复
        auto_fixable = self._is_auto_fixable_from_result(result)

        # 🔥 新增：生成用户友好的位置描述
        position_description = self._generate_position_description(
            result, position, original_text, document_data
        )

        return ProblemFragment(
            fragment_id=f"frag_{uuid.uuid4().hex[:8]}",
            structure=structure,
            category=category,
            severity=severity,
            position=position,
            original_text=original_text,
            range_start=range_start,
            range_end=range_end,
            context_before=context_before,
            context_after=context_after,
            problem_description=problem_description,
            standard_reference=standard_reference,
            auto_fixable=auto_fixable,
            rule_id=result.rule_id,
            rule_name=result.rule_name,
            position_description=position_description  # 🔥 新增字段
        )
    
    def _determine_structure(
        self, 
        issue: CheckIssue, 
        result: CheckResult, 
        document_data: DocumentData
    ) -> str:
        """确定问题所属的文档结构"""
        
        # 从issue的location或context中获取结构信息
        if issue.location:
            return self._map_location_to_structure(issue.location)
        
        # 从rule_id推断结构
        return self._infer_structure_from_rule(result.rule_id, document_data)
    
    def _determine_structure_from_rule(
        self, 
        result: CheckResult, 
        document_data: DocumentData
    ) -> str:
        """从规则ID确定文档结构"""
        return self._infer_structure_from_rule(result.rule_id, document_data)
    
    def _infer_structure_from_rule(self, rule_id: str, document_data: DocumentData) -> str:
        """从规则ID推断文档结构"""
        
        # 规则ID到结构的映射
        rule_structure_mapping = {
            "content.chinese_abstract": "中文摘要",
            "content.english_abstract": "英文摘要",
            "content.chinese_keywords": "中文关键词",
            "content.english_keywords": "英文关键词",
            "content.references": "参考文献",
            "format.level_1_title": "正文",
            "format.level_2_title": "正文",
            "format.level_3_title": "正文",
            "format.body_text": "正文",
            "format.page_setup": "页面设置",
            "format.header_footer": "页眉页脚",
            "structure.section_order": "文档结构",
            # 🔥 新增：中文关键词格式检查规则映射
            "format.chinese_keywords": "中文关键词",
            "format.chinese_keywords_format": "中文关键词",
            "format.chinese_keywords_label": "中文关键词",
            "format.chinese_keywords_separator": "中文关键词",
            # 🔥 新增：其他格式检查规则映射
            "format.english_abstract_title": "英文摘要",
            "format.paragraph": "正文",
            "format.headings": "正文",
            "format.references": "参考文献"
        }
        
        # 精确匹配
        for pattern, structure in rule_structure_mapping.items():
            if pattern in rule_id:
                return structure
        
        # 默认返回正文
        return "正文"
    
    def _init_structure_mapping(self) -> Dict[str, str]:
        """初始化结构映射"""
        return {
            "abstract": "中文摘要",
            "english_abstract": "英文摘要",
            "keywords": "中文关键词", 
            "english_keywords": "英文关键词",
            "references": "参考文献",
            "body": "正文",
            "title": "标题",
            "header": "页眉页脚",
            "footer": "页眉页脚"
        }
    
    def _init_category_mapping(self) -> Dict[str, str]:
        """初始化类别映射"""
        return {
            "format": "format",
            "structure": "structure", 
            "content": "content",
            "style": "format",
            "reference": "citation"
        }
    
    def _init_severity_mapping(self) -> Dict[CheckSeverity, str]:
        """初始化严重程度映射"""
        return {
            CheckSeverity.CRITICAL: "severe",
            CheckSeverity.ERROR: "severe",
            CheckSeverity.WARNING: "general",
            CheckSeverity.INFO: "suggestion"
        }

    def _map_severity(self, severity: CheckSeverity) -> str:
        """映射严重程度"""
        return self.severity_mapping.get(severity, "general")

    def _determine_category(self, issue: CheckIssue, result: CheckResult) -> str:
        """确定问题类别"""
        # 从issue类型推断
        issue_type_str = str(issue.issue_type).lower()
        for key, category in self.category_mapping.items():
            if key in issue_type_str:
                return category

        # 从rule_id推断
        return self._determine_category_from_rule(result)

    def _determine_category_from_rule(self, result: CheckResult) -> str:
        """从规则确定问题类别"""
        # 🔥 新增：优先使用检测结果中的详细问题类别
        if result.details and isinstance(result.details, dict):
            problems = result.details.get("problems", [])
            if problems and len(problems) > 0:
                # 使用第一个问题的类别
                first_problem = problems[0]
                category = first_problem.get("category")
                if category:
                    return category

        # 回退到原有的技术分类逻辑
        rule_id = result.rule_id.lower()

        if "format" in rule_id or "font" in rule_id or "size" in rule_id:
            return "format"
        elif "structure" in rule_id or "order" in rule_id:
            return "structure"
        elif "content" in rule_id or "length" in rule_id:
            return "content"
        elif "reference" in rule_id:
            return "citation"
        else:
            return "format"  # 默认为格式问题

    def _get_position_info(
        self,
        issue: CheckIssue,
        document_data: DocumentData
    ) -> Tuple[int, int, int]:
        """获取位置信息"""

        # 从issue中获取位置
        if issue.line_number is not None:
            position = self._line_to_position(issue.line_number, document_data)
            range_start = position
            range_end = position + 50  # 默认范围
            return position, range_start, range_end

        # 从element_id获取位置
        if issue.element_id:
            position = self._element_to_position(issue.element_id, document_data)
            range_start = position
            range_end = position + 100
            return position, range_start, range_end

        # 默认位置
        return 0, 0, 50

    def _get_position_from_result(
        self,
        result: CheckResult,
        document_data: DocumentData
    ) -> Tuple[int, int, int]:
        """从检测结果获取位置信息"""

        if result.position is not None:
            position = result.position
            range_start = position
            range_end = position + 100
            return position, range_start, range_end

        # 根据规则类型推断位置
        position = self._infer_position_from_rule(result.rule_id, document_data)
        range_start = position
        range_end = position + 100
        return position, range_start, range_end

    def _line_to_position(self, line_number: int, document_data: DocumentData) -> int:
        """将行号转换为字符位置"""
        if not document_data.elements:
            return 0

        current_line = 1
        position = 0

        for element in document_data.elements:
            text = element.get("text", "")
            if current_line >= line_number:
                break

            # 计算文本中的换行数
            line_count = text.count('\n') + 1
            if current_line + line_count > line_number:
                # 目标行在当前元素中
                lines = text.split('\n')
                target_line_index = line_number - current_line
                position += sum(len(line) + 1 for line in lines[:target_line_index])
                break

            position += len(text)
            current_line += line_count

        return position

    def _element_to_position(self, element_id: str, document_data: DocumentData) -> int:
        """将元素ID转换为字符位置"""
        if not document_data.elements:
            return 0

        position = 0
        for element in document_data.elements:
            if element.get("id") == element_id:
                return position
            position += len(element.get("text", ""))

        return 0

    def _infer_position_from_rule(self, rule_id: str, document_data: DocumentData) -> int:
        """从规则ID推断位置"""

        # 根据规则类型查找相关内容的位置
        if "chinese_keywords" in rule_id:
            return self._find_structure_position("中文关键词", document_data)
        elif "english_keywords" in rule_id:
            return self._find_structure_position("英文关键词", document_data)
        elif "chinese_abstract" in rule_id:
            return self._find_structure_position("中文摘要", document_data)
        elif "english_abstract" in rule_id:
            return self._find_structure_position("英文摘要", document_data)
        elif "references" in rule_id:
            return self._find_structure_position("参考文献", document_data)
        else:
            return 0

    def _find_structure_position(self, structure_name: str, document_data: DocumentData) -> int:
        """查找特定结构在文档中的位置"""
        if not document_data.elements:
            return 0

        position = 0
        for element in document_data.elements:
            text = element.get("text", "").strip()

            # 检查是否匹配结构名称
            if self._matches_structure_name(text, structure_name):
                return position

            position += len(element.get("text", ""))

        return 0

    def _matches_structure_name(self, text: str, structure_name: str) -> bool:
        """检查文本是否匹配结构名称"""
        text_lower = text.lower()

        structure_patterns = {
            "中文关键词": ["关键词", "关键字"],
            "英文关键词": ["keywords", "key words"],
            "中文摘要": ["摘要", "概要"],
            "英文摘要": ["abstract"],
            "参考文献": ["参考文献", "reference", "参考资料"]
        }

        patterns = structure_patterns.get(structure_name, [structure_name.lower()])

        for pattern in patterns:
            if pattern.lower() in text_lower:
                return True

        return False

    def _extract_text_context(
        self,
        position: int,
        range_start: int,
        range_end: int,
        document_data: DocumentData
    ) -> Tuple[str, str, str]:
        """提取原文和上下文"""

        if not document_data.elements:
            return "", "", ""

        # 构建完整文档文本
        full_text = ""
        element_positions = []

        for element in document_data.elements:
            element_text = element.get("text", "")
            element_positions.append({
                "start": len(full_text),
                "end": len(full_text) + len(element_text),
                "element": element
            })
            full_text += element_text

        # 确保范围有效
        range_start = max(0, min(range_start, len(full_text)))
        range_end = max(range_start, min(range_end, len(full_text)))

        # 提取原文
        original_text = full_text[range_start:range_end].strip()

        # 提取上下文
        context_before_start = max(0, range_start - 50)
        context_after_end = min(len(full_text), range_end + 50)

        context_before = full_text[context_before_start:range_start].strip()
        context_after = full_text[range_end:context_after_end].strip()

        return original_text, context_before, context_after

    def _extract_text_context_from_result(
        self,
        result: CheckResult,
        position: int,
        range_start: int,
        range_end: int,
        document_data: DocumentData
    ) -> Tuple[str, str, str]:
        """从检测结果中提取原文和上下文"""

        # 🔥 新增：优先使用检测结果中的原文信息
        if result.details and isinstance(result.details, dict):
            problems = result.details.get("problems", [])
            if problems and len(problems) > 0:
                first_problem = problems[0]
                original_text = first_problem.get("original_text", "")
                if original_text:
                    # 使用检测结果中的原文，但仍需要提取上下文
                    context_before, context_after = self._extract_context_around_text(
                        original_text, document_data
                    )
                    return original_text, context_before, context_after

        # 回退到默认的文本提取方法
        return self._extract_text_context(position, range_start, range_end, document_data)

    def _extract_context_around_text(self, target_text: str, document_data: DocumentData) -> Tuple[str, str]:
        """在文档中查找目标文本并提取上下文"""

        if not document_data.elements:
            return "", ""

        # 查找包含目标文本的段落
        for i, element in enumerate(document_data.elements):
            if element.get("type") == "paragraph":
                text = element.get("text", "")
                if target_text in text:
                    # 找到了，提取前后上下文
                    context_before = ""
                    context_after = ""

                    # 前面的段落作为前置上下文
                    if i > 0:
                        prev_element = document_data.elements[i-1]
                        if prev_element.get("type") == "paragraph":
                            context_before = prev_element.get("text", "")[:50] + "..."

                    # 后面的段落作为后置上下文
                    if i < len(document_data.elements) - 1:
                        next_element = document_data.elements[i+1]
                        if next_element.get("type") == "paragraph":
                            context_after = "..." + next_element.get("text", "")[:50]

                    return context_before, context_after

        return "", ""

    def _generate_problem_description(self, issue: CheckIssue, result: CheckResult) -> str:
        """生成问题描述"""
        if issue.description:
            return issue.description
        elif issue.title:
            return issue.title
        else:
            return result.message

    def _generate_problem_description_from_result(self, result: CheckResult) -> str:
        """从检测结果生成问题描述"""
        # 🔥 新增：使用详细的问题信息
        if result.details and isinstance(result.details, dict):
            problems = result.details.get("problems", [])
            if problems and len(problems) > 0:
                # 使用第一个问题的详细描述
                first_problem = problems[0]
                return first_problem.get("description", result.message)

        return result.message

    def _generate_standard_reference(self, issue: CheckIssue, result: CheckResult) -> str:
        """生成标准参考"""

        # 从issue的suggestions中提取标准
        if issue and issue.suggestions:
            return "; ".join(issue.suggestions)

        # 从result的details中提取标准信息
        details = result.details
        if isinstance(details, dict):
            # 查找期望值
            expected_value = details.get("expected_value") or details.get("required_value")
            if expected_value:
                return str(expected_value)

            # 查找标准设置
            required_setup = details.get("required_setup")
            if required_setup:
                return self._format_required_setup(required_setup)

        # 根据规则类型生成默认标准参考
        return self._generate_default_standard_reference(result.rule_id)

    def _generate_standard_reference_from_result(self, result: CheckResult) -> str:
        """从检测结果生成标准参考"""
        # 🔥 新增：使用详细的标准信息
        if result.details and isinstance(result.details, dict):
            problems = result.details.get("problems", [])
            if problems and len(problems) > 0:
                # 使用第一个问题的标准参考
                first_problem = problems[0]
                return first_problem.get("standard", "请参考相关格式标准")

        return self._generate_standard_reference(None, result)

    def _generate_position_description(
        self,
        result: CheckResult,
        position: int,
        original_text: str,
        document_data: DocumentData
    ) -> str:
        """生成用户友好的位置描述"""

        # 🔥 新增：根据规则类型和原文内容生成位置描述
        rule_id = result.rule_id.lower()

        # 中文关键词相关问题
        if "chinese_keywords" in rule_id:
            return "中文关键词段落"

        # 英文关键词相关问题
        elif "english_keywords" in rule_id:
            return "英文关键词段落"

        # 中文摘要相关问题
        elif "chinese_abstract" in rule_id:
            return "中文摘要段落"

        # 英文摘要相关问题
        elif "english_abstract" in rule_id:
            return "英文摘要段落"

        # 参考文献相关问题
        elif "references" in rule_id:
            return "参考文献部分"

        # 标题相关问题
        elif "title" in rule_id or "heading" in rule_id:
            if "level_1" in rule_id:
                return "一级标题"
            elif "level_2" in rule_id:
                return "二级标题"
            elif "level_3" in rule_id:
                return "三级标题"
            else:
                return "标题段落"

        # 正文相关问题
        elif "body" in rule_id or "paragraph" in rule_id:
            return "正文段落"

        # 页面设置相关问题
        elif "page_setup" in rule_id:
            return "页面设置"

        # 页眉页脚相关问题
        elif "header" in rule_id or "footer" in rule_id:
            return "页眉页脚"

        # 尝试从原文内容推断位置
        if original_text:
            text = original_text.strip()

            # 关键词段落
            if text.startswith("关键词：") or text.startswith("关键词:"):
                return "中文关键词段落"
            elif text.startswith("Keywords:") or text.startswith("Key words:"):
                return "英文关键词段落"

            # 摘要段落
            elif "摘要" in text[:10]:
                return "中文摘要段落"
            elif text.startswith("Abstract"):
                return "英文摘要段落"

            # 标题判断
            elif len(text) < 50 and any(char in text for char in "123456789一二三四五六七八九"):
                return "标题段落"

        # 尝试从文档数据中获取更精确的位置信息
        if document_data and document_data.elements:
            # 查找包含该位置的元素
            for element in document_data.elements:
                element_position = element.get("position", 0)
                if abs(element_position - position) < 100:  # 位置接近
                    element_type = element.get("type", "")
                    if element_type == "heading":
                        level = element.get("level", 1)
                        return f"{['一', '二', '三', '四', '五'][level-1] if level <= 5 else str(level)}级标题"
                    elif element_type == "paragraph":
                        return "正文段落"

        # 默认描述
        return f"文档位置 {position}"

    def _format_required_setup(self, required_setup: Dict[str, Any]) -> str:
        """格式化要求设置"""
        formatted_parts = []

        for key, value in required_setup.items():
            if isinstance(value, dict):
                display_value = value.get("value", value)
            else:
                display_value = value

            formatted_parts.append(f"{key}: {display_value}")

        return "; ".join(formatted_parts)

    def _generate_default_standard_reference(self, rule_id: str) -> str:
        """生成默认标准参考"""

        standard_references = {
            "format.body_text": "正文：宋体，小四号，1.5倍行距，首行缩进2字符",
            "format.level_1_title": "一级标题：黑体，三号，居中，加粗",
            "format.level_2_title": "二级标题：黑体，四号，左对齐，加粗",
            "format.level_3_title": "三级标题：黑体，小四号，左对齐，加粗",
            "format.page_setup": "页面设置：A4纸，上下边距2.5cm，左边距3.0cm，右边距2.0cm",
            "content.chinese_keywords": "中文关键词：3-5个，用分号分隔",
            "content.english_keywords": "英文关键词：3-5个，用分号分隔",
            "content.chinese_abstract": "中文摘要：300-500字",
            "content.english_abstract": "英文摘要：300-500词",
            "content.references": "参考文献：至少10条，格式规范"
        }

        for pattern, reference in standard_references.items():
            if pattern in rule_id:
                return reference

        return "请参考相关格式标准"

    def _is_auto_fixable(self, issue: CheckIssue, result: CheckResult) -> bool:
        """判断是否可自动修复"""

        # 检查issue的metadata
        if issue and issue.metadata.get("auto_fixable"):
            return True

        # 检查result的metadata
        if result.metadata.get("auto_fixable"):
            return True

        # 根据问题类型判断
        return self._is_auto_fixable_by_type(result.rule_id)

    def _is_auto_fixable_from_result(self, result: CheckResult) -> bool:
        """从检测结果判断是否可自动修复"""
        return self._is_auto_fixable(None, result)

    def _is_auto_fixable_by_type(self, rule_id: str) -> bool:
        """根据规则类型判断是否可自动修复"""

        # 可自动修复的规则类型
        auto_fixable_rules = [
            "format.body_text",      # 正文格式
            "format.level_2_title",  # 二级标题格式
            "format.level_3_title",  # 三级标题格式
            "content.english_keywords",  # 英文关键词格式
        ]

        # 不可自动修复的规则类型
        non_auto_fixable_rules = [
            "structure.section_order",  # 章节顺序
            "content.chinese_abstract", # 中文摘要内容
            "content.english_abstract", # 英文摘要内容
            "format.level_1_title",     # 一级标题（通常涉及内容）
        ]

        for rule in auto_fixable_rules:
            if rule in rule_id:
                return True

        for rule in non_auto_fixable_rules:
            if rule in rule_id:
                return False

        # 默认根据问题类型判断
        if "format" in rule_id and "font" in rule_id:
            return True  # 字体格式问题通常可自动修复
        elif "format" in rule_id and "size" in rule_id:
            return True  # 字号问题通常可自动修复
        elif "structure" in rule_id:
            return False  # 结构问题通常需要手动修复
        else:
            return False  # 默认不可自动修复

    def _map_location_to_structure(self, location: str) -> str:
        """将位置信息映射到文档结构"""
        location_lower = location.lower()

        for key, structure in self.structure_mapping.items():
            if key in location_lower:
                return structure

        return "正文"  # 默认返回正文
