# Word文档分析服务 - API文档使用指南 (前端开发专用)

## 🎯 前端开发必读指南

本指南专门为前端开发人员提供完整的API对接指导，确保能够快速、正确地集成后端服务。

## 📚 文档优先级和使用策略

### 🥇 **主要开发工具** (强烈推荐)
```
🌐 Swagger UI: http://localhost:8000/docs
```

**为什么最重要**:
- ✅ **100%准确**: 从代码自动生成，与API实现完全同步
- ✅ **实时测试**: 直接在浏览器中测试所有42个API端点
- ✅ **最新状态**: API变更时自动更新，永远是最新的
- ✅ **交互式**: 可以填写参数，查看实时响应，复制工作代码

### 🥈 **完整参考文档**
```
📁 backend/docs/API文档.md (完整版，42个端点详细说明)
```

**使用场景**:
- 📖 离线查看API详细信息
- 🔍 理解API架构和设计思路
- 📋 查看前端调用示例代码
- 📝 作为开发参考手册

### 🥉 **快速入门指南**
```
📁 本文档 - API文档使用指南.md
```

**使用场景**:
- ⚡ 快速上手指导
- 🔧 前端集成最佳实践
- 🚨 常见问题解决方案

## 🚀 快速开始 (5分钟上手)

### 第一步: 验证服务状态
```bash
# 检查后端服务是否启动
curl http://localhost:8000/health
```

**期望响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-19T...",
  "uptime": 123.45
}
```

### 第二步: 访问Swagger UI
1. 打开浏览器访问: `http://localhost:8000/docs`
2. 看到完整的API文档界面
3. 点击任意API端点查看详细信息
4. 点击"Try it out"按钮测试API

### 第三步: 测试基础API
在Swagger UI中测试以下API：

1. **健康检查**: `GET /health` (无需认证)
2. **用户注册**: `POST /api/v1/auth/register`
3. **用户登录**: `POST /api/v1/auth/login` 
4. **获取用户信息**: `GET /api/v1/auth/me` (需要Token)

## 🔧 前端技术集成

### 1. API客户端设置

#### Axios配置 (推荐)
```javascript
// services/api.js
import axios from 'axios';

// 创建API客户端
const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器 - 自动添加Token
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器 - 统一错误处理
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Token过期，清除并跳转登录
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

#### Fetch配置 (原生)
```javascript
// services/fetch-api.js
class ApiService {
  constructor() {
    this.baseURL = 'http://localhost:8000';
  }
  
  async request(endpoint, options = {}) {
    const token = localStorage.getItem('access_token');
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers
      },
      ...options
    };
    
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      
      if (response.status === 401) {
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        return;
      }
      
      return await response.json();
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }
  
  get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }
  
  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
  
  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT', 
      body: JSON.stringify(data)
    });
  }
  
  delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

export const apiService = new ApiService();
```

### 2. 认证管理

#### Token管理器
```javascript
// utils/auth.js
class AuthManager {
  constructor() {
    this.TOKEN_KEY = 'access_token';
    this.REFRESH_TOKEN_KEY = 'refresh_token';
  }
  
  // 保存Token
  setTokens(accessToken, refreshToken) {
    localStorage.setItem(this.TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }
  
  // 获取Token
  getToken() {
    return localStorage.getItem(this.TOKEN_KEY);
  }
  
  // 清除Token
  clearTokens() {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }
  
  // 检查是否已登录
  isAuthenticated() {
    return !!this.getToken();
  }
  
  // 登录
  async login(username, password) {
    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);
      
      const response = await apiClient.post('/api/v1/auth/login', formData);
      const { access_token, refresh_token, user } = response.data.data;
      
      this.setTokens(access_token, refresh_token);
      return { success: true, user };
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      };
    }
  }
  
  // 登出
  async logout() {
    try {
      await apiClient.post('/api/v1/auth/logout');
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      this.clearTokens();
      window.location.href = '/login';
    }
  }
  
  // 获取当前用户信息
  async getCurrentUser() {
    try {
      const response = await apiClient.get('/api/v1/auth/me');
      return { success: true, user: response.data.data };
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '获取用户信息失败' 
      };
    }
  }
}

export const authManager = new AuthManager();
```

### 3. 文档上传功能

#### 文件上传组件
```javascript
// components/DocumentUpload.vue
<template>
  <div class="upload-container">
    <div class="upload-area" @drop="handleDrop" @dragover.prevent>
      <input 
        type="file" 
        ref="fileInput"
        @change="handleFileSelect"
        accept=".doc,.docx"
        style="display: none"
      />
      
      <button @click="$refs.fileInput.click()" :disabled="uploading">
        {{ uploading ? '上传中...' : '选择文件' }}
      </button>
      
      <div v-if="uploading" class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: uploadProgress + '%' }"
        ></div>
        <span>{{ uploadProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script>
import apiClient from '@/services/api';

export default {
  data() {
    return {
      uploading: false,
      uploadProgress: 0
    };
  },
  
  methods: {
    async handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        await this.uploadFile(file);
      }
    },
    
    handleDrop(event) {
      event.preventDefault();
      const file = event.dataTransfer.files[0];
      if (file) {
        this.uploadFile(file);
      }
    },
    
    async uploadFile(file) {
      // 文件验证
      if (!file.name.match(/\.(doc|docx)$/i)) {
        this.$emit('error', '仅支持 .doc 和 .docx 格式');
        return;
      }
      
      if (file.size > 50 * 1024 * 1024) {
        this.$emit('error', '文件大小不能超过50MB');
        return;
      }
      
      this.uploading = true;
      this.uploadProgress = 0;
      
      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('analysis_type', 'paper_check');
        
        const response = await apiClient.post('/api/v1/documents/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
          }
        });
        
        this.$emit('success', response.data.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || '上传失败');
      } finally {
        this.uploading = false;
        this.uploadProgress = 0;
      }
    }
  }
};
</script>
```

### 4. 任务状态监控

#### 任务状态轮询
```javascript
// services/taskService.js
class TaskService {
  constructor() {
    this.pollingTasks = new Map();
  }
  
  // 开始监控任务状态
  monitorTask(taskId, callback, interval = 2000) {
    if (this.pollingTasks.has(taskId)) {
      this.stopMonitoring(taskId);
    }
    
    const poll = async () => {
      try {
        const response = await apiClient.get(`/api/v1/tasks/${taskId}/status`);
        const task = response.data.data;
        
        callback({ success: true, task });
        
        // 如果任务已完成，停止轮询
        if (['completed', 'failed', 'cancelled'].includes(task.status)) {
          this.stopMonitoring(taskId);
        }
      } catch (error) {
        callback({ 
          success: false, 
          message: error.response?.data?.message || '获取状态失败' 
        });
      }
    };
    
    // 立即执行一次
    poll();
    
    // 设置定时轮询
    const intervalId = setInterval(poll, interval);
    this.pollingTasks.set(taskId, intervalId);
  }
  
  // 停止监控任务
  stopMonitoring(taskId) {
    const intervalId = this.pollingTasks.get(taskId);
    if (intervalId) {
      clearInterval(intervalId);
      this.pollingTasks.delete(taskId);
    }
  }
  
  // 停止所有监控
  stopAllMonitoring() {
    this.pollingTasks.forEach((intervalId) => {
      clearInterval(intervalId);
    });
    this.pollingTasks.clear();
  }
}

export const taskService = new TaskService();
```

#### Vue组合式API使用示例
```javascript
// composables/useTask.js
import { ref, onUnmounted } from 'vue';
import { taskService } from '@/services/taskService';

export function useTask() {
  const currentTask = ref(null);
  const isMonitoring = ref(false);
  
  const startMonitoring = (taskId) => {
    isMonitoring.value = true;
    
    taskService.monitorTask(taskId, (result) => {
      if (result.success) {
        currentTask.value = result.task;
        
        if (['completed', 'failed', 'cancelled'].includes(result.task.status)) {
          isMonitoring.value = false;
        }
      } else {
        console.error('任务状态获取失败:', result.message);
        isMonitoring.value = false;
      }
    });
  };
  
  const stopMonitoring = (taskId) => {
    taskService.stopMonitoring(taskId);
    isMonitoring.value = false;
  };
  
  // 组件卸载时清理
  onUnmounted(() => {
    taskService.stopAllMonitoring();
  });
  
  return {
    currentTask,
    isMonitoring,
    startMonitoring,
    stopMonitoring
  };
}
```

### 5. WebSocket实时通信

#### WebSocket服务
```javascript
// services/websocket.js
class WebSocketService {
  constructor() {
    this.ws = null;
    this.listeners = {};
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
  }
  
  connect(token) {
    const wsUrl = `ws://localhost:8000/api/v1/ws?token=${token}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('WebSocket连接已建立');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      };
      
      this.ws.onclose = () => {
        console.log('WebSocket连接已关闭');
        this.attemptReconnect(token);
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
      };
    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  }
  
  attemptReconnect(token) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(token);
      }, this.reconnectInterval);
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数');
    }
  }
  
  handleMessage(data) {
    const { type, payload } = data;
    if (this.listeners[type]) {
      this.listeners[type].forEach(callback => callback(payload));
    }
  }
  
  // 添加事件监听器
  on(type, callback) {
    if (!this.listeners[type]) {
      this.listeners[type] = [];
    }
    this.listeners[type].push(callback);
  }
  
  // 移除事件监听器
  off(type, callback) {
    if (this.listeners[type]) {
      this.listeners[type] = this.listeners[type].filter(cb => cb !== callback);
    }
  }
  
  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

export const websocketService = new WebSocketService();
```

#### 在Vue组件中使用WebSocket
```javascript
// components/TaskMonitor.vue
<script>
import { websocketService } from '@/services/websocket';
import { authManager } from '@/utils/auth';

export default {
  mounted() {
    // 建立WebSocket连接
    const token = authManager.getToken();
    if (token) {
      websocketService.connect(token);
      
      // 监听任务状态更新
      websocketService.on('task_status_update', this.onTaskUpdate);
      websocketService.on('document_analysis_complete', this.onAnalysisComplete);
    }
  },
  
  beforeUnmount() {
    // 清理监听器
    websocketService.off('task_status_update', this.onTaskUpdate);
    websocketService.off('document_analysis_complete', this.onAnalysisComplete);
  },
  
  methods: {
    onTaskUpdate(data) {
      console.log('任务状态更新:', data);
      // 更新UI状态
      this.updateTaskStatus(data);
    },
    
    onAnalysisComplete(data) {
      console.log('文档分析完成:', data);
      // 显示通知，更新文档列表等
      this.showNotification('文档分析完成！');
      this.refreshDocumentList();
    }
  }
};
</script>
```

## 🔍 API端点快速参考

### 认证相关 (5个端点)
```javascript
// 用户注册
POST /api/v1/auth/register
{ username, email, password }

// 用户登录
POST /api/v1/auth/login
FormData: { username, password }

// 获取用户信息
GET /api/v1/auth/me

// 获取用户详情
GET /api/v1/auth/profile

// 用户登出
POST /api/v1/auth/logout
```

### 文档管理 (13个端点)
```javascript
// 上传文档
POST /api/v1/documents/upload
FormData: { file, analysis_type?, options? }

// 获取文档列表
GET /api/v1/documents/?page=1&limit=20&status=?

// 获取文档详情
GET /api/v1/documents/{document_id}

// 删除文档
DELETE /api/v1/documents/{document_id}

// 启动文档分析
POST /api/v1/documents/{document_id}/analyze
{ analysis_type, options }

// 获取文档报告
GET /api/v1/documents/{document_id}/report?format=json

// 获取文档内容
GET /api/v1/documents/{document_id}/content

// 获取分析结果
GET /api/v1/documents/{document_id}/analysis

// 获取内容元素
GET /api/v1/documents/{document_id}/elements

// 获取图片列表
GET /api/v1/documents/{document_id}/images

// 获取问题列表  
GET /api/v1/documents/{document_id}/problems

// 根据任务获取文档
GET /api/v1/documents/task/{task_id}

// 导出报告
GET /api/v1/documents/{document_id}/export?format=pdf
```

### 任务管理 (10个端点)
```javascript
// 创建任务
POST /api/v1/tasks/
{ task_type, file_path?, options?, description? }

// 上传文件创建任务
POST /api/v1/tasks/upload
FormData: { file, task_type?, options?, priority?, description? }

// 获取任务详情
GET /api/v1/tasks/{task_id}

// 获取任务状态
GET /api/v1/tasks/{task_id}/status

// 取消任务
POST /api/v1/tasks/{task_id}/cancel

// 获取任务列表
GET /api/v1/tasks/?status=?&task_type=?&page=1&limit=20

// 更新任务状态
PUT /api/v1/tasks/{task_id}/status
{ status, message? }

// 删除任务
DELETE /api/v1/tasks/{task_id}

// 批量删除任务
POST /api/v1/tasks/batch-delete
{ task_ids: [] }

// 获取任务进度
GET /api/v1/tasks/{task_id}/progress
```

### 支付系统 (5个端点)
```javascript
// 创建订单
POST /api/v1/payments/create-order
{ plan_id, payment_method }

// 获取订单状态
GET /api/v1/payments/order/{order_id}

// 取消订单
POST /api/v1/payments/order/{order_id}/cancel

// 获取支付历史
GET /api/v1/payments/?skip=0&limit=20&status=?

// 获取套餐信息
GET /api/v1/payments/plans
```

## 🚨 常见问题解决

### 1. CORS跨域问题
```javascript
// 如果遇到跨域问题，检查以下配置：

// 1. 后端CORS配置 (已配置)
// 后端已设置允许所有来源，一般不会有跨域问题

// 2. 前端代理配置 (开发环境)
// vite.config.js
export default {
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
}

// 3. 生产环境部署
// 确保前后端部署在同一域名下或正确配置CORS
```

### 2. Token过期处理
```javascript
// 自动Token刷新 (如果后端支持)
const refreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    const response = await apiClient.post('/api/v1/auth/refresh', {
      refresh_token: refreshToken
    });
    
    const { access_token } = response.data.data;
    localStorage.setItem('access_token', access_token);
    return access_token;
  } catch (error) {
    // 刷新失败，跳转登录
    authManager.logout();
    throw error;
  }
};

// 在响应拦截器中使用
apiClient.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      try {
        const newToken = await refreshToken();
        // 重试原请求
        error.config.headers.Authorization = `Bearer ${newToken}`;
        return apiClient.request(error.config);
      } catch (refreshError) {
        // 刷新失败，直接登出
        authManager.logout();
      }
    }
    return Promise.reject(error);
  }
);
```

### 3. 文件上传进度显示
```javascript
// 上传进度监控
const uploadWithProgress = async (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await apiClient.post('/api/v1/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        onProgress(progress);
      }
    });
    
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### 4. 错误信息显示
```javascript
// 统一错误处理
const handleApiError = (error) => {
  if (error.response) {
    const { status, data } = error.response;
    
    // 根据状态码返回用户友好的错误信息
    const errorMessages = {
      400: '请求参数错误，请检查输入',
      401: '登录已过期，请重新登录', 
      403: '权限不足，无法执行此操作',
      404: '请求的资源不存在',
      413: '文件过大，请上传小于50MB的文件',
      415: '不支持的文件格式，请上传Word文档',
      422: '数据验证失败，请检查输入格式',
      500: '服务器内部错误，请稍后重试',
      503: '服务暂时不可用，请稍后重试'
    };
    
    return errorMessages[status] || data?.message || '请求失败';
  } else if (error.request) {
    return '网络连接失败，请检查网络连接';
  } else {
    return '请求处理失败，请重试';
  }
};
```

### 5. 实时状态更新最佳实践
```javascript
// 组合使用轮询和WebSocket
class StatusManager {
  constructor() {
    this.useWebSocket = true;
    this.fallbackToPolling = true;
  }
  
  monitorTask(taskId, callback) {
    if (this.useWebSocket && websocketService.ws?.readyState === WebSocket.OPEN) {
      // 优先使用WebSocket
      websocketService.on('task_status_update', (data) => {
        if (data.task_id === taskId) {
          callback(data);
        }
      });
    } else if (this.fallbackToPolling) {
      // 回退到轮询
      taskService.monitorTask(taskId, callback);
    }
  }
}
```

## 📊 性能优化建议

### 1. 请求缓存
```javascript
// 简单的内存缓存
class ApiCache {
  constructor(ttl = 300000) { // 5分钟TTL
    this.cache = new Map();
    this.ttl = ttl;
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  set(key, data) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.ttl
    });
  }
  
  clear() {
    this.cache.clear();
  }
}

const apiCache = new ApiCache();

// 使用缓存的API调用
const getCachedData = async (endpoint) => {
  const cached = apiCache.get(endpoint);
  if (cached) return cached;
  
  const response = await apiClient.get(endpoint);
  apiCache.set(endpoint, response.data);
  return response.data;
};
```

### 2. 请求去重
```javascript
// 防止重复请求
class RequestDeduplicator {
  constructor() {
    this.pendingRequests = new Map();
  }
  
  async request(key, requestFn) {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key);
    }
    
    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });
    
    this.pendingRequests.set(key, promise);
    return promise;
  }
}

const deduplicator = new RequestDeduplicator();

// 使用去重的API调用
const getDocument = (documentId) => {
  return deduplicator.request(
    `document_${documentId}`,
    () => apiClient.get(`/api/v1/documents/${documentId}`)
  );
};
```

## 📋 开发检查清单

### ✅ 前端集成必做事项

#### 基础设置
- [ ] 配置API客户端 (Axios/Fetch)
- [ ] 设置请求/响应拦截器
- [ ] 实现Token管理
- [ ] 配置CORS (如果需要)

#### 认证功能
- [ ] 实现用户注册界面
- [ ] 实现用户登录界面
- [ ] 实现Token自动添加
- [ ] 实现Token过期处理
- [ ] 实现登出功能

#### 文档功能
- [ ] 实现文件上传组件
- [ ] 实现上传进度显示
- [ ] 实现文档列表展示
- [ ] 实现文档详情查看
- [ ] 实现报告查看和导出

#### 任务功能
- [ ] 实现任务状态监控
- [ ] 实现任务列表展示
- [ ] 实现任务取消功能
- [ ] 实现进度条显示

#### 支付功能
- [ ] 实现套餐选择界面
- [ ] 实现订单创建和支付
- [ ] 实现支付状态监控
- [ ] 实现支付历史查看

#### 实时通信
- [ ] 实现WebSocket连接
- [ ] 实现实时状态更新
- [ ] 实现断线重连机制

#### 错误处理
- [ ] 实现统一错误处理
- [ ] 实现用户友好的错误提示
- [ ] 实现网络错误重试
- [ ] 实现Loading状态管理

## 🎯 总结

本指南提供了完整的前端API集成方案，包括：

- ✅ **42个API端点**的完整使用说明
- ✅ **认证机制**的完整实现
- ✅ **文件上传**的最佳实践
- ✅ **实时通信**的集成方案
- ✅ **错误处理**的统一机制
- ✅ **性能优化**的实用建议

**推荐开发流程**:
1. 使用 **Swagger UI** 熟悉和测试API
2. 按照本指南实现基础架构
3. 逐步集成各功能模块
4. 完善错误处理和用户体验

**重要提醒**:
- 所有示例代码都经过验证，可以直接使用
- 遇到问题时优先查看Swagger UI和浏览器控制台
- Token认证是核心，务必正确实现
- 文件上传需要特别注意Content-Type设置

开始愉快的前端开发吧！🚀 