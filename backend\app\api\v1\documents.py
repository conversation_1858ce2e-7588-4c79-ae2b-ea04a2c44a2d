"""
Word文档分析服务 - 文档处理API
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends, UploadFile, File, Response, Form
from pydantic import BaseModel
import asyncio
import json
import os
from pathlib import Path
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.document import Document, ContentElement, Image
from app.models.paper_check import PaperCheckResult, Problem
from app.database import crud
from app.database.session import get_db
from app.core.logging import logger
from app.core.response import success_response, error_response
from app.core.config import settings
from app.security import get_current_user_id

router = APIRouter()


@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    analysis_type: str = Form("paper_check"),
    options: Optional[str] = Form(None),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    上传文档并创建分析任务
    
    - **file**: 上传的文档文件
    - **analysis_type**: 分析类型 (paper_check, format_check, content_analysis)
    - **options**: 分析选项 (JSON字符串格式)
    """
    try:
        # 记录上传请求信息
        logger.info(f"用户 {user_id} 上传文档: {file.filename} ({file.size} bytes), 分析类型: {analysis_type}")
        
        # 验证文件格式
        if not file.filename.lower().endswith(('.docx', '.doc')):
            raise HTTPException(status_code=415, detail="不支持的文件格式，请上传 .docx 或 .doc 文件")

        # 验证文件大小（50MB限制）
        MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
        if file.size > MAX_FILE_SIZE:
            raise HTTPException(status_code=413, detail="文件过大，请上传小于 50MB 的文件")

        # 1. 验证用户余额
        user = await crud.get_user_by_id(session, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        if user.check_balance <= 0:
            raise HTTPException(status_code=402, detail="检测次数不足，请充值后再试")

        # 2. 保存文件（使用统一配置的绝对路径）
        import uuid
        task_id = f"task_{uuid.uuid4().hex}"
        
        # 使用settings中统一管理的路径
        user_dir = settings.files.upload_path / user_id
        
        # 创建用户目录
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件保存路径（绝对路径）
        file_path = user_dir / f"{task_id}_{file.filename}"
        
        # 保存文件
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)
        
        # 扣除用户余额
        await crud.update_user_check_balance(session, user_id=user_id, checks_to_add=-1)
        
        # 解析分析选项
        task_options = {}
        if options:
            try:
                task_options = json.loads(options)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="选项格式错误，请使用有效的JSON格式")
        
        # 创建任务到数据库
        from app.models.task import TaskCreate, TaskStatus, TaskType
        from app.tasks.manager import task_manager
        
        # 映射分析类型
        type_mapping = {
            'paper_check': TaskType.PAPER_CHECK,
            'format_check': TaskType.FORMAT_CHECK,
            'content_analysis': TaskType.CONTENT_ANALYSIS
        }
        task_type = type_mapping.get(analysis_type, TaskType.PAPER_CHECK)
        
        # 验证分析类型映射
        logger.debug(f"分析类型映射: {analysis_type} -> {task_type}")
        
        # 3. 创建任务数据（使用绝对路径字符串）
        task_data = TaskCreate(
            task_id=task_id,
            user_id=user_id,
            task_type=task_type,
            file_path=str(file_path),  # 使用绝对路径
            filename=file.filename,
            file_size=len(content),
            analysis_options=task_options,
            status=TaskStatus.PENDING
        )
        
        # 记录任务创建
        logger.debug(f"创建任务: {task_id}, 类型: {task_type}, 文件路径: {file_path}")
        
        # 保存到数据库
        await crud.create_task(session, task_data)
        
        # 启动后台任务处理
        asyncio.create_task(task_manager.process_task(task_id))
        
        logger.info(f"文件上传成功并创建任务: {file.filename}, 任务ID: {task_id}, 绝对路径: {file_path}")
        
        return success_response(
            data={
                "task_id": task_id,
                "filename": file.filename,
                "file_size": len(content),
                "status": "pending_analysis",
                "remaining_balance": user.check_balance - 1
            },
            message="分析任务已创建，正在等待处理"
        )
        
    except HTTPException:
        # 如果是HTTP异常，直接重新抛出
        raise
    except Exception as e:
        # 如果发生其他异常（如文件写入失败），需要回滚余额
        await crud.update_user_check_balance(session, user_id=user_id, checks_to_add=1)
        logger.error(f"任务创建失败，已回滚用户 {user_id} 的余额。错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建分析任务时发生内部错误，您的余额未扣除，请重试。")


class DocumentSummary(BaseModel):
    """文档摘要信息"""
    document_id: str
    task_id: str
    filename: str
    file_size: Optional[int] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    created_at: str
    status: str


class DocumentContent(BaseModel):
    """文档内容信息"""
    document: Document
    elements: List[ContentElement]
    images: List[Image]
    total_elements: int
    total_images: int


class DocumentAnalysisResult(BaseModel):
    """文档分析结果"""
    document: Document
    check_result: Optional[PaperCheckResult] = None
    problems: List[Problem] = []
    summary: dict


class AnalysisRequest(BaseModel):
    """分析请求"""
    analysis_type: str = "paper_check"  # 分析类型：paper_check, format_check, content_analysis
    options: Optional[dict] = {}  # 分析选项


@router.get("/{document_id}")
async def get_document_detail(
    document_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档详细信息
    
    - **document_id**: 文档ID
    """
    try:
        document = await crud.get_document(session, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档未找到")
        
        logger.info(f"获取文档详细信息: {document_id}")
        
        return success_response(
            data=document.model_dump() if hasattr(document, 'model_dump') else document.__dict__,
            message="获取文档信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档详细信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取文档信息失败")


@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    删除文档
    
    - **document_id**: 文档ID
    """
    try:
        # 🔥 关键修复：智能识别ID类型并处理
        task = None
        document = None
        
        # 1. 如果是reanalyze_或task_格式，优先查找任务
        if document_id.startswith('reanalyze_') or document_id.startswith('task_'):
            task = await crud.get_task(session, document_id)
            if not task:
                raise HTTPException(status_code=404, detail="任务或文档未找到")
            
            # 验证任务所有权
            if hasattr(task, 'user_id') and task.user_id != user_id:
                raise HTTPException(status_code=403, detail="无权限删除此文档")
            
            # 尝试获取关联的文档记录（可能不存在，如果任务未完成）
            document = await crud.get_document_by_task_id(session, document_id)
            
            logger.info(f"删除任务格式文档: {document_id}, 任务状态: {task.status}, 有文档记录: {document is not None}")
            
        else:
            # 2. 普通document_id，直接查找文档
            document = await crud.get_document(session, document_id)
            if not document:
                raise HTTPException(status_code=404, detail="文档未找到")
            
            # 获取关联的任务信息
            if document.task_id:
                task = await crud.get_task(session, document.task_id)
                
                # 验证任务所有权
                if task and hasattr(task, 'user_id') and task.user_id != user_id:
                    raise HTTPException(status_code=403, detail="无权限删除此文档")
        
        # 🔥 关键修复：检查文件引用计数
        file_deleted = False
        file_preserved_reason = None
        
        if task and task.file_path:
            file_path = task.file_path
            
            if os.path.exists(file_path):
                # 检查是否还有其他任务引用同一个文件
                try:
                    from sqlalchemy import text
                    result = await session.execute(
                        text("SELECT COUNT(*) as count FROM tasks WHERE file_path = :file_path AND task_id != :task_id"),
                        {"file_path": file_path, "task_id": task.task_id}
                    )
                    other_references = result.fetchone().count
                    
                    if other_references == 0:
                        # 没有其他引用，可以安全删除文件
                        try:
                            os.remove(file_path)
                            file_deleted = True
                            logger.info(f"删除文件: {file_path}")
                        except Exception as e:
                            logger.warning(f"删除文件失败: {file_path}, 错误: {str(e)}")
                            file_preserved_reason = f"文件删除失败: {str(e)}"
                    else:
                        file_preserved_reason = f"文件还有 {other_references} 个其他引用"
                        logger.info(f"文件 {file_path} 还有 {other_references} 个其他引用，不删除物理文件")
                        
                except Exception as e:
                    logger.error(f"检查文件引用失败: {str(e)}，为安全起见不删除文件")
                    file_preserved_reason = f"引用检查失败: {str(e)}"
            else:
                file_preserved_reason = "文件不存在"
        
        # 删除数据库记录
        try:
            records_deleted = {
                "task_deleted": False,
                "document_deleted": False
            }
            
            # 删除任务记录（如果存在）
            if task:
                task_deleted = await crud.delete_task(session, task.task_id)
                records_deleted["task_deleted"] = task_deleted
                if task_deleted:
                    logger.info(f"删除任务记录: {task.task_id}")
            
            # 删除文档记录（如果存在且不是通过任务级联删除）
            if document and not records_deleted["task_deleted"]:
                doc_deleted = await crud.delete_document(session, document.document_id)
                records_deleted["document_deleted"] = doc_deleted
                if doc_deleted:
                    logger.info(f"删除文档记录: {document.document_id}")
            elif records_deleted["task_deleted"]:
                # 任务删除时会级联删除文档
                records_deleted["document_deleted"] = True
                logger.info(f"文档记录通过级联删除: {document_id}")
            
        except Exception as e:
            logger.error(f"删除数据库记录失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"删除数据库记录失败: {str(e)}")
        
        logger.info(f"文档删除成功: {document_id}")
        
        return success_response(
            data={
                "document_id": document_id, 
                "deleted": True,
                "file_deleted": file_deleted,
                "file_status": "已删除" if file_deleted else f"已保留 ({file_preserved_reason})",
                "task_deleted": records_deleted["task_deleted"],
                "document_deleted": records_deleted["document_deleted"],
                "records_info": f"任务记录: {'已删除' if records_deleted['task_deleted'] else '无/未删除'}, 文档记录: {'已删除' if records_deleted['document_deleted'] else '无/未删除'}"
            },
            message="文档删除成功"
        )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档删除失败: {str(e)}")
        raise HTTPException(status_code=500, detail="文档删除失败")


@router.post("/{document_id}/analyze")
async def start_document_analysis(
    document_id: str, 
    request: AnalysisRequest,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    启动文档分析
    
    - **document_id**: 文档ID  
    - **request**: 分析请求参数
    """
    try:
        # 检查文档是否存在
        document = await crud.get_document(session, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档未找到")
        
        # 创建分析任务
        import uuid
        task_id = f"analysis_{uuid.uuid4().hex}"
        
        # 这里应该启动实际的文档分析任务
        # await start_analysis_task(document_id, request.analysis_type, request.options)
        
        logger.info(f"启动文档分析: {document_id}, 类型: {request.analysis_type}")
        
        # 模拟分析任务创建成功
        return success_response(
            data={
                "document_id": document_id,
                "task_id": task_id,
                "analysis_type": request.analysis_type,
                "status": "started",
                "estimated_time": "5-10分钟"
            },
            message="文档分析任务已启动"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动文档分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail="启动文档分析失败")


@router.post("/{document_id}/reanalyze")
async def reanalyze_document(
    document_id: str,
    request: AnalysisRequest,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    重新分析文档
    
    - **document_id**: 文档ID或任务ID
    - **request**: 分析请求参数
    
    注意：重新分析将消耗一次检测次数
    """
    try:
        # 1. 智能检查ID类型并获取任务信息
        task = None
        document = None
        
        # 如果传入的是task_id格式，直接获取任务
        if document_id.startswith('task_') or document_id.startswith('reanalyze_'):
            task = await crud.get_task(session, document_id)
            if task:
                # 尝试通过任务获取关联的文档
                document = await crud.get_document_by_task_id(session, document_id)
        else:
            # 传入的可能是document_id，先尝试获取文档
            document = await crud.get_document(session, document_id)
            if document and document.task_id:
                # 通过文档获取关联的任务
                task = await crud.get_task(session, document.task_id)
        
        # 如果都没找到，返回404
        if not task:
            raise HTTPException(status_code=404, detail="文档或任务未找到")
        
        # 2. 检查用户余额
        user = await crud.get_user_by_id(session, user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        if user.check_balance <= 0:
            raise HTTPException(status_code=402, detail="检测次数不足，请充值后再试")
        
        # 3. 验证任务所有权
        if task.user_id != user_id:
            raise HTTPException(status_code=403, detail="无权访问此任务")
        
        # 4. 验证原始文件是否存在
        original_file_path = Path(task.file_path)
        if not original_file_path.exists():
            raise HTTPException(status_code=404, detail="原始文档文件不存在，无法重新分析")
        
        # 5. 扣除用户余额
        await crud.update_user_check_balance(session, user_id=user_id, checks_to_add=-1)
        
        # 6. 创建重新分析任务
        import uuid
        from app.models.task import TaskCreate, TaskStatus, TaskType
        from app.tasks.manager import task_manager
        
        # 生成新的任务ID
        new_task_id = f"reanalyze_{uuid.uuid4().hex}"
        
        # 映射分析类型
        type_mapping = {
            'paper_check': TaskType.PAPER_CHECK,
            'format_check': TaskType.FORMAT_CHECK,
            'content_analysis': TaskType.CONTENT_ANALYSIS
        }
        task_type = type_mapping.get(request.analysis_type, TaskType.PAPER_CHECK)
        
        # 创建新任务数据
        task_data = TaskCreate(
            task_id=new_task_id,
            user_id=user_id,
            task_type=task_type,
            file_path=str(original_file_path),
            filename=f"重新分析_{task.filename}",
            file_size=original_file_path.stat().st_size,
            analysis_options=request.options or {},
            status=TaskStatus.PENDING
        )
        
        # 保存到数据库
        await crud.create_task(session, task_data)
        
        # 启动后台任务处理
        asyncio.create_task(task_manager.process_task(new_task_id))
        
        logger.info(f"重新分析任务已创建: {new_task_id}, 原始任务: {task.task_id}, 用户: {user_id}")
        
        return success_response(
            data={
                "document_id": document_id,
                "task_id": new_task_id,
                "original_task_id": task.task_id,
                "analysis_type": request.analysis_type,
                "status": "pending",
                "estimated_time": "5-10分钟",
                "remaining_balance": user.check_balance - 1,
                "message": "重新分析任务已创建，将消耗1次检测次数"
            },
            message="重新分析任务已启动"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        # 如果发生其他异常，需要回滚余额
        try:
            await crud.update_user_check_balance(session, user_id=user_id, checks_to_add=1)
            logger.error(f"重新分析任务创建失败，已回滚用户 {user_id} 的余额。错误: {str(e)}")
        except Exception as rollback_error:
            logger.error(f"回滚用户余额失败: {str(rollback_error)}")
        
        raise HTTPException(status_code=500, detail=f"重新分析任务创建失败: {str(e)}")


@router.get("/{document_id}/report")
async def get_document_report(
    document_id: str,
    format: str = Query("json", description="报告格式: json, html, text"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档检测报告
    
    - **document_id**: 文档ID
    - **format**: 报告格式 (json, html, text)
    """
    try:
        # 检查文档是否存在
        document = await crud.get_document(session, document_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档未找到")
        
        # 获取检测结果
        check_result = await crud.get_paper_check_result_by_document(session, document_id)
        if not check_result:
            # 返回空报告
            empty_report = {
                "document_id": document_id,
                "filename": getattr(document, 'title', 'unknown'),
                "check_status": "未检测",
                "compliance_score": None,
                "problems": [],
                "summary": "该文档尚未进行检测，请先启动分析任务。"
            }
            
            if format.lower() == "html":
                html_content = _generate_html_report(empty_report)
                return Response(content=html_content, media_type="text/html")
            elif format.lower() == "text":
                text_content = _generate_text_report(empty_report)
                return Response(content=text_content, media_type="text/plain")
            else:
                return success_response(data=empty_report, message="获取报告成功")
        
        # 获取问题列表
        problems = await crud.get_problems_by_result(session, check_result.result_id)
        
        # 生成报告数据
        report_data = {
            "document_id": document_id,
            "filename": getattr(document, 'title', 'unknown'),
            "check_date": check_result.checked_at.isoformat() if check_result.checked_at else None,
            "standard_name": check_result.standard_name,
            "compliance_score": check_result.overall_score,
            "compliance_status": check_result.compliance_status,
            "summary": check_result.summary,
            "total_problems": len(problems),
            "problems": [
                {
                    "id": p.problem_id,
                    "type": p.problem_type,
                    "severity": p.severity,
                    "description": p.description,
                    "location": p.location,
                    "suggestion": p.suggestion
                } for p in problems
            ]
        }
        
        logger.info(f"生成文档检测报告: {document_id}, 格式: {format}")
        
        # 根据格式返回报告
        if format.lower() == "html":
            html_content = _generate_html_report(report_data)
            return Response(content=html_content, media_type="text/html")
        elif format.lower() == "text":
            text_content = _generate_text_report(report_data)
            return Response(content=text_content, media_type="text/plain")
        else:
            return success_response(data=report_data, message="获取报告成功")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取文档报告失败")


def _generate_html_report(report_data: dict) -> str:
    """生成HTML格式报告"""
    problems_html = ""
    if report_data.get("problems"):
        for problem in report_data["problems"]:
            severity_class = {
                "critical": "danger",
                "warning": "warning", 
                "info": "info"
            }.get(problem.get("severity", "info"), "info")
            
            problems_html += f"""
            <div class="alert alert-{severity_class}">
                <h5>{problem.get('type', '未知问题')}</h5>
                <p><strong>描述:</strong> {problem.get('description', '')}</p>
                <p><strong>位置:</strong> {problem.get('location', '未指定')}</p>
                <p><strong>建议:</strong> {problem.get('suggestion', '无建议')}</p>
            </div>
            """
    
    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>文档检测报告 - {report_data.get('filename', '未知文档')}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; }}
            .score {{ font-size: 24px; font-weight: bold; color: #28a745; }}
            .alert {{ padding: 15px; margin: 10px 0; border-radius: 5px; }}
            .alert-danger {{ background-color: #f8d7da; border: 1px solid #f5c6cb; }}
            .alert-warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; }}
            .alert-info {{ background-color: #d1ecf1; border: 1px solid #bee5eb; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>文档检测报告</h1>
            <p><strong>文档名称:</strong> {report_data.get('filename', '未知文档')}</p>
            <p><strong>检测时间:</strong> {report_data.get('check_date', '未知')}</p>
            <p><strong>检测标准:</strong> {report_data.get('standard_name', '默认标准')}</p>
            <p><strong>合规状态:</strong> {report_data.get('compliance_status', '未知')}</p>
            <p class="score">合规得分: {report_data.get('compliance_score', 'N/A')}</p>
        </div>
        
        <h2>问题详情 ({report_data.get('total_problems', 0)}个问题)</h2>
        {problems_html if problems_html else '<p>未发现问题</p>'}
        
        <h2>总结</h2>
        <p>{report_data.get('summary', '无总结信息')}</p>
    </body>
    </html>
    """
    
    return html_template


def _generate_text_report(report_data: dict) -> str:
    """生成文本格式报告"""
    report_lines = [
        "=" * 50,
        f"文档检测报告",
        "=" * 50,
        f"文档名称: {report_data.get('filename', '未知文档')}",
        f"检测时间: {report_data.get('check_date', '未知')}",
        f"检测标准: {report_data.get('standard_name', '默认标准')}",
        f"合规状态: {report_data.get('compliance_status', '未知')}",
        f"合规得分: {report_data.get('compliance_score', 'N/A')}",
        "",
        f"问题详情 ({report_data.get('total_problems', 0)}个问题)",
        "-" * 30,
    ]
    
    if report_data.get("problems"):
        for i, problem in enumerate(report_data["problems"], 1):
            report_lines.extend([
                f"{i}. {problem.get('type', '未知问题')} [{problem.get('severity', 'info').upper()}]",
                f"   描述: {problem.get('description', '')}",
                f"   位置: {problem.get('location', '未指定')}",
                f"   建议: {problem.get('suggestion', '无建议')}",
                ""
            ])
    else:
        report_lines.append("未发现问题")
    
    report_lines.extend([
        "",
        "总结",
        "-" * 10,
        report_data.get('summary', '无总结信息'),
        "",
        "=" * 50
    ])
    
    return "\n".join(report_lines)


@router.get("/{document_id}", response_model=Document)
async def get_document(
    document_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档基本信息
    
    - **document_id**: 文档ID
    """
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    logger.info(f"获取文档信息: {document_id}")
    return document


@router.get("/{document_id}/content", response_model=DocumentContent)
async def get_document_content(
    document_id: str,
    include_text: bool = Query(True, description="是否包含文本内容"),
    include_images: bool = Query(True, description="是否包含图片信息"),
    element_type: Optional[str] = Query(None, description="内容元素类型筛选"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档详细内容
    
    - **document_id**: 文档ID
    - **include_text**: 是否包含文本内容
    - **include_images**: 是否包含图片信息
    - **element_type**: 内容元素类型筛选 (paragraph, heading, table, list)
    """
    # 获取文档基本信息
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取内容元素
    elements = []
    if include_text:
        elements = await crud.get_content_elements_by_document(
            session,
            document_id, 
            element_type=element_type
        )
    
    # 获取图片信息
    images = []
    if include_images:
        images = await crud.get_images_by_document(session, document_id)
    
    logger.info(f"获取文档内容: {document_id}", 
               elements_count=len(elements), 
               images_count=len(images))
    
    return DocumentContent(
        document=document,
        elements=elements,
        images=images,
        total_elements=len(elements),
        total_images=len(images)
    )


@router.get("/{document_id}/analysis", response_model=DocumentAnalysisResult)
async def get_document_analysis(
    document_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档分析结果
    
    - **document_id**: 文档ID
    """
    # 获取文档基本信息
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取检测结果
    check_result = await crud.get_paper_check_result_by_document(session, document_id)
    
    # 获取问题列表
    problems = []
    if check_result:
        problems = await crud.get_problems_by_result(session, check_result.result_id)
    
    # 生成分析摘要
    summary = {
        "total_problems": len(problems),
        "critical_problems": len([p for p in problems if p.severity == "critical"]),
        "warning_problems": len([p for p in problems if p.severity == "warning"]),
        "info_problems": len([p for p in problems if p.severity == "info"]),
        "compliance_score": check_result.overall_score if check_result else None,
        "overall_status": check_result.overall_status if check_result else "未检测"
    }
    
    logger.info(f"获取文档分析结果: {document_id}", 
               problems_count=len(problems),
               compliance_score=summary["compliance_score"])
    
    return DocumentAnalysisResult(
        document=document,
        check_result=check_result,
        problems=problems,
        summary=summary
    )


@router.get("/{document_id}/elements", response_model=List[ContentElement])
async def get_document_elements(
    document_id: str,
    element_type: Optional[str] = Query(None, description="元素类型筛选"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(50, ge=1, le=200, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档内容元素列表
    
    - **document_id**: 文档ID
    - **element_type**: 元素类型筛选
    - **page**: 页码
    - **limit**: 每页数量
    """
    # 验证文档是否存在
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取内容元素
    skip = (page - 1) * limit
    elements = await crud.get_content_elements_by_document(
        session,
        document_id,
        element_type=element_type,
        skip=skip,
        limit=limit
    )
    
    logger.info(f"获取文档元素列表: {document_id}", 
               element_type=element_type,
               count=len(elements))
    
    return elements


@router.get("/{document_id}/images", response_model=List[Image])
async def get_document_images(
    document_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档中的图片列表
    
    - **document_id**: 文档ID
    """
    # 验证文档是否存在
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取图片列表
    images = await crud.get_images_by_document(session, document_id)
    
    logger.info(f"获取文档图片列表: {document_id}", count=len(images))
    
    return images


@router.get("/{document_id}/problems", response_model=List[Problem])
async def get_document_problems(
    document_id: str,
    severity: Optional[str] = Query(None, description="问题严重程度筛选"),
    category: Optional[str] = Query(None, description="问题类别筛选"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(50, ge=1, le=200, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档检测问题列表
    
    - **document_id**: 文档ID
    - **severity**: 问题严重程度筛选 (critical, warning, info)
    - **category**: 问题类别筛选
    - **page**: 页码
    - **limit**: 每页数量
    """
    # 验证文档是否存在
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取检测结果
    check_result = await crud.get_paper_check_result_by_document(session, document_id)
    if not check_result:
        return []
    
    # 获取问题列表
    skip = (page - 1) * limit
    problems = await crud.get_problems_by_result(
        session,
        check_result.result_id,
        severity=severity,
        category=category,
        skip=skip,
        limit=limit
    )
    
    logger.info(f"获取文档问题列表: {document_id}", 
               severity=severity,
               category=category,
               count=len(problems))
    
    return problems


@router.get("/task/{task_id}", response_model=List[DocumentSummary])
async def get_documents_by_task(
    task_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    根据任务ID获取相关文档列表
    
    - **task_id**: 任务ID
    """
    # 获取任务相关的文档
    documents = await crud.get_documents_by_task(session, task_id)
    
    # 转换为摘要格式
    summaries = []
    for doc in documents:
        summary = DocumentSummary(
            document_id=doc.document_id,
            task_id=doc.task_id,
            filename=doc.filename,
            file_size=doc.file_size,
            page_count=doc.page_count,
            word_count=doc.word_count,
            created_at=doc.created_at.isoformat() if doc.created_at else "",
            status="已分析" if doc.analysis_completed else "分析中"
        )
        summaries.append(summary)
    
    logger.info(f"获取任务文档列表: {task_id}", count=len(summaries))
    
    return summaries


@router.get("/{document_id}/export")
async def export_document_report(
    document_id: str,
    format: str = Query("json", description="导出格式 (json, pdf, html)"),
    include_problems: bool = Query(True, description="是否包含问题详情"),
    include_suggestions: bool = Query(True, description="是否包含修改建议"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    导出文档分析报告
    
    - **document_id**: 文档ID
    - **format**: 导出格式 (json, pdf, html)
    - **include_problems**: 是否包含问题详情
    - **include_suggestions**: 是否包含修改建议
    """
    # 验证文档是否存在
    document = await crud.get_document(session, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 获取完整的分析结果
    check_result = await crud.get_paper_check_result_by_document(session, document_id)
    problems = []
    if check_result and include_problems:
        problems = await crud.get_problems_by_result(session, check_result.result_id)
    
    # 构建报告数据
    report_data = {
        "document": {
            "id": document.document_id,
            "filename": document.filename,
            "analysis_date": document.created_at.isoformat() if document.created_at else None
        },
        "analysis_summary": {
            "overall_status": check_result.overall_status if check_result else "未检测",
            "compliance_score": check_result.overall_score if check_result else None,
            "total_problems": len(problems)
        }
    }
    
    if include_problems:
        report_data["problems"] = [
            {
                "type": p.problem_type,
                "severity": p.severity,
                "category": p.category,
                "description": p.description,
                "location": p.location,
                "suggestion": p.suggestion if include_suggestions else None
            }
            for p in problems
        ]
    
    logger.info(f"导出文档报告: {document_id}", format=format)
    
    if format.lower() == "json":
        return report_data
    elif format.lower() == "html":
        # 生成HTML报告
        html_content = _generate_html_report(report_data)
        return Response(
            content=html_content,
            media_type="text/html",
            headers={"Content-Disposition": f"attachment; filename=report_{document_id}.html"}
        )
    elif format.lower() == "pdf":
        # 生成PDF报告
        pdf_content = await _generate_pdf_report(report_data)
        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename=report_{document_id}.pdf"}
        )
    else:
        raise HTTPException(status_code=400, detail=f"不支持的导出格式: {format}，支持的格式: json, html, pdf")


@router.get("/", response_model=List[DocumentSummary])
async def get_documents(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取文档列表（基于任务数据）
    
    - **page**: 页码
    - **limit**: 每页数量
    - **status**: 状态筛选
    """
    skip = (page - 1) * limit
    
    try:
        # 直接从TASKS表获取用户的任务列表
        from app.models.task import TaskStatus
        
        # 构建状态筛选
        task_status = None
        if status:
            try:
                task_status = TaskStatus(status)
            except ValueError:
                logger.warning(f"无效的状态筛选: {status}")
        
        # 获取用户的任务列表
        tasks = await crud.get_user_tasks(session, user_id=user_id, skip=skip, limit=limit, status=task_status)
        
        # 转换为DocumentSummary格式
        document_summaries = []
        for task in tasks:
            # 尝试获取关联的文档信息（如果存在）
            document = await crud.get_document_by_task_id(session, task.task_id)
            
            summary = DocumentSummary(
                document_id=task.task_id,  # 使用task_id作为document_id
                task_id=task.task_id,
                filename=task.filename,  # 直接使用任务中的filename
                file_size=task.file_size,  # 直接使用任务中的file_size
                page_count=document.pages if document else None,  # 如果有文档记录，使用其页数
                word_count=document.words if document else None,  # 如果有文档记录，使用其字数
                created_at=task.created_at.isoformat() if task.created_at else "",  # 使用任务创建时间
                status=task.status.value if hasattr(task.status, 'value') else str(task.status)
            )
            document_summaries.append(summary)
        
        logger.info(f"获取文档列表成功，共 {len(document_summaries)} 个文档", page=page, limit=limit, status=status)
        
        return success_response(
            data={
                "documents": document_summaries,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": len(document_summaries)
                }
            },
            message="获取文档列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        # 返回标准错误响应
        return error_response(message=f"获取文档列表失败: {str(e)}", code=500)


# =================== 报告生成辅助函数 ===================

async def _generate_pdf_report(report_data: dict) -> bytes:
    """
    生成PDF格式的分析报告
    
    Args:
        report_data: 报告数据字典
        
    Returns:
        PDF二进制数据
    """
    try:
        # 首先生成HTML报告
        html_content = _generate_html_report(report_data)
        
        # 尝试使用weasyprint将HTML转换为PDF
        try:
            import weasyprint
            
            # 生成PDF
            pdf_document = weasyprint.HTML(string=html_content)
            pdf_bytes = pdf_document.write_pdf()
            
            return pdf_bytes
            
        except ImportError:
            # 如果weasyprint不可用，使用简单的文本格式生成PDF
            logger.warning("weasyprint未安装，使用备用PDF生成方案")
            
            try:
                from reportlab.pdfgen import canvas
                from reportlab.lib.pagesizes import A4
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
                import io
                
                # 创建PDF缓冲区
                buffer = io.BytesIO()
                
                # 创建PDF文档
                p = canvas.Canvas(buffer, pagesize=A4)
                width, height = A4
                
                # 设置字体（如果有中文字体）
                try:
                    # 尝试使用系统中文字体
                    pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                    font_name = 'SimSun'
                except:
                    font_name = 'Helvetica'
                
                # 添加标题
                p.setFont(font_name, 16)
                p.drawString(50, height - 50, f"文档分析报告")
                
                # 添加文档信息
                document_info = report_data.get("document", {})
                y_position = height - 100
                
                p.setFont(font_name, 12)
                p.drawString(50, y_position, f"文档名称: {document_info.get('filename', '未知')}")
                y_position -= 30
                
                p.drawString(50, y_position, f"分析时间: {document_info.get('analysis_date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}")
                y_position -= 50
                
                # 添加分析摘要
                analysis_summary = report_data.get("analysis_summary", {})
                p.setFont(font_name, 14)
                p.drawString(50, y_position, "分析摘要:")
                y_position -= 30
                
                p.setFont(font_name, 10)
                p.drawString(70, y_position, f"发现问题数: {analysis_summary.get('total_problems', 0)}")
                y_position -= 20
                p.drawString(70, y_position, f"合规分数: {analysis_summary.get('compliance_score', 'N/A')}")
                y_position -= 20
                p.drawString(70, y_position, f"整体状态: {analysis_summary.get('overall_status', '未知')}")
                y_position -= 50
                
                # 添加问题列表
                problems = report_data.get("problems", [])
                if problems:
                    p.setFont(font_name, 14)
                    p.drawString(50, y_position, f"检测问题 ({len(problems)}):")
                    y_position -= 30
                    
                    p.setFont(font_name, 10)
                    for i, problem in enumerate(problems[:10]):  # 限制显示前10个问题
                        if y_position < 100:  # 如果页面空间不足，创建新页面
                            p.showPage()
                            y_position = height - 50
                        
                        p.drawString(70, y_position, f"{i+1}. {problem.get('description', '未知问题')[:50]}...")
                        y_position -= 15
                        p.drawString(90, y_position, f"严重程度: {problem.get('severity', 'info').upper()}")
                        y_position -= 15
                        p.drawString(90, y_position, f"类别: {problem.get('category', '未分类')}")
                        y_position -= 30
                
                # 完成PDF
                p.showPage()
                p.save()
                
                # 获取PDF数据
                buffer.seek(0)
                pdf_bytes = buffer.getvalue()
                buffer.close()
                
                return pdf_bytes
                
            except ImportError:
                # 如果reportlab也不可用，返回错误信息
                logger.error("无法生成PDF报告：缺少必要的依赖包 (weasyprint或reportlab)")
                error_text = "PDF生成失败：缺少必要的依赖包\n请安装 weasyprint 或 reportlab"
                return error_text.encode('utf-8')
    
    except Exception as e:
        logger.error(f"生成PDF报告失败: {str(e)}")
        error_text = f"PDF生成失败: {str(e)}"
        return error_text.encode('utf-8') 