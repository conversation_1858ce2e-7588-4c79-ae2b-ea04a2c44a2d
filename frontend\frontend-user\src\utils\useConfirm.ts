import { ref, nextTick } from 'vue'
import { createApp, type App } from 'vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'

export interface ConfirmOptions {
  title?: string
  message?: string
  type?: 'info' | 'warning' | 'danger' | 'success'
  confirmText?: string
  cancelText?: string
}

export interface ConfirmDialogInstance {
  show: () => void
  hide: () => void
}

class ConfirmManager {
  private dialogInstance: any = null
  private dialogApp: App | null = null
  private container: HTMLElement | null = null

  private createDialog(options: ConfirmOptions): Promise<boolean> {
    return new Promise((resolve) => {
      // 清理之前的对话框
      this.cleanup()

      // 创建容器
      this.container = document.createElement('div')
      document.body.appendChild(this.container)

      // 创建Vue应用实例
      this.dialogApp = createApp(ConfirmDialog, {
        ...options,
        onConfirm: () => {
          resolve(true)
          this.cleanup()
        },
        onCancel: () => {
          resolve(false)
          this.cleanup()
        }
      })

      // 挂载组件
      const vm = this.dialogApp.mount(this.container)
      this.dialogInstance = vm

      // 显示对话框
      nextTick(() => {
        this.dialogInstance?.show?.()
      })
    })
  }

  private cleanup() {
    if (this.dialogApp) {
      this.dialogApp.unmount()
      this.dialogApp = null
    }
    if (this.container) {
      document.body.removeChild(this.container)
      this.container = null
    }
    this.dialogInstance = null
  }

  // 通用确认对话框
  confirm(message: string, options: ConfirmOptions = {}): Promise<boolean> {
    return this.createDialog({
      title: '确认操作',
      message,
      type: 'info',
      confirmText: '确定',
      cancelText: '取消',
      ...options
    })
  }

  // 警告确认对话框
  warning(message: string, options: Omit<ConfirmOptions, 'type'> = {}): Promise<boolean> {
    return this.createDialog({
      title: '警告',
      message,
      type: 'warning',
      confirmText: '继续',
      cancelText: '取消',
      ...options
    })
  }

  // 危险操作确认对话框
  danger(message: string, options: Omit<ConfirmOptions, 'type'> = {}): Promise<boolean> {
    return this.createDialog({
      title: '危险操作',
      message,
      type: 'danger',
      confirmText: '删除',
      cancelText: '取消',
      ...options
    })
  }

  // 成功确认对话框
  success(message: string, options: Omit<ConfirmOptions, 'type'> = {}): Promise<boolean> {
    return this.createDialog({
      title: '确认',
      message,
      type: 'success',
      confirmText: '确定',
      cancelText: '取消',
      ...options
    })
  }

  // 信息确认对话框
  info(message: string, options: Omit<ConfirmOptions, 'type'> = {}): Promise<boolean> {
    return this.createDialog({
      title: '信息',
      message,
      type: 'info',
      confirmText: '确定',
      cancelText: '取消',
      ...options
    })
  }
}

// 创建全局实例
const confirmManager = new ConfirmManager()

// 导出全局方法
export const $confirm = confirmManager

// Vue 插件安装函数
export function installConfirm(app: App) {
  app.config.globalProperties.$confirm = confirmManager
}

// 使用示例和文档
export const confirmExamples = {
  // 基础用法
  basic: async () => {
    const result = await $confirm.confirm('确定要删除这个文件吗？')
    if (result) {
      console.log('用户确认删除')
    }
  },

  // 危险操作
  danger: async () => {
    const result = await $confirm.danger('此操作将永久删除数据，无法恢复！')
    if (result) {
      console.log('用户确认危险操作')
    }
  },

  // 自定义选项
  custom: async () => {
    const result = await $confirm.warning('系统将重启，可能影响在线用户', {
      title: '系统维护',
      confirmText: '立即重启',
      cancelText: '稍后重启'
    })
    if (result) {
      console.log('用户确认立即重启')
    }
  }
}

// 最佳实践指南
export const confirmBestPractices = {
  // 1. 使用合适的类型
  useAppropriateType: {
    info: '一般信息确认',
    warning: '需要用户注意的操作',
    danger: '不可逆的删除操作',
    success: '积极的确认操作'
  },

  // 2. 提供清晰的描述
  provideClearDescription: {
    good: '确定要删除"项目报告.docx"吗？删除后无法恢复。',
    bad: '确定要删除吗？'
  },

  // 3. 使用合适的按钮文本
  useAppropriateButtonText: {
    delete: { confirmText: '删除', cancelText: '取消' },
    restart: { confirmText: '重启', cancelText: '稍后' },
    logout: { confirmText: '退出', cancelText: '取消' }
  }
}

// 类型定义
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $confirm: ConfirmManager
  }
}

// 导出useConfirm函数
export function useConfirm() {
  return confirmManager
}

export default confirmManager 