"""
论文格式检测器

该模块实现了论文格式规范检查功能，包括：
- 页面格式检查（页边距、页面大小、方向等）
- 字体格式检查（字体类型、字号、一致性等）
- 段落格式检查（缩进、行距、对齐等）
- 样式格式检查（标题样式、列表样式等）
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
import structlog

from .rule_engine import CheckResult, CheckSeverity
from ..core.exceptions import DocumentAnalysisException


logger = structlog.get_logger(__name__)


class FormatCheckType(Enum):
    """格式检查类型"""
    PAGE_FORMAT = "page_format"         # 页面格式
    FONT_FORMAT = "font_format"         # 字体格式
    PARAGRAPH_FORMAT = "paragraph_format"  # 段落格式
    STYLE_FORMAT = "style_format"       # 样式格式


@dataclass
class FormatIssue:
    """格式问题数据类"""
    issue_type: FormatCheckType         # 问题类型
    severity: CheckSeverity             # 严重程度
    message: str                        # 问题描述
    position: Optional[int] = None      # 问题位置
    expected_value: Optional[Any] = None  # 期望值
    actual_value: Optional[Any] = None   # 实际值
    suggestions: List[str] = field(default_factory=list)  # 修复建议
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "issue_type": self.issue_type.value,
            "severity": self.severity.value,
            "message": self.message,
            "position": self.position,
            "expected_value": self.expected_value,
            "actual_value": self.actual_value,
            "suggestions": self.suggestions
        }


@dataclass
class FormatCheckResult:
    """格式检查结果数据类"""
    check_type: FormatCheckType         # 检查类型
    passed: bool                        # 是否通过
    issues: List[FormatIssue] = field(default_factory=list)  # 问题列表
    statistics: Dict[str, Any] = field(default_factory=dict)  # 统计信息
    execution_time: float = 0.0         # 执行时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "check_type": self.check_type.value,
            "passed": self.passed,
            "issues": [issue.to_dict() for issue in self.issues],
            "statistics": self.statistics,
            "execution_time": self.execution_time
        }


class FormatChecker:
    """论文格式检测器"""
    
    def __init__(self, standards_config: Optional[Dict[str, Any]] = None):
        """
        初始化格式检测器
        
        Args:
            standards_config: 格式标准配置
        """
        self.logger = structlog.get_logger(__name__)
        self.standards = standards_config or self._get_default_standards()
        
        # 检查统计
        self.check_stats = {
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "total_issues": 0
        }
    
    def _get_default_standards(self) -> Dict[str, Any]:
        """获取默认格式标准"""
        return {
            "page_format": {
                "paper_size": "A4",
                "orientation": "portrait",
                "margins": {
                    "top": 25.4,      # mm
                    "bottom": 25.4,   # mm
                    "left": 31.8,     # mm
                    "right": 31.8     # mm
                },
                "margin_tolerance": 2.0  # mm
            },
            "font_format": {
                "body_font": {
                    "name": "宋体",
                    "size": 12,
                    "size_tolerance": 0.5
                },
                "allowed_fonts": ["宋体", "黑体", "Times New Roman", "Arial"],
                "max_font_types": 4
            },
            "paragraph_format": {
                "line_spacing": 1.5,
                "line_spacing_tolerance": 0.1,
                "first_line_indent": 24,  # pt
                "indent_tolerance": 2,    # pt
                "alignment": "justify"
            }
        }
    
    async def check_page_format(self, analysis_data: Dict[str, Any]) -> FormatCheckResult:
        """
        检查页面格式
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            页面格式检查结果
        """
        start_time = asyncio.get_event_loop().time()
        issues = []
        
        try:
            # 基础实现 - 检查页面格式
            page_format = analysis_data.get("page_format", {})
            standards = self.standards["page_format"]
            
            # 检查纸张大小
            if page_format.get("paper_size") != standards["paper_size"]:
                issues.append(FormatIssue(
                    issue_type=FormatCheckType.PAGE_FORMAT,
                    severity=CheckSeverity.WARNING,
                    message="纸张大小不符合标准",
                    expected_value=standards["paper_size"],
                    actual_value=page_format.get("paper_size"),
                    suggestions=["请将纸张大小设置为A4"]
                ))
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return FormatCheckResult(
                check_type=FormatCheckType.PAGE_FORMAT,
                passed=len(issues) == 0,
                issues=issues,
                statistics={"total_issues": len(issues)},
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error("页面格式检查失败", error=str(e))
            
            return FormatCheckResult(
                check_type=FormatCheckType.PAGE_FORMAT,
                passed=False,
                issues=[FormatIssue(
                    issue_type=FormatCheckType.PAGE_FORMAT,
                    severity=CheckSeverity.ERROR,
                    message=f"页面格式检查失败: {str(e)}"
                )],
                execution_time=execution_time
            )
    
    async def check_font_format(self, analysis_data: Dict[str, Any]) -> FormatCheckResult:
        """
        检查字体格式
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            字体格式检查结果
        """
        start_time = asyncio.get_event_loop().time()
        issues = []
        
        try:
            # 基础实现 - 检查字体格式
            font_formats = analysis_data.get("font_formats", [])
            standards = self.standards["font_format"]
            
            # 统计字体使用情况
            unique_fonts = set()
            for font_format in font_formats:
                if isinstance(font_format, dict):
                    font_name = font_format.get("name")
                    if font_name:
                        unique_fonts.add(font_name)
            
            # 检查字体类型数量
            if len(unique_fonts) > standards["max_font_types"]:
                issues.append(FormatIssue(
                    issue_type=FormatCheckType.FONT_FORMAT,
                    severity=CheckSeverity.WARNING,
                    message="使用的字体类型过多",
                    expected_value=f"最多{standards['max_font_types']}种",
                    actual_value=f"{len(unique_fonts)}种",
                    suggestions=["建议减少字体类型，保持文档一致性"]
                ))
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            return FormatCheckResult(
                check_type=FormatCheckType.FONT_FORMAT,
                passed=len(issues) == 0,
                issues=issues,
                statistics={
                    "total_issues": len(issues),
                    "unique_fonts": len(unique_fonts)
                },
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            self.logger.error("字体格式检查失败", error=str(e))
            
            return FormatCheckResult(
                check_type=FormatCheckType.FONT_FORMAT,
                passed=False,
                issues=[FormatIssue(
                    issue_type=FormatCheckType.FONT_FORMAT,
                    severity=CheckSeverity.ERROR,
                    message=f"字体格式检查失败: {str(e)}"
                )],
                execution_time=execution_time
            )
    
    def get_check_stats(self) -> Dict[str, Any]:
        """获取检查统计"""
        stats = self.check_stats.copy()
        
        # 计算通过率
        if stats["total_checks"] > 0:
            stats["pass_rate"] = stats["passed_checks"] / stats["total_checks"]
        else:
            stats["pass_rate"] = 0.0
        
        return stats

    async def check_document_format(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        检查文档格式（主要入口方法）
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            Dict[str, Any]: 格式检查结果
        """
        try:
            start_time = asyncio.get_event_loop().time()
            
            # 执行各种格式检查
            page_result = await self.check_page_format(analysis_data)
            font_result = await self.check_font_format(analysis_data)
            
            # 汇总结果
            all_issues = []
            all_issues.extend(page_result.issues)
            all_issues.extend(font_result.issues)
            
            # 计算总体格式评分
            total_issues = len(all_issues)
            critical_issues = sum(1 for issue in all_issues if issue.severity == CheckSeverity.ERROR)
            major_issues = sum(1 for issue in all_issues if issue.severity == CheckSeverity.WARNING)
            minor_issues = sum(1 for issue in all_issues if issue.severity == CheckSeverity.INFO)
            
            # 评分算法：严重问题扣30分，重要问题扣10分，一般问题扣5分
            format_score = max(0, 100 - (critical_issues * 30 + major_issues * 10 + minor_issues * 5))
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # 更新统计信息
            self.check_stats["total_checks"] += 1
            if format_score >= 80:
                self.check_stats["passed_checks"] += 1
            else:
                self.check_stats["failed_checks"] += 1
            self.check_stats["total_issues"] += total_issues
            
            return {
                "format_score": format_score,
                "total_issues": total_issues,
                "critical_issues": critical_issues,
                "major_issues": major_issues,
                "minor_issues": minor_issues,
                "issues": [issue.to_dict() for issue in all_issues],
                "page_result": page_result.to_dict(),
                "font_result": font_result.to_dict(),
                "execution_time": execution_time,
                "passed": format_score >= 80
            }
            
        except Exception as e:
            self.logger.error("文档格式检查失败", error=str(e))
            
            # 更新统计信息
            self.check_stats["total_checks"] += 1
            self.check_stats["failed_checks"] += 1
            
            return {
                "format_score": 0,
                "total_issues": 1,
                "critical_issues": 1,
                "major_issues": 0,
                "minor_issues": 0,
                "issues": [{
                    "issue_type": "system_error",
                    "severity": "error",
                    "message": f"格式检查失败: {str(e)}",
                    "position": None,
                    "expected_value": None,
                    "actual_value": None,
                    "suggestions": []
                }],
                "execution_time": 0,
                "passed": False,
                "error": str(e)
            }


# 工厂函数：创建格式检查器实例
def get_format_checker(standards_config: Optional[Dict[str, Any]] = None) -> FormatChecker:
    """
    创建格式检查器实例
    
    Args:
        standards_config: 可选的格式标准配置
        
    Returns:
        FormatChecker: 格式检查器实例
    """
    return FormatChecker(standards_config) 