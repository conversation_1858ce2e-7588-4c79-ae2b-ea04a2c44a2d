# Word 文档分析服务 - 后端

一个基于 FastAPI + PostgreSQL 的企业级 Word 文档分析和论文检测服务。

## 🚀 项目状态

**当前版本**: v1.0.0  
**开发状态**: ✅ **生产就绪**  
**验证状态**: ✅ **93.8%通过** (2024-12-19)  
**服务状态**: 🟢 **正常运行**

### 最新验证结果 (全面验证)
- 🔍 **API文档一致性**: 100%通过 (11/11)
- 🔐 **认证授权安全**: 100%通过 (4/4) 
- 💼 **业务逻辑完整性**: 100%通过 (5/5)
- ⚠️ **错误处理一致性**: 100%通过 (2/2)
- ⚡ **性能稳定性**: 100%通过 (5/5)
- 🗄️ **数据库完整性**: 50%通过 (1/2)
- 🎨 **前端集成准备度**: 67%通过 (2/3)

**总体成功率**: 93.8% (30/32测试通过)

详细验证报告请查看: [final_verification_summary.md](final_verification_summary.md)

## 项目概述

本服务提供专业的 Word 文档内容提取、格式分析和论文合规性检测功能，支持多用户环境和高并发处理。

### 核心功能

- **文档分析**: Word 文档内容提取、结构分析、格式识别
- **论文检测**: 格式合规性验证、结构完整性检查、问题识别
  - 🆕 **分层规则系统**: 支持引用机制和模块化配置
  - 🆕 **智能错误处理**: 用户友好的错误提示和改进建议
  - 🆕 **全面检测覆盖**: 结构、格式、内容三维度检测
- **图片处理**: 文档图片提取、处理和存储
- **任务管理**: 异步任务处理、进度跟踪、批量操作
- **用户管理**: 注册登录、权限控制、数据隔离
- **系统监控**: 性能统计、健康检查、日志记录

## 技术架构

### 技术栈

- **Web 框架**: FastAPI 0.104.1
- **数据库**: PostgreSQL 17.5 + SQLAlchemy 2.0.23
- **缓存**: Redis
- **文档处理**: pywin32 (Windows COM 接口)
- **异步支持**: Python 3.12 + asyncio
- **认证**: JWT + bcrypt
- **日志**: 结构化 JSON 日志

### 项目结构

```
backend/
├── app/                    # 应用核心
│   ├── api/               # API 路由
│   │   └── v1/           # API v1 版本
│   │       ├── auth.py   # 认证 API
│   │       ├── documents.py # 文档处理 API
│   │       ├── tasks.py  # 任务管理 API
│   │       ├── images.py # 图片处理 API
│   │       ├── system.py # 系统管理 API
│   │       └── health.py # 健康检查 API
│   ├── core/              # 核心功能
│   │   ├── config.py     # 配置管理
│   │   ├── security.py   # 安全认证
│   │   ├── exceptions.py # 异常处理
│   │   ├── logging.py    # 日志配置
│   │   └── ...           # 其他核心模块
│   ├── database/          # 数据库层
│   │   ├── init_db.py    # 数据库初始化
│   │   ├── connection.py # 连接管理
│   │   ├── crud.py       # CRUD 操作
│   │   └── ...           # 其他数据库模块
│   ├── services/          # 业务服务
│   │   ├── document_processor.py # 文档处理
│   │   ├── word_com.py   # Word COM 接口
│   │   ├── file_handler.py # 文件处理
│   │   └── ...           # 其他服务
│   ├── analyzers/         # 分析器
│   │   ├── content_parser.py # 内容解析
│   │   ├── format_analyzer.py # 格式分析
│   │   └── ...           # 其他分析器
│   ├── checkers/          # 检测器
│   │   ├── structure_checker.py # 结构检测
│   │   ├── format_checker.py # 格式检测
│   │   └── ...           # 其他检测器
│   ├── tasks/             # 任务管理
│   │   ├── manager.py    # 任务管理器
│   │   ├── queue.py      # 任务队列
│   │   └── ...           # 其他任务模块
│   ├── models/            # 数据模型
│   │   ├── user.py       # 用户模型
│   │   ├── task.py       # 任务模型
│   │   └── ...           # 其他模型
│   └── main.py           # 应用入口
├── config/               # 配置文件
│   └── paper_standards.json # 论文标准配置
├── docs/                 # 项目文档
├── tests/                # 测试代码
├── data/                 # 数据目录
│   ├── uploads/          # 上传文件
│   ├── temp/             # 临时文件
│   ├── images/           # 图片文件
│   └── reports/          # 报告文件
├── logs/                 # 日志文件
├── config.yaml           # 主配置文件
├── requirements.txt      # 依赖包
└── Dockerfile           # Docker 配置
```

## 快速开始

### 环境要求

- **操作系统**: Windows 10/11 (需要 COM 接口支持)
- **Python**: 3.12+
- **数据库**: PostgreSQL 17.5+
- **缓存**: Redis (可选)
- **Microsoft Word**: 已安装 (用于 COM 接口)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd paper-check-win/backend
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   venv\Scripts\activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置数据库**
   ```bash
   # 创建 PostgreSQL 数据库
   createdb word_service
   ```

5. **配置环境变量**
   ```bash
   # 创建 .env 文件
   DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/word_service
   REDIS_URL=redis://localhost:6379/0
   SECRET_KEY=your-secret-key-here
   DEBUG=true
   ```

6. **初始化数据库**
   ```bash
   # 数据库表结构会在应用启动时自动创建
   ```

### 启动服务

#### 开发模式
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 生产模式
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

#### 使用批处理脚本
```bash
# Windows
start-services.bat    # 启动服务
check-status.bat      # 检查状态
stop-services.bat     # 停止服务
```

### Docker 部署

```bash
# 构建镜像
docker build -t word-analysis-service .

# 运行容器
docker-compose up -d
```

## 配置说明

### 主配置文件 (`config.yaml`)

配置文件采用分层结构，包含以下主要部分：

```yaml
# 服务配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1

# 数据库配置
database:
  url: "postgresql+asyncpg://postgres:password@localhost:5432/word_service"
  pool_size: 10
  max_overflow: 20

# 任务处理配置
tasks:
  max_concurrent: 20
  timeout: 300
  worker_count: 3

# Word COM 配置
word_com:
  startup_timeout: 30
  operation_timeout: 120
  pool_size: 3

# 文件处理配置
files:
  max_size: 104857600  # 100MB
  allowed_extensions: [".docx"]
  upload_path: "data/uploads"

# 安全配置
security:
  secret_key: "your-secret-key"
  access_token_expire_minutes: 1440
```

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DATABASE_URL` | 数据库连接字符串 | - |
| `REDIS_URL` | Redis 连接字符串 | - |
| `SECRET_KEY` | JWT 密钥 | - |
| `DEBUG` | 调试模式 | `false` |
| `MAX_CONCURRENT_TASKS` | 最大并发任务数 | `20` |
| `MAX_FILE_SIZE` | 最大文件大小 | `104857600` |

## API 文档

### 服务地址

- **开发环境**: `http://localhost:8000`
- **API 文档**: `http://localhost:8000/docs`
- **ReDoc 文档**: `http://localhost:8000/redoc`

### 主要 API 端点

#### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌

#### 文档处理
- `POST /api/v1/documents/upload` - 上传文档
- `GET /api/v1/documents/{document_id}` - 获取文档信息
- `POST /api/v1/documents/{document_id}/analyze` - 分析文档

#### 任务管理
- `POST /api/v1/tasks/create` - 创建任务
- `GET /api/v1/tasks/{task_id}` - 获取任务状态
- `DELETE /api/v1/tasks/{task_id}` - 取消任务
- `GET /api/v1/tasks/` - 获取任务列表

#### 图片处理
- `GET /api/v1/images/{image_id}` - 获取图片
- `POST /api/v1/images/extract` - 提取文档图片

#### 系统管理
- `GET /api/v1/system/stats` - 系统统计
- `GET /api/v1/system/config` - 系统配置
- `POST /api/v1/system/cleanup` - 清理资源

#### 健康检查
- `GET /health/` - 健康检查
- `GET /health/detailed` - 详细健康状态

### 请求示例

#### 上传文档
```bash
curl -X POST "http://localhost:8000/api/v1/documents/upload" \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.docx"
```

#### 创建分析任务
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/create" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "paper_check",
    "file_path": "uploads/document.docx",
    "analysis_options": {
      "check_format": true,
      "extract_images": true
    }
  }'
```

## 开发指南

### 代码规范

- 遵循 **PEP 8** 代码风格
- 使用 **类型注解** (Type Hints)
- 所有函数和类必须有 **docstring**
- 变量和函数使用 **snake_case**
- 类名使用 **PascalCase**

### 开发工作流

1. **功能开发**
   - 在对应模块下添加新功能
   - 编写单元测试
   - 更新 API 文档

2. **数据库变更**
   - 修改 `init_db.py` 中的 DDL 语句
   - 更新相关数据模型
   - 添加迁移测试

3. **API 开发**
   - 在 `api/v1/` 下添加新端点
   - 定义 Pydantic 模型
   - 实现业务逻辑
   - 添加认证和权限检查

4. **规则开发**
   - 查看 [新规则添加指南](docs/新规则添加指南.md)
   - 在 `config/rules/` 下配置新规则
   - 实现对应的检查函数
   - 更新执行计划和测试用例

### 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_document_processor.py

# 生成覆盖率报告
pytest --cov=app --cov-report=html
```

### 代码质量

```bash
# 代码格式化
black app/
isort app/

# 代码检查
flake8 app/
mypy app/
```

## 监控和运维

### 日志

- **位置**: `logs/word_service.log`
- **格式**: 结构化 JSON
- **级别**: DEBUG, INFO, WARNING, ERROR
- **轮转**: 每日轮转，保留 5 个文件

### 监控指标

- 请求处理时间
- 任务处理状态
- 数据库连接池状态
- Word COM 实例状态
- 系统资源使用

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8000/health/

# 详细健康状态
curl http://localhost:8000/health/detailed
```

## 故障排除

### 常见问题

1. **Word COM 接口错误**
   - 确保已安装 Microsoft Word
   - 检查 COM 组件注册
   - 重启 Word 应用程序

2. **数据库连接失败**
   - 检查 PostgreSQL 服务状态
   - 验证连接字符串
   - 检查防火墙设置

3. **任务处理超时**
   - 调整任务超时时间
   - 检查文件大小限制
   - 监控系统资源

4. **文件上传失败**
   - 检查文件大小限制
   - 验证文件格式
   - 确保存储空间充足

### 日志查看

```bash
# 查看最新日志
tail -f logs/word_service.log

# 按级别过滤
grep "ERROR" logs/word_service.log

# 按任务ID查找
grep "task_id:your-task-id" logs/word_service.log
```

## 许可证

[项目许可证信息]

## 联系方式

- **项目维护者**: [维护者信息]
- **问题反馈**: [Issues链接]
- **技术支持**: [联系方式]

---

> 本文档最后更新: 2024-12-19 (项目清理和整理完成) 