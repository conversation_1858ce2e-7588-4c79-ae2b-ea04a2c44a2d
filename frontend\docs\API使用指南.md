# Word文档分析服务 - 前端开发API文档

## 📋 基本信息

**服务名称**: Word文档分析服务  
**API版本**: v1.0.0  
**基础URL**: `http://localhost:8000`  
**认证方式**: JWT Bearer Token  
**文档更新**: 2024-12-19  

## 🚀 快速开始

### 环境配置
```javascript
const API_BASE_URL = 'http://localhost:8000';
const API_TIMEOUT = 30000;
```

### 认证流程
```javascript
// 1. 用户登录
const loginUser = async (username, password) => {
  const response = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `username=${username}&password=${password}`
  });
  
  const data = await response.json();
  if (data.success) {
    localStorage.setItem('access_token', data.data.access_token);
    return data.data;
  }
  throw new Error(data.message);
};

// 2. 获取认证头
const getAuthHeaders = () => ({
  'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
  'Content-Type': 'application/json'
});
```

## 🔐 1. 认证API

### 1.1 用户注册
```http
POST /api/v1/auth/register
```

**请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "用户注册成功",
  "data": {
    "user": {
      "id": "user_123",
      "username": "testuser",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 1.2 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/x-www-form-urlencoded
```

**请求体**:
```
username=testuser&password=password123
```

**响应**:
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 86400
  }
}
```

### 1.3 获取用户信息
```http
GET /api/v1/auth/profile
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "username": "testuser",
    "email": "<EMAIL>",
    "statistics": {
      "total_documents": 15,
      "total_tasks": 28
    }
  }
}
```

### 1.4 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

## 📄 2. 文档处理API

### 2.1 文档上传
```http
POST /api/v1/documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**表单字段**:
- `file`: Word文档文件 (.doc, .docx)
- `analysis_type`: 分析类型 (paper_check, format_check, structure_check)

**JavaScript示例**:
```javascript
const uploadDocument = async (file, analysisType = 'paper_check') => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('analysis_type', analysisType);
  
  const response = await fetch(`${API_BASE_URL}/api/v1/documents/upload`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`
    },
    body: formData
  });
  
  return response.json();
};
```

**响应**:
```json
{
  "success": true,
  "data": {
    "document_id": "doc_123456",
    "task_id": "task_789012",
    "filename": "论文.docx",
    "file_size": 2048576,
    "status": "uploaded"
  }
}
```

### 2.2 获取文档列表
```http
GET /api/v1/documents?page=1&limit=20&status=completed
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认20, 最大100)
- `status`: 状态筛选 (uploaded, processing, completed, failed)

**响应**:
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "doc_123456",
        "filename": "论文.docx",
        "status": "completed",
        "uploaded_at": "2024-12-19T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15
    }
  }
}
```

### 2.3 获取文档详情
```http
GET /api/v1/documents/{document_id}
Authorization: Bearer <token>
```

### 2.4 启动文档分析
```http
POST /api/v1/documents/{document_id}/analyze
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "analysis_type": "paper_check",
  "options": {
    "check_format": true,
    "check_structure": true,
    "check_references": true
  }
}
```

### 2.5 获取分析结果
```http
GET /api/v1/documents/{document_id}/analysis
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "status": "completed",
    "results": {
      "overall_score": 85,
      "total_problems": 12,
      "categories": {
        "format": { "score": 80, "problems": 8 },
        "structure": { "score": 90, "problems": 3 },
        "references": { "score": 85, "problems": 1 }
      }
    }
  }
}
```

### 2.6 生成检测报告
```http
GET /api/v1/documents/{document_id}/report?format=json
Authorization: Bearer <token>
```

**查询参数**:
- `format`: 报告格式 (json, html, text, markdown)

### 2.7 删除文档
```http
DELETE /api/v1/documents/{document_id}
Authorization: Bearer <token>
```

## ⚙️ 3. 任务管理API

### 3.1 获取任务列表
```http
GET /api/v1/tasks?page=1&limit=20&status=processing
Authorization: Bearer <token>
```

**查询参数**:
- `status`: 任务状态 (pending, processing, completed, failed)
- `task_type`: 任务类型 (paper_check, format_check, structure_check)
- `page`: 页码
- `limit`: 每页数量

### 3.2 获取任务详情
```http
GET /api/v1/tasks/{task_id}
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "task_789012",
    "type": "paper_check",
    "status": "completed",
    "progress": 100,
    "created_at": "2024-12-19T10:01:00Z",
    "completed_at": "2024-12-19T10:03:30Z",
    "result": {
      "overall_score": 85,
      "total_problems": 12
    }
  }
}
```

### 3.3 获取任务状态
```http
GET /api/v1/tasks/{task_id}/status
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_789012",
    "status": "processing",
    "progress": 65,
    "current_step": "格式检测",
    "estimated_remaining": "1分钟"
  }
}
```

### 3.4 取消任务
```http
POST /api/v1/tasks/{task_id}/cancel
Authorization: Bearer <token>
```

## 🖼️ 4. 图片资源API

### 4.1 获取图片
```http
GET /api/v1/images/{image_id}?size=original
Authorization: Bearer <token>
```

**查询参数**:
- `size`: 图片尺寸 (thumbnail, medium, original)

### 4.2 上传图片
```http
POST /api/v1/images/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

## 🔧 5. 系统管理API

### 5.1 系统统计
```http
GET /api/v1/system/stats
Authorization: Bearer <token>
```

### 5.2 系统配置
```http
GET /api/v1/system/config
Authorization: Bearer <token>
```

### 5.3 健康检查
```http
GET /health
```

```http
GET /api/v1/system/health
```

## 🛠️ 实用工具类

### API客户端类
```javascript
class WordAnalysisAPI {
  constructor(baseURL = 'http://localhost:8000') {
    this.baseURL = baseURL;
  }
  
  // 获取认证头
  getHeaders(includeAuth = true) {
    const headers = { 'Content-Type': 'application/json' };
    if (includeAuth) {
      const token = localStorage.getItem('access_token');
      if (token) headers.Authorization = `Bearer ${token}`;
    }
    return headers;
  }
  
  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(options.auth !== false),
      ...options
    };
    
    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message);
      }
      
      return data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }
  
  // 错误处理
  handleError(error) {
    if (error.code === 40101) {
      // Token过期，重新登录
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    console.error('API错误:', error);
  }
  
  // 认证方法
  async login(username, password) {
    const response = await fetch(`${this.baseURL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `username=${username}&password=${password}`
    });
    
    const data = await response.json();
    if (data.success) {
      localStorage.setItem('access_token', data.data.access_token);
    }
    return data;
  }
  
  async register(userData) {
    return this.request('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
      auth: false
    });
  }
  
  async getUserProfile() {
    return this.request('/api/v1/auth/profile');
  }
  
  async logout() {
    const result = await this.request('/api/v1/auth/logout', { method: 'POST' });
    localStorage.removeItem('access_token');
    return result;
  }
  
  // 文档方法
  async uploadDocument(file, analysisType = 'paper_check') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('analysis_type', analysisType);
    
    const response = await fetch(`${this.baseURL}/api/v1/documents/upload`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${localStorage.getItem('access_token')}` },
      body: formData
    });
    return response.json();
  }
  
  async getDocuments(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/api/v1/documents?${query}`);
  }
  
  async getDocument(documentId) {
    return this.request(`/api/v1/documents/${documentId}`);
  }
  
  async analyzeDocument(documentId, options = {}) {
    return this.request(`/api/v1/documents/${documentId}/analyze`, {
      method: 'POST',
      body: JSON.stringify({
        analysis_type: options.type || 'paper_check',
        options: options
      })
    });
  }
  
  async getAnalysisResult(documentId) {
    return this.request(`/api/v1/documents/${documentId}/analysis`);
  }
  
  async generateReport(documentId, format = 'json') {
    return this.request(`/api/v1/documents/${documentId}/report?format=${format}`);
  }
  
  async deleteDocument(documentId) {
    return this.request(`/api/v1/documents/${documentId}`, { method: 'DELETE' });
  }
  
  // 任务方法
  async getTasks(params = {}) {
    const query = new URLSearchParams(params).toString();
    return this.request(`/api/v1/tasks?${query}`);
  }
  
  async getTask(taskId) {
    return this.request(`/api/v1/tasks/${taskId}`);
  }
  
  async getTaskStatus(taskId) {
    return this.request(`/api/v1/tasks/${taskId}/status`);
  }
  
  async cancelTask(taskId) {
    return this.request(`/api/v1/tasks/${taskId}/cancel`, { method: 'POST' });
  }
}

// 使用示例
const api = new WordAnalysisAPI();
```

### 任务状态轮询
```javascript
function pollTaskStatus(api, taskId, callback, interval = 2000) {
  const poll = async () => {
    try {
      const result = await api.getTaskStatus(taskId);
      callback(result.data);
      
      // 如果任务未完成，继续轮询
      if (!['completed', 'failed', 'cancelled'].includes(result.data.status)) {
        setTimeout(poll, interval);
      }
    } catch (error) {
      console.error('轮询任务状态失败:', error);
      callback({ status: 'error', error: error.message });
    }
  };
  
  poll();
}

// 使用示例
pollTaskStatus(api, 'task_123', (status) => {
  console.log('任务状态更新:', status);
  // 更新UI
  updateProgressBar(status.progress);
  updateStatusText(status.current_step);
});
```

### 文件上传进度
```javascript
function uploadWithProgress(file, onProgress, onComplete, onError) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('analysis_type', 'paper_check');
  
  const xhr = new XMLHttpRequest();
  
  // 上传进度
  xhr.upload.addEventListener('progress', (e) => {
    if (e.lengthComputable) {
      const progress = (e.loaded / e.total) * 100;
      onProgress(progress);
    }
  });
  
  // 上传完成
  xhr.addEventListener('load', () => {
    if (xhr.status === 200) {
      const result = JSON.parse(xhr.responseText);
      onComplete(result);
    } else {
      onError(new Error('上传失败'));
    }
  });
  
  // 上传错误
  xhr.addEventListener('error', () => {
    onError(new Error('网络错误'));
  });
  
  xhr.open('POST', 'http://localhost:8000/api/v1/documents/upload');
  xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('access_token')}`);
  xhr.send(formData);
}

// 使用示例
uploadWithProgress(
  file,
  (progress) => console.log(`上传进度: ${progress.toFixed(1)}%`),
  (result) => console.log('上传成功:', result),
  (error) => console.error('上传失败:', error)
);
```

## 📱 React集成示例

### 自定义Hook
```javascript
import { useState, useEffect } from 'react';

// API Hook
function useWordAnalysisAPI() {
  const [api] = useState(() => new WordAnalysisAPI());
  return api;
}

// 文档列表Hook
function useDocuments() {
  const api = useWordAnalysisAPI();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchDocuments = async (params = {}) => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.getDocuments(params);
      setDocuments(result.data.documents);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchDocuments();
  }, []);
  
  return { documents, loading, error, refetch: fetchDocuments };
}

// 任务状态Hook
function useTaskStatus(taskId) {
  const api = useWordAnalysisAPI();
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    if (!taskId) return;
    
    setLoading(true);
    pollTaskStatus(api, taskId, (newStatus) => {
      setStatus(newStatus);
      if (['completed', 'failed', 'cancelled'].includes(newStatus.status)) {
        setLoading(false);
      }
    });
  }, [taskId]);
  
  return { status, loading };
}
```

### 组件示例
```javascript
// 文档上传组件
function DocumentUpload({ onUploadComplete }) {
  const api = useWordAnalysisAPI();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  
  const handleFileUpload = (file) => {
    setUploading(true);
    
    uploadWithProgress(
      file,
      (progress) => setProgress(progress),
      (result) => {
        setUploading(false);
        setProgress(0);
        onUploadComplete(result.data);
      },
      (error) => {
        setUploading(false);
        setProgress(0);
        alert('上传失败: ' + error.message);
      }
    );
  };
  
  return (
    <div>
      <input
        type="file"
        accept=".doc,.docx"
        onChange={(e) => handleFileUpload(e.target.files[0])}
        disabled={uploading}
      />
      {uploading && (
        <div>
          <div>上传进度: {progress.toFixed(1)}%</div>
          <progress value={progress} max={100} />
        </div>
      )}
    </div>
  );
}

// 文档列表组件
function DocumentList() {
  const { documents, loading, error, refetch } = useDocuments();
  
  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;
  
  return (
    <div>
      <button onClick={refetch}>刷新</button>
      <ul>
        {documents.map(doc => (
          <li key={doc.id}>
            {doc.filename} - {doc.status}
          </li>
        ))}
      </ul>
    </div>
  );
}

// 任务状态组件
function TaskStatus({ taskId }) {
  const { status, loading } = useTaskStatus(taskId);
  
  if (loading) return <div>处理中...</div>;
  if (!status) return null;
  
  return (
    <div>
      <div>状态: {status.status}</div>
      <div>进度: {status.progress}%</div>
      {status.current_step && <div>当前步骤: {status.current_step}</div>}
    </div>
  );
}
```

## 🔍 调试和测试

### 调试模式
```javascript
const DEBUG = process.env.NODE_ENV === 'development';

function debugLog(message, data) {
  if (DEBUG) {
    console.log(`[API Debug] ${message}`, data);
  }
}

// 在API请求中使用
async request(endpoint, options = {}) {
  debugLog(`请求: ${options.method || 'GET'} ${endpoint}`, options);
  
  const response = await fetch(url, config);
  const data = await response.json();
  
  debugLog(`响应: ${endpoint}`, data);
  
  return data;
}
```

### 错误处理
```javascript
function handleApiError(error, context = '') {
  console.error(`API错误 ${context}:`, error);
  
  // 根据错误类型处理
  switch (error.code) {
    case 40101:
      // Token过期
      localStorage.removeItem('access_token');
      window.location.href = '/login';
      break;
    case 40102:
      alert('权限不足');
      break;
    case 40401:
      alert('文档不存在');
      break;
    case 50001:
      alert('文档分析失败，请重试');
      break;
    default:
      alert(error.message || '操作失败');
  }
}
```

## 📋 开发检查清单

### 基础功能
- [ ] 用户注册/登录
- [ ] Token存储和管理
- [ ] 文档上传功能
- [ ] 文档列表显示
- [ ] 任务状态跟踪
- [ ] 分析结果展示

### 用户体验
- [ ] 上传进度显示
- [ ] 任务状态实时更新
- [ ] 错误提示和处理
- [ ] 加载状态显示
- [ ] 响应式设计

### 性能优化
- [ ] 图片懒加载
- [ ] 列表分页
- [ ] 请求缓存
- [ ] 防抖和节流
- [ ] 代码分割

## 🔗 相关资源

- **Swagger UI**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **ReDoc**: [http://localhost:8000/redoc](http://localhost:8000/redoc)
- **健康检查**: [http://localhost:8000/health](http://localhost:8000/health)

---

**注意**: 本文档基于当前API实现编写，建议结合Swagger UI进行实时测试和验证。如有问题请联系后端开发团队。 