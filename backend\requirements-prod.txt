# ==================================================
# Word文档分析服务 - 生产环境依赖
# ==================================================

# 生产服务器
gunicorn==21.2.0
uvloop==0.19.0  # 高性能事件循环（仅Linux）

# 监控和指标
prometheus-client==0.19.0
statsd==4.0.1

# 安全增强
cryptography==41.0.8
python-jose[cryptography]==3.3.0

# 性能优化
orjson==3.9.10  # 高性能JSON序列化
ujson==5.8.0    # 备选JSON库

# 数据库连接池
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0  # PostgreSQL异步驱动（可选）

# 缓存优化
redis[hiredis]==5.0.1  # 包含C扩展的Redis客户端

# 日志增强
python-json-logger==2.0.7
sentry-sdk[fastapi]==1.38.0  # 错误追踪

# 健康检查
healthcheck==1.3.3

# 配置管理
python-decouple==3.8
environs==10.0.0

# 文件处理优化
python-magic==0.4.27  # 文件类型检测 