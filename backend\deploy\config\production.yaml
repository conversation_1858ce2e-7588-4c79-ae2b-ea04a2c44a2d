# ==================================================
# Word文档分析服务 - 生产环境配置
# ==================================================

# 服务信息
service:
  name: "Word Document Analysis Service"
  version: "1.0.0"
  description: "生产环境Word文档分析服务"
  environment: "production"
  
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  max_connections: 1000
  keepalive_timeout: 5
  graceful_timeout: 30
  
# 应用配置
app:
  debug: false
  reload: false
  title: "Word Document Analysis API"
  description: "生产级Word文档解析和论文检测服务"
  version: "v1"
  docs_url: null  # 生产环境禁用API文档
  redoc_url: null
  openapi_url: null
  
# 数据库配置 (PostgreSQL生产环境)
database:
  url: "postgresql+asyncpg://postgres:${DB_PASSWORD}@postgres:5432/word_service_prod"
  echo: false
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  pool_pre_ping: true
  query_timeout: 60
  
# Redis配置
redis:
  url: "redis://redis:6379/0"
  max_connections: 20
  timeout: 5
  retry_on_timeout: true
  encoding: "utf-8"
  decode_responses: true
  
# 任务处理配置 (PostgreSQL优化)
tasks:
  max_concurrent: 50  # PostgreSQL支持更高并发
  timeout: 600
  queue_name: "word_analysis_tasks_prod"
  retry_attempts: 3
  retry_delay: 10
  batch_size: 50  # PostgreSQL支持更大批次
  worker_count: 10  # 增加工作进程
  
# Word COM接口配置
word_com:
  startup_timeout: 60
  operation_timeout: 300
  restart_interval: 50
  pool_size: 5
  visible: false
  display_alerts: false
  enable_events: false
  
# 文件处理配置 (PostgreSQL优化)
files:
  max_size: 209715200  # 200MB (PostgreSQL支持更大文件)
  allowed_extensions:
    - ".docx"
    - ".doc"
  upload_path: "data/uploads"
  temp_path: "data/temp"
  images_path: "data/images"
  reports_path: "data/reports"
  backup_path: "data/backups"
  temp_retention_hours: 12
  image_quality: 95
  
# 安全配置
security:
  secret_key: "${SECRET_KEY}"
  algorithm: "HS256"
  access_token_expire_minutes: 720
  refresh_token_expire_days: 3
  password_min_length: 12
  bcrypt_rounds: 14
  
# 限流配置
rate_limit:
  per_user_minute: 60
  per_ip_minute: 120
  daily_task_limit: 100
  burst_limit: 5
  
# 日志配置
logging:
  level: "INFO"
  format: "json"
  file: "logs/word_service_prod.log"
  max_size: 52428800  # 50MB
  backup_count: 10
  rotation: "midnight"
  console_output: false
  
# 监控配置
monitoring:
  enabled: true
  stats_retention_days: 90
  health_check_interval: 30
  performance_tracking: true
  metrics_endpoint: "/metrics"
  
# 论文检测配置
paper_check:
  enabled: true
  standards_config: "config/paper_standards.json"
  rules_directory: "config/rules"
  auto_fix_suggestions: true
  default_standard: "undergraduate"
  custom_rules: true
  
# 功能开关
features:
  image_extraction: true
  pdf_report: true
  batch_processing: true
  email_notifications: false
  api_docs: false
  
# 生产配置
production:
  compress_responses: true
  cors_origins:
    - "https://your-domain.com"
    - "https://api.your-domain.com"
  trusted_hosts:
    - "your-domain.com"
    - "api.your-domain.com"
    - "localhost"
  max_request_size: 52428800
  request_timeout: 600
  
# 缓存配置
cache:
  default_ttl: 7200
  max_entries: 50000
  document_analysis_ttl: 14400
  user_session_ttl: 3600
  system_stats_ttl: 300
  
# 错误处理配置
error_handling:
  include_traceback: false
  log_exceptions: true
  custom_error_pages: true
  error_notification: true
  
# 性能配置
performance:
  enable_compression: true
  compression_level: 6
  enable_caching: true
  optimize_images: true
  lazy_loading: true
  
# 数据清理配置
cleanup:
  temp_files:
    enabled: true
    interval_hours: 2
    retention_hours: 12
  log_files:
    enabled: true
    interval_hours: 24
    retention_days: 30
  old_tasks:
    enabled: true
    interval_hours: 24
    retention_days: 30
    
# 备份配置
backup:
  enabled: true
  interval_hours: 6
  retention_days: 7
  compress: true
  remote_storage: false
