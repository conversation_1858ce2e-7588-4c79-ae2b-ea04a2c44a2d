<template>
  <div class="bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 min-h-screen flex items-center justify-center">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 dark:bg-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 dark:bg-blue-600 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
      <div class="absolute top-40 left-40 w-80 h-80 bg-indigo-500 dark:bg-indigo-600 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>
    </div>

    <!-- 主题切换按钮 -->
    <button 
      @click="themeStore.toggleTheme" 
      class="fixed top-6 right-6 z-20 p-3 bg-white/10 dark:bg-gray-800/50 backdrop-blur-lg rounded-full border border-white/20 dark:border-gray-600 text-white hover:bg-white/20 dark:hover:bg-gray-700/50 transition-all duration-300"
    >
      <!-- 太阳图标 (明亮模式) -->
      <svg v-if="themeStore.isDark" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
      <!-- 月亮图标 (暗黑模式) -->
      <svg v-else class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M20.354 15.354A9 9 0 0118.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
      </svg>
    </button>

    <div class="relative z-10 w-full max-w-md px-6">
      <!-- Logo和标题 -->
      <div class="text-center mb-8">
        <div class="mx-auto h-16 w-16 bg-white dark:bg-gray-800 rounded-xl flex items-center justify-center mb-4 shadow-lg">
          <div class="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">W</span>
          </div>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">管理员控制台</h1>
        <p class="text-blue-200 dark:text-gray-300">Word文档分析服务</p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white/10 dark:bg-gray-800/50 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20 dark:border-gray-600">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <div class="form-group">
            <label for="username" class="block text-sm font-medium text-white dark:text-gray-200 mb-2">
              管理员账号
            </label>
            <div class="relative">
              <input 
                v-model="formData.username"
                type="text" 
                id="username" 
                name="username" 
                required
                class="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/20 dark:border-gray-600 rounded-lg text-white dark:text-gray-100 placeholder-white/60 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                placeholder="请输入管理员账号"
              >
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="h-5 w-5 text-white/40 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="password" class="block text-sm font-medium text-white dark:text-gray-200 mb-2">
              登录密码
            </label>
            <div class="relative">
              <input 
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'" 
                id="password" 
                name="password" 
                required
                class="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/20 dark:border-gray-600 rounded-lg text-white dark:text-gray-100 placeholder-white/60 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                placeholder="请输入登录密码"
              >
              <button 
                type="button" 
                @click="showPassword = !showPassword" 
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <svg class="h-5 w-5 text-white/40 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path v-if="!showPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <path v-if="!showPassword" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                </svg>
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <label class="custom-checkbox flex items-center">
              <input v-model="formData.rememberMe" type="checkbox">
              <span class="checkbox-visual"></span>
              <span class="ml-2 text-sm text-white/80 dark:text-gray-300">记住我</span>
            </label>
            <button 
              type="button" 
              @click="forgotPassword" 
              class="text-sm text-blue-300 dark:text-blue-400 hover:text-blue-200 dark:hover:text-blue-300 transition-colors"
            >
              忘记密码？
            </button>
          </div>

          <button 
            type="submit" 
            :disabled="isLoading"
            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 dark:hover:from-blue-700 dark:hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="flex items-center justify-center">
              <svg v-if="!isLoading" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
              </svg>
              <div v-else class="loading-spinner w-5 h-5 mr-2"></div>
              {{ isLoading ? '登录中...' : '管理员登录' }}
            </span>
          </button>
        </form>

        <!-- 安全提醒 -->
        <div class="mt-6 p-4 bg-yellow-500/10 dark:bg-yellow-900/20 border border-yellow-500/20 dark:border-yellow-700/30 rounded-lg">
          <div class="flex items-start">
            <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div>
              <p class="text-sm text-yellow-100 dark:text-yellow-200 font-medium">安全提醒</p>
              <p class="text-sm text-yellow-200/80 dark:text-yellow-300/80 mt-1">
                请妥善保管您的管理员账号信息，避免在公共场所登录。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部链接 -->
      <div class="mt-8 text-center">
        <div class="flex items-center justify-center space-x-4 text-sm text-blue-200 dark:text-gray-300">
          <router-link to="/" class="hover:text-white dark:hover:text-white transition-colors">
            返回用户端
          </router-link>
          <span>•</span>
          <router-link to="/help" class="hover:text-white dark:hover:text-white transition-colors">
            帮助文档
          </router-link>
          <span>•</span>
          <button @click="contactSupport" class="hover:text-white dark:hover:text-white transition-colors">
            技术支持
          </button>
        </div>
        <div class="mt-2 text-xs text-blue-300/70 dark:text-gray-400">
          演示账号：admin / admin123
        </div>
      </div>
    </div>

    <!-- 加载动画 -->
    <div v-if="isLoading" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
      <div class="flex items-center justify-center min-h-screen">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
          <div class="loading-spinner mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-300">正在验证管理员身份...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import { $notify } from '@/utils/useNotifications'

const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()

// 表单状态
const showPassword = ref(false)
const isLoading = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 处理登录
const handleLogin = async () => {
  if (!formData.username || !formData.password) {
    return
  }

  isLoading.value = true

  try {
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 演示账号验证
    if (formData.username === 'admin' && formData.password === 'admin123') {
      // 保存管理员登录状态
      const adminToken = 'admin_token_' + Date.now()
      localStorage.setItem('adminToken', adminToken)
      
      if (formData.rememberMe) {
        localStorage.setItem('adminRememberMe', 'true')
      }

      // 设置用户状态
      userStore.currentUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        phone: '',
        avatar: '',
        full_name: '系统管理员',
        is_active: true,
        created_at: new Date().toISOString(),
        subscription_level: 'admin'
      }

      // 跳转到管理面板
      router.push('/admin/dashboard')
    } else {
      throw new Error('用户名或密码错误')
    }
  } catch (error) {
    console.error('登录失败:', error)
    $notify.error('登录失败：' + (error as Error).message)
  } finally {
    isLoading.value = false
  }
}

// 忘记密码
const forgotPassword = () => {
  $notify.warning('请联系系统管理员重置密码')
}

// 联系支持
const contactSupport = () => {
  $notify.info('技术支持：<EMAIL>')
}

// 页面初始化
onMounted(() => {
  // 如果已经登录，跳转到管理面板
  const adminToken = localStorage.getItem('adminToken')
  if (adminToken) {
    router.push('/admin/dashboard')
    return
  }

  // 演示账号提示
  setTimeout(() => {
    console.log('演示账号：admin / admin123')
  }, 2000)
})
</script>

<style scoped>
.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
</style> 