<template>
  <BaseLayout 
    title="个人中心" 
    description="管理您的个人信息和账户设置"
  >

      <!-- 用户信息卡片 -->
      <BaseCard class="mb-8">
        <div class="flex items-center space-x-6">
          <div class="flex-shrink-0 relative">
            <button @click="uploadAvatar" class="group relative block hover:scale-105 transition-transform">
              <div class="h-20 w-20 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                <span class="text-blue-600 dark:text-blue-400 font-bold text-2xl">
                  {{ userStore.currentUser?.username?.charAt(0).toUpperCase() || 'Y' }}
                </span>
              </div>
              <!-- 上传提示覆盖层 -->
              <div class="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
              </div>
            </button>
            <!-- 编辑按钮 -->
            <button @click="uploadAvatar" class="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white shadow-lg transition-colors">
              <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ userStore.currentUser?.username || '用户名' }}</h2>
            <p class="text-gray-600 dark:text-gray-300">{{ userStore.currentUser?.email || '<EMAIL>' }}</p>
            <div class="flex items-center space-x-4 mt-2">
              <span class="status-badge status-completed text-xs">已验证</span>
              <span v-if="userStore.currentUser?.created_at" class="text-sm text-gray-500 dark:text-gray-400">
                注册时间：{{ new Date(userStore.currentUser.created_at).toLocaleDateString() }}
              </span>
            </div>
          </div>
          <div class="flex-shrink-0 text-right">
            <div class="text-sm text-gray-600 dark:text-gray-300 mb-1">账户余额</div>
            <div class="flex items-center justify-end space-x-1">
              <span class="font-bold text-2xl text-blue-600 dark:text-blue-400">
                {{ userStore.currentUser?.check_balance ?? 0 }}
              </span>
              <span class="text-gray-500 dark:text-gray-400 pt-1">次</span>
            </div>
          </div>
        </div>
      </BaseCard>

      <!-- 设置选项卡 -->
      <BaseCard>
        <div class="tab-navigation">
          <nav class="flex">
            <button @click="switchTab('profile')" 
                    :class="['tab-button', { 'active': activeTab === 'profile' }]">
              基本信息
            </button>
            <button @click="switchTab('stats')" 
                    :class="['tab-button', { 'active': activeTab === 'stats' }]">
              使用统计
            </button>
            <button @click="switchTab('security')" 
                    :class="['tab-button', { 'active': activeTab === 'security' }]">
              安全设置
            </button>
            <button @click="switchTab('preferences')" 
                    :class="['tab-button', { 'active': activeTab === 'preferences' }]">
              偏好设置
            </button>
            <button @click="switchTab('notifications')" 
                    :class="['tab-button', { 'active': activeTab === 'notifications' }]">
              通知设置
            </button>
          </nav>
        </div>

        <!-- 基本信息标签页 -->
        <div v-show="activeTab === 'profile'" class="tab-content">
          <div class="card-body">
            <form @submit.prevent="updateProfile" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <BaseInput
                  v-model="profileData.username"
                  label="用户名"
                  placeholder="请输入用户名"
                  :error="errors.username"
                />
                
                <BaseInput
                  v-model="profileData.email"
                  type="email"
                  label="邮箱地址"
                  placeholder="请输入邮箱地址"
                  :error="errors.email"
                />
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <BaseInput
                  v-model="profileData.realname"
                  label="真实姓名"
                  placeholder="请输入真实姓名"
                />
                
                <BaseInput
                  v-model="profileData.phone"
                  type="tel"
                  label="手机号码"
                  placeholder="请输入手机号码"
                />
              </div>
              
              <BaseInput
                v-model="profileData.organization"
                label="所属机构"
                placeholder="如：清华大学、中科院等"
              />
              
              <BaseInput
                v-model="profileData.bio"
                type="textarea"
                label="个人简介"
                placeholder="简单介绍一下您自己..."
                :rows="4"
              />
              
              <div class="flex justify-end">
                <BaseButton type="submit" variant="primary" :loading="loading.profile">
                  保存更改
                </BaseButton>
              </div>
            </form>
          </div>
        </div>

        <!-- 使用统计标签页 -->
        <div v-show="activeTab === 'stats'" class="tab-content">
          <div class="card-body">
            <!-- 统计概览卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-blue-600 dark:text-blue-400">总分析数</p>
                    <p class="text-2xl font-bold text-blue-700 dark:text-blue-300">{{ userStats.totalAnalyses }}</p>
                  </div>
                  <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-green-600 dark:text-green-400">成功分析</p>
                    <p class="text-2xl font-bold text-green-700 dark:text-green-300">{{ userStats.successfulAnalyses }}</p>
                  </div>
                  <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-purple-600 dark:text-purple-400">使用时长</p>
                    <p class="text-2xl font-bold text-purple-700 dark:text-purple-300">{{ userStats.usageTime }}h</p>
                  </div>
                  <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-orange-600 dark:text-orange-400">节省时间</p>
                    <p class="text-2xl font-bold text-orange-700 dark:text-orange-300">{{ userStats.savedTime }}h</p>
                  </div>
                  <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 图表区域 -->
            <UserStatsChart />
            
            <!-- 最近活动 -->
            <div class="mt-6 bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近活动</h3>
              <div class="space-y-3">
                <div v-for="(activity, index) in recentActivities" :key="index" class="flex items-center space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div class="flex-1">
                    <p class="text-sm text-gray-700 dark:text-gray-300">{{ activity.description }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置标签页 -->
        <div v-show="activeTab === 'security'" class="tab-content">
          <div class="card-body space-y-8">
            <!-- 账户安全概览 -->
            <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 rounded-lg border border-green-200 dark:border-green-800">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-2a8.5 8.5 0 11-17 0 8.5 8.5 0 0117 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">账户安全状态</h3>
                    <p class="text-sm text-green-600 dark:text-green-400">您的账户安全等级：优秀</p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                    <span>安全评分：</span>
                    <span class="font-bold text-green-600 dark:text-green-400 text-lg">{{ securityScore }}/100</span>
                  </div>
                  <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                    <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300" 
                         :style="{ width: securityScore + '%' }"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 修改密码 -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.74 5.74L10.5 17H6v-3.5L16.26 3.74a1.5 1.5 0 012.48 0l.02.02a1.5 1.5 0 010 2.12L16.26 8.26z" />
                </svg>
                修改密码
              </h3>
              
              <!-- 密码强度指示器 -->
              <div v-if="passwordForm.newPassword" class="mb-4 p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">密码强度</span>
                  <span :class="[
                    'text-sm font-medium',
                    passwordStrength.level === 'weak' ? 'text-red-600' :
                    passwordStrength.level === 'medium' ? 'text-yellow-600' : 'text-green-600'
                  ]">
                    {{ passwordStrength.label }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div 
                    :class="[
                      'h-2 rounded-full transition-all duration-300',
                      passwordStrength.level === 'weak' ? 'bg-red-500' :
                      passwordStrength.level === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    ]"
                    :style="{ width: passwordStrength.score + '%' }"
                  ></div>
                </div>
                <div class="mt-2 text-xs text-gray-600 dark:text-gray-400">
                  <ul class="space-y-1">
                    <li :class="passwordValidation.length ? 'text-green-600' : 'text-red-600'">
                      {{ passwordValidation.length ? '✓' : '✗' }} 至少8位字符
                    </li>
                    <li :class="passwordValidation.lowercase ? 'text-green-600' : 'text-red-600'">
                      {{ passwordValidation.lowercase ? '✓' : '✗' }} 包含小写字母
                    </li>
                    <li :class="passwordValidation.uppercase ? 'text-green-600' : 'text-red-600'">
                      {{ passwordValidation.uppercase ? '✓' : '✗' }} 包含大写字母
                    </li>
                    <li :class="passwordValidation.number ? 'text-green-600' : 'text-red-600'">
                      {{ passwordValidation.number ? '✓' : '✗' }} 包含数字
                    </li>
                    <li :class="passwordValidation.special ? 'text-green-600' : 'text-red-600'">
                      {{ passwordValidation.special ? '✓' : '✗' }} 包含特殊字符
                    </li>
                  </ul>
                </div>
              </div>
              <form @submit.prevent="changePassword" class="space-y-4">
                <BaseInput
                  v-model="passwordForm.currentPassword"
                  type="password"
                  label="当前密码"
                  placeholder="请输入当前密码"
                />
                
                <BaseInput
                  v-model="passwordForm.newPassword"
                  type="password"
                  label="新密码"
                  placeholder="请输入新密码"
                />
                
                <BaseInput
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  label="确认新密码"
                  placeholder="请再次输入新密码"
                />
                
                <div class="flex justify-end">
                  <BaseButton type="submit" variant="primary" :loading="loading.password">
                    更新密码
                  </BaseButton>
                </div>
              </form>
            </div>

            <!-- 两步验证 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">两步验证</h3>
              <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="h-10 w-10 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                    <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">短信验证</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300">通过短信接收验证码</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="status-badge status-completed text-xs">已启用</span>
                  <BaseButton @click="toggleTwoFactor" variant="secondary" size="sm">
                    禁用
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- 登录记录 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近登录记录</h3>
              <div class="space-y-3">
                <div v-for="(record, index) in loginRecords" :key="index" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                      <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{{ record.device }}</p>
                      <p class="text-xs text-gray-600 dark:text-gray-400">{{ record.time }} • {{ record.location }}</p>
                    </div>
                  </div>
                  <span v-if="record.current" class="status-badge status-completed text-xs">当前会话</span>
                  <BaseButton v-else variant="danger" size="sm">终止</BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 偏好设置标签页 -->
        <div v-show="activeTab === 'preferences'" class="tab-content">
          <div class="card-body space-y-8">
            <!-- 界面设置 -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">界面设置</h3>
              <div class="space-y-6">
                <!-- 主题设置 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <BaseInput
                    v-model="preferences.language"
                    type="select"
                    label="显示语言"
                    :options="[
                      { value: 'zh-CN', label: '简体中文' },
                      { value: 'en-US', label: 'English' }
                    ]"
                  />
                  
                  <BaseInput
                    v-model="preferences.timezone"
                    type="select"
                    label="时区"
                    :options="[
                      { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
                      { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
                      { value: 'UTC', label: '世界标准时间 (UTC)' },
                      { value: 'America/New_York', label: '纽约时间 (UTC-5)' }
                    ]"
                  />
                </div>
                
                <!-- 主题选择 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    主题外观
                  </label>
                  <div class="grid grid-cols-3 gap-4">
                    <div @click="setTheme('light')" 
                         :class="['cursor-pointer p-4 rounded-lg border-2 transition-all', 
                                  preferences.theme === 'light' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700']">
                      <div class="flex items-center justify-center mb-2">
                        <div class="w-8 h-8 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center">
                          <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      </div>
                      <p class="text-sm text-center text-gray-700 dark:text-gray-300">浅色主题</p>
                    </div>
                    
                    <div @click="setTheme('dark')" 
                         :class="['cursor-pointer p-4 rounded-lg border-2 transition-all', 
                                  preferences.theme === 'dark' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700']">
                      <div class="flex items-center justify-center mb-2">
                        <div class="w-8 h-8 bg-gray-800 rounded-full border-2 border-gray-600 flex items-center justify-center">
                          <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                          </svg>
                        </div>
                      </div>
                      <p class="text-sm text-center text-gray-700 dark:text-gray-300">深色主题</p>
                    </div>
                    
                    <div @click="setTheme('system')" 
                         :class="['cursor-pointer p-4 rounded-lg border-2 transition-all', 
                                  preferences.theme === 'system' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700']">
                      <div class="flex items-center justify-center mb-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-white to-gray-800 rounded-full border-2 border-gray-400 flex items-center justify-center">
                          <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      </div>
                      <p class="text-sm text-center text-gray-700 dark:text-gray-300">跟随系统</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工作流设置 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">工作流设置</h3>
              
              <!-- 默认分析类型 -->
              <div class="mb-8">
                <BaseInput
                  v-model="preferences.defaultAnalysis"
                  type="select"
                  label="默认分析类型"
                  :options="[
                    { value: 'paper_check', label: '论文检测' },
                    { value: 'format_check', label: '格式检查' },
                    { value: 'structure_check', label: '结构分析' },
                    { value: 'comprehensive', label: '综合检查' }
                  ]"
                />
              </div>

              <!-- 任务管理设置 -->
              <div class="mb-8">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  任务管理
                </h4>
                <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6 space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">自动刷新任务状态</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">自动更新任务进度和状态</p>
                    </div>
                    <label class="custom-checkbox">
                      <input v-model="preferences.autoRefresh" type="checkbox">
                      <span class="checkbox-visual"></span>
                    </label>
                  </div>
                  
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">显示处理详情</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">在任务列表中显示详细的处理步骤</p>
                      </div>
                      <label class="custom-checkbox">
                        <input v-model="preferences.showDetails" type="checkbox">
                        <span class="checkbox-visual"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 用户体验设置 -->
              <div class="mb-8">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2m0 0V1a1 1 0 011-1h4a1 1 0 011 1v3M7 4H5a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2h-2"/>
                  </svg>
                  用户体验
                </h4>
                <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6 space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">紧凑模式</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">使用更紧凑的界面布局</p>
                    </div>
                    <label class="custom-checkbox">
                      <input v-model="preferences.compactMode" type="checkbox">
                      <span class="checkbox-visual"></span>
                    </label>
                  </div>
                  
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">高级模式</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">显示更多高级功能和选项</p>
                      </div>
                      <label class="custom-checkbox">
                        <input v-model="preferences.advancedMode" type="checkbox">
                        <span class="checkbox-visual"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自动化功能 -->
              <div class="mb-8">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                  自动化功能
                </h4>
                <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6 space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">自动保存草稿</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">自动保存用户输入的内容</p>
                    </div>
                    <label class="custom-checkbox">
                      <input v-model="preferences.autoSave" type="checkbox">
                      <span class="checkbox-visual"></span>
                    </label>
                  </div>
                  
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">声音提醒</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">任务完成时播放提示音</p>
                      </div>
                      <label class="custom-checkbox">
                        <input v-model="preferences.soundNotifications" type="checkbox">
                        <span class="checkbox-visual"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 性能设置 -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-8 mb-8">
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg class="w-5 h-5 mr-2 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                  性能优化
                </h4>
                <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-6 space-y-4">
                  <div class="flex items-center justify-between">
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">启用动画效果</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">页面切换和交互动画</p>
                    </div>
                    <label class="custom-checkbox">
                      <input v-model="preferences.animations" type="checkbox">
                      <span class="checkbox-visual"></span>
                    </label>
                  </div>
                  
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium text-gray-900 dark:text-white">预加载资源</p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">提前加载常用资源以提高性能</p>
                      </div>
                      <label class="custom-checkbox">
                        <input v-model="preferences.preload" type="checkbox">
                        <span class="checkbox-visual"></span>
                      </label>
                    </div>
                  </div>
                  
                  <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <BaseInput
                      v-model="preferences.maxConcurrentTasks"
                      type="select"
                      label="最大并发任务数"
                      :options="[
                        { value: 1, label: '1个任务' },
                        { value: 2, label: '2个任务' },
                        { value: 3, label: '3个任务' },
                        { value: 5, label: '5个任务' }
                      ]"
                    />
                  </div>
                </div>
              </div>

              <!-- 保存按钮 -->
              <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-600">
                <BaseButton @click="savePreferences" variant="primary" :loading="loading.preferences">
                  保存偏好设置
                </BaseButton>
              </div>
            </div>

            <div class="flex justify-end border-t dark:border-gray-600 pt-6">
              <BaseButton @click="savePreferences" variant="primary" :loading="loading.preferences">
                保存设置
              </BaseButton>
            </div>
          </div>
        </div>

        <!-- 通知设置标签页 -->
        <div v-show="activeTab === 'notifications'" class="tab-content">
          <div class="card-body space-y-8">
            <!-- 邮件通知设置 -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">邮件通知</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">任务完成通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">当文档分析完成时发送邮件</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.emailTaskComplete" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">任务失败通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">当分析过程中出现错误时发送邮件</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.emailTaskFailed" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">支付成功通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">当订单支付成功时发送邮件</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.emailPaymentSuccess" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">周报通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">每周发送使用统计报告</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.emailWeeklyReport" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">系统更新通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">当系统有重要更新时发送邮件</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.emailSystemUpdate" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 浏览器通知设置 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                浏览器通知
                <span class="ml-2 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300 px-2 py-1 rounded">
                  需要浏览器权限
                </span>
              </h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">任务完成通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">在浏览器中显示桌面通知</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.browserTaskComplete" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">支付成功通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">支付完成时显示桌面通知</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.browserPaymentSuccess" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">系统更新通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">系统更新时显示桌面通知</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.browserSystemUpdate" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">浏览器通知权限</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">
                        {{ notificationPermission === 'granted' ? '已允许' : notificationPermission === 'denied' ? '已拒绝' : '待授权' }}
                      </p>
                    </div>
                  </div>
                  <BaseButton 
                    @click="requestNotificationPermission" 
                    variant="secondary" 
                    size="sm"
                    v-if="notificationPermission === 'default'"
                  >
                    请求权限
                  </BaseButton>
                </div>
              </div>
            </div>

            <!-- 短信通知设置 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                短信通知
                <span class="ml-2 text-xs bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 px-2 py-1 rounded">
                  需要绑定手机
                </span>
              </h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">登录异常提醒</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">发现异常登录行为时发送短信</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.smsLogin" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                      <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900 dark:text-white">支付成功通知</p>
                      <p class="text-sm text-gray-600 dark:text-gray-300">大额支付成功时发送短信确认</p>
                    </div>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.smsImportant" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
              </div>
            </div>

            <!-- 通知时间设置 -->
            <div class="border-t dark:border-gray-600 pt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">通知时间设置</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">勿扰模式</p>
                    <p class="text-sm text-gray-600 dark:text-gray-300">在指定时间段内不发送通知</p>
                  </div>
                  <label class="custom-checkbox">
                    <input v-model="notifications.doNotDisturb" type="checkbox">
                    <span class="checkbox-visual"></span>
                  </label>
                </div>
                
                <div v-if="notifications.doNotDisturb" class="grid grid-cols-2 gap-4 pl-4">
                  <BaseInput
                    v-model="notifications.doNotDisturbStart"
                    type="text"
                    label="开始时间"
                    placeholder="22:00"
                  />
                  <BaseInput
                    v-model="notifications.doNotDisturbEnd"
                    type="text"
                    label="结束时间"
                    placeholder="08:00"
                  />
                </div>
              </div>
            </div>

            <div class="flex justify-end border-t dark:border-gray-600 pt-6">
              <BaseButton @click="saveNotifications" variant="primary" :loading="loading.notifications">
                保存通知设置
              </BaseButton>
            </div>
          </div>
        </div>
      </BaseCard>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseInput from '@/components/BaseInput.vue'
import { $notify } from '@/utils/useNotifications'
import UserStatsChart from '@/components/UserStatsChart.vue'
import { userApi } from '@/services/userApi'

const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()

// 用户菜单相关
const userMenuRef = ref<HTMLElement>()
const userMenuOpen = ref(false)

// 活跃标签页
const activeTab = ref('profile')

// 表单数据
const profileData = reactive({
  username: '',
  email: '',
  realname: '',
  phone: '',
  organization: '',
  bio: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const preferences = reactive({
  language: 'zh-CN' as 'zh-CN' | 'en-US',
  timezone: 'Asia/Shanghai',
  defaultAnalysis: 'paper_check',
  autoRefresh: true,
  showDetails: true,
  theme: 'system' as 'light' | 'dark' | 'system',
  animations: true,
  preload: true,
  maxConcurrentTasks: 3,
  soundNotifications: true,
  autoSave: true,
  compactMode: false,
  advancedMode: false
})

const notifications = reactive({
  emailTaskComplete: true,
  emailTaskFailed: true,
  emailPaymentSuccess: false,
  emailWeeklyReport: false,
  emailSystemUpdate: false,
  smsLogin: true,
  smsImportant: false,
  doNotDisturb: false,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00',
  browserTaskComplete: false,
  browserPaymentSuccess: false,
  browserSystemUpdate: false
})

// 密码显示状态
const showPasswords = reactive({
  current: false,
  new: false,
  confirm: false
})

// 登录记录
const loginRecords = ref([
  {
    device: 'Windows Chrome',
    time: '2024-01-15 14:30',
    location: '北京',
    current: true
  },
  {
    device: 'iPhone Safari',
    time: '2024-01-14 20:15',
    location: '上海',
    current: false
  }
])

// 加载状态
const loading = reactive({
  profile: false,
  password: false,
  stats: false,
  loginRecords: false,
  preferences: false,
  notifications: false
})

// 用户统计数据
const userStats = reactive({
  totalAnalyses: 0,
  successfulAnalyses: 0,
  usageTime: 0,
  savedTime: 0
})

// 安全评分
const securityScore = ref(85)

// 密码强度相关
const passwordStrength = ref({
  score: 0,
  level: 'weak' as 'weak' | 'medium' | 'strong',
  label: '弱'
})

const passwordValidation = ref({
  length: false,
  lowercase: false,
  uppercase: false,
  number: false,
  special: false
})

// 最近活动
const recentActivities = ref([
  {
    description: '完成了论文格式检查',
    time: '2小时前'
  },
  {
    description: '上传了新的文档',
    time: '4小时前'
  },
  {
    description: '更新了个人资料',
    time: '1天前'
  },
  {
    description: '完成了文档分析',
    time: '2天前'
  },
  {
    description: '创建了新账户',
    time: '1周前'
  }
])

// 浏览器通知权限状态
const notificationPermission = ref<NotificationPermission>('default')

// 检查浏览器通知权限
const checkNotificationPermission = () => {
  if ('Notification' in window) {
    notificationPermission.value = Notification.permission
  }
}

// 请求浏览器通知权限
const requestNotificationPermission = async () => {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission()
    notificationPermission.value = permission
    if (permission === 'granted') {
      $notify.success('浏览器通知权限已授权')
    } else {
      $notify.warning('浏览器通知权限被拒绝')
    }
  }
}

// 表单错误
const errors = reactive({
  username: '',
  email: ''
})

// 切换用户菜单
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    userMenuOpen.value = false
  }
}

// 切换标签页
const switchTab = (tabName: string) => {
  activeTab.value = tabName
}

// 切换密码可见性
const togglePassword = (type: 'current' | 'new' | 'confirm') => {
  showPasswords[type] = !showPasswords[type]
}

// 更新个人资料
const updateProfile = async () => {
  // 清空错误
  errors.username = ''
  errors.email = ''

  // 验证数据
  if (!profileData.username.trim()) {
    errors.username = '用户名不能为空'
    return
  }
  if (!profileData.email.trim()) {
    errors.email = '邮箱不能为空'
    return
  }

  loading.profile = true
  try {
    const updatePayload = {
      username: profileData.username,
      email: profileData.email,
      realname: profileData.realname,
      phone: profileData.phone,
      organization: profileData.organization,
      bio: profileData.bio
    }
    
    await userApi.updateProfile(updatePayload)
    await userStore.fetchCurrentUser() // 刷新用户信息
    $notify.success('个人资料已成功更新')
  } catch (error: any) {
    const message = error.response?.data?.message || '更新个人资料失败，请重试'
    $notify.error(message)
    console.error("Profile update failed:", error)
  } finally {
    loading.profile = false
  }
}

// 修改密码
const changePassword = async () => {
  const { currentPassword, newPassword, confirmPassword } = passwordForm
  
  // 验证密码
  if (!currentPassword) {
    $notify.warning('请输入当前密码')
    return
  }

  if (!newPassword) {
    $notify.warning('请输入新密码')
    return
  }

  if (newPassword !== confirmPassword) {
    $notify.warning('新密码与确认密码不一致')
    return
  }

  if (newPassword.length < 8) {
    $notify.warning('新密码长度至少8位')
    return
  }

  loading.password = true
  try {
    await userApi.changePassword({
      current_password: currentPassword,
      new_password: newPassword
    })
    
    $notify.success('密码修改成功')
    
    // 清空表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
  } catch (error: any) {
    const message = error.response?.data?.message || '密码修改失败，请重试'
    $notify.error(message)
    console.error("Password change failed:", error)
  } finally {
    loading.password = false
  }
}

// 上传头像
const uploadAvatar = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e: Event) => {
    const target = e.target as HTMLInputElement
    const file = target.files?.[0]
    if (file) {
      $notify.success('头像上传成功')
    }
  }
  input.click()
}

// 切换两步验证
const toggleTwoFactor = () => {
  $notify.warning('确定要禁用两步验证吗？这可能会降低您账户的安全性。', {
    persistent: true,
    title: '安全确认'
  })
  // 注：这里可以考虑实现一个确认对话框组件
  $notify.warning('两步验证已禁用')
}

// 保存偏好设置
const savePreferences = async () => {
  loading.preferences = true
  try {
    await userApi.updatePreferences(preferences)
    localStorage.setItem('userPreferences', JSON.stringify(preferences))
    $notify.success('偏好设置已保存')
  } catch (error) {
    console.error('Failed to save preferences:', error)
    $notify.error('保存偏好设置失败')
  } finally {
    loading.preferences = false
  }
}

// 保存通知设置
const saveNotifications = async () => {
  loading.notifications = true
  try {
    await userApi.updateNotificationSettings({
      email: {
        analysisComplete: notifications.emailTaskComplete,
        paymentSuccess: notifications.emailPaymentSuccess,
        weeklyReport: notifications.emailWeeklyReport,
        systemUpdate: notifications.emailSystemUpdate
      },
      browser: {
        analysisComplete: notifications.browserTaskComplete,
        paymentSuccess: notifications.browserPaymentSuccess,
        systemUpdate: notifications.browserSystemUpdate
      },
      sms: {
        loginAlert: notifications.smsLogin,
        paymentSuccess: notifications.smsImportant
      }
    })
    localStorage.setItem('userNotifications', JSON.stringify(notifications))
    $notify.success('通知设置已保存')
  } catch (error) {
    console.error('Failed to save notification settings:', error)
    $notify.error('保存通知设置失败')
  } finally {
    loading.notifications = false
  }
}

// 设置主题
const setTheme = (theme: 'light' | 'dark' | 'system') => {
  preferences.theme = theme
  // 这里可以添加主题切换逻辑
  $notify.success(`已切换到${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '系统'}主题`)
}

// 加载登录记录
const loadLoginRecords = async () => {
  loading.loginRecords = true
  try {
    const records = await userApi.getLoginRecords(5)
    loginRecords.value = records.map(record => ({
      device: record.device || 'Unknown Device',
      time: new Date(record.login_time).toLocaleString('zh-CN'),
      location: record.location || '未知位置',
      current: record.is_current
    }))
  } catch (error) {
    // 静默处理API不可用的情况，使用模拟数据
    loginRecords.value = [
      {
        device: 'Windows Chrome',
        time: '2024-01-15 14:30',
        location: '北京',
        current: true
      },
      {
        device: 'iPhone Safari',
        time: '2024-01-14 20:15',
        location: '上海',
        current: false
      }
    ]
  } finally {
    loading.loginRecords = false
  }
}

// 初始化用户偏好设置
const initializePreferences = async () => {
  try {
    // 尝试从API加载偏好设置
    const userPrefs = await userApi.getPreferences()
    Object.assign(preferences, userPrefs)
  } catch (error) {
    // 静默处理API不可用的情况，从localStorage加载
    const saved = localStorage.getItem('userPreferences')
    if (saved) {
      Object.assign(preferences, JSON.parse(saved))
    }
  }
}

// 初始化通知设置
const initializeNotifications = async () => {
  try {
    // 尝试从API加载通知设置
    const userNotifications = await userApi.getNotificationSettings()
    Object.assign(notifications, userNotifications)
  } catch (error) {
    // 静默处理API不可用的情况，从localStorage加载
    const saved = localStorage.getItem('userNotifications')
    if (saved) {
      Object.assign(notifications, JSON.parse(saved))
    }
  }
}

// 获取上次登录时间
const getLastLoginTime = () => {
  const currentRecord = loginRecords.value.find(record => record.current)
  if (currentRecord) {
    return currentRecord.time
  }
  return loginRecords.value.length > 0 ? loginRecords.value[0].time : '未知'
}

// 加载用户统计数据
const loadUserStats = async () => {
  loading.stats = true
  try {
    const stats = await userApi.getUserStats()
    Object.assign(userStats, stats)
  } catch (error) {
    // 静默处理API不可用的情况，使用模拟数据
    Object.assign(userStats, {
      totalAnalyses: 42,
      successfulAnalyses: 38,
      usageTime: 1200,
      savedTime: 3600
    })
  } finally {
    loading.stats = false
  }
}

// 格式化相对时间
const formatRelativeTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

// 退出登录
const logout = () => {
  userStore.logout()
  router.push('/auth')
}

// 监听用户数据变化，并更新表单
watch(() => userStore.currentUser, (newUser) => {
  if (newUser) {
    profileData.username = newUser.username || ''
    profileData.email = newUser.email || ''
    profileData.realname = newUser.full_name || ''
    // 注意: phone, organization, bio 等字段在当前后端模型中不存在
    // 如果这些是纯前端状态，可以从localStorage加载
  }
}, { immediate: true, deep: true });

// 计算密码强度
const calculatePasswordStrength = (password: string) => {
  passwordValidation.value = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  }
  
  const validCount = Object.values(passwordValidation.value).filter(Boolean).length
  const score = Math.round((validCount / 5) * 100)
  
  let level: 'weak' | 'medium' | 'strong' = 'weak'
  let label = '弱'
  
  if (score >= 80) {
    level = 'strong'
    label = '强'
  } else if (score >= 60) {
    level = 'medium'
    label = '中等'
  }
  
  passwordStrength.value = { score, level, label }
}

// 监听密码变化
watch(() => passwordForm.newPassword, (newPassword) => {
  if (newPassword) {
    calculatePasswordStrength(newPassword)
  } else {
    passwordStrength.value = { score: 0, level: 'weak', label: '弱' }
    passwordValidation.value = {
      length: false,
      lowercase: false,
      uppercase: false,
      number: false,
      special: false
    }
  }
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  loadLoginRecords()
  loadUserStats()
  initializePreferences()
  initializeNotifications()
  checkNotificationPermission()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>