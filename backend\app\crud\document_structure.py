"""
文档结构统计 CRUD 操作
"""

import os
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func
from sqlalchemy.orm import selectinload

from app.models.document_structure import (
    DocumentStructure, DocumentStructureCreate, DocumentStructureInDB,
    DocumentContentCache, DocumentStructureStats
)
from app.database.models import DocumentStructureTable, DocumentContentCacheTable
from app.core.logging import logger


async def create_document_structure(
    session: AsyncSession, 
    structure_data: DocumentStructureCreate
) -> DocumentStructure:
    """创建文档结构统计记录"""
    try:
        db_structure = DocumentStructureTable(**structure_data.model_dump())
        session.add(db_structure)
        await session.commit()
        await session.refresh(db_structure)
        
        return DocumentStructure.model_validate(db_structure)
    except Exception as e:
        await session.rollback()
        logger.error(f"创建文档结构统计失败: {str(e)}")
        raise


async def create_document_structures_batch(
    session: AsyncSession,
    structures_data: List[DocumentStructureCreate]
) -> List[DocumentStructure]:
    """批量创建文档结构统计记录"""
    try:
        db_structures = [
            DocumentStructureTable(**structure.model_dump()) 
            for structure in structures_data
        ]
        session.add_all(db_structures)
        await session.commit()
        
        # 刷新所有对象以获取生成的ID
        for db_structure in db_structures:
            await session.refresh(db_structure)
        
        return [DocumentStructure.model_validate(db_structure) for db_structure in db_structures]
    except Exception as e:
        await session.rollback()
        logger.error(f"批量创建文档结构统计失败: {str(e)}")
        raise


async def get_document_structures(
    session: AsyncSession, 
    document_id: str
) -> List[DocumentStructure]:
    """获取文档的所有结构统计"""
    try:
        result = await session.execute(
            select(DocumentStructureTable)
            .where(DocumentStructureTable.document_id == document_id)
            .order_by(DocumentStructureTable.page_number, DocumentStructureTable.paragraph_index)
        )
        db_structures = result.scalars().all()
        
        return [DocumentStructure.model_validate(db_structure) for db_structure in db_structures]
    except Exception as e:
        logger.error(f"获取文档结构统计失败: {str(e)}")
        return []


async def get_document_structure_stats(
    session: AsyncSession, 
    document_id: str
) -> DocumentStructureStats:
    """获取文档结构统计汇总"""
    try:
        # 获取所有结构
        structures = await get_document_structures(session, document_id)
        
        # 计算统计信息
        total_structures = len(structures)
        standard_structures = len([s for s in structures if s.structure_type == "standard"])
        non_standard_structures = len([s for s in structures if s.structure_type == "non-standard"])
        missing_structures = len([s for s in structures if s.status == "missing"])
        extra_structures = len([s for s in structures if s.status == "extra"])
        total_words = sum(s.word_count for s in structures)
        total_references = sum(s.reference_count for s in structures)
        
        return DocumentStructureStats(
            document_id=document_id,
            total_structures=total_structures,
            standard_structures=standard_structures,
            non_standard_structures=non_standard_structures,
            missing_structures=missing_structures,
            extra_structures=extra_structures,
            total_words=total_words,
            total_references=total_references,
            structures=structures
        )
    except Exception as e:
        logger.error(f"获取文档结构统计汇总失败: {str(e)}")
        return DocumentStructureStats(document_id=document_id)


async def delete_document_structures(
    session: AsyncSession, 
    document_id: str
) -> bool:
    """删除文档的所有结构统计"""
    try:
        await session.execute(
            delete(DocumentStructureTable)
            .where(DocumentStructureTable.document_id == document_id)
        )
        await session.commit()
        return True
    except Exception as e:
        await session.rollback()
        logger.error(f"删除文档结构统计失败: {str(e)}")
        return False


async def create_document_content_cache(
    session: AsyncSession,
    cache_data: DocumentContentCache
) -> DocumentContentCache:
    """创建文档内容缓存记录"""
    try:
        db_cache = DocumentContentCacheTable(**cache_data.model_dump())
        session.add(db_cache)
        await session.commit()
        await session.refresh(db_cache)
        
        return DocumentContentCache.model_validate(db_cache)
    except Exception as e:
        await session.rollback()
        logger.error(f"创建文档内容缓存失败: {str(e)}")
        raise


async def get_document_content_cache(
    session: AsyncSession,
    document_id: str,
    content_type: str = "full_content"
) -> Optional[DocumentContentCache]:
    """获取文档内容缓存"""
    try:
        result = await session.execute(
            select(DocumentContentCacheTable)
            .where(
                DocumentContentCacheTable.document_id == document_id,
                DocumentContentCacheTable.content_type == content_type
            )
        )
        db_cache = result.scalar_one_or_none()
        
        if db_cache:
            return DocumentContentCache.model_validate(db_cache)
        return None
    except Exception as e:
        logger.error(f"获取文档内容缓存失败: {str(e)}")
        return None


async def delete_document_content_cache(
    session: AsyncSession,
    document_id: str
) -> bool:
    """删除文档内容缓存"""
    try:
        # 先获取缓存记录以删除文件
        result = await session.execute(
            select(DocumentContentCacheTable)
            .where(DocumentContentCacheTable.document_id == document_id)
        )
        caches = result.scalars().all()
        
        # 删除缓存文件
        for cache in caches:
            if os.path.exists(cache.file_path):
                try:
                    os.remove(cache.file_path)
                    logger.info(f"删除缓存文件: {cache.file_path}")
                except Exception as e:
                    logger.warning(f"删除缓存文件失败: {cache.file_path} - {str(e)}")
        
        # 删除数据库记录
        await session.execute(
            delete(DocumentContentCacheTable)
            .where(DocumentContentCacheTable.document_id == document_id)
        )
        await session.commit()
        return True
    except Exception as e:
        await session.rollback()
        logger.error(f"删除文档内容缓存失败: {str(e)}")
        return False


def save_document_content_to_file(content_data: Dict[str, Any], file_path: str) -> bool:
    """将文档内容保存到文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 保存为JSON格式
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(content_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"文档内容已保存到: {file_path}")
        return True
    except Exception as e:
        logger.error(f"保存文档内容到文件失败: {str(e)}")
        return False


def load_document_content_from_file(file_path: str) -> Optional[Dict[str, Any]]:
    """从文件加载文档内容"""
    try:
        if not os.path.exists(file_path):
            logger.warning(f"缓存文件不存在: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content_data = json.load(f)
        
        logger.info(f"从文件加载文档内容: {file_path}")
        return content_data
    except Exception as e:
        logger.error(f"从文件加载文档内容失败: {str(e)}")
        return None
