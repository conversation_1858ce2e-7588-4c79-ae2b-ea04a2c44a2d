"""
文件上传处理模块 (已废弃)

提供文件接收、验证、临时存储等功能

!!! 注意: 此模块已被废弃 !!!

文件上传逻辑已于 2024-05-20 统一重构并整合到
`app.api.v1.documents.upload_document` 接口中，
直接使用统一配置进行文件存储，不再通过此处理器。

请勿在此模块基础上进行任何新的开发。
此文件仅为历史参考保留，并将在未来版本中被完全删除。

"""

import os
import tempfile
import uuid
from typing import Dict, List, Optional, Any, Union, BinaryIO
from pathlib import Path
from datetime import datetime
import structlog
import aiofiles
from fastapi import UploadFile, HTTPException

from app.core.config import settings
from app.core.exceptions import FileHandlerError, ValidationError, SecurityError
from app.core.security import check_file_security, is_file_safe
from app.services.storage import get_storage_manager, StorageManager

logger = structlog.get_logger()

# 文件上传配置
MAX_UPLOAD_SIZE = getattr(settings, 'MAX_UPLOAD_SIZE', 50 * 1024 * 1024)  # 50MB
TEMP_UPLOAD_DIR = getattr(settings, 'TEMP_UPLOAD_DIR', './data/temp_uploads')
CHUNK_SIZE = getattr(settings, 'UPLOAD_CHUNK_SIZE', 8192)  # 8KB chunks

# 支持的文件类型
SUPPORTED_EXTENSIONS = {'.docx', '.doc'}
SUPPORTED_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
}

class FileUploadHandler:
    """文件上传处理器"""
    
    def __init__(self, storage_manager: StorageManager = None):
        """
        初始化文件上传处理器
        
        Args:
            storage_manager: 存储管理器实例
        """
        self.storage_manager = storage_manager or get_storage_manager()
        self.temp_dir = Path(TEMP_UPLOAD_DIR)
        
        # 上传统计
        self.upload_stats = {
            'total_uploads': 0,
            'successful_uploads': 0,
            'failed_uploads': 0,
            'total_bytes_uploaded': 0,
            'security_blocks': 0,
            'validation_errors': 0
        }
        
        # 初始化临时目录
        self._initialize_temp_dir()
    
    def _initialize_temp_dir(self):
        """初始化临时上传目录"""
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"临时上传目录初始化完成: {self.temp_dir}")
        except Exception as e:
            logger.error(f"临时上传目录初始化失败: {str(e)}")
            raise FileHandlerError(f"临时上传目录初始化失败: {str(e)}")
    
    async def handle_upload(
        self,
        upload_file: UploadFile,
        task_id: str = None,
        perform_security_check: bool = True,
        auto_cleanup: bool = True
    ) -> Dict[str, Any]:
        """
        处理文件上传
        
        Args:
            upload_file: FastAPI上传文件对象
            task_id: 关联的任务ID
            perform_security_check: 是否执行安全检查
            auto_cleanup: 是否自动清理临时文件
            
        Returns:
            上传处理结果
            
        Raises:
            FileHandlerError: 文件处理失败
            SecurityError: 安全检查失败
            ValidationError: 验证失败
        """
        temp_file_path = None
        
        try:
            self.upload_stats['total_uploads'] += 1
            
            # 基础验证
            upload_info = await self._validate_upload_file(upload_file)
            
            logger.info(f"开始处理文件上传: {upload_info['filename']}")
            
            # 创建临时文件
            temp_file_path = await self._save_temp_file(upload_file)
            
            # 文件大小检查
            file_size = Path(temp_file_path).stat().st_size
            if file_size > MAX_UPLOAD_SIZE:
                raise ValidationError(f"文件大小超过限制: {file_size} > {MAX_UPLOAD_SIZE}")
            
            # 安全检查
            if perform_security_check:
                try:
                    security_result = check_file_security(temp_file_path, upload_info['filename'])
                    if not security_result['is_safe']:
                        self.upload_stats['security_blocks'] += 1
                        raise SecurityError(f"文件安全检查失败: {', '.join(security_result['risk_reasons'])}")
                except SecurityError:
                    raise
                except Exception as e:
                    logger.warning(f"安全检查异常，但继续处理: {str(e)}")
            
            # 存储文件
            storage_result = self.storage_manager.store_uploaded_file(
                source_path=temp_file_path,
                original_filename=upload_info['filename'],
                task_id=task_id,
                perform_security_check=False  # 已经检查过了
            )
            
            # 更新统计
            self.upload_stats['successful_uploads'] += 1
            self.upload_stats['total_bytes_uploaded'] += file_size
            
            # 构建结果
            result = {
                'file_id': storage_result['file_id'],
                'original_filename': upload_info['filename'],
                'file_size': file_size,
                'content_type': upload_info['content_type'],
                'storage_path': storage_result['storage_path'],
                'relative_path': storage_result['relative_path'],
                'task_id': task_id,
                'uploaded_at': datetime.utcnow().isoformat(),
                'security_checked': perform_security_check,
                'upload_info': upload_info
            }
            
            logger.info(f"文件上传处理成功: {upload_info['filename']} -> {storage_result['file_id']}")
            
            return result
            
        except Exception as e:
            self.upload_stats['failed_uploads'] += 1
            if isinstance(e, ValidationError):
                self.upload_stats['validation_errors'] += 1
            
            logger.error(f"文件上传处理失败: {str(e)}")
            raise e
            
        finally:
            # 清理临时文件
            if auto_cleanup and temp_file_path and Path(temp_file_path).exists():
                try:
                    Path(temp_file_path).unlink()
                    logger.debug(f"临时文件清理成功: {temp_file_path}")
                except Exception as e:
                    logger.warning(f"临时文件清理失败: {str(e)}")
    
    async def _validate_upload_file(self, upload_file: UploadFile) -> Dict[str, Any]:
        """
        验证上传文件
        
        Args:
            upload_file: 上传文件对象
            
        Returns:
            文件信息字典
            
        Raises:
            ValidationError: 验证失败
        """
        try:
            # 检查文件名
            if not upload_file.filename:
                raise ValidationError("文件名不能为空")
            
            filename = upload_file.filename.strip()
            if not filename:
                raise ValidationError("文件名不能为空")
            
            # 检查文件扩展名
            file_ext = Path(filename).suffix.lower()
            if file_ext not in SUPPORTED_EXTENSIONS:
                raise ValidationError(f"不支持的文件类型: {file_ext}，支持的类型: {', '.join(SUPPORTED_EXTENSIONS)}")
            
            # 检查MIME类型
            content_type = upload_file.content_type
            if content_type and content_type not in SUPPORTED_MIME_TYPES:
                logger.warning(f"MIME类型不匹配: {content_type}，但继续处理")
            
            # 检查文件大小（如果可用）
            if hasattr(upload_file, 'size') and upload_file.size:
                if upload_file.size > MAX_UPLOAD_SIZE:
                    raise ValidationError(f"文件大小超过限制: {upload_file.size} > {MAX_UPLOAD_SIZE}")
            
            return {
                'filename': filename,
                'content_type': content_type,
                'file_extension': file_ext,
                'declared_size': getattr(upload_file, 'size', None)
            }
            
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            raise ValidationError(f"文件验证失败: {str(e)}")
    
    async def _save_temp_file(self, upload_file: UploadFile) -> str:
        """
        保存临时文件
        
        Args:
            upload_file: 上传文件对象
            
        Returns:
            临时文件路径
            
        Raises:
            FileHandlerError: 保存失败
        """
        try:
            # 生成临时文件名
            temp_id = str(uuid.uuid4())
            file_ext = Path(upload_file.filename).suffix.lower()
            temp_filename = f"upload_{temp_id}{file_ext}"
            temp_file_path = self.temp_dir / temp_filename
            
            # 重置文件指针
            await upload_file.seek(0)
            
            # 异步保存文件
            async with aiofiles.open(temp_file_path, 'wb') as temp_file:
                while True:
                    chunk = await upload_file.read(CHUNK_SIZE)
                    if not chunk:
                        break
                    await temp_file.write(chunk)
            
            logger.debug(f"临时文件保存成功: {temp_file_path}")
            return str(temp_file_path)
            
        except Exception as e:
            logger.error(f"临时文件保存失败: {str(e)}")
            raise FileHandlerError(f"临时文件保存失败: {str(e)}")
    
    def get_upload_stats(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        stats = self.upload_stats.copy()
        
        # 计算成功率
        if stats['total_uploads'] > 0:
            stats['success_rate'] = (stats['successful_uploads'] / stats['total_uploads']) * 100
        else:
            stats['success_rate'] = 0
        
        # 添加配置信息
        stats['configuration'] = {
            'max_upload_size_mb': MAX_UPLOAD_SIZE / (1024 * 1024),
            'supported_extensions': list(SUPPORTED_EXTENSIONS),
            'temp_dir': str(self.temp_dir),
            'chunk_size': CHUNK_SIZE
        }
        
        return stats

# 全局文件上传处理器实例
_file_handler = None

def get_file_handler() -> FileUploadHandler:
    """获取全局文件上传处理器实例"""
    global _file_handler
    if _file_handler is None:
        _file_handler = FileUploadHandler()
    return _file_handler

async def handle_file_upload(
    upload_file: UploadFile,
    task_id: str = None,
    perform_security_check: bool = True
) -> Dict[str, Any]:
    """
    快捷函数：处理文件上传
    
    Args:
        upload_file: 上传文件对象
        task_id: 任务ID
        perform_security_check: 是否执行安全检查
        
    Returns:
        上传结果
    """
    handler = get_file_handler()
    return await handler.handle_upload(upload_file, task_id, perform_security_check)
