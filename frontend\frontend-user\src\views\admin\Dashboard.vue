<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-red-600 dark:text-red-400 font-medium">仪表盘</router-link>
            <router-link to="/admin/users" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">用户管理</router-link>
            <router-link to="/admin/documents" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">任务监控</router-link>
            <router-link to="/admin/system" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">系统设置</router-link>
            <router-link to="/admin/reports" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">报告中心</router-link>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
          系统仪表盘
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          实时监控系统运行状态和业务数据
        </p>
      </div>

      <!-- 核心指标卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ stats.totalUsers.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">总用户数</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ +{{ stats.newUsersToday }} 今日新增</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ stats.totalDocuments.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">文档处理总数</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ +{{ stats.documentsToday }} 今日</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">¥{{ stats.monthlyRevenue.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">本月收入</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">↑ +12.5%</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ stats.systemUptime }}%</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">系统可用性</p>
            <p class="text-xs text-green-600 dark:text-green-400 mt-1">优秀</p>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 左侧内容 -->
        <div class="space-y-6">
          <!-- 用户增长趋势 -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">用户增长趋势</h2>
            </div>
            <div class="card-body">
              <div class="h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <svg class="h-16 w-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">用户增长图表展示区域</p>
                  <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">过去30天新增用户: {{ stats.monthlyNewUsers }}人</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 实时任务监控 -->
          <div class="card">
            <div class="card-header">
              <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">实时任务监控</h2>
                <button @click="refreshTasks" class="btn btn-secondary btn-sm">
                  <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  刷新
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ taskStats.queued }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">队列中</p>
                </div>
                <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ taskStats.processing }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-400">处理中</p>
                </div>
              </div>
              <div class="space-y-4">
                <div v-for="task in recentTasks" :key="task.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <div :class="[
                        'h-2 w-2 rounded-full',
                        task.status === 'completed' ? 'bg-green-500' : 
                        task.status === 'processing' ? 'bg-yellow-500 animate-pulse' : 
                        task.status === 'failed' ? 'bg-red-500' : 'bg-gray-400'
                      ]"></div>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{{ task.name }}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">{{ task.user }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ task.time }}</p>
                    <span :class="[
                      'inline-flex px-2 py-1 text-xs font-medium rounded-full',
                      task.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                      task.status === 'processing' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                      task.status === 'pending' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                      task.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    ]">
                      {{ getStatusText(task.status) }}
                    </span>
                  </div>
                </div>
                
                <div v-if="recentTasks.length === 0" class="text-center py-8">
                  <svg class="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                  <p class="text-gray-500 dark:text-gray-400">暂无运行中的任务</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容 -->
        <div class="space-y-6">
          <!-- 系统状态 -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">系统状态</h2>
            </div>
            <div class="card-body">
              <div class="space-y-4">
                <div v-for="metric in systemMetrics" :key="metric.name" class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-300">{{ metric.name }}</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        :class="[
                          'h-2 rounded-full transition-all duration-300',
                          metric.value >= 80 ? 'bg-red-500' :
                          metric.value >= 60 ? 'bg-yellow-500' : 'bg-green-500'
                        ]"
                        :style="{ width: metric.value + '%' }"
                      ></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ metric.value }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 最近登录用户 -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">最近登录用户</h2>
            </div>
            <div class="card-body">
              <div class="space-y-3">
                <div v-for="user in recentUsers" :key="user.id" class="flex items-center space-x-3">
                  <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                    <span class="text-blue-600 dark:text-blue-400 font-medium text-sm">{{ user.name.charAt(0) }}</span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ user.name }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.email }}</p>
                  </div>
                  <div class="text-right">
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ user.loginTime }}</p>
                    <div class="flex items-center">
                      <div class="h-2 w-2 bg-green-400 rounded-full mr-1"></div>
                      <span class="text-xs text-green-600 dark:text-green-400">在线</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">快速操作</h2>
            </div>
            <div class="card-body">
              <div class="grid grid-cols-2 gap-3">
                <button @click="$router.push('/admin/users')" class="btn btn-outline-primary text-sm">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                  </svg>
                  用户管理
                </button>
                <button @click="$router.push('/admin/tasks')" class="btn btn-outline-primary text-sm">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  任务监控
                </button>
                <button @click="$router.push('/admin/system')" class="btn btn-outline-primary text-sm">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  </svg>
                  系统设置
                </button>
                <button @click="$router.push('/admin/reports')" class="btn btn-outline-primary text-sm">
                  <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                  报告中心
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'

const router = useRouter()
const themeStore = useThemeStore()

// 组件状态
const showAdminMenu = ref(false)

// 系统统计数据
const stats = reactive({
  totalUsers: 1247,
  newUsersToday: 23,
  totalDocuments: 8945,
  documentsToday: 156,
  monthlyRevenue: 45890,
  systemUptime: 98.5,
  monthlyNewUsers: 342
})

// 系统性能指标
const systemMetrics = reactive([
  { name: 'CPU 使用率', value: 45 },
  { name: '内存使用率', value: 68 },
  { name: '磁盘使用率', value: 32 },
  { name: '网络带宽', value: 25 }
])

// 任务统计
const taskStats = ref({
  queued: 23,
  processing: 15
})

// 模拟最近任务数据
const recentTasks = ref([
  {
    id: 'T20240118-089',
    name: '任务 #T20240118-089',
    user: '论文格式检测 • 服务器01',
    status: 'processing',
    time: '2分钟前'
  },
  {
    id: 'T20240118-090',
    name: '任务 #T20240118-090',
    user: '结构分析 • 服务器02',
    status: 'pending',
    time: '5分钟前'
  },
  {
    id: 'T20240118-088',
    name: '任务 #T20240118-088',
    user: '内容提取 • 服务器01',
    status: 'completed',
    time: '10分钟前'
  }
])

// 最近登录用户
const recentUsers = ref([
  { id: 1, name: '张三', email: '<EMAIL>', loginTime: '5分钟前' },
  { id: 2, name: '李四', email: '<EMAIL>', loginTime: '10分钟前' },
  { id: 3, name: '王五', email: '<EMAIL>', loginTime: '15分钟前' },
  { id: 4, name: '赵六', email: '<EMAIL>', loginTime: '20分钟前' }
])

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: { [key: string]: string } = {
    'completed': '已完成',
    'processing': '处理中',
    'pending': '队列中',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

// 刷新任务列表
const refreshTasks = () => {
  // 模拟刷新数据
  console.log('刷新任务列表...')
}

// 管理员登出
const handleAdminLogout = () => {
  adminLogout(router)
}

// 页面初始化
onMounted(() => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/dashboard')) {
    return
  }

  // 模拟数据定时更新
  const interval = setInterval(() => {
    // 更新一些动态数据
    stats.newUsersToday = Math.floor(Math.random() * 50) + 10
    stats.documentsToday = Math.floor(Math.random() * 200) + 100
    
    // 更新系统性能指标
    systemMetrics.forEach(metric => {
      metric.value = Math.floor(Math.random() * 100)
    })
  }, 30000) // 30秒更新一次

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
/* 管理员特有样式 */
</style> 