<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页码位置显示优化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        .before, .after {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .before { border-color: #ef4444; }
        .after { border-color: #10b981; }
        .header {
            padding: 15px;
            font-weight: bold;
            color: white;
        }
        .before .header { background: #ef4444; }
        .after .header { background: #10b981; }
        .content {
            padding: 20px;
        }
        .fragment-group {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            overflow: hidden;
        }
        .group-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        .fragment-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: grid;
            grid-template-columns: 40px 1fr 120px 100px 1fr;
            gap: 15px;
            align-items: start;
        }
        .fragment-item:last-child {
            border-bottom: none;
        }
        .original-text {
            font-family: monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 8px;
        }
        .position-info {
            font-size: 12px;
            color: #6b7280;
        }
        .position-bad {
            color: #ef4444;
            background: #fef2f2;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .position-good {
            color: #10b981;
            background: #ecfdf5;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .improvement {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .improvement h3 {
            color: #10b981;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .category-badge {
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .severity-badge {
            background: #ef4444;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 页码位置显示优化</h1>
        <p>将位置信息从冗余的"位置：中文关键词段落"改为实用的"位置：第3页"，提升用户体验。</p>

        <div class="improvement">
            <h3>🎯 优化理由</h3>
            <ul class="feature-list">
                <li><strong>避免冗余</strong>：问题片段已按结构分组，无需重复显示结构信息</li>
                <li><strong>实用价值</strong>：页码信息帮助用户快速定位到文档具体位置</li>
                <li><strong>用户习惯</strong>：用户更习惯用页码来定位文档内容</li>
                <li><strong>智能估算</strong>：即使没有精确页码，也能根据位置估算页码</li>
            </ul>
        </div>

        <h2>📊 优化前后对比</h2>
        <div class="comparison">
            <div class="before">
                <div class="header">❌ 优化前：冗余信息</div>
                <div class="content">
                    <div class="fragment-group">
                        <div class="group-header">📋 中文关键词 (1个问题)</div>
                        <div class="fragment-item">
                            <div>1</div>
                            <div>
                                <div class="original-text">关键词:人工智能;机器学习</div>
                                <div class="position-info">
                                    <span class="position-bad">位置: 中文关键词段落</span>
                                </div>
                            </div>
                            <div>
                                <div class="category-badge">对齐方式问题</div>
                            </div>
                            <div>左对齐</div>
                            <div>当前为两端对齐，应为左对齐</div>
                        </div>
                    </div>
                    <div style="color: #ef4444; margin-top: 10px;">
                        ❌ 问题：已经在"中文关键词"分组下，再显示"中文关键词段落"是冗余的
                    </div>
                </div>
            </div>

            <div class="after">
                <div class="header">✅ 优化后：实用信息</div>
                <div class="content">
                    <div class="fragment-group">
                        <div class="group-header">📋 中文关键词 (1个问题)</div>
                        <div class="fragment-item">
                            <div>1</div>
                            <div>
                                <div class="original-text">关键词:人工智能;机器学习</div>
                                <div class="position-info">
                                    <span class="position-good">位置: 第3页</span>
                                </div>
                            </div>
                            <div>
                                <div class="category-badge">对齐方式问题</div>
                            </div>
                            <div>左对齐</div>
                            <div>当前为两端对齐，应为左对齐</div>
                        </div>
                    </div>
                    <div style="color: #10b981; margin-top: 10px;">
                        ✅ 改进：显示页码信息，用户可以快速跳转到第3页查看问题
                    </div>
                </div>
            </div>
        </div>

        <h2>🔍 页码获取策略</h2>
        <div id="page-strategies"></div>

        <h2>📋 技术实现</h2>
        <div class="code-block">
            <h3>1. 页码获取优先级</h3>
            <pre>
def _generate_position_description(self, result, position, original_text, document_data):
    """生成用户友好的位置描述"""
    
    # 🔥 优先级1：从文档元素中获取精确页码
    page_number = self._get_page_number_from_position(position, document_data)
    if page_number and page_number > 0:
        return f"第{page_number}页"
    
    # 🔥 优先级2：从检测结果详情中获取页码
    if result.details:
        problems = result.details.get("problems", [])
        if problems:
            problem_position = problems[0].get("position", 0)
            page_num = self._get_page_number_from_position(problem_position, document_data)
            if page_num:
                return f"第{page_num}页"
    
    # 🔥 优先级3：根据位置估算页码
    estimated_page = self._estimate_page_from_position(position, document_data)
    if estimated_page > 0:
        return f"第{estimated_page}页"
    
    # 🔥 优先级4：显示段落序号
    paragraph_number = self._get_paragraph_number_from_position(position, document_data)
    if paragraph_number > 0:
        return f"第{paragraph_number}段"
    
    # 最后回退到默认描述
    return f"文档位置 {position}"
            </pre>
        </div>

        <div class="code-block">
            <h3>2. 页码估算算法</h3>
            <pre>
def _estimate_page_from_position(self, position, document_data):
    """根据位置估算页码"""
    # 假设每页约2000个字符（可根据实际情况调整）
    chars_per_page = 2000
    estimated_page = max(1, (position // chars_per_page) + 1)
    
    # 边界检查：不超过文档总页数
    if document_data.doc_info:
        total_pages = document_data.doc_info.get("page_count", 0)
        if total_pages > 0:
            estimated_page = min(estimated_page, total_pages)
    
    return estimated_page
            </pre>
        </div>

        <div class="code-block">
            <h3>3. 前端显示逻辑（无需修改）</h3>
            <pre>
&lt;div class="mt-1 text-xs text-gray-500"&gt;
  &lt;span v-if="fragment.position_description"&gt;
    位置: {{ fragment.position_description }}  &lt;!-- 现在显示"第3页" --&gt;
  &lt;/span&gt;
  &lt;span v-else&gt;
    位置: {{ fragment.position }}
  &lt;/span&gt;
&lt;/div&gt;
            </pre>
        </div>
    </div>

    <script>
        // 展示页码获取策略
        function showPageStrategies() {
            const strategies = [
                {
                    strategy: "精确页码",
                    description: "从文档元素的page_number字段获取",
                    example: "元素包含page_number: 3",
                    result: "第3页",
                    priority: 1
                },
                {
                    strategy: "检测结果页码",
                    description: "从检测结果的详细信息中获取",
                    example: "problems[0].position对应的页码",
                    result: "第2页",
                    priority: 2
                },
                {
                    strategy: "位置估算",
                    description: "根据字符位置估算页码",
                    example: "位置4500 ÷ 2000字符/页 = 第3页",
                    result: "第3页",
                    priority: 3
                },
                {
                    strategy: "段落序号",
                    description: "显示段落序号作为备选",
                    example: "第15段",
                    result: "第15段",
                    priority: 4
                },
                {
                    strategy: "内容位置",
                    description: "根据内容类型显示位置",
                    example: "关键词部分、摘要部分",
                    result: "关键词部分",
                    priority: 5
                }
            ]

            let html = `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #ddd; padding: 12px;">优先级</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">策略</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">描述</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">示例</th>
                        <th style="border: 1px solid #ddd; padding: 12px;">显示结果</th>
                    </tr>
                </thead>
                <tbody>
            `

            strategies.forEach((item) => {
                html += `
                <tr>
                    <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
                        <span style="background: #3b82f6; color: white; padding: 4px 8px; border-radius: 4px;">${item.priority}</span>
                    </td>
                    <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">${item.strategy}</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">${item.description}</td>
                    <td style="border: 1px solid #ddd; padding: 12px; font-family: monospace; background: #f8f9fa;">${item.example}</td>
                    <td style="border: 1px solid #ddd; padding: 12px;">
                        <span class="position-good">${item.result}</span>
                    </td>
                </tr>
                `
            })

            html += `
                </tbody>
            </table>
            <div style="color: #10b981; margin-top: 15px;">
                ✅ 系统按优先级依次尝试，确保总能显示有意义的位置信息！
            </div>
            `

            document.getElementById('page-strategies').innerHTML = html
        }

        // 页面加载时显示策略表格
        document.addEventListener('DOMContentLoaded', showPageStrategies)
    </script>
</body>
</html>
