# Word文档分析服务 - Cursor AI 编程助手规则
# 更新时间: 2024-12-19
# 项目状态: 生产就绪 (后端93.8%验证通过，前端90%功能完成)

## 🎯 项目总览

### 项目状态 (已完成深度清理)
- **项目类型**: 企业级全栈Web应用
- **后端状态**: ✅ 93.8%验证通过 (30/32测试) - 生产就绪
- **前端状态**: ✅ 90%功能完成 - 企业级质量
- **文档状态**: ✅ 100%准确 - 无重复内容
- **清理状态**: ✅ 已删除125个冗余文件 (约4.9MB)
- **技术债务**: 极低水平，项目健康

### 核心业务功能
- Word文档智能分析和格式检测
- 论文规范性检查 (GB/T 7714、GB/T 7713标准)
- 多用户认证和权限管理
- 实时任务处理和状态追踪
- 企业级管理后台
- 订阅付费和订单管理

### 技术架构
**后端技术栈**:
- **Python 3.12** + **FastAPI 0.104.1** (异步Web框架)
- **PostgreSQL 17.5** + **SQLAlchemy 2.0.23** + **asyncpg** (异步数据库)
- **Redis 5.0+** (缓存和任务队列)
- **pywin32** (Word COM接口)
- **JWT** (用户认证) + **WebSocket** (实时通信)

**前端技术栈**:
- **Vue 3.5.17** + **TypeScript** (类型安全)
- **Vite 7.0.0** (构建工具) + **Tailwind CSS 3.4.0** (样式)
- **Pinia** (状态管理) + **Vue Router** (路由)
- **Axios** (HTTP客户端) + **WebSocket** (实时通信)

## 📁 项目结构 (已清理优化)

```
paper-check-win/
├── backend/                 # 后端服务 (✅ 93.8%验证通过)
│   ├── app/
│   │   ├── api/v1/         # API路由 (8个模块，33个端点)
│   │   ├── core/           # 核心配置 (日志、安全、性能等)
│   │   ├── database/       # 数据库层 (异步CRUD)
│   │   ├── models/         # Pydantic数据模型
│   │   ├── services/       # 业务逻辑服务
│   │   ├── analyzers/      # 文档分析器
│   │   ├── checkers/       # 论文检测器
│   │   ├── tasks/          # 异步任务管理
│   │   └── main.py         # 应用入口
│   ├── docs/               # 后端文档 (14个核心文档)
│   ├── tests/              # 测试代码 (完整覆盖)
│   ├── config/             # 配置文件
│   ├── deploy/             # 部署配置
│   └── requirements.txt    # 依赖管理
├── frontend/               # 前端项目 (✅ 90%功能完成)
│   ├── docs/               # 前端文档 (10个核心文档，无重复)
│   └── frontend-user/      # Vue3应用 (纯净项目)
│       ├── src/
│       │   ├── views/      # 页面 (用户端15个 + 管理端7个)
│       │   ├── components/ # 组件 (50+可复用组件)
│       │   ├── services/   # API服务层 (完整)
│       │   ├── stores/     # Pinia状态管理
│       │   ├── types/      # TypeScript类型定义
│       │   └── utils/      # 工具函数
│       └── package.json    # 依赖管理
└── docs/                   # 项目总体文档
```

后端服务状态检测url：http://localhost:8000/health
后端服务和前端服务无需启动，由开发者自行启动。

## ⚠️ 关键操作规则 (AI助手必须遵循)

### 🚨 目录切换规则 (重要!)
```bash
# 后端操作 - 必须先切换到backend目录
cd backend
python -m uvicorn app.main:app --reload

# 前端操作 - 必须先切换到frontend/frontend-user目录
cd frontend/frontend-user
npm run dev

# 错误示例 ❌ - 直接在根目录执行会失败
python -m uvicorn app.main:app --reload  # 会报错
```

### 📍 服务地址规范
- **后端API**: http://localhost:8000 (已启动并验证)
- **API文档**: http://localhost:8000/docs (Swagger UI)
- **前端应用**: http://localhost:3000 (用户已启动)
- **管理端**: http://localhost:3000/admin/

### 🔄 AI助手工作流程
1. **分析任务** - 明确要求和目标
2. **检查状态** - 确认服务运行状态
3. **选择目录** - 根据任务切换到正确目录
4. **执行操作** - 按规范执行开发任务
5. **验证结果** - 确认功能正常
6. **更新文档** - 必要时更新相关文档

## 💻 开发规范和最佳实践

### Python 后端开发规范
```python
# ✅ 正确的代码风格
async def get_document_by_id(
    document_id: str, 
    session: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
) -> StandardResponse[Document]:
    """
    获取文档详情
    
    Args:
        document_id: 文档ID
        session: 数据库会话
        user_id: 当前用户ID
        
    Returns:
        StandardResponse[Document]: 文档信息
        
    Raises:
        DocumentNotFoundError: 文档不存在
    """
    try:
        document = await crud.get_document(session, document_id, user_id)
        if not document:
            raise DocumentNotFoundError(f"Document {document_id} not found")
        return StandardResponse.success(data=document)
    except Exception as e:
        logger.error(f"Failed to get document {document_id}: {str(e)}")
        raise
```

**后端开发要求**:
- 使用 **async/await** 模式
- 所有API函数必须包含 `session: AsyncSession = Depends(get_db)`
- 所有保护的API必须包含 `user_id: str = Depends(get_current_user_id)`
- 使用 **类型注解** 和 **docstring**
- 遵循 **PEP 8** 代码风格
- 使用 **结构化日志**

### TypeScript 前端开发规范
```typescript
// ✅ 正确的代码风格
interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: number
  request_id: string
}

const documentApi = {
  async getDocuments(params: DocumentQueryParams): Promise<ApiResponse<Document[]>> {
    try {
      const response = await apiClient.get('/api/v1/documents', { params })
      return response.data
    } catch (error) {
      console.error('Failed to fetch documents:', error)
      throw error
    }
  }
}
```

**前端开发要求**:
- 使用 **TypeScript** 严格模式
- 优先使用 **Composition API**
- 遵循 **Vue3 最佳实践**
- 使用 **Tailwind CSS** 原子化样式
- 实现 **错误边界** 和 **加载状态**

### 数据库操作规范
```python
# ✅ 正确的CRUD操作
async def create_document(
    session: AsyncSession,
    user_id: str,
    document_data: DocumentCreate
) -> Document:
    """创建文档记录"""
    try:
        # 使用命名参数防止SQL注入
        query = text("""
            INSERT INTO documents (id, user_id, filename, status, created_at)
            VALUES (:id, :user_id, :filename, :status, :created_at)
            RETURNING *
        """)
        
        result = await session.execute(query, {
            "id": generate_document_id(),
            "user_id": user_id,
            "filename": document_data.filename,
            "status": "uploaded",
            "created_at": datetime.utcnow()
        })
        
        await session.commit()
        return result.fetchone()
        
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to create document: {str(e)}")
        raise
```

## 🛡️ 安全和质量标准

### 认证和授权
- 所有API端点都已实现JWT认证保护
- 使用 `get_current_user_id` 依赖注入获取用户身份
- 实现了多级权限控制（用户/管理员）

### 输入验证
- 使用Pydantic模型验证所有输入
- 实现SQL注入防护（命名参数）
- 文件上传安全检查

### 错误处理
```python
# ✅ 标准错误处理模式
try:
    result = await some_operation()
    return StandardResponse.success(data=result)
except SpecificError as e:
    logger.warning(f"Expected error: {str(e)}")
    return StandardResponse.error(code=400, message=str(e))
except Exception as e:
    logger.error(f"Unexpected error: {str(e)}")
    return StandardResponse.error(code=500, message="Internal server error")
```

## 🔧 常见问题和解决方案

### 后端常见问题
1. **模块导入错误**
   ```bash
   # 问题: ModuleNotFoundError: No module named 'app'
   # 解决: 必须在backend目录下执行
   cd backend
   python -m uvicorn app.main:app --reload
   ```

2. **数据库连接问题**
   ```python
   # 确保session正确传递
   async def api_function(session: AsyncSession = Depends(get_db)):
       # 使用session进行数据库操作
   ```

3. **认证问题**
   ```python
   # 确保所有保护的API都有认证参数
   async def protected_api(user_id: str = Depends(get_current_user_id)):
       # API逻辑
   ```

### 前端常见问题
1. **API调用错误**
   ```typescript
   // 确保使用正确的baseURL
   const apiClient = axios.create({
     baseURL: 'http://localhost:8000'
   })
   ```

2. **路由问题**
   ```typescript
   // 确保路由配置正确
   const router = createRouter({
     history: createWebHistory(),
     routes: [...]
   })
   ```

## 📊 性能和监控

### 性能优化要求
- 后端API平均响应时间 < 150ms
- 前端首屏加载时间 < 2s
- 数据库查询优化（避免N+1问题）
- 实现适当的缓存策略

### 监控和日志
- 使用结构化JSON日志
- 记录请求ID用于链路追踪
- 实现健康检查端点
- 错误监控和告警

## 🚀 部署和运维

### 开发环境启动
```bash
# 后端服务
cd backend
uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload

# 前端服务  
cd frontend/frontend-user
npm run dev
```

### 生产环境部署
- 使用Docker容器化部署
- Nginx反向代理和负载均衡
- PostgreSQL主从复制
- Redis集群缓存

## 📝 文档维护要求

### AI助手文档更新规则
1. **代码变更后** - 及时更新相关API文档
2. **新功能完成** - 更新功能说明和使用指南
3. **配置修改** - 同步更新配置文档
4. **重要决策** - 记录设计决策和变更历史

### 文档结构维护
- 保持文档索引的实时准确性
- 删除过时和重复的文档
- 确保所有文档链接有效
- 维护版本信息的一致性

## 🎯 AI助手专用指令

### 任务执行优先级
1. **🔴 高优先级** - 安全问题、服务异常、数据错误
2. **🟡 中优先级** - 功能开发、性能优化、UI改进
3. **🟢 低优先级** - 文档完善、代码重构、工具优化

### 代码审查标准
- 代码规范性检查
- 安全漏洞扫描
- 性能影响评估
- 测试覆盖率验证

### 自动化要求
- 执行操作前先验证环境
- 操作完成后进行功能验证
- 重要变更前创建备份
- 异常情况时提供详细诊断

## 🔄 项目当前优先级

### 立即执行 (高优先级)
1. **支付系统API对接** - 前端paymentApi.ts已有基础
2. **API错误处理优化** - 统一前端错误处理机制
3. **前后端联调测试** - 验证核心业务流程

### 计划执行 (中优先级)
1. **端到端测试验证** - 完整业务流程测试
2. **性能优化和监控** - 系统性能调优
3. **用户体验完善** - UI/UX细节优化

### 未来规划 (低优先级)
1. **多文档格式支持** - 扩展PDF、Excel等
2. **AI智能检测** - 集成大语言模型
3. **移动端APP** - iOS/Android应用

## ✅ 成功验证标准

### 功能验证
- API响应正确且性能达标
- 前端界面正常且交互流畅
- 数据库操作正确且一致
- 安全机制有效且可靠

### 质量验证
- 代码通过所有测试用例
- 无安全漏洞和性能问题
- 文档准确且实时更新
- 用户体验符合企业标准

---

**规则版本**: v2.0  
**最后更新**: 2024-12-19  
**项目状态**: 🟢 生产就绪，可全速开发  
**技术债务**: 极低水平，项目健康  

**AI助手请严格遵循以上规则，确保高效准确地执行开发任务！** 