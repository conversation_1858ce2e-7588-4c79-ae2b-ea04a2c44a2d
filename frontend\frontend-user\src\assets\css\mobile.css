/* 移动端触摸优化样式 */

/* 基础触摸优化 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  position: relative;
}

.touch-target::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 44px;
  height: 44px;
  z-index: -1;
}

/* 触摸反馈 */
.touch-feedback {
  transform: scale(1);
  transition: transform 0.1s ease-out;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* 移动端安全区域 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 移动端容器 */
.mobile-container {
  max-width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 移动端卡片 */
.mobile-card {
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.mobile-card:last-child {
  margin-bottom: 0;
}

/* 移动端按钮样式 */
.mobile-btn {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  transition: all 0.15s ease-in-out;
}

.mobile-btn:active {
  transform: scale(0.98);
}

.mobile-btn-primary {
  background-color: #3b82f6;
  color: white;
}

.mobile-btn-primary:hover {
  background-color: #2563eb;
}

.mobile-btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.mobile-btn-secondary:hover {
  background-color: #e5e7eb;
}

/* 移动端输入框 */
.mobile-input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  font-size: 1rem;
  line-height: 1.5rem;
  transition: border-color 0.15s ease-in-out;
}

.mobile-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 移动端导航栏 */
.mobile-navbar {
  height: 56px;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 40;
}

.mobile-navbar.dark {
  background-color: #1f2937;
  border-bottom-color: #374151;
}

/* 移动端底部导航 */
.mobile-tab-bar {
  height: 64px;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  padding-bottom: env(safe-area-inset-bottom);
}

.mobile-tab-bar.dark {
  background-color: #1f2937;
  border-top-color: #374151;
}

/* 移动端网格布局 */
.mobile-grid {
  display: grid;
  gap: 1rem;
  padding: 1rem;
}

.mobile-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.mobile-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* 移动端文本样式 */
.mobile-heading {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  margin-bottom: 0.5rem;
}

.mobile-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.25rem;
}

.mobile-body {
  font-size: 0.875rem;
  line-height: 1.5rem;
}

/* 移动端滚动优化 */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* 移动端模态框 */
.mobile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 50;
}

.mobile-modal-content {
  background-color: white;
  border-radius: 1rem 1rem 0 0;
  width: 100%;
  max-height: 90vh;
  padding: 1rem;
  padding-bottom: calc(1rem + env(safe-area-inset-bottom));
}

.mobile-modal-content.dark {
  background-color: #1f2937;
}

/* 移动端加载状态 */
.mobile-skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 移动端滑动手势 */
.mobile-swipe {
  touch-action: pan-y;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 移动端列表优化 */
.mobile-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.mobile-list-item {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease-in-out;
}

.mobile-list-item:hover {
  background-color: #f9fafb;
}

.mobile-list-item:last-child {
  border-bottom: none;
}

.mobile-list-item.dark {
  border-bottom-color: #374151;
}

.mobile-list-item.dark:hover {
  background-color: #374151;
}

/* 移动端表单优化 */
.mobile-form {
  padding: 1rem;
}

.mobile-form-group {
  margin-bottom: 1rem;
}

.mobile-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.mobile-form-label.dark {
  color: #d1d5db;
}

/* 移动端通知优化 */
.mobile-notification {
  position: fixed;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  z-index: 50;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

.mobile-notification.enter {
  transform: translateY(-100%);
  opacity: 0;
}

.mobile-notification.enter-active {
  transform: translateY(0);
  opacity: 1;
}

.mobile-notification.leave-active {
  transform: translateY(-100%);
  opacity: 0;
}

/* 移动端键盘适配 */
.mobile-keyboard-padding {
  padding-bottom: 0;
  transition: padding-bottom 0.3s ease-in-out;
}

.mobile-keyboard-padding.keyboard-open {
  padding-bottom: 20rem;
}

/* 移动端下拉刷新 */
.mobile-pull-refresh {
  position: relative;
  overflow: hidden;
}

.mobile-pull-refresh-indicator {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

.mobile-pull-refresh-indicator.active {
  top: 10px;
}

/* 移动端工具栏 */
.mobile-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 30;
}

.mobile-toolbar.dark {
  background-color: #1f2937;
  border-bottom-color: #374151;
}

/* 移动端优化的媒体查询 */
@media (max-width: 768px) {
  .desktop-only {
    display: none !important;
  }
  
  .mobile-only {
    display: block !important;
  }
  
  .mobile-flex {
    display: flex !important;
  }
  
  .mobile-hidden {
    display: none !important;
  }
  
  /* 移动端文本大小调整 */
  h1 {
    font-size: 1.5rem;
    line-height: 2rem;
  }
  
  h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  h3 {
    font-size: 1.125rem;
    line-height: 1.5rem;
  }
  
  /* 移动端间距调整 */
  .mobile-spacing {
    padding: 1rem;
  }
  
  .mobile-spacing-sm {
    padding: 0.5rem;
  }
  
  .mobile-spacing-lg {
    padding: 1.5rem;
  }
}

/* 移动端暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .mobile-card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .mobile-input {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .mobile-btn-secondary {
    background-color: #374151;
    color: #f9fafb;
  }
  
  .mobile-btn-secondary:hover {
    background-color: #4b5563;
  }
} 