# 新规则添加指南

本指南详细说明如何在重构后的检测引擎中添加新的检测规则。新的规则系统采用分层结构和引用机制，提供了更好的可维护性和可扩展性。

## 1. 规则系统架构概述

### 1.1 分层结构

新的规则系统采用分层的JSON配置结构：

```
规则文件 (hbkj_bachelor_2024.json)
├── metadata          # 元数据信息
├── definitions        # 共享定义
│   ├── properties    # 属性定义
│   ├── styles        # 样式定义
│   └── patterns      # 模式定义
├── rules             # 规则定义
│   ├── structure     # 结构检查规则
│   ├── format        # 格式检查规则
│   └── content       # 内容检查规则
└── execution_plan    # 执行计划
```

### 1.2 引用机制

使用JSON Pointer (`$ref`) 实现配置复用：

```json
{
  "definitions": {
    "styles": {
      "title_style": {
        "font_family": "黑体",
        "font_size": 16,
        "bold": true,
        "alignment": "center"
      }
    }
  },
  "rules": {
    "format": {
      "level_1_title": {
        "name": "一级标题格式检查",
        "check_function": "check_headings_by_level",
        "parameters": {
          "level": 1,
          "style": {"$ref": "#/definitions/styles/title_style"}
        }
      }
    }
  }
}
```

## 2. 添加新规则的步骤

### 2.1 第一步：确定规则类型

根据检查内容确定规则类型：

- **structure**: 结构检查（章节顺序、完整性等）
- **format**: 格式检查（字体、字号、对齐等）
- **content**: 内容检查（字数、关键词等）

### 2.2 第二步：定义共享配置（可选）

如果规则需要复用的配置，先在 `definitions` 中定义：

```json
{
  "definitions": {
    "properties": {
      "body_font": {
        "font_family": "宋体",
        "font_size": 12,
        "line_spacing": 1.5
      }
    },
    "styles": {
      "body_text_style": {
        "$ref": "#/definitions/properties/body_font",
        "alignment": "justify",
        "first_line_indent": 24
      }
    }
  }
}
```

### 2.3 第三步：添加规则定义

在相应的规则类别下添加新规则：

```json
{
  "rules": {
    "format": {
      "new_rule_name": {
        "name": "规则显示名称",
        "check_function": "对应的检查函数名",
        "parameters": {
          "param1": "value1",
          "param2": {"$ref": "#/definitions/styles/some_style"}
        }
      }
    }
  }
}
```

### 2.4 第四步：更新执行计划

将新规则添加到执行计划中：

```json
{
  "execution_plan": [
    {
      "stage": "第一阶段：结构完整性与顺序检查",
      "rules": [
        {"$ref": "#/rules/structure/section_order"}
      ]
    },
    {
      "stage": "第二阶段：格式检查",
      "rules": [
        {"$ref": "#/rules/format/new_rule_name"}
      ]
    }
  ]
}
```

### 2.5 第五步：实现检查函数（如需要）

如果使用新的检查函数，需要在 `document_analyzer.py` 中实现：

```python
def check_new_feature(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    新功能检查函数。
    
    Args:
        doc_data: 文档数据
        params: 检查参数（已解析引用）
        
    Returns:
        CheckResult: 检查结果
    """
    rule_id = params.get("rule_id", "format.new_feature")
    rule_name = params.get("rule_name", "新功能检查")
    
    # 实现检查逻辑
    # ...
    
    return CheckResult(
        rule_id=rule_id,
        rule_name=rule_name,
        passed=True,  # 或 False
        severity=CheckSeverity.INFO,  # 或其他级别
        message="检查结果消息"
    )
```

然后在 `CHECK_FUNCTIONS` 字典中注册：

```python
CHECK_FUNCTIONS = {
    # ... 其他函数
    "check_new_feature": check_new_feature,
}
```

## 3. 规则配置详解

### 3.1 规则基本结构

每个规则必须包含以下字段：

```json
{
  "name": "规则显示名称",
  "check_function": "检查函数名",
  "parameters": {
    // 检查参数
  }
}
```

### 3.2 参数类型

支持的参数类型：

- **基本类型**: 字符串、数字、布尔值
- **对象**: 嵌套的配置对象
- **数组**: 配置列表
- **引用**: `{"$ref": "路径"}`

### 3.3 引用路径格式

引用路径使用JSON Pointer格式：

- `#/definitions/properties/font_size` - 引用属性定义
- `#/definitions/styles/title_style` - 引用样式定义
- `#/rules/format/some_rule` - 引用其他规则

### 3.4 引用合并策略

当本地配置与引用配置冲突时，本地配置优先：

```json
{
  "style": {
    "$ref": "#/definitions/styles/base_style",
    "font_size": 14  // 覆盖引用中的font_size
  }
}
```

## 4. 常见规则模式

### 4.1 格式检查规则

```json
{
  "format": {
    "heading_format": {
      "name": "标题格式检查",
      "check_function": "check_headings_by_level",
      "parameters": {
        "level": 1,
        "style": {
          "font_family": "黑体",
          "font_size": 16,
          "bold": true,
          "alignment": "center"
        }
      }
    }
  }
}
```

### 4.2 内容检查规则

```json
{
  "content": {
    "word_count": {
      "name": "字数检查",
      "check_function": "check_content_length",
      "parameters": {
        "min": 8000,
        "max": 12000,
        "unit": "字",
        "errorMessage": "正文字数应在{min}-{max}字之间，当前：{current_count}字"
      }
    }
  }
}
```

### 4.3 结构检查规则

```json
{
  "structure": {
    "section_order": {
      "name": "章节顺序检查",
      "check_function": "check_section_order",
      "parameters": {
        "standard_structure": [
          {"name": "摘要", "required": true, "identifiers": ["摘要"]},
          {"name": "目录", "required": true, "identifiers": ["目录"]},
          {"name": "正文", "required": true, "identifiers": ["第", "章"]}
        ]
      }
    }
  }
}
```

## 5. 最佳实践

### 5.1 命名规范

- **规则ID**: 使用小写字母和下划线，如 `level_1_title_format`
- **显示名称**: 使用中文，简洁明了，如 "一级标题格式检查"
- **函数名**: 使用 `check_` 前缀，如 `check_headings_by_level`

### 5.2 参数设计

- 使用引用避免重复配置
- 提供有意义的错误消息模板
- 支持可选参数和默认值

### 5.3 错误处理

- 使用统一的错误格式化系统
- 提供具体的改进建议
- 区分错误严重程度

### 5.4 测试验证

添加新规则后，务必进行测试：

```python
# 创建测试用例
def test_new_rule():
    rule_engine = RuleEngine(CHECK_FUNCTIONS)
    rule_engine.load_rules_from_file("config/rules/hbkj_bachelor_2024.json")
    
    # 创建测试文档
    test_doc = DocumentData(...)
    
    # 执行检测
    results = await rule_engine.execute_check(test_doc)
    
    # 验证结果
    assert any(r.rule_id == "format.new_rule_name" for r in results)
```

## 6. 故障排除

### 6.1 常见错误

1. **引用路径错误**
   ```
   ConfigurationError: 无效的引用路径: #/definitions/invalid/path
   ```
   检查引用路径是否正确，确保被引用的配置存在。

2. **循环引用**
   ```
   ConfigurationError: 检测到循环引用
   ```
   检查引用链，确保没有循环依赖。

3. **检查函数未注册**
   ```
   DocumentAnalysisException: 未知的检查函数: check_unknown_function
   ```
   确保检查函数已在 `CHECK_FUNCTIONS` 中注册。

### 6.2 调试技巧

1. **使用日志**: 检查规则加载和执行日志
2. **单步测试**: 单独测试新添加的规则
3. **参数验证**: 确认传递给检查函数的参数正确

## 7. 示例：添加页边距检查规则

以下是一个完整的示例，展示如何添加页边距检查规则：

### 7.1 添加共享定义

```json
{
  "definitions": {
    "properties": {
      "standard_margins": {
        "top": "2.5cm",
        "bottom": "2.5cm", 
        "left": "3cm",
        "right": "2cm"
      }
    }
  }
}
```

### 7.2 添加规则

```json
{
  "rules": {
    "format": {
      "page_margins": {
        "name": "页边距检查",
        "check_function": "check_page_setup",
        "parameters": {
          "page_setup": {
            "margin_top": {"$ref": "#/definitions/properties/standard_margins/top"},
            "margin_bottom": {"$ref": "#/definitions/properties/standard_margins/bottom"},
            "margin_left": {"$ref": "#/definitions/properties/standard_margins/left"},
            "margin_right": {"$ref": "#/definitions/properties/standard_margins/right"}
          }
        }
      }
    }
  }
}
```

### 7.3 更新执行计划

```json
{
  "execution_plan": [
    {
      "stage": "第三阶段：全局与局部格式精细化检查",
      "rules": [
        {"$ref": "#/rules/format/page_margins"}
      ]
    }
  ]
}
```

这样就完成了一个新规则的添加。新的规则系统提供了强大的配置能力和良好的可维护性，使得添加和修改规则变得更加简单和安全。
