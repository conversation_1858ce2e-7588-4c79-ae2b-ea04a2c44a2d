<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <AppNavbar :isAuthPage="true" />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <!-- 头部 -->
        <div class="text-center">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {{ authTitle }}
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            {{ authSubtitle }}
          </p>
        </div>

        <!-- 登录表单 -->
        <BaseCard v-show="!isRegisterMode && !showSuccessMessage" class="form-container">
          <form @submit.prevent="handleLogin" class="space-y-6">
            <BaseInput
              v-model="loginForm.username"
              label="用户名或邮箱"
              placeholder="请输入用户名或邮箱"
              :error="loginErrors.username"
              required
            />
              
            <BaseInput
              v-model="loginForm.password"
              type="password"
              label="密码"
              placeholder="请输入密码"
              :error="loginErrors.password"
              required
              show-password-toggle
            />
              
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <label class="custom-checkbox">
                    <input v-model="loginForm.remember" type="checkbox" id="remember-me">
                    <span class="checkbox-visual"></span>
                  </label>
                  <label for="remember-me" class="text-sm text-gray-600 dark:text-gray-300 cursor-pointer">
                    记住登录状态
                  </label>
                </div>
                <router-link to="/forgot-password" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                  忘记密码？
                </router-link>
              </div>
              
              <BaseButton 
                type="submit" 
                variant="primary" 
                class="w-full"
                :disabled="loginLoading"
                :loading="loginLoading"
              >
                {{ loginLoading ? '登录中...' : '登录' }}
              </BaseButton>
            </form>
            
            <div class="mt-6 text-center">
              <p class="text-gray-600 dark:text-gray-300">
                还没有账户？
                <button 
                  @click="switchToRegister" 
                  class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                  立即注册
                </button>
              </p>
            </div>
        </BaseCard>

        <!-- 注册表单 -->
        <BaseCard v-show="isRegisterMode && !showSuccessMessage" class="form-container">
            <form @submit.prevent="handleRegister" class="space-y-6">
              <BaseInput
                v-model="registerForm.username"
                label="用户名"
                placeholder="3-20个字符，字母数字下划线"
                :error="registerErrors.username"
                required
              />
              
              <BaseInput
                v-model="registerForm.email"
                type="email"
                label="邮箱地址"
                placeholder="请输入有效的邮箱地址"
                :error="registerErrors.email"
                required
              />
              
              <div>
                <BaseInput
                  v-model="registerForm.password"
                  @input="onPasswordInput"
                  type="password"
                  label="密码"
                  placeholder="8-20个字符，包含数字和字母"
                  :error="registerErrors.password"
                  required
                />
                
                <!-- 密码强度指示器 -->
                <div class="mt-2">
                  <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">密码强度：</div>
                  <div class="flex space-x-1">
                    <div 
                      v-for="i in 4" 
                      :key="i"
                      class="h-1 w-full rounded"
                      :class="i <= passwordStrength ? strengthColors[Math.min(passwordStrength - 1, 3)] : 'bg-gray-200 dark:bg-gray-700'"
                    ></div>
                  </div>
                  <div class="text-xs mt-1" :class="strengthTextClass">{{ strengthText }}</div>
                </div>
              </div>
              
              <BaseInput
                v-model="registerForm.confirmPassword"
                @input="onConfirmPasswordInput"
                type="password"
                label="确认密码"
                placeholder="请再次输入密码"
                :error="registerErrors.confirmPassword"
                required
              />
              
              <div class="flex items-start space-x-3">
                <label class="custom-checkbox">
                  <input v-model="registerForm.agreeTerms" type="checkbox" id="agree-terms" required>
                  <span class="checkbox-visual"></span>
                </label>
                <label for="agree-terms" class="text-sm text-gray-600 dark:text-gray-300 cursor-pointer">
                  我已阅读并同意
                  <a href="#" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">《用户协议》</a>
                  和
                  <a href="#" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">《隐私政策》</a>
                </label>
              </div>
              
              <BaseButton 
                type="submit" 
                variant="primary"
                class="w-full"
                :disabled="registerLoading"
                :loading="registerLoading"
              >
                {{ registerLoading ? '注册中...' : '注册账户' }}
              </BaseButton>
            </form>
            
            <div class="mt-6 text-center">
              <p class="text-gray-600 dark:text-gray-300">
                已有账户？
                <button 
                  @click="switchToLogin" 
                  class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                  立即登录
                </button>
              </p>
            </div>
        </BaseCard>

        <!-- 注册成功提示 -->
        <div v-show="showSuccessMessage" class="card form-container">
          <div class="card-body text-center">
            <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
              <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">注册成功！</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              欢迎加入Word文档分析服务！您可以免费体验1次检测。
            </p>
            <BaseButton @click="goToDashboard" variant="primary">
              进入控制台
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-500 dark:text-gray-400">
        <p>&copy; 2025 Word文档分析服务. 保留所有权利.</p>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import { getFriendlyErrorMessage } from '@/utils/errorHandler'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import BaseButton from '@/components/BaseButton.vue'
import AppNavbar from '@/components/AppNavbar.vue'

const router = useRouter()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 组件状态
const isRegisterMode = ref(false)
const showSuccessMessage = ref(false)
const loginLoading = ref(false)
const registerLoading = ref(false)

// 密码可见性
const showLoginPassword = ref(false)
const showRegisterPassword = ref(false)
const showConfirmPassword = ref(false)

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 错误状态
const loginErrors = reactive({
  username: '',
  password: ''
})

const registerErrors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 密码强度
const passwordStrength = ref(0)
const strengthColors = ['bg-red-400', 'bg-orange-400', 'bg-yellow-400', 'bg-green-400']
const strengthTexts = ['弱', '一般', '中等', '强', '很强']

// 计算属性
const authTitle = computed(() => {
  if (showSuccessMessage.value) return '注册成功'
  return isRegisterMode.value ? '创建账户' : '欢迎登录'
})

const authSubtitle = computed(() => {
  if (showSuccessMessage.value) return ''
  return isRegisterMode.value 
    ? '注册新账户，开始您的文档分析之旅' 
    : '登录您的账户，开始使用文档分析服务'
})

const strengthText = computed(() => {
  if (passwordStrength.value === 0) return '请输入密码'
  return strengthTexts[passwordStrength.value - 1]
})

const strengthTextClass = computed(() => {
  if (passwordStrength.value === 0) return 'text-gray-500 dark:text-gray-400'
  if (passwordStrength.value < 2) return 'text-red-500 dark:text-red-400'
  if (passwordStrength.value < 3) return 'text-yellow-500 dark:text-yellow-400'
  return 'text-green-500 dark:text-green-400'
})

// 方法
const switchToRegister = () => {
  isRegisterMode.value = true
  clearErrors()
}

const switchToLogin = () => {
  isRegisterMode.value = false
  clearErrors()
}

const clearErrors = () => {
  loginErrors.username = ''
  loginErrors.password = ''
  
  registerErrors.username = ''
  registerErrors.email = ''
  registerErrors.password = ''
  registerErrors.confirmPassword = ''
}

// 密码强度检查
const checkPasswordStrength = (password: string): number => {
  let strength = 0
  
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/\d/.test(password)) strength++
  if (/[@$!%*?&]/.test(password)) strength++
  
  return Math.min(strength, 4)
}

const onPasswordInput = () => {
  passwordStrength.value = checkPasswordStrength(registerForm.password)
  
  // 清除密码错误
  registerErrors.password = ''
  
  // 如果确认密码已输入，重新验证匹配
  if (registerForm.confirmPassword) {
    onConfirmPasswordInput()
  }
}

const onConfirmPasswordInput = () => {
  if (registerForm.confirmPassword && registerForm.password !== registerForm.confirmPassword) {
    registerErrors.confirmPassword = '两次输入的密码不一致'
  } else {
    registerErrors.confirmPassword = ''
  }
}

// 表单验证
const validateLoginForm = (): boolean => {
  let isValid = true
  
  if (!loginForm.username.trim()) {
    loginErrors.username = '请输入用户名或邮箱'
    isValid = false
  } else {
    loginErrors.username = ''
  }
  
  if (!loginForm.password) {
    loginErrors.password = '请输入密码'
    isValid = false
  } else {
    loginErrors.password = ''
  }
  
  return isValid
}

const validateRegisterForm = (): boolean => {
  let isValid = true
  
  // 验证用户名
  if (!/^[a-zA-Z0-9_]{3,20}$/.test(registerForm.username)) {
    registerErrors.username = '用户名为3-20个字符，只能包含字母、数字和下划线'
    isValid = false
  } else {
    registerErrors.username = ''
  }
  
  // 验证邮箱
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(registerForm.email)) {
    registerErrors.email = '请输入有效的邮箱地址'
    isValid = false
  } else {
    registerErrors.email = ''
  }
  
  // 验证密码
  if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,20}$/.test(registerForm.password)) {
    registerErrors.password = '密码为8-20个字符，必须包含字母和数字'
    isValid = false
  } else {
    registerErrors.password = ''
  }
  
  // 验证确认密码
  if (registerForm.password !== registerForm.confirmPassword) {
    registerErrors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  } else {
    registerErrors.confirmPassword = ''
  }
  
  return isValid
}

// 表单提交
const handleLogin = async () => {
  if (!validateLoginForm()) return
  
  try {
    loginLoading.value = true
    clearErrors()
    
    // 调用用户store的登录方法
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })
    
    // 登录成功，跳转到仪表盘或重定向目标
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/dashboard')
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 使用统一错误处理
    const friendlyMessage = getFriendlyErrorMessage(error)
    
    // 根据错误类型分配到合适的字段
    if (error.response?.status === 401) {
      loginErrors.password = '用户名或密码错误'
    } else if (error.response?.status === 422) {
      // 验证错误，通常是用户名格式问题
      loginErrors.username = friendlyMessage
    } else {
      // 其他错误显示在用户名字段
      loginErrors.username = friendlyMessage
    }
  } finally {
    loginLoading.value = false
  }
}

const handleRegister = async () => {
  if (!validateRegisterForm()) return
  
  try {
    registerLoading.value = true
    clearErrors()
    
    // 调用用户store的注册方法
    await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    })
    
    // 注册成功，显示成功消息
    showSuccessMessage.value = true
    
  } catch (error: any) {
    console.error('注册失败:', error)
    
    // 使用统一错误处理
    const friendlyMessage = getFriendlyErrorMessage(error)
    
    // 清除所有错误信息
    clearErrors()
    
    // 根据错误状态码和内容分配到正确的字段
    if (error.response?.status === 422) {
      // 验证错误，根据错误内容判断字段
      const errorData = error.response?.data
      if (errorData?.detail && Array.isArray(errorData.detail)) {
        // FastAPI验证错误格式
        errorData.detail.forEach((err: any) => {
          const field = err.loc[err.loc.length - 1]
          if (field === 'username') {
            registerErrors.username = err.msg
          } else if (field === 'email') {
            registerErrors.email = err.msg
          } else if (field === 'password') {
            registerErrors.password = err.msg
          }
        })
      } else {
        // 单一验证错误
        if (friendlyMessage.includes('用户名') || friendlyMessage.includes('username')) {
          registerErrors.username = friendlyMessage
        } else if (friendlyMessage.includes('邮箱') || friendlyMessage.includes('email')) {
          registerErrors.email = friendlyMessage
        } else if (friendlyMessage.includes('密码') || friendlyMessage.includes('password')) {
          registerErrors.password = friendlyMessage
        } else {
          registerErrors.username = friendlyMessage
        }
      }
    } else if (error.response?.status === 400) {
      // 🔥 修复：400业务错误处理（如用户名已存在、邮箱已被注册）
      if (friendlyMessage.includes('邮箱') || friendlyMessage.includes('email')) {
        registerErrors.email = friendlyMessage
      } else if (friendlyMessage.includes('用户名') || friendlyMessage.includes('username')) {
        registerErrors.username = friendlyMessage
      } else {
        // 默认显示在用户名字段
        registerErrors.username = friendlyMessage
      }
    } else {
      // 其他错误
      registerErrors.username = friendlyMessage
    }
  } finally {
    registerLoading.value = false
  }
}

const goToDashboard = () => {
  userStore.fetchCurrentUser().then(() => {
    router.push('/dashboard')
  }).catch(() => {
    // 如果获取用户信息失败，仍然跳转（可能用户信息已经在注册时设置了）
    router.push('/dashboard')
  })
}

// 生命周期
onMounted(() => {
  // 检查是否已登录
  if (userStore.isAuthenticated) {
    router.push('/dashboard')
    return
  }
  
  // 检查URL查询参数，设置相应模式
  const mode = router.currentRoute.value.query.mode
  if (mode === 'register') {
    isRegisterMode.value = true
  } else {
    isRegisterMode.value = false
  }
})
</script>

<style scoped>
/* 组件特有样式 */
.card {
  @apply bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700;
}

.card-body {
  @apply p-8;
}

.form-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-group {
  @apply space-y-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white;
}

.form-input.error {
  @apply border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500;
}

.form-error {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 移除本地checkbox样式，使用全局样式 */

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.loading-spinner {
  @apply animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full;
}
</style> 