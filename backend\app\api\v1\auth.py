"""
认证API路由

提供用户注册、登录、验证码等功能
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials, OAuth2PasswordRequestForm
from pydantic import BaseModel, Field, field_validator
from typing import Optional
import jwt
from datetime import datetime, timed<PERSON><PERSON>
from typing import Annotated
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import logger
from app.core.response import success_response
from app.database import crud
from app.database.session import get_db
from app.models.user import UserCreate, UserResponse as UserModelResponse, UserInDB
from app.security import (
    create_access_token, 
    verify_password, 
    get_password_hash,
    decode_token
)
from app.schemas.token import Token
from app.models.task import TaskStatus

router = APIRouter()
security = HTTPBearer(auto_error=False)


# 请求/响应模型
class RegisterRequest(BaseModel):
    """用户注册请求"""
    username: str = Field(..., min_length=3, max_length=20, description="用户名")
    email: str = Field(..., description="邮箱")
    password: str = Field(..., min_length=8, description="密码")
    
    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v

class LoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

class AuthResponse(BaseModel):
    """认证响应"""
    token: str
    refresh_token: str
    user: UserModelResponse
    expires_in: int


@router.post("/register", response_model=dict, status_code=status.HTTP_201_CREATED)
async def register(request: RegisterRequest, session: AsyncSession = Depends(get_db)):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        db_user_by_username = await crud.get_user_by_username(session=session, username=request.username)
        if db_user_by_username:
            raise HTTPException(status_code=400, detail="用户名已存在")
        
        # 检查邮箱是否已被注册
        db_user_by_email = await crud.get_user_by_email(session=session, email=request.email)
        if db_user_by_email:
            raise HTTPException(status_code=400, detail="邮箱已被注册")
        
        # 创建用户
        user_in = UserCreate(username=request.username, email=request.email, password=request.password)
        hashed_password = get_password_hash(request.password)
        db_user = await crud.create_user(session=session, user_in=user_in, hashed_password=hashed_password)
        
        # 创建token
        token, refresh_token = create_access_token(
            subject={"sub": db_user.username, "user_id": db_user.id}
        )
        
        # 构造响应（与登录API保持一致的字段名）
        user_response = UserModelResponse.model_validate(db_user)
        
        logger.info(
            "用户注册成功",
            user_id=db_user.id,
            username=db_user.username,
            email=db_user.email
        )
        
        return success_response(
            data={
                "access_token": token,  # 🔥 修复：使用access_token字段名与登录API一致
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": settings.security.access_token_expire_minutes * 60,
                "user": user_response.model_dump()
            },
            message="注册成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册处理失败: {str(e)}"
        )

@router.post("/login", response_model=dict)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    session: AsyncSession = Depends(get_db)
):
    """用户登录"""
    try:
        # 查找用户
        user = await crud.get_user_by_username(session=session, username=form_data.username)
        if not user:
            # 尝试通过邮箱查找
            user = await crud.get_user_by_email(session=session, email=form_data.username)
        
        if not user or not verify_password(form_data.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被禁用",
            )
        
        # 创建访问令牌 - 修复字段名错误
        access_token, refresh_token = create_access_token(
            subject={"sub": user.username, "user_id": user.id}
        )
        
        # 记录登录日志
        logger.info(f"用户登录成功: {user.username}")
        
        return success_response(
            data={
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": settings.security.access_token_expire_minutes * 60,
                "user": {
                    "user_id": user.id,  # 修复字段名
                    "username": user.username,
                    "email": user.email,
                    "is_active": user.is_active
                }
            },
            message="登录成功"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录处理失败"
        )

@router.get("/me", response_model=dict)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: AsyncSession = Depends(get_db)
):
    """获取当前用户信息"""
    try:
        if credentials is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="需要提供认证Token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        payload = decode_token(credentials.credentials)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token无效或已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 修复：直接从payload获取user_id
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Token中缺少用户信息")

        user = await crud.get_user_by_id(session=session, user_id=user_id)
        if user is None:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        user_response = UserModelResponse.model_validate(user)
        
        return success_response(
            data=user_response.model_dump(),
            message="获取用户信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户信息失败"
        )

@router.post("/logout", response_model=dict)
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出，使Token失效"""
    try:
        if credentials is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="需要提供认证Token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        payload = decode_token(credentials.credentials)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token无效或已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # TODO: 在实际实现中，这里应该将Token加入黑名单
        # 目前简单返回成功响应
        
        logger.info("用户登出成功")
        
        return success_response(
            data=None,
            message="登出成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登出失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出处理失败"
        )


@router.get("/profile", response_model=dict)
async def get_user_profile(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: AsyncSession = Depends(get_db)
):
    """获取用户详细信息"""
    try:
        if credentials is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="需要提供认证Token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        payload = decode_token(credentials.credentials)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token无效或已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        user_id = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Token中缺少用户信息")

        user = await crud.get_user_by_id(session=session, user_id=user_id)
        if user is None:
            raise HTTPException(status_code=404, detail="用户不存在")
            
        # 获取用户统计信息
        total_tasks = await crud.count_user_tasks(session=session, user_id=user_id)
        completed_tasks = await crud.count_user_tasks(session=session, user_id=user_id, status=TaskStatus.COMPLETED)
        total_documents = await crud.count_user_documents(session=session, user_id=user_id)
        
        user_data = user.model_dump()
        user_data.update({
            "statistics": {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "total_documents": total_documents
            }
        })
        
        return success_response(
            data=user_data,
            message="获取用户详细信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详细信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详细信息失败"
        ) 