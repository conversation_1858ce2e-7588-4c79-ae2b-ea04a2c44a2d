<template>
  <div :class="cardClasses">
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title" :class="headerClasses">
      <slot name="header">
        <div class="flex items-center justify-between">
          <h3 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-white">{{ title }}</h3>
          <slot name="header-actions"></slot>
        </div>
      </slot>
    </div>
    
    <!-- 卡片内容 -->
    <div :class="bodyClasses">
      <slot></slot>
    </div>
    
    <!-- 卡片底部 -->
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 卡片标题
  title?: string
  // 是否启用悬停效果
  hover?: boolean
  // 是否移除内边距
  noPadding?: boolean
  // 自定义样式类
  customClass?: string
  // 卡片变体
  variant?: 'default' | 'border' | 'shadow' | 'outline'
}

const props = withDefaults(defineProps<Props>(), {
  hover: false,
  noPadding: false,
  variant: 'default'
})

// 卡片基础样式计算
const cardClasses = computed(() => {
  const baseClasses = [
    'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700'
  ]
  
  // 添加阴影效果
  if (props.variant === 'shadow') {
    baseClasses.push('shadow-lg')
  } else if (props.variant === 'outline') {
    baseClasses.push('border-2')
  } else {
    baseClasses.push('shadow')
  }
  
  // 悬停效果
  if (props.hover) {
    baseClasses.push('hover:shadow-lg hover:scale-105 cursor-pointer transition-all duration-200')
  }
  
  // 自定义类
  if (props.customClass) {
    baseClasses.push(props.customClass)
  }
  
  return baseClasses.join(' ')
})

// 头部样式
const headerClasses = computed(() => {
  return 'px-6 py-4 border-b border-gray-200 dark:border-gray-700'
})

// 内容区域样式
const bodyClasses = computed(() => {
  return props.noPadding ? '' : 'px-4 py-4'
})

// 底部样式
const footerClasses = computed(() => {
  return 'px-6 py-4 border-t border-gray-200 dark:border-gray-700'
})
</script>

<script lang="ts">
export default {
  name: 'BaseCard'
}
</script> 