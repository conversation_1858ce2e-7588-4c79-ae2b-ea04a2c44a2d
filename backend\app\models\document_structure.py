"""
文档结构统计模型
"""

import uuid
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime


class StructureType(str, Enum):
    """结构类型枚举"""
    STANDARD = "standard"
    NON_STANDARD = "non-standard"
    ACTUAL = "actual"


class StructureStatus(str, Enum):
    """结构状态枚举"""
    PRESENT = "present"
    MISSING = "missing"
    EXTRA = "extra"


class DocumentStructureBase(BaseModel):
    """文档结构基础模型"""
    structure_name: str = Field(..., description="结构名称")
    structure_type: StructureType = Field(..., description="结构类型")
    status: StructureStatus = Field(..., description="结构状态")
    word_count: int = Field(default=0, ge=0, description="字数统计")
    reference_count: int = Field(default=0, ge=0, description="参考文献条数（仅对参考文献结构有效）")
    page_number: Optional[int] = Field(default=None, description="所在页码")
    paragraph_index: Optional[int] = Field(default=None, description="段落索引")
    style_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="样式信息")
    
    class Config:
        from_attributes = True


class DocumentStructureCreate(DocumentStructureBase):
    """文档结构创建模型"""
    structure_id: str = Field(default_factory=lambda: f"struct_{uuid.uuid4().hex}", description="结构ID")
    document_id: str = Field(..., description="关联的文档ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class DocumentStructureInDB(DocumentStructureCreate):
    """数据库中的文档结构模型"""
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")


class DocumentStructure(DocumentStructureInDB):
    """API响应的文档结构模型"""
    pass


class DocumentContentCache(BaseModel):
    """文档内容缓存模型"""
    cache_id: str = Field(default_factory=lambda: f"cache_{uuid.uuid4().hex}", description="缓存ID")
    document_id: str = Field(..., description="关联的文档ID")
    content_type: str = Field(default="full_content", description="内容类型")
    file_path: str = Field(..., description="缓存文件路径")
    file_size: int = Field(default=0, ge=0, description="文件大小")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        from_attributes = True


class DocumentStructureStats(BaseModel):
    """文档结构统计汇总"""
    document_id: str
    total_structures: int = Field(default=0, description="总结构数")
    standard_structures: int = Field(default=0, description="标准结构数")
    non_standard_structures: int = Field(default=0, description="非标准结构数")
    missing_structures: int = Field(default=0, description="缺失结构数")
    extra_structures: int = Field(default=0, description="多余结构数")
    total_words: int = Field(default=0, description="总字数")
    total_references: int = Field(default=0, description="总参考文献条数")
    structures: List[DocumentStructure] = Field(default_factory=list, description="结构详情列表")
