import requests
import json

def check_frontend_status():
    """检查前端状态"""
    try:
        # 检查前端是否运行
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务器运行正常")
            return True
        else:
            print(f"⚠️ 前端服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到前端服务器")
        return False
    except Exception as e:
        print(f"❌ 检查前端状态失败: {str(e)}")
        return False

def check_backend_status():
    """检查后端状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
            return True
        else:
            print(f"⚠️ 后端服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器")
        return False
    except Exception as e:
        print(f"❌ 检查后端状态失败: {str(e)}")
        return False

def main():
    print("🔍 检查服务器状态")
    print("=" * 40)
    
    print("📡 检查后端服务器...")
    backend_ok = check_backend_status()
    
    print("\n🌐 检查前端服务器...")
    frontend_ok = check_frontend_status()
    
    print("\n" + "=" * 40)
    if backend_ok and frontend_ok:
        print("✅ 所有服务器运行正常")
        print("\n📋 测试步骤:")
        print("1. 访问前端登录页面: http://localhost:3000/login")
        print("2. 使用账号登录: 8966097 / heibailan5112")
        print("3. 访问统计页面: http://localhost:3000/document/task_145daa3cebcd4f8c81db456e92cd1135/statistics")
        print("4. 查看问题片段板块是否正确显示")
    else:
        print("❌ 部分服务器未运行")
        if not backend_ok:
            print("   - 后端服务器需要启动")
        if not frontend_ok:
            print("   - 前端服务器需要启动")

if __name__ == "__main__":
    main()
