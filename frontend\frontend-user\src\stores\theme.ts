import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type Theme = 'light' | 'dark' | 'auto'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const currentTheme = ref<Theme>((localStorage.getItem('theme') as Theme) || 'auto')
  const systemTheme = ref<'light' | 'dark'>('light')

  // 计算属性
  const activeTheme = computed(() => {
    if (currentTheme.value === 'auto') {
      return systemTheme.value
    }
    return currentTheme.value
  })

  const isDark = computed(() => activeTheme.value === 'dark')

  // 检测系统主题
  const detectSystemTheme = () => {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      systemTheme.value = 'dark'
    } else {
      systemTheme.value = 'light'
    }
  }

  // 设置主题
  const setTheme = (theme: Theme) => {
    currentTheme.value = theme
    localStorage.setItem('theme', theme)
    applyTheme()
  }

  // 切换主题（简单的明暗模式切换）
  const toggleTheme = () => {
    // 原型设计是简单的明暗模式切换，不包含auto模式
    if (isDark.value) {
      setTheme('light')
    } else {
      setTheme('dark')
    }
  }

  // 应用主题到DOM
  const applyTheme = () => {
    const theme = activeTheme.value
    const root = document.documentElement
    
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    // 设置meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme === 'dark' ? '#1f2937' : '#ffffff')
    }
  }

  // 初始化主题
  const initializeTheme = () => {
    detectSystemTheme()
    
    // 监听系统主题变化
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', detectSystemTheme)
    }
    
    applyTheme()
  }

  // 获取主题图标
  const getThemeIcon = computed(() => {
    switch (currentTheme.value) {
      case 'light':
        return '☀️'
      case 'dark':
        return '🌙'
      case 'auto':
        return '💻'
      default:
        return '💻'
    }
  })

  // 获取主题名称
  const getThemeName = computed(() => {
    switch (currentTheme.value) {
      case 'light':
        return '浅色模式'
      case 'dark':
        return '深色模式'
      case 'auto':
        return '跟随系统'
      default:
        return '跟随系统'
    }
  })

  return {
    // 状态
    currentTheme,
    systemTheme,
    
    // 计算属性
    activeTheme,
    isDark,
    getThemeIcon,
    getThemeName,
    
    // 方法
    setTheme,
    toggleTheme,
    detectSystemTheme,
    applyTheme,
    initializeTheme,
  }
}) 