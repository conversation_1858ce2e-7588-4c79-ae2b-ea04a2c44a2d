"""
Word文档分析服务 - 任务队列管理

实现基于Redis的高性能任务队列系统，支持：
- 任务优先级
- 任务序列化/反序列化
- 死信队列
- 队列统计和监控
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import uuid

import redis.asyncio as redis
from pydantic import BaseModel

from app.core.logging import logger
from app.core.config import get_settings
from app.core.exceptions import TaskException


class TaskPriority(str, Enum):
    """任务优先级"""
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"


class QueuedTask(BaseModel):
    """队列中的任务"""
    task_id: str
    task_type: str
    priority: TaskPriority = TaskPriority.NORMAL
    payload: Dict[str, Any]
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 5分钟超时


class TaskQueue:
    """Redis任务队列管理器"""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        """
        初始化任务队列
        
        Args:
            redis_client: Redis客户端，如果为None则创建新连接
        """
        self.settings = get_settings()
        self.redis_client = redis_client
        
        # 队列名称
        self.queue_names = {
            TaskPriority.HIGH: "task_queue:high",
            TaskPriority.NORMAL: "task_queue:normal", 
            TaskPriority.LOW: "task_queue:low"
        }
        
        # 其他队列
        self.processing_queue = "task_queue:processing"
        self.dead_letter_queue = "task_queue:dead_letter"
        self.delayed_queue = "task_queue:delayed"
        
        # 统计信息
        self.stats = {
            'tasks_enqueued': 0,
            'tasks_dequeued': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_retried': 0
        }
    
    async def initialize(self) -> bool:
        """初始化队列连接"""
        try:
            if self.redis_client is None:
                # 使用基础连接，兼容没有Redis的环境
                try:
                    self.redis_client = redis.from_url(
                        self.settings.redis.url,
                        encoding="utf-8",
                        decode_responses=True,
                        socket_connect_timeout=5,
                        socket_timeout=5
                    )
                    # 测试连接
                    await self.redis_client.ping()
                    logger.info("任务队列Redis连接初始化成功")
                except Exception as e:
                    logger.warning(f"Redis不可用，使用内存队列模式: {str(e)}")
                    self.redis_client = None
                    return True
            
            # 加载统计信息（不影响初始化结果）
            if self.redis_client:
                try:
                    await self._load_stats()
                except Exception as e:
                    logger.warning(f"加载队列统计失败，将使用默认值: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"任务队列初始化失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭队列连接"""
        if self.redis_client:
            await self._save_stats()
            await self.redis_client.close()
            logger.info("任务队列连接已关闭")
    
    async def enqueue(
        self, 
        task_id: str,
        task_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        delay: Optional[int] = None,
        max_retries: int = 3,
        timeout: int = 300
    ) -> bool:
        """将任务加入队列"""
        try:
            task = QueuedTask(
                task_id=task_id,
                task_type=task_type,
                priority=priority,
                payload=payload,
                created_at=datetime.utcnow(),
                scheduled_at=datetime.utcnow() + timedelta(seconds=delay) if delay else None,
                max_retries=max_retries,
                timeout=timeout
            )
            
            if self.redis_client is None:
                # 内存模式，直接返回成功
                logger.debug(f"内存模式：任务已入队: {task_id}")
                self.stats['tasks_enqueued'] += 1
                return True
            
            task_data = task.model_dump_json()
            
            if delay and delay > 0:
                # 延迟任务
                score = time.time() + delay
                await self.redis_client.zadd(self.delayed_queue, {task_data: score})
                logger.debug(f"延迟任务已入队: {task_id}, 延迟 {delay} 秒")
            else:
                # 立即执行任务
                queue_name = self.queue_names[priority]
                await self.redis_client.lpush(queue_name, task_data)
                logger.debug(f"任务已入队: {task_id}, 优先级: {priority}")
            
            self.stats['tasks_enqueued'] += 1
            return True
            
        except Exception as e:
            logger.error(f"任务入队失败: {task_id} - {str(e)}")
            return False
    
    async def dequeue(self, timeout: int = 10) -> Optional[QueuedTask]:
        """
        从队列中取出任务（按优先级）
        
        Args:
            timeout: 阻塞等待超时时间（秒）
            
        Returns:
            QueuedTask: 取出的任务，如果队列为空返回None
        """
        try:
            # 处理延迟任务
            await self._process_delayed_tasks()
            
            # 按优先级顺序检查队列
            queue_list = [
                self.queue_names[TaskPriority.HIGH],
                self.queue_names[TaskPriority.NORMAL],
                self.queue_names[TaskPriority.LOW]
            ]
            
            # 使用BRPOP阻塞式取出任务
            result = await self.redis_client.brpop(queue_list, timeout=timeout)
            
            if result:
                queue_name, task_data = result
                task = QueuedTask.model_validate_json(task_data)
                
                # 将任务添加到处理队列
                await self.redis_client.lpush(self.processing_queue, task_data)
                
                self.stats['tasks_dequeued'] += 1
                logger.debug(f"任务已出队: {task.task_id}")
                
                return task
            
            return None
            
        except Exception as e:
            logger.error(f"任务出队失败: {str(e)}")
            return None
    
    async def complete_task(self, task_id: str) -> bool:
        """
        标记任务为完成
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 从处理队列中移除任务
            await self._remove_from_processing(task_id)
            
            self.stats['tasks_completed'] += 1
            logger.debug(f"任务已完成: {task_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"标记任务完成失败: {task_id} - {str(e)}")
            return False
    
    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """
        标记任务为失败
        
        Args:
            task_id: 任务ID
            error: 错误信息
            retry: 是否重试
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 从处理队列中获取任务
            task_data = await self._get_from_processing(task_id)
            
            if task_data:
                task = QueuedTask.model_validate_json(task_data)
                
                if retry and task.retry_count < task.max_retries:
                    # 重试任务
                    task.retry_count += 1
                    retry_delay = min(2 ** task.retry_count, 300)  # 指数退避，最大5分钟
                    
                    await self.enqueue(
                        task_id=task.task_id,
                        task_type=task.task_type,
                        payload=task.payload,
                        priority=task.priority,
                        delay=retry_delay,
                        max_retries=task.max_retries,
                        timeout=task.timeout
                    )
                    
                    self.stats['tasks_retried'] += 1
                    logger.info(f"任务重试: {task_id}, 第 {task.retry_count} 次重试，延迟 {retry_delay} 秒")
                else:
                    # 移入死信队列
                    dead_task = {
                        'task': task.model_dump(),
                        'error': error,
                        'failed_at': datetime.utcnow().isoformat()
                    }
                    await self.redis_client.lpush(self.dead_letter_queue, json.dumps(dead_task))
                    
                    self.stats['tasks_failed'] += 1
                    logger.error(f"任务失败，移入死信队列: {task_id} - {error}")
                
                # 从处理队列移除
                await self._remove_from_processing(task_id)
            
            return True
            
        except Exception as e:
            logger.error(f"标记任务失败失败: {task_id} - {str(e)}")
            return False
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        try:
            if self.redis_client is None:
                return {
                    'mode': 'memory',
                    'queue_sizes': {'high': 0, 'normal': 0, 'low': 0},
                    'processing_count': 0,
                    'dead_letter_count': 0,
                    'delayed_count': 0,
                    **self.stats
                }
            
            stats = {
                'mode': 'redis',
                'queue_sizes': {},
                'processing_count': 0,
                'dead_letter_count': 0,
                'delayed_count': 0,
                **self.stats
            }
            
            # 获取各优先级队列大小
            for priority, queue_name in self.queue_names.items():
                stats['queue_sizes'][priority.value] = await self.redis_client.llen(queue_name)
            
            # 获取其他队列大小
            stats['processing_count'] = await self.redis_client.llen(self.processing_queue)
            stats['dead_letter_count'] = await self.redis_client.llen(self.dead_letter_queue)
            stats['delayed_count'] = await self.redis_client.zcard(self.delayed_queue)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取队列统计失败: {str(e)}")
            return self.stats
    
    async def clear_queue(self, priority: Optional[TaskPriority] = None) -> bool:
        """
        清空队列
        
        Args:
            priority: 要清空的优先级队列，如果为None则清空所有队列
            
        Returns:
            bool: 操作是否成功
        """
        try:
            if priority:
                # 清空指定优先级队列
                queue_name = self.queue_names[priority]
                await self.redis_client.delete(queue_name)
                logger.info(f"已清空 {priority.value} 优先级队列")
            else:
                # 清空所有队列
                all_queues = list(self.queue_names.values()) + [
                    self.processing_queue,
                    self.dead_letter_queue,
                    self.delayed_queue
                ]
                await self.redis_client.delete(*all_queues)
                logger.info("已清空所有队列")
            
            return True
            
        except Exception as e:
            logger.error(f"清空队列失败: {str(e)}")
            return False
    
    async def _process_delayed_tasks(self):
        """处理延迟任务"""
        try:
            current_time = time.time()
            
            # 获取应该执行的延迟任务
            delayed_tasks = await self.redis_client.zrangebyscore(
                self.delayed_queue, 
                0, 
                current_time,
                withscores=True
            )
            
            for task_data, score in delayed_tasks:
                try:
                    task = QueuedTask.model_validate_json(task_data)
                    
                    # 将任务移到正常队列
                    queue_name = self.queue_names[task.priority]
                    await self.redis_client.lpush(queue_name, task_data)
                    
                    # 从延迟队列移除
                    await self.redis_client.zrem(self.delayed_queue, task_data)
                    
                    logger.debug(f"延迟任务已激活: {task.task_id}")
                    
                except Exception as e:
                    logger.error(f"处理延迟任务失败: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"处理延迟任务列表失败: {str(e)}")
    
    async def _remove_from_processing(self, task_id: str):
        """从处理队列移除任务"""
        try:
            # 获取处理队列中的所有任务
            processing_tasks = await self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for task_data in processing_tasks:
                try:
                    task = QueuedTask.model_validate_json(task_data)
                    if task.task_id == task_id:
                        await self.redis_client.lrem(self.processing_queue, 1, task_data)
                        break
                except Exception:
                    continue
                    
        except Exception as e:
            logger.error(f"从处理队列移除任务失败: {task_id} - {str(e)}")
    
    async def _get_from_processing(self, task_id: str) -> Optional[str]:
        """从处理队列获取任务数据"""
        try:
            processing_tasks = await self.redis_client.lrange(self.processing_queue, 0, -1)
            
            for task_data in processing_tasks:
                try:
                    task = QueuedTask.model_validate_json(task_data)
                    if task.task_id == task_id:
                        return task_data
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"从处理队列获取任务失败: {task_id} - {str(e)}")
            return None
    
    async def _load_stats(self):
        """加载统计信息"""
        try:
            if self.redis_client:
                stats_data = await self.redis_client.get("task_queue:stats")
                if stats_data:
                    saved_stats = json.loads(stats_data)
                    self.stats.update(saved_stats)
                    logger.debug("队列统计信息已加载")
        except Exception as e:
            logger.warning(f"加载队列统计失败: {str(e)}")
    
    async def _save_stats(self):
        """保存统计信息"""
        try:
            if self.redis_client:
                await self.redis_client.set("task_queue:stats", json.dumps(self.stats))
                logger.debug("队列统计信息已保存")
        except Exception as e:
            logger.warning(f"保存队列统计失败: {str(e)}")


# 全局任务队列实例
_task_queue: Optional[TaskQueue] = None


async def get_task_queue() -> TaskQueue:
    """获取全局任务队列实例"""
    global _task_queue
    
    if _task_queue is None:
        _task_queue = TaskQueue()
        await _task_queue.initialize()
    
    return _task_queue


async def get_queue_status() -> Dict[str, Any]:
    """获取任务队列状态（用于健康检查）"""
    try:
        # 导入任务管理器
        from app.tasks.manager import task_manager
        
        start_time = asyncio.get_event_loop().time()
        
        # 检查任务管理器是否可用
        if hasattr(task_manager, 'get_status'):
            # 获取任务管理器状态
            manager_status = task_manager.get_status()
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "task_manager": "available",
                "queue_status": manager_status,
                "message": "任务队列正常"
            }
        else:
            # 基础检查
            return {
                "status": "healthy",
                "task_manager": "basic",
                "message": "任务队列基础功能可用"
            }
            
    except ImportError as e:
        logger.error(f"任务管理器模块导入失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": f"任务管理器模块导入失败: {str(e)}",
            "message": "任务队列模块不可用"
        }
    except Exception as e:
        logger.error(f"任务队列状态检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "任务队列检查失败"
        }

async def test_task_queue_basic() -> bool:
    """基础任务队列测试"""
    try:
        from app.tasks.manager import task_manager
        return True
    except Exception as e:
        logger.error(f"任务队列基础测试失败: {str(e)}")
        return False

async def get_task_queue_info() -> Dict[str, Any]:
    """获取任务队列详细信息"""
    try:
        from app.tasks.manager import task_manager
        from app.database import crud
        
        # 获取任务统计
        task_stats = {
            "total_tasks": await crud.count_tasks() if hasattr(crud, 'count_tasks') else 0,
            "pending_tasks": 0,
            "running_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0
        }
        
        try:
            # 尝试获取更详细的统计
            from app.models.task import TaskStatus
            if hasattr(crud, 'count_tasks'):
                task_stats.update({
                    "pending_tasks": await crud.count_tasks(status=TaskStatus.PENDING),
                    "running_tasks": await crud.count_tasks(status=TaskStatus.PROCESSING),
                    "completed_tasks": await crud.count_tasks(status=TaskStatus.COMPLETED),
                    "failed_tasks": await crud.count_tasks(status=TaskStatus.FAILED)
                })
        except Exception:
            # 如果获取详细统计失败，使用基础统计
            pass
        
        return {
            "available": True,
            "task_stats": task_stats,
            "manager_type": "async",
            "last_check": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取任务队列信息失败: {str(e)}")
        return {
            "available": False,
            "error": str(e)
        } 