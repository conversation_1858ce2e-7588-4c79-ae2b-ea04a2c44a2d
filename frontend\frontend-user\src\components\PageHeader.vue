<template>
  <div :class="[mobile ? 'mb-4' : 'mb-8']">
    <!-- 面包屑导航 -->
    <nav v-if="breadcrumbs && breadcrumbs.length > 0" :class="[mobile ? 'mb-2' : 'mb-4']">
      <ol class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
        <li v-for="(crumb, index) in breadcrumbs" :key="index" class="flex items-center">
          <router-link 
            v-if="crumb.to && index < breadcrumbs.length - 1"
            :to="crumb.to"
            class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            :class="{ 'text-xs': mobile }"
          >
            {{ crumb.text }}
          </router-link>
          <span 
            v-else 
            class="text-gray-900 dark:text-gray-100"
            :class="{ 'text-xs': mobile }"
          >
            {{ crumb.text }}
          </span>
          <svg 
            v-if="index < breadcrumbs.length - 1"
            :class="[mobile ? 'h-3 w-3 mx-1.5' : 'h-4 w-4 mx-2', 'text-gray-400 dark:text-gray-500']"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
          </svg>
        </li>
      </ol>
    </nav>

    <!-- 页面标题和操作区域 -->
    <div class="flex flex-col md:flex-row md:items-center justify-between">
      <div class="flex-1">
        <h1 
          v-if="title" 
          :class="[
            mobile 
              ? 'text-xl font-bold text-gray-900 dark:text-white mb-1' 
              : 'text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2'
          ]"
        >
          {{ title }}
        </h1>
        <p 
          v-if="description" 
          :class="[
            mobile 
              ? 'text-sm text-gray-600 dark:text-gray-300 line-clamp-2' 
              : 'text-gray-600 dark:text-gray-300'
          ]"
        >
          {{ description }}
        </p>
      </div>
      
      <!-- 操作按钮区域 -->
      <div v-if="$slots.actions" :class="[mobile ? 'mt-3 flex-shrink-0' : 'mt-4 md:mt-0']">
        <div :class="{ 'flex flex-wrap gap-2': mobile }">
          <slot name="actions"></slot>
        </div>
      </div>
    </div>

    <!-- 移动端附加信息区域 -->
    <div v-if="mobile && $slots.extra" class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
      <slot name="extra"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Breadcrumb {
  text: string
  to?: string
}

interface Props {
  title?: string
  description?: string
  breadcrumbs?: Breadcrumb[]
  mobile?: boolean
}

defineProps<Props>()
</script>

<script lang="ts">
export default {
  name: 'PageHeader'
}
</script>

<style scoped>
/* 移动端文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 移动端操作按钮优化 */
.mobile-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* 移动端按钮样式调整 */
@media (max-width: 768px) {
  .mobile-actions .btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }
  
  .mobile-actions .btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}
</style> 