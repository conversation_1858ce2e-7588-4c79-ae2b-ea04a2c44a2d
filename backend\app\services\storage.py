"""
文件存储管理模块 (已重构，统一使用配置)

提供文件系统存储封装、路径管理、文件索引等功能。
路径信息完全由 app.core.config.settings 提供。
"""

import os
import shutil
import uuid
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from datetime import datetime, timedelta
import structlog
import json

from app.core.config import settings, BASE_DIR
from app.core.exceptions import StorageError, ValidationError
from app.core.security import check_file_security

logger = structlog.get_logger()

# 注意：所有路径和保留时间配置已移至 app.core.config.py
# class FileSettings, 请勿在此处添加硬编码路径。

class StorageManager:
    """文件存储管理器"""
    
    def __init__(self):
        """
        初始化存储管理器。
        所有路径均从全局配置 settings 中获取。
        """
        self.base_dir = BASE_DIR
        self.upload_dir = settings.files.upload_path
        self.processed_dir = settings.files.processed_path
        self.images_dir = settings.files.images_path
        self.temp_dir = settings.files.temp_path
        
        # 存储统计
        self.storage_stats = {
            'files_stored': 0,
            'files_retrieved': 0,
            'files_deleted': 0,
            'total_size_bytes': 0,
            'storage_operations': 0
        }
        
        # 文件索引
        self.file_index = {}
        self._load_file_index()
    
    def _get_index_file_path(self) -> Path:
        """获取文件索引文件的路径"""
        # 将索引文件放在temp目录下
        return self.temp_dir / 'storage_file_index.json'

    def _load_file_index(self):
        """加载文件索引"""
        index_file = self._get_index_file_path()
        try:
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    self.file_index = json.load(f)
                logger.info(f"文件索引加载完成，包含 {len(self.file_index)} 个文件")
            else:
                self.file_index = {}
                logger.info("创建新的文件索引")
        except Exception as e:
            logger.warning(f"文件索引加载失败，创建新索引: {str(e)}")
            self.file_index = {}
    
    def _save_file_index(self):
        """保存文件索引"""
        index_file = self._get_index_file_path()
        try:
            with open(index_file, 'w', encoding='utf-8') as f:
                json.dump(self.file_index, f, ensure_ascii=False, indent=2)
            logger.debug("文件索引保存成功")
        except Exception as e:
            logger.error(f"文件索引保存失败: {str(e)}")
    
    def store_uploaded_file(
        self, 
        source_path: str, 
        original_filename: str,
        task_id: str = None,
        perform_security_check: bool = True
    ) -> Dict[str, Any]:
        """
        存储上传的文件
        
        Args:
            source_path: 源文件路径
            original_filename: 原始文件名
            task_id: 关联的任务ID
            perform_security_check: 是否执行安全检查
            
        Returns:
            存储结果字典
            
        Raises:
            StorageError: 存储失败
            SecurityError: 安全检查失败
        """
        try:
            self.storage_stats['storage_operations'] += 1
            
            source_path = Path(source_path)
            if not source_path.exists():
                raise ValidationError(f"源文件不存在: {source_path}")
            
            # 安全检查
            if perform_security_check:
                security_result = check_file_security(str(source_path), original_filename)
                if not security_result['is_safe']:
                    raise StorageError(f"文件安全检查失败: {', '.join(security_result['risk_reasons'])}")
            
            # 生成存储信息
            file_id = str(uuid.uuid4())
            file_ext = Path(original_filename).suffix.lower()
            stored_filename = f"{file_id}{file_ext}"
            
            # 确定存储路径
            storage_path = self.upload_dir / stored_filename
            
            # 复制文件
            shutil.copy2(source_path, storage_path)
            
            # 获取文件信息
            file_stat = storage_path.stat()
            file_size = file_stat.st_size
            
            # 创建文件记录
            file_record = {
                'file_id': file_id,
                'original_filename': original_filename,
                'stored_filename': stored_filename,
                'storage_path': str(storage_path),
                'relative_path': str(storage_path.relative_to(self.base_dir)),
                'file_size': file_size,
                'file_extension': file_ext,
                'task_id': task_id,
                'storage_type': 'upload',
                'created_at': datetime.utcnow().isoformat(),
                'accessed_at': datetime.utcnow().isoformat(),
                'access_count': 0,
                'security_checked': perform_security_check,
                'metadata': {
                    'mime_type': None,
                    'encoding': None,
                    'hash_md5': None,
                    'hash_sha256': None
                }
            }
            
            # 添加到索引
            self.file_index[file_id] = file_record
            self._save_file_index()
            
            # 更新统计
            self.storage_stats['files_stored'] += 1
            self.storage_stats['total_size_bytes'] += file_size
            
            logger.info(f"文件存储成功: {original_filename} -> {stored_filename}")
            
            return {
                'file_id': file_id,
                'storage_path': str(storage_path),
                'relative_path': file_record['relative_path'],
                'file_size': file_size,
                'stored_at': file_record['created_at']
            }
            
        except Exception as e:
            logger.error(f"文件存储失败: {str(e)}")
            raise StorageError(f"文件存储失败: {str(e)}")
    
    def store_processed_file(
        self,
        source_path: str,
        file_id: str,
        file_type: str = 'processed',
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        存储处理后的文件
        
        Args:
            source_path: 源文件路径
            file_id: 文件ID
            file_type: 文件类型 (processed, image, temp)
            metadata: 文件元数据
            
        Returns:
            存储结果字典
        """
        try:
            source_path = Path(source_path)
            if not source_path.exists():
                raise ValidationError(f"源文件不存在: {source_path}")
            
            # 确定存储目录
            if file_type == 'image':
                target_dir = self.images_dir
            elif file_type == 'temp':
                target_dir = self.temp_dir
            else:
                target_dir = self.processed_dir
            
            # 生成存储路径
            file_ext = source_path.suffix.lower()
            stored_filename = f"{file_id}_{file_type}{file_ext}"
            storage_path = target_dir / stored_filename
            
            # 复制文件
            shutil.copy2(source_path, storage_path)
            
            # 获取文件信息
            file_stat = storage_path.stat()
            file_size = file_stat.st_size
            
            # 创建文件记录
            processed_file_id = f"{file_id}_{file_type}"
            file_record = {
                'file_id': processed_file_id,
                'parent_file_id': file_id,
                'stored_filename': stored_filename,
                'storage_path': str(storage_path),
                'relative_path': str(storage_path.relative_to(self.base_dir)),
                'file_size': file_size,
                'file_extension': file_ext,
                'storage_type': file_type,
                'created_at': datetime.utcnow().isoformat(),
                'accessed_at': datetime.utcnow().isoformat(),
                'access_count': 0,
                'metadata': metadata or {}
            }
            
            # 添加到索引
            self.file_index[processed_file_id] = file_record
            self._save_file_index()
            
            # 更新统计
            self.storage_stats['files_stored'] += 1
            self.storage_stats['total_size_bytes'] += file_size
            
            logger.info(f"处理文件存储成功: {stored_filename}")
            
            return {
                'file_id': processed_file_id,
                'storage_path': str(storage_path),
                'relative_path': file_record['relative_path'],
                'file_size': file_size,
                'stored_at': file_record['created_at']
            }
            
        except Exception as e:
            logger.error(f"处理文件存储失败: {str(e)}")
            raise StorageError(f"处理文件存储失败: {str(e)}")
    
    def get_file_path(self, file_id: str) -> Optional[str]:
        """
        获取文件路径
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件路径，如果不存在返回None
        """
        try:
            if file_id not in self.file_index:
                return None
            
            file_record = self.file_index[file_id]
            storage_path = Path(file_record['storage_path'])
            
            if not storage_path.exists():
                logger.warning(f"文件不存在: {storage_path}")
                return None
            
            # 更新访问信息
            file_record['accessed_at'] = datetime.utcnow().isoformat()
            file_record['access_count'] += 1
            self._save_file_index()
            
            self.storage_stats['files_retrieved'] += 1
            
            return str(storage_path)
            
        except Exception as e:
            logger.error(f"获取文件路径失败: {str(e)}")
            return None
    
    def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件信息字典，如果不存在返回None
        """
        if file_id not in self.file_index:
            return None
        
        file_record = self.file_index[file_id].copy()
        
        # 检查文件是否仍然存在
        storage_path = Path(file_record['storage_path'])
        file_record['file_exists'] = storage_path.exists()
        
        return file_record
    
    def delete_file(self, file_id: str, force: bool = False) -> bool:
        """
        删除文件
        
        Args:
            file_id: 文件ID
            force: 是否强制删除（忽略错误）
            
        Returns:
            是否删除成功
        """
        try:
            if file_id not in self.file_index:
                if not force:
                    raise ValidationError(f"文件不存在: {file_id}")
                return False
            
            file_record = self.file_index[file_id]
            storage_path = Path(file_record['storage_path'])
            
            # 删除物理文件
            if storage_path.exists():
                storage_path.unlink()
                logger.info(f"物理文件删除成功: {storage_path}")
            elif not force:
                logger.warning(f"物理文件不存在: {storage_path}")
            
            # 从索引中移除
            file_size = file_record.get('file_size', 0)
            del self.file_index[file_id]
            self._save_file_index()
            
            # 更新统计
            self.storage_stats['files_deleted'] += 1
            self.storage_stats['total_size_bytes'] -= file_size
            
            logger.info(f"文件删除成功: {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败: {str(e)}")
            if not force:
                raise StorageError(f"文件删除失败: {str(e)}")
            return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            # 统计文件类型
            file_type_stats = {}
            for file_record in self.file_index.values():
                storage_type = file_record.get('storage_type', 'unknown')
                if storage_type not in file_type_stats:
                    file_type_stats[storage_type] = {'count': 0, 'size_bytes': 0}
                file_type_stats[storage_type]['count'] += 1
                file_type_stats[storage_type]['size_bytes'] += file_record.get('file_size', 0)
            
            return {
                'operation_stats': self.storage_stats.copy(),
                'file_type_stats': file_type_stats,
                'total_files': len(self.file_index),
                'storage_base_dir': str(self.base_dir),
                'index_file_count': len(self.file_index)
            }
            
        except Exception as e:
            logger.error(f"获取存储统计失败: {str(e)}")
            return {'error': str(e)}

# 全局存储管理器实例
_storage_manager = None

def get_storage_manager() -> StorageManager:
    """获取全局存储管理器实例"""
    global _storage_manager
    if _storage_manager is None:
        _storage_manager = StorageManager()
    return _storage_manager

def store_uploaded_file(source_path: str, original_filename: str, task_id: str = None) -> Dict[str, Any]:
    """
    快捷函数：存储上传文件
    
    Args:
        source_path: 源文件路径
        original_filename: 原始文件名
        task_id: 任务ID
        
    Returns:
        存储结果
    """
    manager = get_storage_manager()
    return manager.store_uploaded_file(source_path, original_filename, task_id)

def get_file_path(file_id: str) -> Optional[str]:
    """
    快捷函数：获取文件路径
    
    Args:
        file_id: 文件ID
        
    Returns:
        文件路径
    """
    manager = get_storage_manager()
    return manager.get_file_path(file_id)
