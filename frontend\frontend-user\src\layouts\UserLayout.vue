<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <!-- Logo和导航 -->
          <div class="flex items-center">
            <router-link to="/" class="flex items-center">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
              <span class="ml-2 text-xl font-bold text-gray-900 dark:text-white">Word文档分析</span>
            </router-link>
            
            <nav class="hidden md:ml-8 md:flex md:space-x-8">
              <router-link
                to="/dashboard"
                class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                仪表盘
              </router-link>
              <router-link
                to="/documents"
                class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                文档管理
              </router-link>
              <router-link
                to="/tasks"
                class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                任务中心
              </router-link>
            </nav>
          </div>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <button
              @click="toggleTheme"
              class="text-gray-500 hover:text-gray-700 p-2 rounded-md"
            >
              <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 17.77L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z" />
              </svg>
            </button>
            
            <div class="relative" v-if="user">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <img
                  class="h-8 w-8 rounded-full"
                  :src="user.avatar || '/default-avatar.png'"
                  :alt="user.username"
                />
              </button>
              
              <!-- 用户菜单下拉 -->
              <div
                v-show="showUserMenu"
                class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
              >
                <div class="py-1">
                  <router-link
                    to="/profile"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    个人中心
                  </router-link>
                  <router-link
                    to="/orders"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    订单管理
                  </router-link>
                  <button
                    @click="logout"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    退出登录
                  </button>
                </div>
              </div>
            </div>
            
            <router-link
              v-else
              to="/auth"
              class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
            >
              登录
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="text-center text-sm text-gray-500">
          <p>&copy; 2025 Word文档分析服务. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'

const router = useRouter()
const userStore = useUserStore()
const themeStore = useThemeStore()

const showUserMenu = ref(false)

const user = computed(() => userStore.currentUser)

const toggleTheme = () => {
  themeStore.toggleTheme()
}

const logout = async () => {
  userStore.logout()
  router.push('/auth')
}
</script>

<style scoped>
.router-link-active {
  @apply text-blue-600 bg-blue-50;
}
</style> 