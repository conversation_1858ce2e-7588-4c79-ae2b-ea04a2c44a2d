{"success": true, "processing_time": 2.8626182079315186, "error_message": null, "warnings": [], "document_info": {"title": "新媒体技术对舞蹈编导创作手法的影响研究", "author": "", "major": "", "department": "", "student_id": "", "advisor": "", "date": "", "degree_type": "", "school": ""}, "content_stats": {"page_size": "612x792", "font_count": 2, "fonts_used": ["MS Mincho", "Cambria"], "line_count": 22, "page_count": 1, "word_count": 336, "chart_count": 0, "field_count": 0, "image_count": 0, "margin_info": {"top": 72.0, "left": 90.0, "right": 90.0, "bottom": 72.0}, "style_count": 1, "styles_used": [{"name": "正文", "count": 14}], "table_count": 0, "comment_count": 0, "drawing_count": 0, "endnote_count": 0, "formula_count": 0, "heading_count": 0, "section_count": 1, "textbox_count": 0, "version_count": 0, "bookmark_count": 0, "equation_count": 0, "footnote_count": 0, "grammar_errors": 0, "revision_count": 0, "character_count": 674, "hyperlink_count": 0, "paragraph_count": 11, "reference_count": 0, "spelling_errors": 0, "page_orientation": "portrait", "line_spacing_info": {"13.800000190734863": 14}, "track_changes_count": 0, "characters_with_spaces": 736}, "document_structures": [{"name": "中文摘要", "status": "present", "type": "standard", "page": 1, "content": {"text": "摘要", "style": "正文", "paragraph_index": 2}, "identifiers_matched": [], "required": true, "count": "129字"}, {"name": "中文关键词", "status": "present", "type": "standard", "page": 1, "content": {"text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法", "style": "正文", "paragraph_index": 4}, "identifiers_matched": [], "required": true, "count": "5个"}, {"name": "英文摘要", "status": "present", "type": "standard", "page": 1, "content": {"text": "ABSTRACT", "style": "正文", "paragraph_index": 5}, "identifiers_matched": [], "required": true, "count": "49词"}, {"name": "英文关键词", "status": "present", "type": "standard", "page": 1, "content": {"text": "Key Words: dance creation; virtual reality; augmented reality; short video communication; choreographic techniques", "style": "正文", "paragraph_index": 7}, "identifiers_matched": [], "required": true, "count": "5个"}, {"name": "正文", "status": "present", "type": "standard", "page": 1, "content": {"text": "第一章 绪论", "style": "正文", "paragraph_index": 8}, "identifiers_matched": [], "required": true, "count": "90字"}, {"name": "封面", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "任务书", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "开题报告", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "诚信声明", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "版权声明", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "目录", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "参考文献", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "致谢", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}, {"name": "附录", "status": "missing", "type": "standard", "page": null, "content": {}, "identifiers_matched": [], "required": true, "count": "0字"}], "detection_standard": null, "standard_name": "论文检测", "task_type": "paper_check", "status": "completed", "compliance_score": 85.0, "problems_found": 0, "check_result": [{"issues": [], "passed": false, "details": {"error_count": 9, "order_errors": [], "warning_count": 0, "detailed_report": "📋 检测报告\n========================================\n\n📚 结构问题:\n  1. ❌ 缺少必需章节：封面\n     💡 建议：请在文档中添加封面部分，确保章节完整性。\n  2. ❌ 缺少必需章节：任务书\n     💡 建议：请在文档中添加任务书部分，确保章节完整性。\n  3. ❌ 缺少必需章节：开题报告\n     💡 建议：请在文档中添加开题报告部分，确保章节完整性。\n  4. ❌ 缺少必需章节：诚信声明\n     💡 建议：请在文档中添加诚信声明部分，确保章节完整性。\n  5. ❌ 缺少必需章节：版权声明\n     💡 建议：请在文档中添加版权声明部分，确保章节完整性。\n  6. ❌ 缺少必需章节：目录\n     💡 建议：请在文档中添加目录部分，确保章节完整性。\n  7. ❌ 缺少必需章节：参考文献\n     💡 建议：请在文档中添加参考文献部分，确保章节完整性。\n  8. ❌ 缺少必需章节：致谢\n     💡 建议：请在文档中添加致谢部分，确保章节完整性。\n  9. ❌ 缺少必需章节：附录\n     💡 建议：请在文档中添加附录部分，确保章节完整性。\n\n📊 总结：共发现9个问题", "missing_sections": ["封面", "任务书", "开题报告", "诚信声明", "版权声明", "目录", "参考文献", "致谢", "附录"], "identified_sections": ["中文摘要", "中文关键词", "英文摘要", "英文关键词", "正文"], "total_sections_found": 5, "required_sections_count": 14}, "message": "发现9个错误。 主要问题：缺少必需章节：封面; 缺少必需章节：任务书; 缺少必需章节：开题报告等", "rule_id": "structure.section_order", "metadata": {}, "position": null, "severity": "error", "rule_name": "章节顺序检查", "timestamp": "2025-08-02T12:52:42.475674", "suggestions": [], "execution_time": 0.0003505000004224712}, {"issues": [], "passed": true, "details": {"keyword_count": 5, "abstract_length": 127}, "message": "摘要关键词检查通过", "rule_id": "structure.abstract_and_keywords", "metadata": {}, "position": null, "severity": "info", "rule_name": "摘要关键词检查", "timestamp": "2025-08-02T12:52:42.475674", "suggestions": [], "execution_time": 3.8500000300700776e-05}, {"issues": [], "passed": false, "details": {}, "message": "内部错误: 'dict' object has no attribute 'document_structures'", "rule_id": "content.chinese_abstract_word_count", "metadata": {"error": true}, "position": null, "severity": "critical", "rule_name": "中文摘要字数检查", "timestamp": "2025-08-02T12:52:42.479674", "suggestions": [], "execution_time": 0.0}, {"issues": [], "passed": false, "details": {}, "message": "内部错误: 'dict' object has no attribute 'document_structures'", "rule_id": "content.english_abstract_word_count", "metadata": {"error": true}, "position": null, "severity": "critical", "rule_name": "英文摘要词数检查", "timestamp": "2025-08-02T12:52:42.480702", "suggestions": [], "execution_time": 0.0}, {"issues": [], "passed": false, "details": {}, "message": "内部错误: 'dict' object has no attribute 'document_structures'", "rule_id": "content.chinese_keywords_count", "metadata": {"error": true}, "position": null, "severity": "critical", "rule_name": "中文关键词数量检查", "timestamp": "2025-08-02T12:52:42.482675", "suggestions": [], "execution_time": 0.0}, {"issues": [], "passed": false, "details": {}, "message": "内部错误: 'dict' object has no attribute 'document_structures'", "rule_id": "content.english_keywords_count", "metadata": {"error": true}, "position": null, "severity": "critical", "rule_name": "英文关键词数量检查", "timestamp": "2025-08-02T12:52:42.484676", "suggestions": [], "execution_time": 0.0}, {"issues": [], "passed": false, "details": {}, "message": "内部错误: 'dict' object has no attribute 'document_structures'", "rule_id": "content.references_item_count", "metadata": {"error": true}, "position": null, "severity": "critical", "rule_name": "参考文献条目数量检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 0.0}, {"issues": [], "passed": true, "details": {"actual_setup": {}}, "message": "页面设置检查通过（基础检查）", "rule_id": "format.page_setup", "metadata": {}, "position": null, "severity": "info", "rule_name": "页面设置检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 4.540000009001233e-05}, {"issues": [], "passed": true, "details": {"total_paragraphs": 7, "checked_paragraphs": 7}, "message": "正文格式检查通过，检查了7个段落", "rule_id": "format.body_text", "metadata": {}, "position": null, "severity": "info", "rule_name": "正文格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 5.710000004910398e-05}, {"issues": [], "passed": false, "details": {}, "message": "未找到1级标题", "rule_id": "format.headings", "metadata": {}, "position": null, "severity": "warning", "rule_name": "标题格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 2.3399999918183312e-05}, {"issues": [], "passed": false, "details": {}, "message": "未找到1级标题", "rule_id": "format.headings", "metadata": {}, "position": null, "severity": "warning", "rule_name": "标题格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 1.680000059423037e-05}, {"issues": [], "passed": false, "details": {}, "message": "未找到1级标题", "rule_id": "format.headings", "metadata": {}, "position": null, "severity": "warning", "rule_name": "标题格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 1.4700000974698924e-05}, {"issues": [], "passed": true, "details": {}, "message": "页眉页脚格式检查通过（无具体要求）", "rule_id": "format.header_footer", "metadata": {}, "position": null, "severity": "info", "rule_name": "页眉页脚格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 1.359999987471383e-05}, {"issues": [], "passed": false, "details": {}, "message": "未找到参考文献部分", "rule_id": "format.references", "metadata": {}, "position": null, "severity": "warning", "rule_name": "参考文献格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 1.8000000636675395e-05}, {"issues": [], "passed": false, "details": {"format_errors": ["段落 'ABSTRACT': 正文英文字体应使用Times New Roman，期望：Times New Roman，实际：", "段落 'ABSTRACT': 一级标题字号应为三号(16pt)，期望：三号，实际：12.0", "段落 'ABSTRACT': alignment格式不符合要求，期望：center，实际：1", "段落 'ABSTRACT': bold格式不符合要求，期望：True，实际：False"], "checked_paragraphs": 1}, "message": "段落格式检查失败：段落 'ABSTRACT': 正文英文字体应使用Times New Roman，期望：Times New Roman，实际：; 段落 'ABSTRACT': 一级标题字号应为三号(16pt)，期望：三号，实际：12.0...", "rule_id": "format.paragraph", "metadata": {}, "position": null, "severity": "error", "rule_name": "特定段落格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 3.6899999031447805e-05}, {"issues": [], "passed": false, "details": {"format_errors": ["段落 '关键词：舞蹈创作；虚拟现实（VR）；增强...': 正文中文字体应使用宋体，期望：宋体，实际：", "段落 '关键词：舞蹈创作；虚拟现实（VR）；增强...': 正文字号应为小四号(12pt)，期望：小四，实际：12.0", "段落 '关键词：舞蹈创作；虚拟现实（VR）；增强...': alignment格式不符合要求，期望：left，实际：1"], "checked_paragraphs": 1}, "message": "段落格式检查失败：段落 '关键词：舞蹈创作；虚拟现实（VR）；增强...': 正文中文字体应使用宋体，期望：宋体，实际：; 段落 '关键词：舞蹈创作；虚拟现实（VR）；增强...': 正文字号应为小四号(12pt)，期望：小四，实际：12.0...", "rule_id": "format.paragraph", "metadata": {}, "position": null, "severity": "error", "rule_name": "特定段落格式检查", "timestamp": "2025-08-02T12:52:42.486703", "suggestions": [], "execution_time": 0.00013359999866224825}], "check_summary": {"major_problems": 0, "minor_problems": 0, "total_problems": 0, "compliance_score": 85.0}, "analysis_summary": {"document_type": "report", "quality_score": 0, "hierarchy_score": 0, "completeness_score": 0}, "processing_meta": {"processing_times": {"preprocessing": 0.002000093460083008, "raw_extraction": 1.9713902473449707, "structure_analysis": 0.002000570297241211}, "extraction_method": "word_com_pool", "processing_pipeline": "optimized_v2"}}