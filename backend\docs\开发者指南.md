# 开发者指南

本指南为开发者提供了在检测引擎v2.0基础上进行开发的详细说明。

## 1. 系统架构概述

### 1.1 检测引擎v2.0架构

```
检测引擎 v2.0
├── 规则引擎 (RuleEngine)
│   ├── 规则加载器
│   ├── 引用解析器
│   └── 执行调度器
├── 检查函数库 (CHECK_FUNCTIONS)
│   ├── 结构检查函数
│   ├── 格式检查函数
│   └── 内容检查函数
├── 错误处理系统
│   ├── 错误格式化器
│   ├── 消息模板
│   └── 建议生成器
└── 配置系统
    ├── 分层规则配置
    ├── 引用机制
    └── 执行计划
```

### 1.2 核心组件

#### RuleEngine (规则引擎)
- **位置**: `app/checkers/rule_engine.py`
- **功能**: 规则加载、引用解析、检测执行
- **特性**: 支持$ref引用、循环检测、异步执行

#### CHECK_FUNCTIONS (检查函数库)
- **位置**: `app/services/document_analyzer.py`
- **功能**: 具体的检测逻辑实现
- **特性**: 标准化接口、统一返回格式

#### ErrorFormatter (错误格式化器)
- **位置**: `app/utils/error_formatter.py`
- **功能**: 错误消息格式化和用户建议
- **特性**: 多语言支持、分类管理

## 2. 开发环境设置

### 2.1 依赖安装

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 2.2 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置
vim .env
```

### 2.3 数据库初始化

```bash
# 运行数据库迁移
alembic upgrade head

# 创建测试数据
python scripts/create_test_data.py
```

## 3. 开发工作流

### 3.1 添加新检查函数

1. **定义检查函数**
   ```python
   def check_new_feature(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
       """新功能检查函数"""
       # 实现检查逻辑
       return CheckResult(...)
   ```

2. **注册检查函数**
   ```python
   CHECK_FUNCTIONS["check_new_feature"] = check_new_feature
   ```

3. **配置规则**
   ```json
   {
     "rules": {
       "format": {
         "new_feature": {
           "name": "新功能检查",
           "check_function": "check_new_feature",
           "parameters": {...}
         }
       }
     }
   }
   ```

4. **编写测试**
   ```python
   def test_check_new_feature():
       # 测试代码
       pass
   ```

### 3.2 修改现有规则

1. **修改规则配置**
   - 编辑 `config/rules/hbkj_bachelor_2024.json`
   - 使用引用机制避免重复

2. **更新检查函数**
   - 修改对应的检查函数
   - 保持向后兼容性

3. **更新测试**
   - 修改相关测试用例
   - 确保测试覆盖率

### 3.3 添加新规则标准

1. **创建新规则文件**
   ```bash
   cp config/rules/hbkj_bachelor_2024.json config/rules/new_standard_2024.json
   ```

2. **修改元数据**
   ```json
   {
     "metadata": {
       "standard_id": "new_standard_2024",
       "name": "新标准检测规则",
       "version": "1.0.0"
     }
   }
   ```

3. **配置规则内容**
   - 定义共享配置
   - 配置具体规则
   - 设置执行计划

## 4. 测试指南

### 4.1 单元测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_rule_engine.py

# 运行特定测试函数
pytest tests/test_rule_engine.py::test_load_rules

# 生成覆盖率报告
pytest --cov=app tests/
```

### 4.2 集成测试

```bash
# 运行集成测试
pytest tests/test_integration.py

# 运行边界情况测试
pytest tests/test_edge_cases_and_exceptions.py
```

### 4.3 性能测试

```bash
# 运行性能测试
python scripts/performance_test.py

# 内存使用测试
python scripts/memory_test.py
```

## 5. 调试技巧

### 5.1 规则调试

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 单独测试规则
rule_engine = RuleEngine(CHECK_FUNCTIONS)
rule_engine.load_rules_from_file("config/rules/hbkj_bachelor_2024.json")
result = await rule_engine.execute_single_rule("format.level_1_title", test_doc)
```

### 5.2 引用调试

```python
# 检查引用解析
resolved_params = rule_engine._resolve_params({"$ref": "#/definitions/styles/title_style"})
print(resolved_params)
```

### 5.3 错误调试

```python
# 使用错误格式化器
from app.utils.error_formatter import error_formatter

errors = [{"code": "MISSING_SECTION", "section_name": "摘要"}]
formatted = error_formatter.format_multiple_errors(errors)
print(error_formatter.create_detailed_report(formatted))
```

## 6. 代码规范

### 6.1 Python代码规范

- 使用 **Black** 进行代码格式化
- 使用 **isort** 进行导入排序
- 使用 **flake8** 进行代码检查
- 使用 **mypy** 进行类型检查

```bash
# 格式化代码
black app/ tests/

# 排序导入
isort app/ tests/

# 代码检查
flake8 app/ tests/

# 类型检查
mypy app/
```

### 6.2 文档规范

- 所有函数必须有docstring
- 使用Google风格的docstring
- 重要的类和模块需要详细说明

```python
def check_example(doc_data: DocumentData, params: Dict[str, Any]) -> CheckResult:
    """
    示例检查函数。
    
    Args:
        doc_data: 文档数据，包含文档内容和元信息
        params: 检查参数，已解析所有引用
        
    Returns:
        CheckResult: 检查结果，包含通过状态、消息等信息
        
    Raises:
        DocumentAnalysisException: 当文档数据无效时抛出
    """
    pass
```

### 6.3 测试规范

- 测试函数名以 `test_` 开头
- 使用描述性的测试名称
- 每个测试只验证一个功能点
- 使用fixture提高测试复用性

```python
def test_check_function_returns_correct_result():
    """测试检查函数返回正确的结果"""
    # Arrange
    doc_data = create_test_document()
    params = {"min_length": 100}
    
    # Act
    result = check_content_length(doc_data, params)
    
    # Assert
    assert isinstance(result, CheckResult)
    assert result.rule_id == "content.length_check"
```

## 7. 部署指南

### 7.1 开发环境部署

```bash
# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 7.2 生产环境部署

```bash
# 使用Gunicorn启动
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用Docker部署
docker build -t paper-check-backend .
docker run -p 8000:8000 paper-check-backend
```

### 7.3 配置管理

- 使用环境变量管理配置
- 敏感信息使用密钥管理系统
- 不同环境使用不同的配置文件

## 8. 监控和维护

### 8.1 日志管理

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)
```

### 8.2 性能监控

- 使用APM工具监控性能
- 定期检查内存使用
- 监控API响应时间

### 8.3 错误追踪

- 使用Sentry等工具追踪错误
- 定期检查错误日志
- 及时修复发现的问题

## 9. 常见问题

### 9.1 规则加载失败

**问题**: 规则文件加载失败
**解决**: 检查JSON格式、引用路径、文件权限

### 9.2 检查函数未找到

**问题**: 执行时提示检查函数未找到
**解决**: 确认函数已在CHECK_FUNCTIONS中注册

### 9.3 引用解析错误

**问题**: $ref引用解析失败
**解决**: 检查引用路径是否正确、被引用的配置是否存在

### 9.4 性能问题

**问题**: 检测执行时间过长
**解决**: 优化检查函数逻辑、使用缓存、减少不必要的计算

## 10. 贡献指南

### 10.1 提交代码

1. Fork项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

### 10.2 代码审查

- 所有代码必须经过审查
- 确保测试通过
- 检查代码质量和规范

### 10.3 文档更新

- 新功能需要更新文档
- 保持文档与代码同步
- 提供清晰的使用示例

---

这份开发者指南提供了在检测引擎v2.0基础上进行开发的完整指导。如有疑问，请参考相关文档或联系开发团队。
