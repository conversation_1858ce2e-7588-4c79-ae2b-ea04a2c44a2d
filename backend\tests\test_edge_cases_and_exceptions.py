"""
边界情况和异常处理测试

测试检测引擎在各种极端情况下的稳定性和健壮性。
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch

from app.checkers.rule_engine import RuleEngine
from app.services.document_analyzer import CHECK_FUNCTIONS, create_formatted_check_result
from app.services.document_processor import DocumentData
from app.models.check_result import CheckResult, CheckSeverity
from app.core.exceptions import ConfigurationError
from app.utils.error_formatter import error_formatter, ErrorCategory, ErrorSeverity as ErrSeverity


class TestEdgeCasesAndExceptions:
    """边界情况和异常处理测试"""

    @pytest.fixture
    def rule_engine(self):
        """规则引擎fixture"""
        engine = RuleEngine(CHECK_FUNCTIONS)
        return engine

    @pytest.fixture
    def empty_document(self):
        """空文档fixture"""
        return DocumentData(
            file_path="empty.docx",
            doc_info={},
            content_stats={},
            elements=[]
        )

    @pytest.fixture
    def malformed_document(self):
        """格式错误的文档fixture"""
        return DocumentData(
            file_path="malformed.docx",
            doc_info=None,  # None值
            content_stats={"invalid_key": "invalid_value"},
            elements=[
                {"type": None, "text": None},  # None值
                {"invalid_structure": True},  # 缺少必需字段
                {"type": "paragraph", "text": ""},  # 空文本
                {"type": "heading", "level": "invalid"},  # 无效级别
            ]
        )

    @pytest.fixture
    def extreme_document(self):
        """极端情况文档fixture"""
        return DocumentData(
            file_path="extreme.docx",
            doc_info={
                "page_count": 0,  # 零页
                "word_count": -1,  # 负数
                "creation_time": "invalid_date"
            },
            content_stats={
                "total_paragraphs": 10000,  # 极大数值
                "total_headings": 0
            },
            elements=[
                # 超长文本
                {"type": "paragraph", "text": "A" * 100000},
                # 特殊字符
                {"type": "paragraph", "text": "测试\n\r\t\0特殊字符"},
                # Unicode字符
                {"type": "paragraph", "text": "🎉📝✅❌⚠️💡"},
                # 空白字符
                {"type": "paragraph", "text": "   \n\t\r   "},
            ]
        )

    def test_empty_document_handling(self, rule_engine, empty_document):
        """测试空文档处理"""
        # 所有检查函数都应该能处理空文档
        for func_name, func in CHECK_FUNCTIONS.items():
            try:
                result = func(empty_document, {})
                assert isinstance(result, CheckResult)
                assert hasattr(result, 'passed')
                assert hasattr(result, 'message')
                print(f"✅ {func_name}: 正确处理空文档")
            except Exception as e:
                pytest.fail(f"函数 {func_name} 处理空文档时出错: {e}")

    def test_malformed_document_handling(self, rule_engine, malformed_document):
        """测试格式错误文档处理"""
        for func_name, func in CHECK_FUNCTIONS.items():
            try:
                result = func(malformed_document, {})
                assert isinstance(result, CheckResult)
                # 应该能够处理而不崩溃
                print(f"✅ {func_name}: 正确处理格式错误文档")
            except Exception as e:
                # 记录但不失败，某些函数可能合理地抛出异常
                print(f"⚠️ {func_name}: 处理格式错误文档时抛出异常: {e}")

    def test_extreme_values_handling(self, rule_engine, extreme_document):
        """测试极端值处理"""
        for func_name, func in CHECK_FUNCTIONS.items():
            try:
                result = func(extreme_document, {})
                assert isinstance(result, CheckResult)
                print(f"✅ {func_name}: 正确处理极端值")
            except Exception as e:
                print(f"⚠️ {func_name}: 处理极端值时出错: {e}")

    def test_invalid_parameters(self, rule_engine, empty_document):
        """测试无效参数处理"""
        invalid_params_list = [
            None,  # None参数
            {"invalid_key": "invalid_value"},  # 无效键值
            {"rule_id": None},  # None值
            {"min": "not_a_number"},  # 类型错误
            {"style": {"font_size": "invalid"}},  # 嵌套无效值
        ]

        for func_name, func in CHECK_FUNCTIONS.items():
            for invalid_params in invalid_params_list:
                try:
                    result = func(empty_document, invalid_params or {})
                    assert isinstance(result, CheckResult)
                    print(f"✅ {func_name}: 正确处理无效参数 {invalid_params}")
                except Exception as e:
                    print(f"⚠️ {func_name}: 处理无效参数时出错: {e}")

    @pytest.mark.asyncio
    async def test_rule_engine_with_invalid_rules(self, rule_engine):
        """测试规则引擎处理无效规则"""
        # 测试无效的规则配置
        invalid_configs = [
            {},  # 空配置
            {"metadata": {}},  # 缺少必需字段
            {"metadata": {"standard_id": "test"}, "rules": None},  # None规则
            {"metadata": {"standard_id": "test"}, "rules": {"invalid": "structure"}},  # 无效结构
        ]

        for config in invalid_configs:
            try:
                rule_engine.metadata = config.get("metadata", {})
                rule_engine.rules = config.get("rules", {})
                rule_engine.execution_plan = config.get("execution_plan", [])
                
                # 尝试执行检测
                results = await rule_engine.execute_check(DocumentData("test.docx", {}, {}, []))
                print(f"✅ 处理无效配置: {type(config)}")
            except Exception as e:
                print(f"⚠️ 处理无效配置时出错: {e}")

    def test_circular_reference_detection(self, rule_engine):
        """测试循环引用检测"""
        # 创建循环引用的配置
        rule_engine.definitions = {
            "styles": {
                "style_a": {"$ref": "#/definitions/styles/style_b"},
                "style_b": {"$ref": "#/definitions/styles/style_a"}
            }
        }

        with pytest.raises(ConfigurationError, match="循环引用"):
            rule_engine._resolve_params({"$ref": "#/definitions/styles/style_a"})

    def test_deep_reference_nesting(self, rule_engine):
        """测试深层引用嵌套"""
        # 创建深层嵌套的引用
        rule_engine.definitions = {
            "level1": {"$ref": "#/definitions/level2"},
            "level2": {"$ref": "#/definitions/level3"},
            "level3": {"$ref": "#/definitions/level4"},
            "level4": {"$ref": "#/definitions/level5"},
            "level5": {"value": "final_value"}
        }

        try:
            result = rule_engine._resolve_params({"$ref": "#/definitions/level1"})
            assert result["value"] == "final_value"
            print("✅ 正确处理深层引用嵌套")
        except Exception as e:
            pytest.fail(f"深层引用嵌套处理失败: {e}")

    def test_memory_usage_with_large_documents(self):
        """测试大文档的内存使用"""
        # 创建大文档
        large_elements = []
        for i in range(10000):
            large_elements.append({
                "type": "paragraph",
                "text": f"这是第{i}段内容，包含一些测试文本。" * 10
            })

        large_doc = DocumentData(
            file_path="large.docx",
            doc_info={"page_count": 1000, "word_count": 500000},
            content_stats={"total_paragraphs": 10000},
            elements=large_elements
        )

        # 测试内存使用
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss

        # 执行检查
        from app.services.document_analyzer import check_text_format
        result = check_text_format(large_doc, {})

        memory_after = process.memory_info().rss
        memory_increase = memory_after - memory_before

        print(f"内存使用增加: {memory_increase / 1024 / 1024:.2f} MB")
        
        # 内存增加应该在合理范围内（小于100MB）
        assert memory_increase < 100 * 1024 * 1024, f"内存使用过多: {memory_increase / 1024 / 1024:.2f} MB"

    def test_concurrent_execution(self, rule_engine):
        """测试并发执行"""
        import threading
        import time

        results = []
        errors = []

        def worker(worker_id):
            try:
                doc = DocumentData(
                    file_path=f"test_{worker_id}.docx",
                    doc_info={"page_count": 10},
                    content_stats={"total_paragraphs": 20},
                    elements=[{"type": "paragraph", "text": f"Worker {worker_id} content"}]
                )
                
                from app.services.document_analyzer import check_text_format
                result = check_text_format(doc, {})
                results.append((worker_id, result))
            except Exception as e:
                errors.append((worker_id, e))

        # 创建多个线程
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        assert len(results) == 10, f"期望10个结果，实际{len(results)}个"
        assert len(errors) == 0, f"并发执行出现错误: {errors}"
        print("✅ 并发执行测试通过")

    def test_error_formatter_edge_cases(self):
        """测试错误格式化器的边界情况"""
        # 测试空错误列表
        summary = error_formatter.create_summary_message([])
        assert "检查通过" in summary

        # 测试无效错误代码
        error_detail = error_formatter.format_error("INVALID_CODE")
        assert error_detail.code == "INVALID_CODE"

        # 测试格式化参数缺失
        error_detail = error_formatter.format_error("MISSING_SECTION", invalid_param="test")
        assert error_detail.message is not None

        print("✅ 错误格式化器边界情况测试通过")

    def test_unicode_and_special_characters(self, rule_engine):
        """测试Unicode和特殊字符处理"""
        special_doc = DocumentData(
            file_path="special_chars.docx",
            doc_info={"page_count": 1},
            content_stats={"total_paragraphs": 4},
            elements=[
                {"type": "paragraph", "text": "中文测试内容"},
                {"type": "paragraph", "text": "English content"},
                {"type": "paragraph", "text": "Русский текст"},
                {"type": "paragraph", "text": "🎉📝✅❌⚠️💡🔍📊"},
                {"type": "paragraph", "text": "Special chars: @#$%^&*()[]{}|\\:;\"'<>,.?/"},
                {"type": "paragraph", "text": "\n\r\t\0\x01\x02"},  # 控制字符
            ]
        )

        for func_name, func in CHECK_FUNCTIONS.items():
            try:
                result = func(special_doc, {})
                assert isinstance(result, CheckResult)
                print(f"✅ {func_name}: 正确处理特殊字符")
            except Exception as e:
                print(f"⚠️ {func_name}: 处理特殊字符时出错: {e}")

    @pytest.mark.asyncio
    async def test_timeout_handling(self, rule_engine):
        """测试超时处理"""
        # 模拟慢速检查函数
        def slow_check_function(doc_data, params):
            import time
            time.sleep(2)  # 模拟慢速操作
            return CheckResult("test", "测试", True, CheckSeverity.INFO, "慢速检查完成")

        # 替换一个检查函数
        original_func = CHECK_FUNCTIONS.get("check_text_format")
        CHECK_FUNCTIONS["check_text_format"] = slow_check_function

        try:
            # 设置超时
            doc = DocumentData("test.docx", {}, {}, [])
            
            start_time = asyncio.get_event_loop().time()
            result = slow_check_function(doc, {})
            end_time = asyncio.get_event_loop().time()
            
            execution_time = end_time - start_time
            print(f"慢速函数执行时间: {execution_time:.2f}秒")
            
            assert execution_time >= 2.0, "函数应该至少执行2秒"
            
        finally:
            # 恢复原函数
            if original_func:
                CHECK_FUNCTIONS["check_text_format"] = original_func

    def test_resource_cleanup(self, rule_engine):
        """测试资源清理"""
        # 创建大量临时对象
        temp_objects = []
        for i in range(1000):
            doc = DocumentData(
                file_path=f"temp_{i}.docx",
                doc_info={"page_count": i},
                content_stats={"total_paragraphs": i * 10},
                elements=[{"type": "paragraph", "text": f"Content {i}"}] * 100
            )
            temp_objects.append(doc)

        # 执行检查
        from app.services.document_analyzer import check_content_length
        for doc in temp_objects[:10]:  # 只测试前10个
            result = check_content_length(doc, {"min": 1, "max": 1000, "unit": "字"})
            assert isinstance(result, CheckResult)

        # 清理
        del temp_objects
        import gc
        gc.collect()

        print("✅ 资源清理测试完成")

    def test_create_formatted_check_result(self):
        """测试格式化检查结果创建函数"""
        # 测试成功情况
        result = create_formatted_check_result(
            rule_id="test.rule",
            rule_name="测试规则",
            success_message="测试成功"
        )
        assert result.passed is True
        assert result.severity == CheckSeverity.INFO

        # 测试错误情况
        errors = [{"code": "MISSING_SECTION", "section_name": "摘要"}]
        result = create_formatted_check_result(
            rule_id="test.rule",
            rule_name="测试规则",
            errors=errors
        )
        assert result.passed is False
        assert result.severity == CheckSeverity.ERROR

        # 测试警告情况
        warnings = [{"code": "PAGE_SETUP_ERROR", "setting_name": "边距"}]
        result = create_formatted_check_result(
            rule_id="test.rule", 
            rule_name="测试规则",
            warnings=warnings
        )
        assert result.passed is True
        assert result.severity == CheckSeverity.WARNING

        print("✅ 格式化检查结果创建测试通过")
