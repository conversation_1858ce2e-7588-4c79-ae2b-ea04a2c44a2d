/**
 * 前端性能优化工具类
 * 提供懒加载、缓存、防抖等性能优化功能
 */

// 图片懒加载
export class LazyLoader {
  private observer: IntersectionObserver | null = null
  private images: Set<HTMLImageElement> = new Set()

  constructor() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              this.loadImage(entry.target as HTMLImageElement)
            }
          })
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      )
    }
  }

  observe(img: HTMLImageElement) {
    if (this.observer && img.dataset.src) {
      this.images.add(img)
      this.observer.observe(img)
    }
  }

  private loadImage(img: HTMLImageElement) {
    if (img.dataset.src) {
      img.src = img.dataset.src
      img.removeAttribute('data-src')
      this.observer?.unobserve(img)
      this.images.delete(img)
    }
  }

  disconnect() {
    this.observer?.disconnect()
    this.images.clear()
  }
}

// 内存缓存管理
export class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private maxSize: number
  private defaultTTL: number

  constructor(maxSize = 50, defaultTTL = 5 * 60 * 1000) { // 默认5分钟
    this.maxSize = maxSize
    this.defaultTTL = defaultTTL
  }

  set<T>(key: string, data: T, ttl?: number): void {
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    })
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

// 虚拟滚动类
export class VirtualScroll {
  private container: HTMLElement
  private items: any[]
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0

  constructor(
    container: HTMLElement,
    items: any[],
    itemHeight: number,
    visibleCount: number
  ) {
    this.container = container
    this.items = items
    this.itemHeight = itemHeight
    this.visibleCount = visibleCount
    this.setupScrollListener()
  }

  private setupScrollListener() {
    this.container.addEventListener('scroll', throttle(() => {
      this.updateVisibleRange()
    }, 16)) // 60fps
  }

  private updateVisibleRange() {
    const scrollTop = this.container.scrollTop
    this.startIndex = Math.floor(scrollTop / this.itemHeight)
    this.endIndex = Math.min(
      this.startIndex + this.visibleCount + 1,
      this.items.length
    )
  }

  getVisibleItems() {
    return this.items.slice(this.startIndex, this.endIndex)
  }

  getStartIndex() {
    return this.startIndex
  }

  getEndIndex() {
    return this.endIndex
  }
}

// 性能监控
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // 测量函数执行时间
  measureFunction<T extends (...args: any[]) => any>(
    name: string,
    func: T
  ): T {
    return ((...args: any[]) => {
      const start = performance.now()
      const result = func(...args)
      const end = performance.now()
      
      this.recordMetric(name, end - start)
      return result
    }) as T
  }

  // 测量异步函数执行时间
  async measureAsyncFunction<T extends (...args: any[]) => Promise<any>>(
    name: string,
    func: T,
    ...args: Parameters<T>
  ): Promise<ReturnType<T>> {
    const start = performance.now()
    const result = await func(...args)
    const end = performance.now()
    
    this.recordMetric(name, end - start)
    return result
  }

  // 记录指标
  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    const values = this.metrics.get(name)!
    values.push(value)
    
    // 只保留最近100个数据点
    if (values.length > 100) {
      values.shift()
    }
  }

  // 获取指标统计
  getMetricStats(name: string) {
    const values = this.metrics.get(name)
    if (!values || values.length === 0) {
      return null
    }

    const sorted = [...values].sort((a, b) => a - b)
    const sum = values.reduce((a, b) => a + b, 0)
    
    return {
      count: values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)]
    }
  }

  // 获取所有指标
  getAllMetrics() {
    const result: Record<string, any> = {}
    for (const name of this.metrics.keys()) {
      result[name] = this.getMetricStats(name)
    }
    return result
  }
}

// 资源预加载
export class ResourcePreloader {
  private loadedResources = new Set<string>()

  // 预加载图片
  preloadImage(src: string): Promise<void> {
    if (this.loadedResources.has(src)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.loadedResources.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  // 预加载多个图片
  preloadImages(srcs: string[]): Promise<void[]> {
    return Promise.all(srcs.map(src => this.preloadImage(src)))
  }

  // 预加载组件
  async preloadComponent(loader: () => Promise<any>): Promise<any> {
    try {
      const component = await loader()
      return component
    } catch (error) {
      console.error('Failed to preload component:', error)
      throw error
    }
  }

  // 预加载CSS
  preloadCSS(href: string): Promise<void> {
    if (this.loadedResources.has(href)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = href
      link.onload = () => {
        this.loadedResources.add(href)
        resolve()
      }
      link.onerror = reject
      document.head.appendChild(link)
    })
  }
}

// 全局实例
export const lazyLoader = new LazyLoader()
export const memoryCache = new MemoryCache()
export const performanceMonitor = PerformanceMonitor.getInstance()
export const resourcePreloader = new ResourcePreloader()

// 工具函数
export const performanceUtils = {
  // 获取页面加载性能
  getPageLoadMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    return {
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      request: navigation.responseStart - navigation.requestStart,
      response: navigation.responseEnd - navigation.responseStart,
      domParse: navigation.domContentLoadedEventStart - navigation.responseEnd,
      domReady: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      total: navigation.loadEventEnd - navigation.navigationStart
    }
  },

  // 获取内存使用情况
  getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100
      }
    }
    return null
  },

  // 获取网络信息
  getNetworkInfo() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
    }
    return null
  }
}

export default {
  LazyLoader,
  MemoryCache,
  VirtualScroll,
  PerformanceMonitor,
  ResourcePreloader,
  debounce,
  throttle,
  lazyLoader,
  memoryCache,
  performanceMonitor,
  resourcePreloader,
  performanceUtils
} 