#!/usr/bin/env python3
"""
测试更新后的schema是否能正确验证规则文件
"""

import json
import jsonschema
from jsonschema import validate, ValidationError

def test_schema_validation():
    """测试schema验证功能"""
    
    print("🔍 测试Schema验证功能")
    print("=" * 50)
    
    try:
        # 1. 加载schema文件
        print("1️⃣ 加载Schema文件...")
        with open('backend/config/rules/rule_schema.json', 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        print(f"✅ Schema加载成功，版本: {schema.get('version', 'unknown')}")
        print(f"   描述: {schema.get('$comment', 'no comment')}")
        
        # 2. 加载规则文件
        print("\n2️⃣ 加载规则文件...")
        with open('backend/config/rules/hbkj_bachelor_2024.json', 'r', encoding='utf-8') as f:
            rules = json.load(f)
        
        print("✅ 规则文件加载成功")
        print(f"   标准ID: {rules['metadata']['standard_id']}")
        print(f"   版本: {rules['metadata']['version']}")
        
        # 3. 执行schema验证
        print("\n3️⃣ 执行Schema验证...")
        
        try:
            validate(instance=rules, schema=schema)
            print("✅ Schema验证通过！")
        except ValidationError as e:
            print(f"❌ Schema验证失败: {e.message}")
            print(f"   错误路径: {' -> '.join(str(p) for p in e.path)}")
            return False
        
        # 4. 验证配置驱动参数
        print("\n4️⃣ 验证配置驱动参数...")
        
        content_rules = rules.get('rules', {}).get('content', {})
        
        # 检查每个内容规则的参数
        for rule_key, rule_config in content_rules.items():
            params = rule_config.get('parameters', {})
            check_function = rule_config.get('check_function')
            
            print(f"   检查规则: {rule_key}")
            print(f"     检查函数: {check_function}")
            
            if check_function == 'check_content_length':
                # 验证必需参数
                required_params = ['unit', 'target_structure', 'count_method', 'errorMessage']
                missing_params = []
                
                for param in required_params:
                    if param not in params:
                        missing_params.append(param)
                
                if missing_params:
                    print(f"     ❌ 缺失参数: {missing_params}")
                    return False
                else:
                    print(f"     ✅ 参数完整")
                    print(f"       目标结构: {params['target_structure']}")
                    print(f"       统计方法: {params['count_method']}")
                    print(f"       计量单位: {params['unit']}")
        
        # 5. 验证参数值的有效性
        print("\n5️⃣ 验证参数值的有效性...")
        
        valid_count_methods = ["characters", "english_words", "keywords", "references", "pages"]
        valid_units = ["字", "词", "个", "条", "页"]
        
        for rule_key, rule_config in content_rules.items():
            params = rule_config.get('parameters', {})
            
            count_method = params.get('count_method')
            unit = params.get('unit')
            
            if count_method and count_method not in valid_count_methods:
                print(f"❌ 无效的统计方法: {count_method} (规则: {rule_key})")
                return False
            
            if unit and unit not in valid_units:
                print(f"❌ 无效的计量单位: {unit} (规则: {rule_key})")
                return False
        
        print("✅ 所有参数值验证通过")
        
        # 6. 验证执行计划的完整性
        print("\n6️⃣ 验证执行计划的完整性...")
        
        execution_plan = rules.get('execution_plan', [])
        all_rules = set()
        
        # 收集所有规则引用
        for phase in execution_plan:
            phase_rules = phase.get('rules', [])
            for rule_ref in phase_rules:
                ref_path = rule_ref.get('$ref', '')
                if ref_path.startswith('#/rules/'):
                    all_rules.add(ref_path)
        
        # 检查是否所有定义的规则都在执行计划中
        defined_rules = set()
        for category in ['structure', 'content', 'format']:
            category_rules = rules.get('rules', {}).get(category, {})
            for rule_key in category_rules.keys():
                defined_rules.add(f"#/rules/{category}/{rule_key}")
        
        missing_in_plan = defined_rules - all_rules
        extra_in_plan = all_rules - defined_rules
        
        if missing_in_plan:
            print(f"⚠️ 执行计划中缺失的规则: {missing_in_plan}")
        
        if extra_in_plan:
            print(f"⚠️ 执行计划中多余的规则: {extra_in_plan}")
        
        if not missing_in_plan and not extra_in_plan:
            print("✅ 执行计划与规则定义完全匹配")
        
        print("\n🎉 Schema验证测试完成！")
        
        # 7. 总结
        print("\n📋 Schema更新总结:")
        print("✅ 增加了配置驱动架构的参数定义")
        print("✅ 支持target_structure、count_method等新参数")
        print("✅ 增加了参数类型和值的验证")
        print("✅ 提供了详细的参数说明文档")
        print("✅ 兼容现有的规则文件结构")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_schema_validation()
    exit(0 if success else 1)
