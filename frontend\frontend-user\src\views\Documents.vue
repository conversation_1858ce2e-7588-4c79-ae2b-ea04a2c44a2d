<template>
  <!-- 使用BaseLayout组件 -->
  <BaseLayout
    title="我的文档"
    description="管理您的所有文档和分析结果"
  >
    <!-- 标题右侧操作按钮 -->
    <template #header-actions>
      <BaseButton
        to="/upload"
        variant="primary"
        prepend-icon="M12 6v6m0 0v6m0-6h6m-6 0H6"
      >
        上传新文档
      </BaseButton>
    </template>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="text-center py-16">
      <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="mt-4 text-gray-600 dark:text-gray-400">正在加载文档列表...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-16 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <p class="text-red-600 dark:text-red-400">加载文档失败</p>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">{{ error }}</p>
      <BaseButton @click="loadDocuments" class="mt-4" variant="primary">重试</BaseButton>
    </div>

    <!-- 主内容 -->
    <div v-else>
      <!-- 筛选和搜索栏 - 移动端优化 -->
      <BaseCard class="mb-6">
        <div class="space-y-4 md:space-y-0 md:grid md:grid-cols-4 md:gap-4">
          <!-- 搜索框 -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 md:hidden">搜索文档</label>
            <BaseInput
              v-model="searchQuery"
              @input="filterDocuments"
              placeholder="搜索文档名称..."
              prepend-icon="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </div>
          
          <!-- 筛选器 - 移动端两列布局 -->
          <div class="grid grid-cols-2 gap-3 md:col-span-2 md:grid-cols-2 md:gap-4">
            <!-- 状态筛选 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 md:hidden">状态筛选</label>
              <BaseInput
                v-model="statusFilter"
                @change="filterDocuments"
                type="select"
                :options="[
                  { value: '', label: '所有状态' },
                  { value: 'completed', label: '已完成' },
                  { value: 'processing', label: '处理中' },
                  { value: 'pending', label: '待处理' },
                  { value: 'failed', label: '失败' }
                ]"
              />
            </div>
            
            <!-- 排序方式 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 md:hidden">排序方式</label>
              <BaseInput
                v-model="sortOrder"
                @change="sortDocuments"
                type="select"
                :options="[
                  { value: 'date-desc', label: '最新上传' },
                  { value: 'date-asc', label: '最早上传' },
                  { value: 'name-asc', label: '文件名A-Z' },
                  { value: 'name-desc', label: '文件名Z-A' },
                  { value: 'size-desc', label: '文件大小↓' },
                  { value: 'size-asc', label: '文件大小↑' }
                ]"
              />
            </div>
          </div>
        </div>
      </BaseCard>

      <!-- 批量操作栏 - 移动端优化 -->
      <BaseCard class="mb-6">
        <!-- 桌面端：单行布局 -->
        <div class="hidden md:flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              已选择 <span class="font-medium text-gray-900 dark:text-gray-100">{{ selectedDocuments.size }}</span> 个文档
            </span>
            <BaseButton @click="selectAllFiltered" variant="link" size="sm" class="no-focus-outline">全选所有</BaseButton>
            <BaseButton @click="selectCurrentPage" variant="link" size="sm" class="no-focus-outline">全选当前页</BaseButton>
            <BaseButton @click="clearSelection" variant="link" size="sm" :disabled="selectedDocuments.size === 0" class="no-focus-outline">清除选择</BaseButton>
          </div>
          <div class="flex items-center space-x-2">
            <BaseButton 
              @click="batchDownload" 
              variant="secondary" 
              size="sm"
              :disabled="selectedDocuments.size === 0"
              prepend-icon="M12 10v6m0 0l-4-4m4 4l4-4m3-6H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"
            >
              批量下载
            </BaseButton>
            <BaseButton 
              @click="showDeleteModal" 
              variant="danger" 
              size="sm"
              :disabled="selectedDocuments.size === 0"
              prepend-icon="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            >
              批量删除
            </BaseButton>
          </div>
        </div>
        
        <!-- 移动端：多行布局 -->
        <div class="md:hidden space-y-3">
          <!-- 第一行：选择状态 -->
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-300">
              已选择 <span class="font-medium text-gray-900 dark:text-gray-100">{{ selectedDocuments.size }}</span> 个文档
            </span>
            <BaseButton @click="clearSelection" variant="link" size="sm" :disabled="selectedDocuments.size === 0" class="no-focus-outline text-xs">清除选择</BaseButton>
          </div>
          
          <!-- 第二行：选择操作 -->
          <div class="flex items-center space-x-2">
            <BaseButton @click="selectAllFiltered" variant="link" size="sm" class="no-focus-outline flex-1 text-xs">全选所有</BaseButton>
            <BaseButton @click="selectCurrentPage" variant="link" size="sm" class="no-focus-outline flex-1 text-xs">全选当前页</BaseButton>
          </div>
          
          <!-- 第三行：批量操作 -->
          <div class="grid grid-cols-2 gap-2">
            <BaseButton 
              @click="batchDownload" 
              variant="secondary" 
              size="sm"
              :disabled="selectedDocuments.size === 0"
              prepend-icon="M12 10v6m0 0l-4-4m4 4l4-4m3-6H5a2 2 0 00-2 2v10a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"
              class="text-xs"
            >
              批量下载
            </BaseButton>
            <BaseButton 
              @click="showDeleteModal" 
              variant="danger" 
              size="sm"
              :disabled="selectedDocuments.size === 0"
              prepend-icon="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              class="text-xs"
            >
              批量删除
            </BaseButton>
          </div>
        </div>
      </BaseCard>

      <!-- 视图切换 - 移动端优化 -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-1 md:space-x-2">
          <BaseButton 
            @click="switchView('grid')" 
            :variant="currentView === 'grid' ? 'primary' : 'secondary'"
            size="sm"
            prepend-icon="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            class="hidden sm:block"
          >
          </BaseButton>
          <BaseButton 
            @click="switchView('grid')" 
            :variant="currentView === 'grid' ? 'primary' : 'secondary'"
            size="sm"
            prepend-icon="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            class="sm:hidden"
          />
          <BaseButton 
            @click="switchView('list')" 
            :variant="currentView === 'list' ? 'primary' : 'secondary'"
            size="sm"
            prepend-icon="M4 6h16M4 10h16M4 14h16M4 18h16"
            class="hidden sm:block"
          >
          </BaseButton>
          <BaseButton 
            @click="switchView('list')" 
            :variant="currentView === 'list' ? 'primary' : 'secondary'"
            size="sm"
            prepend-icon="M4 6h16M4 10h16M4 14h16M4 18h16"
            class="sm:hidden"
          />
        </div>
        <div class="text-xs md:text-sm text-gray-600 dark:text-gray-300">
          共 <span class="font-medium text-gray-900 dark:text-gray-100">{{ filteredDocuments.length }}</span> 个
        </div>
      </div>

      <!-- 文档列表 (网格视图) - 移动端优化 -->
      <div v-show="currentView === 'grid'" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        <BaseCard v-for="doc in paginatedDocuments" :key="doc.document_id" variant="shadow" class="transition-all hover:shadow-lg hover:scale-105 cursor-pointer">
          <div class="flex items-start justify-between mb-3">
            <label class="custom-checkbox">
              <input type="checkbox" @change="toggleSelection(doc.document_id)" :checked="selectedDocuments.has(doc.document_id)">
              <span class="checkbox-visual"></span>
            </label>
            <div class="file-icon docx">
              DOCX
            </div>
          </div>
          <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-2 text-truncate leading-tight">{{ doc.filename }}</h3>
          <div class="space-y-1.5 md:space-y-2 text-xs md:text-sm text-gray-600 dark:text-gray-300">
            <div class="flex justify-between items-center">
              <span>大小</span>
              <span class="text-gray-900 dark:text-gray-100 font-medium">{{ formatFileSize(doc.file_size || 0) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span>类型</span>
              <span class="text-gray-900 dark:text-gray-100 font-medium truncate ml-2">{{ getAnalysisTypeName(getDocumentTask(doc)?.task_type || '') }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span>上传</span>
              <span class="text-gray-900 dark:text-gray-100 font-medium">{{ formatDate(doc.created_at || '') }}</span>
            </div>
          </div>
          <div class="mt-3 md:mt-4">
            <component :is="'div'" v-html="getStatusBadge(doc)"></component>
          </div>
          <div class="mt-3 md:mt-4 flex flex-wrap gap-1.5 md:gap-2">
            <component :is="'div'" v-html="getActionButtons(doc)"></component>
          </div>
        </BaseCard>
        
        <!-- 网格视图空状态 -->
        <div v-if="filteredDocuments.length === 0" class="col-span-full text-center py-12">
          <div class="empty-state">
            <div class="empty-state-icon">📄</div>
            <p class="dark:text-gray-300">没有找到匹配的文档</p>
          </div>
        </div>
      </div>

      <!-- 文档列表 (列表视图) -->
      <BaseCard v-show="currentView === 'list'">
          <div v-if="filteredDocuments.length === 0" class="text-center py-12">
            <div class="empty-state">
              <div class="empty-state-icon">📄</div>
              <p class="dark:text-gray-300">没有找到匹配的文档</p>
            </div>
        </div>
          <div v-else class="overflow-x-auto rounded-lg">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 rounded-lg overflow-hidden">
              <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <label class="custom-checkbox">
                      <input type="checkbox" @change="toggleSelectAll" v-model="selectAll">
                      <span class="checkbox-visual" :class="{
                        'checkbox-partial': selectAllState === 'partial'
                      }"></span>
                    </label>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    文件名
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    文件大小
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  上传时间
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                </th>
              </tr>
            </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="doc in paginatedDocuments" :key="doc.document_id" class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <label class="custom-checkbox">
                      <input type="checkbox" @change="toggleSelection(doc.document_id)" :checked="selectedDocuments.has(doc.document_id)">
                      <span class="checkbox-visual"></span>
                    </label>
                  </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                        <div class="file-icon docx file-icon-small">
                          DOC
                    </div>
                      </div>
                      <div>
                        <p class="font-medium text-gray-900 dark:text-gray-100">{{ doc.filename }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ getAnalysisTypeName(getDocumentTask(doc)?.task_type || '') }}</p>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {{ formatFileSize(doc.file_size || 0) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {{ formatDate(doc.created_at || '', true) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <component :is="'div'" v-html="getStatusBadge(doc)"></component>
                </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <component :is="'div'" v-html="getActionButtons(doc)"></component>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </BaseCard>

      <!-- 分页 -->
      <BasePagination 
        :current-page="currentPage"
        :page-size="pageSize"
        :total="filteredDocuments.length"
        @page-change="goToPage"
      />

    <!-- 删除确认模态框 -->
    <div v-show="isDeleteModalVisible" class="fixed inset-0 bg-gray-900/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-md mx-auto transform transition-all">
        <!-- 头部 -->
        <div class="flex items-center justify-center pt-6 pb-4">
          <div class="flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full">
            <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
          </div>
        </div>
        
        <!-- 内容 -->
        <div class="px-6 pb-6">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white text-center mb-3">
            确认删除文档
          </h3>
          <p class="text-gray-600 dark:text-gray-300 text-center mb-6 leading-relaxed">
            确定要删除选中的文档吗？<br/>
            <span class="text-red-600 dark:text-red-400 font-medium">删除后无法恢复</span>
          </p>
          
          <!-- 文档列表 -->
          <div v-if="selectedDocuments.size > 0" class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6 max-h-32 overflow-y-auto">
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              将删除以下 {{ selectedDocuments.size }} 个文档：
            </p>
            <div class="space-y-1">
              <div v-for="docId in selectedDocuments" :key="docId" class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 text-red-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="truncate">{{ getDocumentName(docId) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 按钮组 -->
          <div class="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
            <BaseButton 
              @click="hideDeleteModal" 
              variant="secondary" 
              class="w-full sm:w-auto px-3 py-3 font-medium"
            >
              取消
            </BaseButton>
            <BaseButton 
              @click="confirmDelete" 
              variant="danger" 
              class="w-full sm:w-auto px-3 py-3 font-medium"
              prepend-icon="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            >
              确认删除
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- 通知容器 -->
    <ToastContainer />
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import BasePagination from '@/components/BasePagination.vue'
import ToastContainer from '@/components/ToastContainer.vue'
import { DocumentApi } from '@/services/documentApi'
import { TaskApi } from '@/services/taskApi'
import type { Document, Task } from '@/types'
import { useNotifications } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'

const router = useRouter()
const documentApi = new DocumentApi()
const taskApi = new TaskApi()
const { addNotification } = useNotifications()

// 状态管理
const isLoading = ref(true)
const error = ref<string | null>(null)
const currentView = ref('list')
const currentPage = ref(1)
const pageSize = ref(15)
const selectedDocuments = ref(new Set<string>())
const isDeleteModalVisible = ref(false)

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const sortOrder = ref('date-desc')
const selectAll = ref(false)

// 真实文档数据
const allDocuments = ref<Document[]>([])
const documentTasks = ref<{ [key: string]: Task }>({})

// 过滤后的文档
const filteredDocuments = computed(() => {
  let filtered = [...allDocuments.value]

  // 搜索过滤
  if (searchQuery.value) {
    filtered = filtered.filter(doc =>
      doc.filename.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(doc => {
      const task = documentTasks.value[doc.task_id]
      return task?.status === statusFilter.value
    })
  }

  return filtered
})

// 分页文档
const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDocuments.value.slice(start, end)
})



// 视图切换按钮样式
const gridViewBtnClass = computed(() => 
  currentView.value === 'grid' 
    ? 'inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors'
    : 'inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors'
)

const listViewBtnClass = computed(() => 
  currentView.value === 'list' 
    ? 'inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors'
    : 'inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors'
)

// 添加一个新的计算属性来确定表头多选框的状态
const selectAllState = computed(() => {
  const currentPageDocIds = paginatedDocuments.value.map(doc => doc.document_id)
  const selectedCurrentPageCount = currentPageDocIds.filter(id => selectedDocuments.value.has(id)).length
  
  if (selectedCurrentPageCount === 0) {
    return 'none' // 未选择
  } else if (selectedCurrentPageCount === currentPageDocIds.length) {
    return 'all' // 全部选择
  } else {
    return 'partial' // 部分选择
  }
})

// 数据加载方法
const loadDocuments = async () => {
  isLoading.value = true
  error.value = null
  
  try {
    console.log('开始加载文档数据...')
    
    // 获取文档列表 - 直接调用API返回原始响应
    const response = await documentApi.getDocuments(1, 100)
    
    // 如果响应是包装格式 {documents: [], pagination: {}}
    if (response && typeof response === 'object' && 'documents' in response) {
      allDocuments.value = (response as any).documents
      console.log('获取到文档:', (response as any).documents.length, '个')
    } else {
      // 如果响应直接是数组
      allDocuments.value = Array.isArray(response) ? response : []
      console.log('获取到文档:', allDocuments.value.length, '个')
    }
    
    // 调试：打印前几个文档的ID格式
    if (allDocuments.value.length > 0) {
      console.log('文档ID格式示例:')
      allDocuments.value.slice(0, 3).forEach((doc, index) => {
        console.log(`文档${index + 1}:`, {
          document_id: doc.document_id,
          task_id: doc.task_id,
          filename: doc.filename,
          id_type: typeof doc.document_id,
          is_uuid: typeof doc.document_id === 'string' && doc.document_id.includes('-')
        })
      })
    }
    
    // 获取每个文档对应的任务信息（如果需要）
    const taskPromises = allDocuments.value.map(async (doc) => {
      try {
        if (doc.task_id) {
          const task = await taskApi.getTask(doc.task_id)
          if (task) {
            documentTasks.value[doc.task_id] = task
          }
        }
      } catch (error) {
        console.warn(`获取任务 ${doc.task_id} 失败:`, error)
      }
    })
    
    await Promise.all(taskPromises)
    console.log('任务信息加载完成')
    
  } catch (err: any) {
    console.error('加载文档数据失败:', err)
    error.value = err.message || '加载文档数据失败'
  } finally {
    isLoading.value = false
  }
}

// 方法
const filterDocuments = () => {
  currentPage.value = 1
}

const sortDocuments = () => {
  // 实现排序逻辑
  const sorted = [...allDocuments.value]
  switch (sortOrder.value) {
    case 'date-desc':
      sorted.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime())
      break
    case 'date-asc':
      sorted.sort((a, b) => new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime())
      break
    case 'name-asc':
      sorted.sort((a, b) => a.filename.localeCompare(b.filename))
      break
    case 'name-desc':
      sorted.sort((a, b) => b.filename.localeCompare(a.filename))
      break
    case 'size-desc':
      sorted.sort((a, b) => (b.file_size || 0) - (a.file_size || 0))
      break
    case 'size-asc':
      sorted.sort((a, b) => (a.file_size || 0) - (b.file_size || 0))
      break
  }
  allDocuments.value = sorted
}

const switchView = (view: string) => {
  currentView.value = view
}

const toggleSelection = (docId: string) => {
  if (selectedDocuments.value.has(docId)) {
    selectedDocuments.value.delete(docId)
  } else {
    selectedDocuments.value.add(docId)
  }
  
  // 同步表头多选框状态
  updateSelectAllState()
}

// 修改updateSelectAllState方法
const updateSelectAllState = () => {
  // 如果当前页面的所有文档都被选中，则勾选表头多选框
  const currentPageDocIds = paginatedDocuments.value.map(doc => doc.document_id)
  const allCurrentPageSelected = currentPageDocIds.every(id => selectedDocuments.value.has(id))
  selectAll.value = allCurrentPageSelected && currentPageDocIds.length > 0
}

const toggleSelectAll = () => {
  if (selectAll.value) {
    // 选择当前页面的所有文档
    paginatedDocuments.value.forEach(doc => selectedDocuments.value.add(doc.document_id))
  } else {
    // 取消选择当前页面的所有文档
    paginatedDocuments.value.forEach(doc => selectedDocuments.value.delete(doc.document_id))
  }
}

const selectAllFiltered = () => {
  filteredDocuments.value.forEach(doc => selectedDocuments.value.add(doc.document_id))
  // 如果选择了所有筛选的文档，同步表头多选框状态
  selectAll.value = true
}

const selectCurrentPage = () => {
  // 先清除所有选择
  selectedDocuments.value.clear()
  // 然后只选择当前页面的文档
  paginatedDocuments.value.forEach(doc => selectedDocuments.value.add(doc.document_id))
  // 同步表头多选框状态
  updateSelectAllState()
}

const clearSelection = () => {
  selectedDocuments.value.clear()
  selectAll.value = false
}

const batchDownload = async () => {
  const selectedCount = selectedDocuments.value.size
  const selectedDocIds = Array.from(selectedDocuments.value)
  
  if (selectedCount === 0) {
    addNotification({
      type: 'warning',
      title: '操作提示',
      message: '请先选择要下载的文档'
    })
    return
  }

  try {
    addNotification({
      type: 'info',
      title: '开始批量下载',
      message: `正在准备下载 ${selectedCount} 个文档，请稍候...`
    })

    // 分类处理不同格式的文档ID
    const taskDocuments = [] // Task ID格式的文档
    const documentIdDocuments = [] // 数字ID格式的文档
    const invalidDocuments = [] // 无效格式的文档
    
    for (const docId of selectedDocIds) {
      const doc = allDocuments.value.find(d => d.document_id === docId)
      if (!doc) {
        invalidDocuments.push(docId)
        continue
      }
      
      if (typeof doc.document_id === 'string') {
        if (doc.document_id.startsWith('task_')) {
          // Task ID格式，使用导出报告功能
          taskDocuments.push({ docId, taskId: doc.document_id, doc })
        } else if (doc.document_id.includes('-')) {
          // UUID格式，当前不支持下载
          invalidDocuments.push(docId)
        } else {
          // 尝试转换为数字
          const downloadId = parseInt(doc.document_id)
          if (isNaN(downloadId)) {
            invalidDocuments.push(docId)
          } else {
            documentIdDocuments.push({ docId, downloadId, doc })
          }
        }
      } else {
        // 数字类型
        const downloadId = Number(doc.document_id)
        if (isNaN(downloadId)) {
          invalidDocuments.push(docId)
        } else {
          documentIdDocuments.push({ docId, downloadId, doc })
        }
      }
    }

    if (invalidDocuments.length > 0) {
      console.warn('以下文档ID格式不支持下载:', invalidDocuments)
    }

    if (taskDocuments.length === 0 && documentIdDocuments.length === 0) {
      addNotification({
        type: 'error',
        title: '下载失败',
        message: '所选文档的ID格式都不支持下载操作'
      })
      return
    }

    // 执行批量下载
    const downloadPromises = []
    
    // 下载Task格式的文档 (导出报告)
    if (taskDocuments.length > 0) {
      downloadPromises.push(
        ...taskDocuments.map(({ taskId }) => 
          documentApi.exportDocumentReport(taskId, 'pdf', true, true)
        )
      )
    }
    
    // 下载Document ID格式的文档
    if (documentIdDocuments.length > 0) {
      downloadPromises.push(
        ...documentIdDocuments.map(({ downloadId }) => 
          documentApi.downloadDocument(downloadId)
        )
      )
    }

    const downloadResults = await Promise.allSettled(downloadPromises)

    // 统计下载结果
    const apiFailedCount = downloadResults.filter(result => result.status === 'rejected').length
    const formatFailedCount = invalidDocuments.length
    
    // 计算成功下载的文档数量
    let successCount = 0
    if (downloadResults.length > 0 && downloadResults.every(result => result.status === 'fulfilled')) {
      // 所有API调用成功
      successCount = taskDocuments.length + documentIdDocuments.length
    } else if (apiFailedCount === 0) {
      // 没有API失败，说明全部成功
      successCount = taskDocuments.length + documentIdDocuments.length
    } else {
      // 有API失败，需要更精确的计算
      successCount = (taskDocuments.length + documentIdDocuments.length) - apiFailedCount
    }
    
    const totalFailedCount = apiFailedCount + formatFailedCount

    // 显示下载结果通知
    if (totalFailedCount === 0) {
      addNotification({
        type: 'success',
        title: '下载成功',
        message: `已成功启动 ${successCount} 个文档的下载`
      })
    } else if (successCount === 0) {
      let message = `下载失败，共 ${totalFailedCount} 个文档未能下载`
      if (formatFailedCount > 0) {
        message += `（其中 ${formatFailedCount} 个文档格式不支持下载）`
      }
      addNotification({
        type: 'error',
        title: '下载失败',
        message
      })
    } else {
      let message = `成功下载 ${successCount} 个文档，${totalFailedCount} 个文档下载失败`
      if (formatFailedCount > 0) {
        message += `（其中 ${formatFailedCount} 个文档格式不支持下载）`
      }
      addNotification({
        type: 'warning',
        title: '部分下载成功',
        message
      })
    }

    // 记录失败的详细信息
    const failedResults = downloadResults.filter(result => result.status === 'rejected') as PromiseRejectedResult[]
    if (failedResults.length > 0) {
      console.error('批量下载部分失败:', failedResults.map(result => result.reason))
    }

    // 下载成功后清除选择
    if (successCount > 0) {
      clearSelection()
    }
    
  } catch (error: any) {
    console.error('批量下载发生未预期错误:', error)
    addNotification({
      type: 'error',
      title: '下载失败',
      message: error.message || '下载过程中发生错误，请稍后重试'
    })
  }
}

const showDeleteModal = () => {
  isDeleteModalVisible.value = true
  // 禁用背景滚动
  document.body.style.overflow = 'hidden'
}

const hideDeleteModal = () => {
  isDeleteModalVisible.value = false
  // 恢复背景滚动
  document.body.style.overflow = 'auto'
}

const confirmDelete = async () => {
  const selectedCount = selectedDocuments.value.size
  const selectedDocIds = Array.from(selectedDocuments.value)
  
  if (selectedCount === 0) {
    addNotification({
      type: 'warning',
      title: '操作提示',
      message: '请先选择要删除的文档'
    })
    hideDeleteModal()
    return
  }

  try {
    addNotification({
      type: 'info',
      title: '正在删除',
      message: `正在删除 ${selectedCount} 个文档，请稍候...`
    })

    // 分类处理不同格式的文档ID
    const taskDocuments = [] // Task ID格式的文档
    const documentIdDocuments = [] // 数字ID格式的文档
    const invalidDocuments = [] // 无效格式的文档
    
    for (const docId of selectedDocIds) {
      const doc = allDocuments.value.find(d => d.document_id === docId)
      if (!doc) {
        invalidDocuments.push(docId)
        continue
      }
      
      if (typeof doc.document_id === 'string') {
        if (doc.document_id.startsWith('task_')) {
          // Task ID格式，使用TaskApi删除
          taskDocuments.push({ docId, taskId: doc.document_id, doc })
        } else if (doc.document_id.startsWith('reanalyze_')) {
          // 重新分析生成的文档ID，使用DocumentApi删除
          const deleteId = doc.document_id
          documentIdDocuments.push({ docId, deleteId, doc })
        } else if (doc.document_id.includes('-')) {
          // UUID格式，当前不支持删除
          invalidDocuments.push(docId)
        } else {
          // 其他字符串格式，直接使用DocumentApi删除
          const deleteId = doc.document_id
          documentIdDocuments.push({ docId, deleteId, doc })
        }
      } else {
        // 数字类型，转换为字符串
        const deleteId = String(doc.document_id)
        documentIdDocuments.push({ docId, deleteId, doc })
      }
    }

    if (invalidDocuments.length > 0) {
      console.warn('以下文档ID格式不支持删除:', invalidDocuments)
    }

    if (taskDocuments.length === 0 && documentIdDocuments.length === 0) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: '所选文档的ID格式都不支持删除操作'
      })
      hideDeleteModal()
      return
    }

    // 执行批量删除
    const deletePromises = []
    
    // 删除Task格式的文档
    if (taskDocuments.length > 0) {
      if (taskDocuments.length === 1) {
        // 单个任务删除
        deletePromises.push(taskApi.deleteTask(taskDocuments[0].taskId))
      } else {
        // 批量任务删除
        const taskIds = taskDocuments.map(item => item.taskId)
        deletePromises.push(
          taskApi.deleteTasks(taskIds).then(result => {
            // 记录批量删除的详细结果
            console.log('批量删除任务结果:', result)
            if (result.failed_count > 0) {
              console.warn('部分任务删除失败:', result.failed_tasks)
            }
            return result
          })
        )
      }
    }
    
    // 删除Document ID格式的文档
    if (documentIdDocuments.length > 0) {
      deletePromises.push(
        ...documentIdDocuments.map(({ deleteId }) => documentApi.deleteDocument(deleteId))
      )
    }

    const deleteResults = await Promise.allSettled(deletePromises)

    // 统计删除结果
    const apiFailedCount = deleteResults.filter(result => result.status === 'rejected').length
    const formatFailedCount = invalidDocuments.length
    
    // 计算成功删除的文档数量
    let successCount = 0
    if (deleteResults.length > 0 && deleteResults.every(result => result.status === 'fulfilled')) {
      // 所有API调用成功
      successCount = taskDocuments.length + documentIdDocuments.length
    } else if (apiFailedCount === 0) {
      // 没有API失败，说明全部成功
      successCount = taskDocuments.length + documentIdDocuments.length
    } else {
      // 有API失败，需要更精确的计算
      successCount = (taskDocuments.length + documentIdDocuments.length) - apiFailedCount
    }
    
    const totalFailedCount = apiFailedCount + formatFailedCount

    // 显示删除结果通知
    if (totalFailedCount === 0) {
      addNotification({
        type: 'success',
        title: '删除成功',
        message: `已成功删除 ${successCount} 个文档`
      })
    } else if (successCount === 0) {
      let message = `删除失败，共 ${totalFailedCount} 个文档未能删除`
      if (formatFailedCount > 0) {
        message += `（其中 ${formatFailedCount} 个文档格式不支持删除）`
      }
      addNotification({
        type: 'error',
        title: '删除失败',
        message
      })
    } else {
      let message = `成功删除 ${successCount} 个文档，${totalFailedCount} 个文档删除失败`
      if (formatFailedCount > 0) {
        message += `（其中 ${formatFailedCount} 个文档格式不支持删除）`
      }
      addNotification({
        type: 'warning',
        title: '部分删除成功',
        message
      })
    }

    // 记录失败的详细信息
    const failedResults = deleteResults.filter(result => result.status === 'rejected') as PromiseRejectedResult[]
    if (failedResults.length > 0) {
      console.error('批量删除部分失败:', failedResults.map(result => result.reason))
    }

    // 重新加载数据
    await loadDocuments()
    hideDeleteModal()
    clearSelection()
    
  } catch (error: any) {
    console.error('批量删除发生未预期错误:', error)
    addNotification({
      type: 'error',
      title: '删除失败',
      message: error.message || '删除过程中发生错误，请稍后重试'
    })
    // 即使删除失败也要恢复滚动和关闭模态框
    hideDeleteModal()
  }
}



const goToPage = (page: number) => {
  currentPage.value = page
}

// 工具函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string, fullFormat = false) => {
  if (!dateString) return '';

  // 标准化日期字符串，确保 new Date() 能正确解析
  const date = new Date(dateString.replace(' ', 'T'));

  if (isNaN(date.getTime())) {
    return dateString; // 解析失败则返回原始字符串
  }

  // 手动拼接成 "YYYY-MM-DD HH:mm:ss" 格式
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  if (fullFormat) {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  
  // 短格式，例如 "MM-DD HH:mm"
  return `${month}-${day} ${hours}:${minutes}`;
};

const getAnalysisTypeName = (type: string) => {
  const types: { [key: string]: string } = {
    'paper_check': '论文检测',
    'format_check': '格式检查',
    'content_analysis': '结构分析'  // 修复：content_analysis 对应用户选择的"结构分析"
  }
  return types[type] || type
}

const getDocumentStatus = (doc: Document) => {
  const task = documentTasks.value[doc.task_id]
  return task?.status || 'unknown'
}

const getDocumentProgress = (doc: Document) => {
  const task = documentTasks.value[doc.task_id]
  return task?.progress || 0
}

const getStatusBadge = (doc: Document) => {
  const status = getDocumentStatus(doc)
  const progress = getDocumentProgress(doc)
  
  const statusMap: { [key: string]: string } = {
    'pending': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">待处理</span>',
    'processing': `
      <div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">处理中</span>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
          <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all" style="width: ${progress}%"></div>
        </div>
      </div>
    `,
    'running': `
      <div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">处理中</span>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
          <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all" style="width: ${progress}%"></div>
        </div>
      </div>
    `,
    'completed': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">已完成</span>',
    'failed': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">失败</span>',
    'error': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">失败</span>'
  }
  return statusMap[status] || `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">${status}</span>`
}

const getActionButtons = (doc: Document) => {
  const status = getDocumentStatus(doc)
  let buttons = `<a href="/document/${doc.document_id}" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm transition-colors">查看</a>`
  
  if (status === 'completed') {
    buttons += ` <button class="download-btn text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm transition-colors" data-doc-id="${doc.document_id}">下载</button>`
  }
  
  buttons += ` <button class="delete-btn text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 text-sm transition-colors" data-doc-id="${doc.document_id}">删除</button>`
  
  return buttons
}

const getDocumentName = (docId: string) => {
  const doc = allDocuments.value.find(d => d.document_id === docId)
  return doc ? doc.filename : ''
}

// 获取文档对应的任务
const getDocumentTask = (doc: Document) => {
  return documentTasks.value[doc.task_id]
}

// 单个文档删除
const deleteSingleDocument = async (docId: string) => {
  try {
    const doc = allDocuments.value.find(d => d.document_id === docId)
    if (!doc) {
      addNotification({
        type: 'error',
        title: '删除失败',
        message: '找不到指定的文档'
      })
      return
    }

    console.log('删除文档信息:', { docId, documentId: doc.document_id, doc })

    // 确认删除
    const confirmed = await $confirm.danger(
      `确定要删除文档"${doc.filename}"吗？删除后无法恢复。`,
      {
        title: '删除文档',
        confirmText: '确定删除',
        cancelText: '取消'
      }
    )

    if (!confirmed) return

    addNotification({
      type: 'info',
      title: '正在删除',
      message: `正在删除文档"${doc.filename}"...`
    })

    // 根据文档ID格式选择删除方式
    if (typeof doc.document_id === 'string') {
      if (doc.document_id.startsWith('task_')) {
        // Task ID格式，使用TaskApi删除
        console.log('使用TaskApi删除任务:', doc.document_id)
        await taskApi.deleteTask(doc.document_id)
      } else if (doc.document_id.startsWith('reanalyze_')) {
        // 重新分析生成的文档ID，使用DocumentApi删除
        console.log('删除重新分析文档:', doc.document_id)
        await documentApi.deleteDocument(doc.document_id)
      } else if (doc.document_id.includes('-')) {
        // UUID格式
        console.warn('文档ID为UUID格式:', doc.document_id)
        addNotification({
          type: 'error',
          title: '删除失败',
          message: '当前文档格式不支持删除操作（UUID格式）'
        })
        return
      } else {
        // 其他字符串格式的文档ID，直接使用DocumentApi删除
        console.log('删除文档:', doc.document_id)
        await documentApi.deleteDocument(doc.document_id)
      }
    } else {
      // 如果已经是数字类型，转换为字符串后使用DocumentApi删除
      const deleteId = String(doc.document_id)
      console.log('删除数字ID文档:', deleteId)
      await documentApi.deleteDocument(deleteId)
    }
    
    addNotification({
      type: 'success',
      title: '删除成功',
      message: `文档"${doc.filename}"已成功删除`
    })

    // 重新加载数据
    await loadDocuments()
    
  } catch (error: any) {
    console.error('删除文档失败:', error)
    addNotification({
      type: 'error',
      title: '删除失败',
      message: error.message || '删除文档时发生错误，请稍后重试'
    })
  }
}

// 单个文档下载
const downloadSingleDocument = async (docId: string) => {
  try {
    const doc = allDocuments.value.find(d => d.document_id === docId)
    if (!doc) {
      addNotification({
        type: 'error',
        title: '下载失败',
        message: '找不到指定的文档'
      })
      return
    }

    console.log('下载文档信息:', { docId, documentId: doc.document_id, doc })

    addNotification({
      type: 'info',
      title: '开始下载',
      message: `正在下载文档"${doc.filename}"...`
    })

    // 根据文档ID格式选择下载方式
    if (typeof doc.document_id === 'string') {
      if (doc.document_id.startsWith('task_')) {
        // Task ID格式，使用导出报告功能
        console.log('使用导出报告功能下载任务结果:', doc.document_id)
        await documentApi.exportDocumentReport(doc.document_id, 'pdf', true, true)
      } else if (doc.document_id.includes('-')) {
        // UUID格式
        console.warn('文档ID为UUID格式:', doc.document_id)
        addNotification({
          type: 'error',
          title: '下载失败',
          message: '当前文档格式不支持下载操作（UUID格式）'
        })
        return
      } else {
        // 尝试转换为数字，使用DocumentApi下载
        const downloadId = parseInt(doc.document_id)
        if (isNaN(downloadId)) {
          console.error('无法转换文档ID为数字:', doc.document_id)
          addNotification({
            type: 'error',
            title: '下载失败',
            message: `文档ID格式不支持: ${doc.document_id}`
          })
          return
        }
        await documentApi.downloadDocument(downloadId)
      }
    } else {
      // 如果已经是数字类型，使用DocumentApi下载
      const downloadId = Number(doc.document_id)
      if (isNaN(downloadId)) {
        console.error('文档ID不是有效数字:', doc.document_id)
        addNotification({
          type: 'error',
          title: '下载失败',
          message: '文档ID格式错误'
        })
        return
      }
      await documentApi.downloadDocument(downloadId)
    }
    
    addNotification({
      type: 'success',
      title: '下载成功',
      message: `文档"${doc.filename}"下载已开始`
    })
    
  } catch (error: any) {
    console.error('下载文档失败:', error)
    addNotification({
      type: 'error',
      title: '下载失败',
      message: error.message || '下载文档时发生错误，请稍后重试'
    })
  }
}

// 事件委托处理按钮点击
const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const docId = target.getAttribute('data-doc-id')
  
  if (!docId) return
  
  if (target.classList.contains('delete-btn')) {
    deleteSingleDocument(docId)
  } else if (target.classList.contains('download-btn')) {
    downloadSingleDocument(docId)
  }
}

// 挂载时加载数据
onMounted(() => {
  loadDocuments()
  
  // 添加事件委托监听器
  document.addEventListener('click', handleActionClick)
})

// 添加组件卸载时恢复滚动的逻辑
onUnmounted(() => {
  document.body.style.overflow = 'auto'
  // 移除事件监听器
  document.removeEventListener('click', handleActionClick)
})
</script> 

<style scoped>
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700;
}

/* 表格圆角增强 */
.card .overflow-x-auto {
  border-radius: 0.5rem;
}

.card table {
  border-radius: 0.5rem;
  overflow: hidden;
  border-collapse: separate;
  border-spacing: 0;
}

.card table thead th:first-child {
  border-top-left-radius: 0.5rem;
}

.card table thead th:last-child {
  border-top-right-radius: 0.5rem;
}

.card table tbody tr:last-child td:first-child {
  border-bottom-left-radius: 0.5rem;
}

.card table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 0.5rem;
}

.card-body {
  @apply px-6 py-4;
}

.card-hover {
  @apply hover:shadow-lg hover:scale-105 cursor-pointer;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

/* 确保搜索框图标和文字不重叠 */
:deep(.form-input.pl-10) {
  padding-left: 2.5rem !important;
}

:deep(.input-addon-left) {
  width: 2.5rem !important;
  left: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: none !important;
  z-index: 10 !important;
}

/* form-input 基础样式增强 */
.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  color: #111827;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
}

.dark .form-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.dark .form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.2);
}

/* 文件图标样式 - 严格按照原型设计 */
.file-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: #3b82f6;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.6rem;
  letter-spacing: 0.025em;
  padding: 0.25rem;
  line-height: 1;
  text-align: center;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
}

.file-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.file-icon.docx {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.file-icon.doc {
  background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
}

.file-icon.pdf {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

/* 表格中较小的文件图标 */
.file-icon-small {
  width: 2rem;
  height: 2rem;
  font-size: 0.625rem;
  padding: 0.1875rem;
}

/* 暗黑模式下的文件图标 */
.dark .file-icon {
  background-color: #2563eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .file-icon:hover {
  background-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.dark .file-icon.docx {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

/* 空状态样式 */
.empty-state {
  @apply text-center;
}

.empty-state-icon {
  @apply text-4xl mb-4;
}

/* 文本截断样式 */
.text-truncate {
  @apply truncate;
}

/* Documents页面特定样式：减少筛选板块的表单间距 */
:deep(.form-group) {
  @apply mb-0;
}

/* 移除链接按钮点击时的边框/outline */
:deep(.no-focus-outline) {
  outline: none !important;
  box-shadow: none !important;
}

:deep(.no-focus-outline:focus) {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 部分选择状态样式 - 扩展components.css中的标准复选框 */
.checkbox-visual.checkbox-partial {
  @apply bg-blue-600 dark:bg-blue-500 border-blue-600 dark:border-blue-500;
}

.checkbox-visual.checkbox-partial::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 2px;
  background: white;
  border: none;
  border-radius: 1px;
}

/* 移动端优化样式 */
@media (max-width: 640px) {
  /* 批量操作按钮文字大小优化 */
  .md\:hidden .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  
  /* 确保批量操作按钮有足够的点击区域 */
  .md\:hidden button {
    min-height: 2.5rem;
    padding: 0.5rem 0.75rem;
  }
  
  /* 文档卡片在移动端的间距优化 */
  .grid.gap-4 > * {
    margin-bottom: 0;
  }
  
  /* 表单标签在移动端的样式 */
  label.block {
    font-weight: 500;
    color: #374151;
  }
  
  .dark label.block {
    color: #d1d5db;
  }
  
  /* 移动端文档信息字体调整 */
  .space-y-1\.5 .flex {
    font-size: 0.75rem;
  }
  
  /* 移动端文档标题行高优化 */
  .leading-tight {
    line-height: 1.25;
  }
  
  /* 移动端操作按钮包装优化 */
  .flex-wrap {
    align-items: flex-start;
  }
}
</style> 