name: 🚀 生产环境部署

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging
      force_deploy:
        description: '强制部署 (跳过测试)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/frontend
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  quality-check:
    name: 📊 代码质量检查
    runs-on: ubuntu-latest
    if: ${{ !inputs.force_deploy }}
    outputs:
      version: ${{ steps.version.outputs.version }}
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📋 获取版本信息
        id: version
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            VERSION=$(jq -r '.version' package.json)-${GITHUB_SHA::8}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "📦 版本: $VERSION"

      - name: 🔧 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: 📦 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 🔍 类型检查
        run: pnpm run type-check

      - name: 🔍 代码检查
        run: pnpm run lint

      - name: 🔒 安全审计
        run: pnpm audit --audit-level high

  # 测试
  test:
    name: 🧪 测试
    runs-on: ubuntu-latest
    if: ${{ !inputs.force_deploy }}
    needs: quality-check
    strategy:
      matrix:
        test-type: [unit, e2e]
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 🔧 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 安装 pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: 📦 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 🧪 运行单元测试
        if: matrix.test-type == 'unit'
        run: |
          pnpm run test:coverage
          echo "COVERAGE_REPORT<<EOF" >> $GITHUB_ENV
          cat coverage/lcov-report/index.html >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: 📊 上传覆盖率报告
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/lcov.info
          flags: frontend
          name: frontend-coverage

      - name: 🎭 安装 Playwright
        if: matrix.test-type == 'e2e'
        run: npx playwright install --with-deps

      - name: 🧪 运行 E2E 测试
        if: matrix.test-type == 'e2e'
        run: pnpm run test:e2e

      - name: 📸 上传 E2E 测试结果
        if: matrix.test-type == 'e2e' && failure()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report/

  # 构建和推送Docker镜像
  build-and-push:
    name: 🐳 构建和推送镜像
    runs-on: ubuntu-latest
    needs: [quality-check, test]
    if: always() && (needs.quality-check.result == 'success' || inputs.force_deploy) && (needs.test.result == 'success' || inputs.force_deploy)
    permissions:
      contents: read
      packages: write
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 🔧 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 登录 Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 📋 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=stable,enable={{is_default_branch}}
            type=sha,prefix={{branch}}-,suffix=-{{date 'YYYYMMDD-HHmmss'}}

      - name: 🔨 构建并推送镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.prod
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ needs.quality-check.outputs.version || github.sha }}
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            GIT_COMMIT=${{ github.sha }}
            GIT_BRANCH=${{ github.ref_name }}

      - name: 📋 设置镜像输出
        id: image
        run: |
          echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.quality-check.outputs.version || github.sha }}" >> $GITHUB_OUTPUT

  # 安全扫描
  security-scan:
    name: 🔒 安全扫描
    runs-on: ubuntu-latest
    needs: build-and-push
    if: always() && needs.build-and-push.result == 'success'
    steps:
      - name: 🔒 运行 Trivy 漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build-and-push.outputs.image }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 📊 上传 Trivy 扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # 部署到Staging环境
  deploy-staging:
    name: 🚀 部署到Staging环境
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    if: always() && needs.build-and-push.result == 'success' && (github.ref == 'refs/heads/main' || inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.papercheck.com
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 🚀 部署到Staging
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/papercheck-staging
            export IMAGE_TAG="${{ needs.build-and-push.outputs.image }}"
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d
            sleep 30
            curl -f https://staging.papercheck.com/health || exit 1

      - name: 🏥 Staging健康检查
        run: |
          sleep 10
          curl -f https://staging.papercheck.com/health
          curl -f https://staging.papercheck.com/health

  # 部署到生产环境
  deploy-production:
    name: 🎯 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan, deploy-staging]
    if: always() && needs.build-and-push.result == 'success' && needs.deploy-staging.result == 'success' && (startsWith(github.ref, 'refs/tags/') || inputs.environment == 'production')
    environment:
      name: production
      url: https://papercheck.com
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4

      - name: 📦 创建备份
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            BACKUP_DIR="/opt/backups/$(date +%Y%m%d_%H%M%S)"
            mkdir -p $BACKUP_DIR
            cd /opt/papercheck
            docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U papercheck papercheck > $BACKUP_DIR/database.sql
            docker save papercheck/frontend:current > $BACKUP_DIR/frontend-image.tar || true
            echo "备份完成: $BACKUP_DIR"

      - name: 🚀 蓝绿部署
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /opt/papercheck
            export NEW_IMAGE="${{ needs.build-and-push.outputs.image }}"
            export BACKUP_IMAGE="papercheck/frontend:backup-$(date +%Y%m%d_%H%M%S)"
            
            # 标记当前镜像为备份
            docker tag papercheck/frontend:latest $BACKUP_IMAGE || true
            
            # 更新到新镜像
            docker pull $NEW_IMAGE
            docker tag $NEW_IMAGE papercheck/frontend:latest
            
            # 执行蓝绿部署
            chmod +x scripts/blue-green-deploy.sh
            ./scripts/blue-green-deploy.sh

      - name: 🏥 生产环境健康检查
        run: |
          sleep 30
          for i in {1..10}; do
            if curl -f https://papercheck.com/health; then
              echo "✅ 健康检查通过"
              break
            fi
            if [ $i -eq 10 ]; then
              echo "❌ 健康检查失败"
              exit 1
            fi
            echo "等待服务启动... ($i/10)"
            sleep 10
          done

      - name: 🧪 烟雾测试
        run: |
          # 检查主要页面
          curl -f https://papercheck.com/
          curl -f https://papercheck.com/health
          
          # 检查API响应时间
          RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" https://papercheck.com)
          echo "响应时间: ${RESPONSE_TIME}s"
          
          if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
            echo "⚠️ 响应时间较慢: ${RESPONSE_TIME}s"
          else
            echo "✅ 响应时间正常: ${RESPONSE_TIME}s"
          fi

  # 发送通知
  notify:
    name: 📢 发送通知
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always()
    steps:
      - name: 📢 发送Slack通知
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ needs.deploy-production.result }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              attachments: [{
                color: '${{ needs.deploy-production.result }}' === 'success' ? 'good' : 'danger',
                title: '🚀 Paper Check Frontend 部署',
                fields: [
                  {
                    title: '环境',
                    value: 'Production',
                    short: true
                  },
                  {
                    title: '版本',
                    value: '${{ needs.quality-check.outputs.version || github.sha }}',
                    short: true
                  },
                  {
                    title: '状态',
                    value: '${{ needs.deploy-production.result }}' === 'success' ? '✅ 成功' : '❌ 失败',
                    short: true
                  },
                  {
                    title: '分支',
                    value: '${{ github.ref_name }}',
                    short: true
                  },
                  {
                    title: '提交者',
                    value: '${{ github.actor }}',
                    short: true
                  },
                  {
                    title: '链接',
                    value: 'https://papercheck.com',
                    short: true
                  }
                ]
              }]
            }

      - name: 📧 发送邮件通知
        if: failure()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: ${{ secrets.SMTP_PORT }}
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: "❌ Paper Check Frontend 部署失败"
          to: ${{ secrets.ALERT_EMAIL }}
          from: "GitHub Actions <${{ secrets.SMTP_FROM }}>"
          html_body: |
            <h2>🚨 Paper Check Frontend 部署失败</h2>
            <p><strong>仓库:</strong> ${{ github.repository }}</p>
            <p><strong>分支:</strong> ${{ github.ref_name }}</p>
            <p><strong>提交:</strong> ${{ github.sha }}</p>
            <p><strong>触发者:</strong> ${{ github.actor }}</p>
            <p><strong>工作流:</strong> <a href="${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}">${{ github.workflow }}</a></p>
            <p>请检查日志并尽快修复问题。</p>

  # 清理旧镜像
  cleanup:
    name: 🧹 清理旧镜像
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always() && needs.deploy-production.result == 'success'
    steps:
      - name: 🧹 清理旧的Docker镜像
        uses: actions/delete-package-versions@v4
        with:
          package-name: '${{ github.repository }}/frontend'
          package-type: 'container'
          min-versions-to-keep: 10
          delete-only-untagged-versions: false 