# ==================================================
# Word文档分析服务 - Docker容器启动脚本
# ==================================================

param(
    [string]$Environment = "production",
    [string]$LogLevel = "INFO",
    [int]$Port = 8000,
    [string]$Host = "0.0.0.0"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "========================================" -ForegroundColor Green
Write-Host "Word文档分析服务 - 容器启动" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "日志级别: $LogLevel" -ForegroundColor Yellow
Write-Host "端口: $Port" -ForegroundColor Yellow
Write-Host "主机: $Host" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

# 检查Python环境
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Error "Python未正确安装或配置"
    exit 1
}

# 检查必要的目录
$requiredDirs = @(
    "C:\app\data",
    "C:\app\logs", 
    "C:\app\data\uploads",
    "C:\app\data\temp",
    "C:\app\data\images",
    "C:\app\data\reports"
)

foreach ($dir in $requiredDirs) {
    if (!(Test-Path $dir)) {
        Write-Host "创建目录: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# 设置环境变量
$env:ENVIRONMENT = $Environment
$env:LOG_LEVEL = $LogLevel
$env:PYTHONPATH = "C:\app"
$env:PYTHONUNBUFFERED = "1"

# 检查配置文件
if (!(Test-Path "C:\app\config.yaml")) {
    Write-Error "配置文件 config.yaml 不存在"
    exit 1
}

# 初始化数据库
Write-Host "初始化数据库..." -ForegroundColor Yellow
try {
    python C:\app\scripts\init_database.py
    Write-Host "数据库初始化完成" -ForegroundColor Green
} catch {
    Write-Warning "数据库初始化失败，继续启动服务"
}

# 检查Word COM接口（如果在Windows环境中）
Write-Host "检查Word COM接口..." -ForegroundColor Yellow
try {
    python -c "import win32com.client; word = win32com.client.Dispatch('Word.Application'); word.Quit()"
    Write-Host "Word COM接口检查通过" -ForegroundColor Green
} catch {
    Write-Warning "Word COM接口检查失败，请确保已安装Microsoft Word"
}

# 启动服务
Write-Host "启动Word文档分析服务..." -ForegroundColor Green
Write-Host "访问地址: http://${Host}:${Port}" -ForegroundColor Cyan
Write-Host "API文档: http://${Host}:${Port}/docs" -ForegroundColor Cyan
Write-Host "健康检查: http://${Host}:${Port}/health" -ForegroundColor Cyan

# 根据环境选择启动方式
if ($Environment -eq "development") {
    # 开发模式 - 启用热重载
    python -m uvicorn app.main:app --host $Host --port $Port --reload --log-level $LogLevel.ToLower()
} else {
    # 生产模式 - 使用优化配置
    python -m uvicorn app.main:app --host $Host --port $Port --workers 1 --log-level $LogLevel.ToLower() --access-log --no-use-colors
}

# 如果服务异常退出
Write-Error "服务异常退出"
exit 1 