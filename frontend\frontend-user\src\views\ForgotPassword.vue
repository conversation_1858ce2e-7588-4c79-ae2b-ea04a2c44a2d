<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <AppNavbar :isAuthPage="true" />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <!-- 头部 -->
        <div class="text-center">
          <div class="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
          </div>
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            重置密码
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            输入您的邮箱地址，我们将发送重置密码的链接
          </p>
        </div>

        <!-- 重置密码表单 -->
        <BaseCard v-if="!emailSent" class="form-container">
          <form class="space-y-6" @submit.prevent="handleForgotPassword">
            <BaseInput
              v-model="formData.email"
              type="email"
              label="邮箱地址"
              placeholder="请输入您的注册邮箱"
              :error="errors.email"
              required
            />

            <div class="form-group">
              <label for="captcha" class="form-label">验证码</label>
              <div class="flex space-x-3">
                <BaseInput
                  v-model="formData.captcha"
                  placeholder="请输入验证码"
                  :error="errors.captcha"
                  required
                  class="flex-1"
                />
                <div class="flex-shrink-0">
                  <div @click="refreshCaptcha" 
                       class="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded border border-gray-300 dark:border-gray-600 flex items-center justify-center cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <span class="text-gray-600 dark:text-gray-300 text-sm font-mono font-semibold">{{ captchaText }}</span>
                  </div>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">点击验证码可刷新</p>
            </div>

            <BaseButton 
              type="submit" 
              :disabled="isLoading"
              :loading="isLoading"
              variant="primary"
              class="w-full"
              prepend-icon="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2 2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            >
              {{ isLoading ? '发送中...' : '发送重置邮件' }}
            </BaseButton>
          </form>

          <div class="mt-6 text-center space-y-3">
            <div class="text-sm">
              <router-link to="/auth" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                返回登录页面
              </router-link>
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300">
              没有收到邮件？
              <button @click="resendEmail" 
                      :disabled="isLoading" 
                      class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 underline transition-colors disabled:opacity-50">
                重新发送
              </button>
            </div>
          </div>
        </BaseCard>

        <!-- 邮件发送成功状态 -->
        <BaseCard v-if="emailSent" class="form-container text-center">
          <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">邮件发送成功</h3>
          <div class="text-gray-600 dark:text-gray-300 mb-4 space-y-2">
            <p>重置密码的邮件已发送到您的邮箱。</p>
            <p>请检查收件箱（包括垃圾邮件文件夹）。</p>
            <p class="text-sm">邮件中的链接将在 <span class="font-medium text-blue-600 dark:text-blue-400">30分钟</span> 后失效。</p>
          </div>
          <div class="space-x-3">
            <BaseButton @click="resendEmail" variant="secondary">
              重新发送
            </BaseButton>
            <BaseButton to="/auth" variant="primary">
              返回登录
            </BaseButton>
          </div>
        </BaseCard>

        <!-- 帮助信息 -->
        <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                重置密码帮助
              </h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <ul class="list-disc list-inside space-y-1">
                  <li>请确保输入的是您注册时使用的邮箱</li>
                  <li>邮件可能会进入垃圾邮件文件夹，请检查</li>
                  <li>重置链接有效期为30分钟</li>
                  <li>如仍有问题，请<a href="#" class="underline hover:no-underline">联系客服</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import BaseButton from '@/components/BaseButton.vue'
import { $notify } from '@/utils/useNotifications'
import AppNavbar from '@/components/AppNavbar.vue'

const themeStore = useThemeStore()

// 表单状态
const emailSent = ref(false)
const isLoading = ref(false)
const captchaText = ref('A8K9')

// 表单数据
const formData = reactive({
  email: '',
  captcha: ''
})

// 错误信息
const errors = reactive({
  email: '',
  captcha: ''
})

// 生成随机验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaText.value = generateCaptcha()
}

// 清空错误信息
const clearErrors = () => {
  errors.email = ''
  errors.captcha = ''
}

// 处理忘记密码
const handleForgotPassword = async () => {
  clearErrors()
  
  // 基本验证
  if (!formData.email) {
    errors.email = '请输入邮箱地址'
    return
  }
  
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = '请输入有效的邮箱地址'
    return
  }
  
  if (!formData.captcha) {
    errors.captcha = '请输入验证码'
    return
  }
  
  if (formData.captcha.toUpperCase() !== captchaText.value) {
    errors.captcha = '验证码不正确'
    refreshCaptcha()
    return
  }
  
  isLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟成功响应
    emailSent.value = true
    
    // 清空表单
    formData.email = ''
    formData.captcha = ''
    refreshCaptcha()
    
  } catch (error) {
    console.error('发送重置邮件失败:', error)
    $notify.error('发送失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 重新发送邮件
const resendEmail = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    $notify.success('重置邮件已重新发送')
  } catch (error) {
    console.error('重新发送失败:', error)
    $notify.error('重新发送失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  refreshCaptcha()
})
</script> 