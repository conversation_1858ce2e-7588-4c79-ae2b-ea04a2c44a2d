# Word文档分析服务 - 前端项目配置指南

## 📋 项目概述

基于Vue 3 + TypeScript + Tailwind CSS + 自定义组件的现代化前端应用配置指南。本项目为Word文档分析服务的前端界面，提供文档上传、分析和管理功能。

## 🚀 快速开始

### 1. 环境要求

```bash
Node.js >= 18.0.0
npm >= 8.0.0 或 yarn >= 1.22.0
```

### 2. 项目初始化

```bash
# 使用 create-vue 脚手架
npm create vue@latest word-analysis-frontend

# 配置选择：
✅ TypeScript
✅ Router  
✅ Pinia
❌ PWA
✅ ESLint
✅ Prettier
❌ Playwright
❌ Cypress

cd word-analysis-frontend
npm install
```

### 3. 依赖安装

#### 生产依赖
```bash
# UI组件库
npm install ant-design-vue @ant-design/icons-vue

# HTTP客户端
npm install axios

# Vue生态工具
npm install @vueuse/core

# 表单验证
npm install vee-validate yup

# 图表库
npm install vue-echarts echarts

# 文件上传
npm install vue-upload-component

# 日期处理
npm install dayjs
```

#### 开发依赖
```bash
# Tailwind CSS及相关工具
npm install -D tailwindcss autoprefixer postcss
npm install -D @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio

# 自动导入
npm install -D unplugin-vue-components unplugin-auto-import

# Prettier插件
npm install -D prettier-plugin-tailwindcss

# 开发工具
npm install -D @types/node
```

## ⚙️ 配置文件设置

### 1. Vite配置 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [AntDesignVueResolver()],
      imports: [
        'vue',
        'vue-router', 
        'pinia',
        '@vueuse/core'
      ],
      dts: 'src/auto-imports.d.ts',
      eslintrc: {
        enabled: true
      }
    }),
    Components({
      resolvers: [AntDesignVueResolver({ importStyle: false })],
      dts: 'src/components.d.ts'
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path
      }
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          '@primary-color': '#1890ff',
          '@success-color': '#52c41a', 
          '@warning-color': '#faad14',
          '@error-color': '#f5222d',
          '@info-color': '#1890ff',
          '@border-radius-base': '6px'
        },
        javascriptEnabled: true
      }
    },
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue'],
          echarts: ['vue-echarts', 'echarts']
        }
      }
    }
  }
})
```

### 2. Tailwind CSS配置

#### 安装和初始化
```bash
# 安装Tailwind CSS
npm install -D tailwindcss autoprefixer postcss

# 生成配置文件
npx tailwindcss init -p
```

#### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#e6f7ff',
          100: '#bae7ff',
          200: '#91d5ff', 
          300: '#69c0ff',
          400: '#40a9ff',
          500: '#1890ff',
          600: '#096dd9',
          700: '#0050b3',
          800: '#003a8c',
          900: '#002766',
        },
        success: '#52c41a',
        warning: '#faad14',
        error: '#f5222d',
        info: '#1890ff',
        // 自定义颜色
        gray: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
          300: '#e0e0e0',
          400: '#bdbdbd',
          500: '#9e9e9e',
          600: '#757575',
          700: '#616161',
          800: '#424242',
          900: '#212121',
        }
      },
      fontFamily: {
        sans: [
          'PingFang SC',
          'Helvetica Neue', 
          'Helvetica',
          'Arial',
          'sans-serif'
        ]
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ],
  corePlugins: {
    preflight: false
  }
}
```

#### PostCSS配置 (postcss.config.js)
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {}
  }
}
```

### 3. 样式文件配置

#### 主样式入口 (src/styles/main.css)
```css
@import './tailwind.css';
@import './element-overrides.css';
@import './components.css';

/* 全局基础样式 */
html {
  @apply h-full;
  font-size: 14px;
}

body {
  @apply h-full bg-gray-50 text-gray-900;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  @apply h-full;
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  @apply w-1.5;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full hover:bg-gray-500;
}

/* 选择文本样式 */
::selection {
  @apply bg-primary-500 text-white;
}

/* 焦点样式 */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}
```

#### Tailwind CSS入口 (src/styles/tailwind.css)
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  .card-body {
    @apply p-6;
  }
  
  /* 表单组件 */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  /* 页面容器 */
  .page-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .page-header {
    @apply mb-8;
  }
  
  .page-title {
    @apply text-3xl font-bold text-gray-900 mb-2;
  }
  
  .page-subtitle {
    @apply text-lg text-gray-600;
  }
}

/* 工具类 */
@layer utilities {
  .text-ellipsis {
    @apply truncate;
  }
  
  .text-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* 动画工具 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}
```

#### Element Plus样式重写 (src/styles/element-overrides.css)
```css
/* Element Plus 样式重写 */

/* 按钮样式 */
.el-button--primary {
  @apply !bg-primary-500 !border-primary-500 hover:!bg-primary-600 hover:!border-primary-600 focus:!bg-primary-600 focus:!border-primary-600;
}

.el-button--success {
  @apply !bg-green-500 !border-green-500 hover:!bg-green-600 hover:!border-green-600;
}

.el-button--warning {
  @apply !bg-yellow-500 !border-yellow-500 hover:!bg-yellow-600 hover:!border-yellow-600;
}

.el-button--danger {
  @apply !bg-red-500 !border-red-500 hover:!bg-red-600 hover:!border-red-600;
}

/* 卡片样式 */
.el-card {
  @apply !border-gray-200 !rounded-xl;
}

.el-card__body {
  @apply !p-6;
}

/* 表格样式 */
.el-table {
  @apply !border-gray-200;
}

.el-table th.el-table__cell {
  @apply !bg-gray-50 !text-gray-700 !font-semibold;
}

.el-table .el-table__row:hover > td {
  @apply !bg-gray-50;
}

/* 表单样式 */
.el-form-item__label {
  @apply !text-gray-700 !font-medium;
}

.el-input__wrapper {
  @apply !border-gray-300 hover:!border-gray-400 focus:!border-primary-500;
}

.el-input__wrapper.is-focus {
  @apply !border-primary-500 !shadow-sm;
}

/* 对话框样式 */
.el-dialog {
  @apply !rounded-xl;
}

.el-dialog__header {
  @apply !border-b !border-gray-200;
}

.el-dialog__title {
  @apply !text-xl !font-semibold !text-gray-900;
}

/* 消息样式 */
.el-message {
  @apply !rounded-lg !shadow-medium;
}

/* 标签样式 */
.el-tag {
  @apply !rounded-full;
}

/* 进度条样式 */
.el-progress-bar__outer {
  @apply !bg-gray-200 !rounded-full;
}

.el-progress-bar__inner {
  @apply !rounded-full;
}

/* 上传组件样式 */
.el-upload-dragger {
  @apply !border-2 !border-dashed !border-gray-300 !rounded-xl hover:!border-primary-400 !transition-colors;
}

.el-upload-dragger:hover {
  @apply !bg-gray-50;
}
```

#### 组件样式 (src/styles/components.css)
```css
/* 自定义组件样式 */

/* 加载状态 */
.loading-container {
  @apply flex items-center justify-center py-12;
}

.loading-spinner {
  @apply animate-spin text-2xl text-primary-500;
}

.loading-text {
  @apply ml-3 text-gray-600;
}

/* 空状态 */
.empty-state {
  @apply text-center py-16;
}

.empty-state-icon {
  @apply text-6xl text-gray-300 mb-4;
}

.empty-state-title {
  @apply text-xl font-medium text-gray-500 mb-2;
}

.empty-state-description {
  @apply text-gray-400 mb-6;
}

/* 状态标签 */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge--success {
  @apply bg-green-100 text-green-800;
}

.status-badge--warning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge--danger {
  @apply bg-red-100 text-red-800;
}

.status-badge--info {
  @apply bg-blue-100 text-blue-800;
}

/* 文档卡片 */
.document-card {
  @apply card card-hover border border-gray-200 cursor-pointer;
}

.document-card:hover {
  @apply border-primary-200;
}

.document-card-header {
  @apply flex items-center justify-between mb-4;
}

.document-card-title {
  @apply text-lg font-semibold text-gray-800 truncate;
}

.document-card-meta {
  @apply space-y-2 text-sm text-gray-600;
}

.document-card-actions {
  @apply mt-4 flex space-x-2;
}

/* 统计卡片 */
.stats-card {
  @apply card border border-gray-200;
}

.stats-card-content {
  @apply p-6;
}

.stats-card-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-4;
}

.stats-card-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
}

.stats-card-label {
  @apply text-sm text-gray-600;
}

/* 导航面包屑 */
.breadcrumb {
  @apply flex items-center space-x-2 text-sm text-gray-600 mb-6;
}

.breadcrumb-item {
  @apply hover:text-primary-500 transition-colors;
}

.breadcrumb-separator {
  @apply text-gray-400;
}

/* 分页组件 */
.pagination-container {
  @apply flex justify-center mt-8;
}

/* 搜索框 */
.search-input {
  @apply form-input max-w-md;
}

/* 筛选器 */
.filter-bar {
  @apply flex flex-wrap gap-4 p-4 bg-white rounded-lg shadow-sm border border-gray-200 mb-6;
}

/* 工具栏 */
.toolbar {
  @apply flex items-center justify-between mb-6;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.toolbar-right {
  @apply flex items-center space-x-2;
}
```

### 4. TypeScript配置

#### tsconfig.json
```json
{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.vue"
  ],
  "exclude": [
    "src/**/__tests__/*"
  ],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": [
      "node",
      "element-plus/global"
    ],
    "allowJs": true,
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false
  }
}
```

### 5. ESLint配置

#### .eslintrc.cjs
```javascript
/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting',
    './.eslintrc-auto-import.json'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    // Vue相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-vars': 'error',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    
    // TypeScript相关规则
    '@typescript-eslint/no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_' 
    }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    
    // 通用规则
    'prefer-const': 'error',
    'no-var': 'error',
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
```

### 6. Prettier配置

#### .prettierrc.json
```json
{
  "semi": false,
  "singleQuote": true,
  "quoteProps": "as-needed",
  "trailingComma": "es5", 
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 100,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "always",
  "endOfLine": "lf",
  "vueIndentScriptAndStyle": false,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

### 7. 环境变量配置

#### .env
```bash
# 应用配置
VITE_APP_TITLE=Word文档分析系统
VITE_APP_VERSION=1.0.0

# API配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# 上传配置
VITE_UPLOAD_MAX_SIZE=10485760
VITE_UPLOAD_ACCEPT=.doc,.docx

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
```

#### .env.development
```bash
# 开发环境
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:8000
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
```

#### .env.production
```bash
# 生产环境
NODE_ENV=production
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false
```

## 📦 package.json脚本

```json
{
  "name": "word-analysis-frontend",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/",
    "type-check": "vue-tsc --noEmit",
    "build:dev": "vue-tsc && vite build --mode development",
    "build:prod": "vue-tsc && vite build --mode production",
    "analyze": "vite build --mode analyze"
  }
}
```

## 🎯 开发工具配置

### 1. VS Code设置 (.vscode/settings.json)
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.vue": "vue"
  },
  "emmet.includeLanguages": {
    "vue-html": "html"
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "tailwindCSS.includeLanguages": {
    "vue": "html",
    "vue-html": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["class:\\s*['\"`]([^'\"`]*)['\"`]", "['\"`]([^'\"`]*)['\"`]"]
  ]
}
```

### 2. VS Code扩展推荐 (.vscode/extensions.json)
```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

## 🚀 启动项目

```bash
# 开发环境启动
npm run dev

# 类型检查
npm run type-check

# 代码检查和修复
npm run lint

# 代码格式化
npm run format

# 构建项目
npm run build

# 预览构建结果
npm run preview
```

## 📋 注意事项
