# Word文档分析服务 - 测试指南

## 🎯 测试概述

本指南提供Word文档分析服务的完整测试策略，包括单元测试、集成测试、性能测试、API测试等，确保服务质量和可靠性。

## 🧪 测试框架架构

### 测试技术栈
- **单元测试**: pytest + pytest-asyncio
- **API测试**: httpx + FastAPI TestClient
- **数据库测试**: SQLAlchemy + 内存数据库
- **模拟测试**: unittest.mock + pytest-mock
- **覆盖率**: pytest-cov
- **性能测试**: locust + asyncio
- **端到端测试**: selenium (可选)

### 测试目录结构
```
backend/tests/
├── conftest.py              # pytest配置和fixtures
├── test_basic.py           # 基础测试
├── unit/                   # 单元测试
│   ├── test_auth.py        # 认证模块测试
│   ├── test_documents.py   # 文档处理测试
│   ├── test_tasks.py       # 任务管理测试
│   ├── test_models.py      # 数据模型测试
│   └── test_services.py    # 业务服务测试
├── integration/            # 集成测试
│   ├── test_api_auth.py    # 认证API集成测试
│   ├── test_api_documents.py # 文档API集成测试
│   ├── test_database.py    # 数据库集成测试
│   └── test_word_com.py    # COM接口集成测试
├── performance/            # 性能测试
│   ├── test_load.py        # 负载测试
│   ├── test_stress.py      # 压力测试
│   └── locustfile.py       # Locust配置
├── e2e/                    # 端到端测试
│   ├── test_user_flow.py   # 用户流程测试
│   └── test_admin_flow.py  # 管理员流程测试
├── fixtures/               # 测试数据
│   ├── sample_documents/   # 示例文档
│   ├── test_data.json     # 测试数据
│   └── mock_responses.py   # 模拟响应
└── utils/                  # 测试工具
    ├── test_helpers.py     # 测试助手函数
    └── test_client.py      # 测试客户端
```

## ⚙️ 测试环境配置

### 1. 测试依赖安装
```bash
# 在backend目录下
cd backend

# 安装测试依赖
pip install -r requirements-dev.txt

# 验证pytest安装
pytest --version
```

### 2. 测试配置文件

#### pytest配置 (pytest.ini)
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
python_classes = Test*
addopts = 
    -v
    --tb=short
    --strict-markers
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
    auth: 认证相关测试
    database: 数据库相关测试
    com: COM接口相关测试
asyncio_mode = auto
```

#### 测试环境配置
```python
# tests/conftest.py
import asyncio
import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.config import settings
from app.database.connection import get_database
from app.database.init_db import create_tables

# 测试数据库URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    await create_tables(engine)
    yield engine
    await engine.dispose()

@pytest_asyncio.fixture
async def test_db(test_engine):
    """创建测试数据库会话"""
    TestSessionLocal = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    async with TestSessionLocal() as session:
        yield session
        await session.rollback()

@pytest.fixture
def test_client():
    """创建测试客户端"""
    with TestClient(app) as client:
        yield client

@pytest_asyncio.fixture
async def async_client():
    """创建异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }

@pytest.fixture
def auth_headers(test_client, sample_user_data):
    """认证头信息"""
    # 注册用户
    response = test_client.post("/api/v1/auth/register", json=sample_user_data)
    assert response.status_code == 200
    
    # 登录获取token
    login_data = {
        "username": sample_user_data["username"],
        "password": sample_user_data["password"]
    }
    response = test_client.post("/api/v1/auth/login", data=login_data)
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    return {"Authorization": f"Bearer {token}"}
```

## 🔬 单元测试

### 1. 认证模块测试

#### 用户认证测试
```python
# tests/unit/test_auth.py
import pytest
from unittest.mock import Mock, patch
from app.services.auth_service import AuthService
from app.core.security import verify_password, create_access_token

@pytest.mark.unit
class TestAuthService:
    
    @pytest_asyncio.fixture
    async def auth_service(self, test_db):
        return AuthService(test_db)
    
    async def test_register_user(self, auth_service, sample_user_data):
        """测试用户注册"""
        user = await auth_service.register_user(sample_user_data)
        assert user.username == sample_user_data["username"]
        assert user.email == sample_user_data["email"]
        assert user.id is not None
    
    async def test_register_duplicate_user(self, auth_service, sample_user_data):
        """测试重复用户注册"""
        await auth_service.register_user(sample_user_data)
        
        with pytest.raises(ValueError, match="用户名已存在"):
            await auth_service.register_user(sample_user_data)
    
    async def test_authenticate_user(self, auth_service, sample_user_data):
        """测试用户认证"""
        await auth_service.register_user(sample_user_data)
        
        user = await auth_service.authenticate_user(
            sample_user_data["username"],
            sample_user_data["password"]
        )
        assert user is not None
        assert user.username == sample_user_data["username"]
    
    async def test_authenticate_invalid_user(self, auth_service):
        """测试无效用户认证"""
        user = await auth_service.authenticate_user("invalid", "password")
        assert user is None

@pytest.mark.unit
class TestSecurity:
    
    def test_password_verification(self):
        """测试密码验证"""
        password = "testpassword123"
        hashed = "$2b$12$hash"
        
        with patch('app.core.security.bcrypt.checkpw') as mock_check:
            mock_check.return_value = True
            assert verify_password(password, hashed) is True
    
    def test_create_access_token(self):
        """测试访问令牌创建"""
        data = {"sub": "testuser"}
        token = create_access_token(data)
        assert isinstance(token, str)
        assert len(token) > 0
```

### 2. 文档处理测试

#### 文档服务测试
```python
# tests/unit/test_documents.py
import pytest
from unittest.mock import Mock, patch, AsyncMock
from app.services.document_processor import DocumentProcessor

@pytest.mark.unit
class TestDocumentProcessor:
    
    @pytest.fixture
    def processor(self):
        return DocumentProcessor()
    
    @pytest.fixture
    def sample_docx_path(self):
        return "tests/fixtures/sample_documents/test.docx"
    
    async def test_extract_text_content(self, processor, sample_docx_path):
        """测试文本内容提取"""
        with patch.object(processor, '_extract_with_com') as mock_extract:
            mock_extract.return_value = {
                "content": "测试文档内容",
                "paragraphs": ["段落1", "段落2"],
                "headings": ["标题1", "标题2"]
            }
            
            result = await processor.extract_text_content(sample_docx_path)
            assert result["content"] == "测试文档内容"
            assert len(result["paragraphs"]) == 2
    
    async def test_analyze_structure(self, processor, sample_docx_path):
        """测试文档结构分析"""
        with patch.object(processor, '_analyze_structure_com') as mock_analyze:
            mock_analyze.return_value = {
                "sections": ["摘要", "引言", "正文", "结论"],
                "heading_levels": [1, 1, 2, 1],
                "total_pages": 10
            }
            
            result = await processor.analyze_structure(sample_docx_path)
            assert len(result["sections"]) == 4
            assert result["total_pages"] == 10
    
    async def test_extract_images(self, processor, sample_docx_path):
        """测试图片提取"""
        with patch.object(processor, '_extract_images_com') as mock_extract:
            mock_extract.return_value = [
                {
                    "filename": "image1.jpg",
                    "width": 500,
                    "height": 300,
                    "format": "JPEG"
                }
            ]
            
            result = await processor.extract_images(sample_docx_path)
            assert len(result) == 1
            assert result[0]["format"] == "JPEG"

    @patch('app.services.document_processor.win32com.client')
    async def test_com_interface_error_handling(self, mock_com, processor):
        """测试COM接口错误处理"""
        mock_com.Dispatch.side_effect = Exception("COM接口错误")
        
        with pytest.raises(Exception, match="COM接口错误"):
            await processor.extract_text_content("invalid.docx")
```

### 3. 任务管理测试

#### 任务服务测试
```python
# tests/unit/test_tasks.py
import pytest
from unittest.mock import Mock, AsyncMock
from app.services.task_manager import TaskManager
from app.models.task import TaskStatus

@pytest.mark.unit
class TestTaskManager:
    
    @pytest.fixture
    def task_manager(self, test_db):
        return TaskManager(test_db)
    
    async def test_create_task(self, task_manager):
        """测试任务创建"""
        task_data = {
            "user_id": 1,
            "file_path": "test.docx",
            "task_type": "document_analysis"
        }
        
        task = await task_manager.create_task(task_data)
        assert task.user_id == 1
        assert task.status == TaskStatus.PENDING
        assert task.id is not None
    
    async def test_update_task_status(self, task_manager):
        """测试任务状态更新"""
        task_data = {
            "user_id": 1,
            "file_path": "test.docx",
            "task_type": "document_analysis"
        }
        
        task = await task_manager.create_task(task_data)
        updated_task = await task_manager.update_status(
            task.id, TaskStatus.PROCESSING
        )
        
        assert updated_task.status == TaskStatus.PROCESSING
    
    async def test_cancel_task(self, task_manager):
        """测试任务取消"""
        task_data = {
            "user_id": 1,
            "file_path": "test.docx",
            "task_type": "document_analysis"
        }
        
        task = await task_manager.create_task(task_data)
        cancelled_task = await task_manager.cancel_task(task.id)
        
        assert cancelled_task.status == TaskStatus.CANCELLED
    
    async def test_estimate_task_time(self, task_manager):
        """测试任务时间估算"""
        file_size = 1024 * 1024  # 1MB
        task_type = "document_analysis"
        
        estimated_time = task_manager.estimate_task_time(file_size, task_type)
        assert estimated_time > 0
        assert isinstance(estimated_time, int)
```

## 🔗 集成测试

### 1. API集成测试

#### 认证API测试
```python
# tests/integration/test_api_auth.py
import pytest

@pytest.mark.integration
class TestAuthAPI:
    
    async def test_register_flow(self, async_client):
        """测试完整注册流程"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "newpassword123"
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        result = response.json()
        assert "token" in result
        assert result["user"]["username"] == user_data["username"]
    
    async def test_login_flow(self, async_client, sample_user_data):
        """测试完整登录流程"""
        # 先注册用户
        await async_client.post("/api/v1/auth/register", json=sample_user_data)
        
        # 登录
        login_data = {
            "username": sample_user_data["username"],
            "password": sample_user_data["password"]
        }
        response = await async_client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        result = response.json()
        assert "access_token" in result
        assert result["token_type"] == "bearer"
    
    async def test_protected_endpoint_access(self, async_client, auth_headers):
        """测试受保护端点访问"""
        response = await async_client.get(
            "/api/v1/auth/me", 
            headers=auth_headers
        )
        assert response.status_code == 200
        
        result = response.json()
        assert "username" in result
```

#### 文档API测试
```python
# tests/integration/test_api_documents.py
import pytest
from io import BytesIO

@pytest.mark.integration
class TestDocumentAPI:
    
    async def test_document_upload_flow(self, async_client, auth_headers):
        """测试文档上传流程"""
        # 创建模拟文档文件
        file_content = b"Mock docx content"
        file_obj = BytesIO(file_content)
        
        files = {"file": ("test.docx", file_obj, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")}
        
        response = await async_client.post(
            "/api/v1/documents/upload",
            files=files,
            headers=auth_headers
        )
        assert response.status_code == 200
        
        result = response.json()
        assert "task_id" in result
        assert "filename" in result
    
    async def test_document_list(self, async_client, auth_headers):
        """测试文档列表获取"""
        response = await async_client.get(
            "/api/v1/documents/",
            headers=auth_headers
        )
        assert response.status_code == 200
        
        result = response.json()
        assert "items" in result
        assert "total" in result
    
    async def test_document_analysis_results(self, async_client, auth_headers):
        """测试文档分析结果获取"""
        # 假设已有分析完成的文档
        document_id = 1
        
        response = await async_client.get(
            f"/api/v1/documents/{document_id}/analysis",
            headers=auth_headers
        )
        # 根据实际情况，可能返回200或404
        assert response.status_code in [200, 404]
```

### 2. 数据库集成测试

#### 数据库操作测试
```python
# tests/integration/test_database.py
import pytest
from app.database.crud import UserCRUD, TaskCRUD

@pytest.mark.integration
@pytest.mark.database
class TestDatabaseOperations:
    
    async def test_user_crud_operations(self, test_db):
        """测试用户CRUD操作"""
        user_crud = UserCRUD(test_db)
        
        # 创建用户
        user_data = {
            "username": "dbtest",
            "email": "<EMAIL>",
            "hashed_password": "hashed_password"
        }
        user = await user_crud.create_user(user_data)
        assert user.id is not None
        
        # 查询用户
        found_user = await user_crud.get_user_by_username("dbtest")
        assert found_user.username == "dbtest"
        
        # 更新用户
        updated_user = await user_crud.update_user(user.id, {"email": "<EMAIL>"})
        assert updated_user.email == "<EMAIL>"
        
        # 删除用户
        await user_crud.delete_user(user.id)
        deleted_user = await user_crud.get_user_by_id(user.id)
        assert deleted_user is None
    
    async def test_task_crud_operations(self, test_db):
        """测试任务CRUD操作"""
        task_crud = TaskCRUD(test_db)
        
        # 创建任务
        task_data = {
            "user_id": 1,
            "filename": "test.docx",
            "file_path": "/uploads/test.docx",
            "task_type": "document_analysis"
        }
        task = await task_crud.create_task(task_data)
        assert task.id is not None
        
        # 查询任务
        found_task = await task_crud.get_task_by_id(task.id)
        assert found_task.filename == "test.docx"
        
        # 更新任务状态
        updated_task = await task_crud.update_task_status(task.id, "completed")
        assert updated_task.status == "completed"
```

## 🚀 性能测试

### 1. 负载测试配置

#### Locust配置文件
```python
# tests/performance/locustfile.py
from locust import HttpUser, task, between
import json

class WordServiceUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """测试开始时执行的操作"""
        # 注册用户
        user_data = {
            "username": f"user_{self.environment.parsed_options.num_users}_{id(self)}",
            "email": f"user_{id(self)}@example.com",
            "password": "testpassword123"
        }
        
        response = self.client.post("/api/v1/auth/register", json=user_data)
        if response.status_code == 200:
            # 登录获取token
            login_data = {
                "username": user_data["username"],
                "password": user_data["password"]
            }
            response = self.client.post("/api/v1/auth/login", data=login_data)
            if response.status_code == 200:
                token = response.json()["access_token"]
                self.headers = {"Authorization": f"Bearer {token}"}
            else:
                self.headers = {}
        else:
            self.headers = {}
    
    @task(3)
    def test_health_check(self):
        """健康检查测试"""
        self.client.get("/health")
    
    @task(2)
    def test_system_stats(self):
        """系统统计测试"""
        self.client.get("/api/v1/system/stats", headers=self.headers)
    
    @task(1)
    def test_document_list(self):
        """文档列表测试"""
        self.client.get("/api/v1/documents/", headers=self.headers)
    
    @task(1)
    def test_task_list(self):
        """任务列表测试"""
        self.client.get("/api/v1/tasks/", headers=self.headers)

class AdminUser(HttpUser):
    wait_time = between(2, 5)
    weight = 1  # 管理员用户权重较低
    
    def on_start(self):
        """管理员登录"""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = self.client.post("/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            token = response.json()["access_token"]
            self.headers = {"Authorization": f"Bearer {token}"}
        else:
            self.headers = {}
    
    @task
    def test_admin_operations(self):
        """管理员操作测试"""
        self.client.get("/api/v1/system/stats", headers=self.headers)
        self.client.post("/api/v1/system/cleanup", headers=self.headers)
```

### 2. 压力测试脚本

#### 性能测试运行脚本
```python
# tests/performance/test_stress.py
import asyncio
import aiohttp
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

async def stress_test_endpoint(session, url, headers=None, data=None):
    """压力测试单个端点"""
    try:
        start_time = time.time()
        if data:
            async with session.post(url, headers=headers, json=data) as response:
                await response.text()
                return time.time() - start_time, response.status
        else:
            async with session.get(url, headers=headers) as response:
                await response.text()
                return time.time() - start_time, response.status
    except Exception as e:
        return None, 500

async def run_stress_test(base_url, endpoint, concurrent_users=50, requests_per_user=10):
    """运行压力测试"""
    print(f"开始压力测试: {endpoint}")
    print(f"并发用户数: {concurrent_users}, 每用户请求数: {requests_per_user}")
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for user in range(concurrent_users):
            for request in range(requests_per_user):
                task = stress_test_endpoint(session, f"{base_url}{endpoint}")
                tasks.append(task)
        
        results = await asyncio.gather(*tasks)
    
    # 分析结果
    response_times = [r[0] for r in results if r[0] is not None]
    status_codes = [r[1] for r in results]
    
    success_count = sum(1 for code in status_codes if 200 <= code < 300)
    error_count = len(status_codes) - success_count
    
    print(f"测试完成!")
    print(f"总请求数: {len(results)}")
    print(f"成功请求: {success_count}")
    print(f"失败请求: {error_count}")
    print(f"成功率: {success_count/len(results)*100:.2f}%")
    
    if response_times:
        print(f"平均响应时间: {statistics.mean(response_times):.3f}s")
        print(f"最小响应时间: {min(response_times):.3f}s")
        print(f"最大响应时间: {max(response_times):.3f}s")
        print(f"响应时间中位数: {statistics.median(response_times):.3f}s")
    
    return {
        "total_requests": len(results),
        "success_count": success_count,
        "error_count": error_count,
        "success_rate": success_count/len(results),
        "response_times": response_times
    }

if __name__ == "__main__":
    base_url = "http://localhost:8000"
    endpoints = [
        "/health",
        "/api/v1/system/stats",
        "/api/v1/documents/",
        "/api/v1/tasks/"
    ]
    
    for endpoint in endpoints:
        asyncio.run(run_stress_test(base_url, endpoint))
        print("-" * 50)
```

## 🔄 端到端测试

### 1. 用户流程测试

#### 完整用户流程
```python
# tests/e2e/test_user_flow.py
import pytest
import asyncio
from httpx import AsyncClient

@pytest.mark.e2e
class TestUserFlow:
    
    async def test_complete_user_journey(self):
        """测试完整用户使用流程"""
        async with AsyncClient(base_url="http://localhost:8000") as client:
            
            # 1. 用户注册
            user_data = {
                "username": "e2euser",
                "email": "<EMAIL>",
                "password": "e2epassword123"
            }
            response = await client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 200
            token = response.json()["token"]
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # 2. 查看个人信息
            response = await client.get("/api/v1/auth/me", headers=headers)
            assert response.status_code == 200
            user_info = response.json()
            assert user_info["username"] == "e2euser"
            
            # 3. 查看文档列表（应为空）
            response = await client.get("/api/v1/documents/", headers=headers)
            assert response.status_code == 200
            docs = response.json()
            assert docs["total"] == 0
            
            # 4. 查看任务列表（应为空）
            response = await client.get("/api/v1/tasks/", headers=headers)
            assert response.status_code == 200
            tasks = response.json()
            assert len(tasks) == 0
            
            # 5. 上传文档（模拟）
            # 注意：这里需要实际的文档文件或模拟
            
            # 6. 查看系统状态
            response = await client.get("/api/v1/system/stats", headers=headers)
            assert response.status_code == 200
            stats = response.json()
            assert "total_tasks" in stats
    
    async def test_document_processing_flow(self):
        """测试文档处理完整流程"""
        # 这个测试需要实际的文档文件和COM接口
        pass
```

## 📊 测试执行和报告

### 1. 测试执行命令

#### 基本测试执行
```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit                    # 只运行单元测试
pytest -m integration             # 只运行集成测试
pytest -m "not slow"              # 排除慢速测试

# 运行特定文件的测试
pytest tests/unit/test_auth.py

# 运行特定测试函数
pytest tests/unit/test_auth.py::TestAuthService::test_register_user

# 并行运行测试
pytest -n auto                    # 自动检测CPU核心数
pytest -n 4                       # 使用4个进程

# 生成详细报告
pytest --html=reports/report.html --self-contained-html
```

#### 覆盖率测试
```bash
# 生成覆盖率报告
pytest --cov=app --cov-report=html --cov-report=term

# 只查看未覆盖的代码
pytest --cov=app --cov-report=term-missing

# 设置覆盖率阈值
pytest --cov=app --cov-fail-under=80
```

#### 性能测试执行
```bash
# 使用Locust运行负载测试
locust -f tests/performance/locustfile.py --host=http://localhost:8000

# 无Web界面运行
locust -f tests/performance/locustfile.py --host=http://localhost:8000 \
       --users 100 --spawn-rate 10 --run-time 60s --headless

# 运行压力测试
python tests/performance/test_stress.py
```

### 2. 持续集成配置

#### GitHub Actions配置
```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: windows-latest
    
    services:
      postgres:
        image: postgres:17.5
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: word_service_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Install dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: Run unit tests
      run: |
        cd backend
        pytest tests/unit/ -v --cov=app --cov-report=xml
    
    - name: Run integration tests
      run: |
        cd backend
        pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost:5432/word_service_test
        REDIS_URL: redis://localhost:6379/0
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: unittests
        name: codecov-umbrella
```

### 3. 测试报告和监控

#### 生成测试报告
```python
# tests/utils/report_generator.py
import json
import datetime
from pathlib import Path

def generate_test_report(test_results, output_path="reports/test_report.json"):
    """生成JSON格式的测试报告"""
    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "summary": {
            "total_tests": test_results.get("total", 0),
            "passed": test_results.get("passed", 0),
            "failed": test_results.get("failed", 0),
            "skipped": test_results.get("skipped", 0),
            "coverage": test_results.get("coverage", 0)
        },
        "details": test_results.get("details", []),
        "performance": test_results.get("performance", {}),
        "environment": {
            "python_version": test_results.get("python_version"),
            "os": test_results.get("os"),
            "dependencies": test_results.get("dependencies", [])
        }
    }
    
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    return report
```

## 🎯 测试最佳实践

### 1. 测试命名规范
- 测试文件：`test_*.py`
- 测试类：`Test*`
- 测试方法：`test_*`
- 描述性命名：`test_should_return_error_when_invalid_input`

### 2. 测试数据管理
- 使用fixtures提供测试数据
- 每个测试应该是独立的
- 避免测试间的依赖关系
- 使用工厂模式创建测试对象

### 3. 模拟和存根
- 对外部依赖使用模拟（数据库、API、文件系统）
- 测试真实业务逻辑而非外部服务
- 使用patch装饰器或context manager

### 4. 异步测试注意事项
- 使用pytest-asyncio处理异步测试
- 正确管理事件循环
- 确保异步资源正确清理

## 🚨 常见问题解决

### Q1: 测试运行缓慢
```bash
# 使用并行执行
pip install pytest-xdist
pytest -n auto

# 跳过慢速测试
pytest -m "not slow"
```

### Q2: 数据库测试污染
```python
# 使用事务回滚
@pytest.fixture
async def test_db():
    async with session_factory() as session:
        async with session.begin():
            yield session
            await session.rollback()
```

### Q3: COM接口测试问题
```python
# 模拟COM接口
@patch('app.services.word_com.win32com.client')
def test_com_operation(mock_com):
    mock_com.Dispatch.return_value = Mock()
    # 测试逻辑
```

---

📊 **测试覆盖率目标：80%以上**
🎯 **性能目标：响应时间 < 200ms**
✅ **质量目标：0 Critical Bug** 