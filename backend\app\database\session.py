"""
数据库会话管理 - 异步PostgreSQL版本
"""
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.connection import get_db_session, get_database_session
from app.core.logging import logger

async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖注入函数
    用于FastAPI的Depends()
    """
    async for session in get_db_session():
        yield session

# 向后兼容的别名
get_db = get_db_session


class SessionManager:
    """简化的数据库会话管理器"""
    
    @staticmethod
    async def execute_crud_operation(crud_func, *args, **kwargs):
        """
        执行CRUD操作，自动管理session生命周期
        
        Args:
            crud_func: CRUD函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            CRUD函数的返回值
        """
        from app.database.connection import get_database_session
        
        session = await get_database_session()
        try:
            # 将session作为第一个参数传递
            result = await crud_func(session, *args, **kwargs)
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            logger.error(f"CRUD操作失败: {str(e)}")
            raise
        finally:
            await session.close()


# 创建全局实例
session_manager = SessionManager() 