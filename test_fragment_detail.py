"""
获取中文关键词问题片段的详细信息
"""

import requests
import json

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heibailan5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def get_fragment_detail(token, task_id, fragment_id):
    """获取问题片段详细信息"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}/{fragment_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        print(f"🔍 获取问题片段详情: {url}")
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            fragment = data.get("data", {})
            
            print("✅ 问题片段详情获取成功!")
            print(f"📋 片段ID: {fragment.get('fragment_id', 'N/A')}")
            print(f"📋 结构: {fragment.get('structure', 'N/A')}")
            print(f"📋 类别: {fragment.get('category', 'N/A')}")
            print(f"📋 严重程度: {fragment.get('severity', 'N/A')}")
            print(f"📋 位置: {fragment.get('position', 'N/A')}")
            print(f"📋 规则ID: {fragment.get('rule_id', 'N/A')}")
            print(f"📋 可自动修复: {fragment.get('auto_fixable', 'N/A')}")
            
            print(f"\n📄 原文片段:")
            print(f"   {fragment.get('original_text', 'N/A')}")
            
            print(f"\n🔍 问题描述:")
            problem_desc = fragment.get('problem_description', 'N/A')
            print(f"   {problem_desc}")
            
            # 检查是否包含对齐问题
            if "alignment" in problem_desc.lower() or "对齐" in problem_desc:
                print(f"\n🎯 ✅ 发现对齐问题!")
                
                # 提取对齐相关的错误信息
                lines = problem_desc.split(';')
                for line in lines:
                    if "alignment" in line.lower() or "对齐" in line:
                        print(f"   对齐错误: {line.strip()}")
            else:
                print(f"\n⚠️ 未在问题描述中找到对齐相关信息")
            
            print(f"\n📚 标准参考:")
            print(f"   {fragment.get('standard_reference', 'N/A')}")
            
            # 检查扩展信息
            if "extended_context" in fragment:
                print(f"\n📖 扩展上下文:")
                extended = fragment["extended_context"]
                print(f"   前文: {extended.get('paragraph_before', 'N/A')}")
                print(f"   后文: {extended.get('paragraph_after', 'N/A')}")
            
            if "standard_format" in fragment:
                print(f"\n📝 标准格式示例:")
                print(f"   {fragment.get('standard_format', 'N/A')}")
            
            if "fix_confidence" in fragment:
                print(f"\n🔧 修复置信度: {fragment.get('fix_confidence', 'N/A')}")
            
            if "manual_review_required" in fragment:
                print(f"\n👁️ 需要人工审核: {fragment.get('manual_review_required', 'N/A')}")
            
            return fragment
        else:
            print(f"❌ 获取问题片段详情失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 获取问题片段详情失败: {str(e)}")
        return None

def main():
    """主测试流程"""
    print("🔍 获取中文关键词问题片段详细信息")
    print("=" * 60)
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌")
        return
    
    # 使用已知的任务ID和片段ID
    task_id = "task_c3345acc3e4647b1b7586acbdb10a587"
    fragment_id = "frag_6f8be46a"  # 中文关键词格式问题片段
    
    print(f"🔍 分析任务: {task_id}")
    print(f"🔍 问题片段: {fragment_id}")
    
    fragment = get_fragment_detail(token, task_id, fragment_id)
    
    if fragment:
        print(f"\n🎉 成功获取中文关键词问题片段详情!")
        
        # 检查是否是我们添加的规则检测到的问题
        rule_id = fragment.get('rule_id', '')
        problem_desc = fragment.get('problem_description', '')
        
        if "format.paragraph" in rule_id and "关键词" in problem_desc:
            print(f"\n✅ 确认这是我们添加的中文关键词格式检查规则检测到的问题!")
            
            if "alignment" in problem_desc.lower():
                print(f"✅ 确认检测到了对齐问题!")
            else:
                print(f"⚠️ 问题描述中可能包含对齐问题，但被截断了")
        
        print(f"\n🌐 前端查看: http://localhost:3000/document/{task_id}/statistics")
    
    print("\n" + "=" * 60)
    print("🏁 分析完成")

if __name__ == "__main__":
    main()
