import apiService from './api'
import type { LoginRequest, LoginResponse, RegisterRequest, User } from '@/types'

export class AuthApi {
  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    // 后端期望OAuth2PasswordRequestForm格式
    const formData = new URLSearchParams()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)
    
    const response = await apiService.post<{
      access_token: string
      refresh_token: string
      token_type: string
      user: User
      expires_in: number
    }>('/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    
    // 保存token和用户信息
    if (response.access_token) {
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      localStorage.setItem('user', JSON.stringify(response.user))
    }
    
    return {
      access_token: response.access_token,
      token_type: response.token_type || 'bearer',
      user: response.user
    }
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterRequest): Promise<LoginResponse> {
    const response = await apiService.post<{
      access_token: string
      refresh_token: string
      token_type: string
      user: User
      expires_in: number
    }>('/v1/auth/register', userData)
    
    // 注册成功后自动登录
    if (response.access_token) {
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      localStorage.setItem('user', JSON.stringify(response.user))
    }
    
    return {
      access_token: response.access_token,
      token_type: response.token_type || 'bearer',
      user: response.user
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await apiService.post('/v1/auth/logout')
    } finally {
      // 无论请求是否成功都清理本地数据
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    return apiService.get<User>('/v1/auth/me')
  }

  /**
   * 获取用户详细资料（包含统计信息）
   */
  async getUserProfile(): Promise<any> {
    return apiService.get('/v1/auth/profile')
  }

  /**
   * 更新用户信息
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    return apiService.put<User>('/v1/auth/profile', userData)
  }

  /**
   * 修改密码
   */
  async changePassword(data: {
    old_password: string
    new_password: string
  }): Promise<void> {
    return apiService.post('/v1/auth/change-password', data)
  }

  /**
   * 忘记密码 - 发送重置邮件
   */
  async forgotPassword(email: string): Promise<void> {
    return apiService.post('/v1/auth/forgot-password', { email })
  }

  /**
   * 重置密码
   */
  async resetPassword(data: {
    token: string
    new_password: string
  }): Promise<void> {
    return apiService.post('/v1/auth/reset-password', data)
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(token: string): Promise<void> {
    return apiService.post('/v1/auth/verify-email', { token })
  }

  /**
   * 重新发送验证邮件
   */
  async resendVerificationEmail(): Promise<void> {
    return apiService.post('/v1/auth/resend-verification')
  }

  /**
   * 检查用户名是否可用
   */
  async checkUsernameAvailability(username: string): Promise<{ available: boolean }> {
    return apiService.get<{ available: boolean }>(`/v1/auth/check-username/${username}`)
  }

  /**
   * 检查邮箱是否可用
   */
  async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    return apiService.get<{ available: boolean }>(`/v1/auth/check-email/${email}`)
  }

  /**
   * 刷新Token
   */
  async refreshToken(): Promise<{ access_token: string; refresh_token?: string }> {
    const refreshToken = localStorage.getItem('refresh_token')
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiService.post<{ access_token: string; refresh_token?: string }>(
      '/v1/auth/refresh',
      { refresh_token: refreshToken }
    )

    // 更新存储的token
    localStorage.setItem('access_token', response.access_token)
    if (response.refresh_token) {
      localStorage.setItem('refresh_token', response.refresh_token)
    }

    return response
  }

  /**
   * 获取本地存储的用户信息
   */
  getStoredUser(): User | null {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return !!localStorage.getItem('access_token')
  }

  /**
   * 获取存储的Token
   */
  getStoredToken(): string | null {
    return localStorage.getItem('access_token')
  }
}

export const authApi = new AuthApi()
export default authApi 