<template>
  <div class="document-issues-panel">
    <BaseCard>
      <template #header>
        <div class="flex items-center justify-between w-full">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">问题详情</h3>
          <div class="flex items-center space-x-3">
            <!-- 严重程度筛选 -->
            <select v-model="selectedSeverity" 
                    @change="loadProblems"
                    class="text-sm border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="">全部严重程度</option>
              <option value="critical">严重</option>
              <option value="high">高</option>
              <option value="medium">中等</option>
              <option value="low">低</option>
            </select>
            
            <!-- 问题总数 -->
            <span v-if="!loading && problems.length > 0" 
                  class="text-sm text-gray-600 dark:text-gray-300">
              共 {{ totalProblems }} 个问题
            </span>
          </div>
        </div>
      </template>

      <div v-if="loading" class="text-center py-8">
        <div class="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-gray-600 dark:text-gray-300">正在加载问题列表...</p>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <div class="text-red-500 mb-4">
          <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <p class="text-red-500 mb-4">{{ error }}</p>
        <BaseButton @click="loadProblems" variant="secondary" size="sm">重试</BaseButton>
      </div>

      <div v-else-if="problems.length === 0" class="text-center py-8">
        <div class="text-green-500 mb-4">
          <svg class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <p class="text-gray-600 dark:text-gray-300">
          {{ selectedSeverity ? `没有 ${getSeverityText(selectedSeverity)} 问题` : '恭喜！没有发现任何问题' }}
        </p>
      </div>

      <div v-else class="space-y-4">
        <div v-for="problem in problems" 
             :key="problem.problem_id"
             class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow">
          
          <!-- 问题头部 -->
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-3">
              <span :class="getSeverityBadgeClass(problem.severity)"
                    class="px-2 py-1 rounded-full text-xs font-medium">
                {{ getSeverityText(problem.severity) }}
              </span>
              <span class="text-sm text-gray-600 dark:text-gray-400">
                {{ problem.category }} - {{ problem.problem_type }}
              </span>
            </div>
            
            <!-- 位置信息 -->
            <div v-if="problem.page_number || problem.position" 
                 class="text-xs text-gray-500 dark:text-gray-400">
              <span v-if="problem.page_number">第 {{ problem.page_number }} 页</span>
              <span v-if="problem.page_number && problem.position"> · </span>
              <span v-if="problem.position">位置 {{ problem.position }}</span>
            </div>
          </div>

          <!-- 问题标题 -->
          <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">
            {{ problem.title }}
          </h4>

          <!-- 问题描述 -->
          <p class="text-sm text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
            {{ problem.description }}
          </p>

          <!-- 位置范围 -->
          <div v-if="problem.range_start !== undefined && problem.range_end !== undefined"
               class="text-xs text-gray-500 dark:text-gray-400 mb-3">
            文本范围: {{ problem.range_start }} - {{ problem.range_end }}
          </div>

          <!-- 修改建议 -->
          <div v-if="problem.suggestion" 
               class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3 mb-3">
            <div class="flex items-start space-x-2">
              <svg class="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" 
                   fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <div>
                <p class="text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">修改建议</p>
                <p class="text-sm text-blue-700 dark:text-blue-300">{{ problem.suggestion }}</p>
              </div>
            </div>
          </div>

          <!-- 底部操作 -->
          <div class="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
            <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span v-if="problem.auto_fixable" class="flex items-center space-x-1">
                <svg class="h-3 w-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span>可自动修复</span>
              </span>
              <span v-if="problem.element_id">元素ID: {{ problem.element_id }}</span>
            </div>
            
            <div class="flex items-center space-x-2">
              <BaseButton v-if="problem.auto_fixable"
                         @click="fixProblem(problem)"
                         variant="primary"
                         size="xs">
                自动修复
              </BaseButton>
              <BaseButton @click="locateProblem(problem)"
                         variant="secondary"
                         size="xs">
                定位问题
              </BaseButton>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination && pagination.pages > 1" class="flex justify-center pt-4">
                     <BasePagination 
             :current-page="currentPage"
             :page-size="20"
             :total="pagination.total"
             @page-change="handlePageChange"
           />
        </div>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import BasePagination from '@/components/BasePagination.vue'
import { DocumentApi } from '@/services/documentApi'
import type { DocumentProblem } from '@/types'
import { useNotifications } from '@/utils/useNotifications'

// Props
interface Props {
  documentId: string
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// 状态
const loading = ref(false)
const error = ref<string | null>(null)
const problems = ref<DocumentProblem[]>([])
const pagination = ref<any>(null)
const currentPage = ref(1)
const selectedSeverity = ref('')
const totalProblems = ref(0)

// 服务
const documentApi = new DocumentApi()
const { addNotification } = useNotifications()

// 方法
const loadProblems = async () => {
  if (!props.documentId) return

  loading.value = true
  error.value = null

  try {
    const result = await documentApi.getDocumentProblems(
      props.documentId,
      selectedSeverity.value || undefined,
      undefined,
      currentPage.value,
      20
    )
    
    problems.value = result.problems || []
    pagination.value = result.pagination || null
    totalProblems.value = result.pagination?.total || problems.value.length
    
  } catch (err: any) {
    error.value = err.message || '加载问题列表失败'
    console.error('加载问题列表失败:', err)
  } finally {
    loading.value = false
  }
}

const getSeverityBadgeClass = (severity: string) => {
  const classes = {
    'critical': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    'high': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
    'low': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
  }
  return classes[severity as keyof typeof classes] || classes.low
}

const getSeverityText = (severity: string) => {
  const texts = {
    'critical': '严重',
    'high': '高',
    'medium': '中等',
    'low': '低'
  }
  return texts[severity as keyof typeof texts] || severity
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadProblems()
}

const fixProblem = async (problem: DocumentProblem) => {
  try {
    // TODO: 实现自动修复功能
    addNotification({
      type: 'info',
      title: '功能开发中',
      message: `自动修复功能正在开发中：${problem.title}`
    })
  } catch (err: any) {
    addNotification({
      type: 'error',
      title: '修复失败',
      message: err.message || '自动修复失败'
    })
  }
}

const locateProblem = (problem: DocumentProblem) => {
  // TODO: 实现问题定位功能
  let locationInfo = '问题位置：'
  if (problem.page_number) {
    locationInfo += `第 ${problem.page_number} 页`
  }
  if (problem.position) {
    locationInfo += ` 位置 ${problem.position}`
  }
  if (problem.range_start !== undefined && problem.range_end !== undefined) {
    locationInfo += ` 文本范围 ${problem.range_start}-${problem.range_end}`
  }
  
  addNotification({
    type: 'info',
    title: '问题定位',
    message: locationInfo
  })
}

// 监听documentId变化
watch(() => props.documentId, (newId) => {
  if (newId && props.autoLoad) {
    currentPage.value = 1
    selectedSeverity.value = ''
    loadProblems()
  }
})

// 监听筛选条件变化
watch(selectedSeverity, () => {
  currentPage.value = 1
  if (props.documentId) {
    loadProblems()
  }
})

// 生命周期
onMounted(() => {
  if (props.documentId && props.autoLoad) {
    loadProblems()
  }
})

// 暴露方法
defineExpose({
  loadProblems,
  problems,
  totalProblems
})
</script>

<style scoped>
.document-issues-panel :deep(.card-header) {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
}

.document-issues-panel :deep(.card-content) {
  @apply p-6;
}
</style> 