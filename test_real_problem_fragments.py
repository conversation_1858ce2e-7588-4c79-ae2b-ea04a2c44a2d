import requests
import json
import time
import os

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heiba<PERSON>n5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def upload_document(token):
    """上传测试文档"""
    try:
        upload_url = "http://localhost:8000/api/v1/documents/upload"
        headers = {"Authorization": f"Bearer {token}"}
        
        # 检查测试文档是否存在
        test_doc_path = "docs/test.docx"
        if not os.path.exists(test_doc_path):
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return None
        
        with open(test_doc_path, 'rb') as f:
            files = {"file": ("test.docx", f, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")}
            data = {"document_type": "thesis"}
            
            response = requests.post(upload_url, headers=headers, files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("data", {}).get("task_id")
            print(f"✅ 文档上传成功: {task_id}")
            return task_id
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上传文档失败: {str(e)}")
        return None

def wait_for_task_completion(token, task_id, max_wait_time=120):
    """等待任务完成"""
    try:
        task_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            response = requests.get(task_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                task_data = result.get("data", {})
                status = task_data.get("status")
                progress = task_data.get("progress", 0)
                
                print(f"📊 任务状态: {status} ({progress}%)")
                
                if status == "completed":
                    print("✅ 任务完成")
                    return True
                elif status == "failed":
                    print("❌ 任务失败")
                    error_message = task_data.get("error_message", "未知错误")
                    print(f"错误信息: {error_message}")
                    return False
                else:
                    print("⏳ 等待任务完成...")
                    time.sleep(5)
            else:
                print(f"❌ 获取任务状态失败: {response.status_code}")
                return False
        
        print("⏰ 等待超时")
        return False
        
    except Exception as e:
        print(f"❌ 等待任务完成失败: {str(e)}")
        return False

def test_problem_fragments(token, task_id):
    """测试问题片段API"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        print(f"🔍 测试问题片段API: {url}")
        
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 20
        })
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 问题片段API测试成功!")
            print(f"📋 问题片段总数: {data['data']['total_count']}")
            
            fragments = data['data']['fragments']
            print(f"🔍 当前页片段数量: {len(fragments)}")
            
            # 按结构分组统计
            structure_stats = {}
            severity_stats = {}
            
            for fragment in fragments:
                structure = fragment.get('structure', '未知')
                severity = fragment.get('severity', '未知')
                
                structure_stats[structure] = structure_stats.get(structure, 0) + 1
                severity_stats[severity] = severity_stats.get(severity, 0) + 1
            
            print("\n📊 按结构分组统计:")
            for structure, count in structure_stats.items():
                print(f"   {structure}: {count} 个")
            
            print("\n📊 按严重程度统计:")
            for severity, count in severity_stats.items():
                print(f"   {severity}: {count} 个")
            
            # 显示前几个问题片段的详细信息
            print("\n📌 问题片段详情:")
            for i, fragment in enumerate(fragments[:5]):
                print(f"\n   片段 {i+1}:")
                print(f"     ID: {fragment.get('fragment_id', 'N/A')}")
                print(f"     结构: {fragment.get('structure', 'N/A')}")
                print(f"     类别: {fragment.get('category', 'N/A')}")
                print(f"     严重程度: {fragment.get('severity', 'N/A')}")
                print(f"     位置: {fragment.get('position', 'N/A')}")
                print(f"     原文: {fragment.get('original_text', 'N/A')[:50]}...")
                print(f"     问题描述: {fragment.get('problem_description', 'N/A')}")
                print(f"     标准参考: {fragment.get('standard_reference', 'N/A')}")
                print(f"     可自动修复: {fragment.get('auto_fixable', False)}")
                print(f"     规则ID: {fragment.get('rule_id', 'N/A')}")
            
            return True
        else:
            print(f"❌ 问题片段API测试失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试问题片段失败: {str(e)}")
        return False

def test_fragment_detail(token, task_id, fragment_id):
    """测试问题片段详情API"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}/{fragment_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        print(f"\n🔍 测试问题片段详情API: {url}")
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            fragment = data.get("data", {})
            
            print("✅ 问题片段详情API测试成功!")
            print(f"📋 片段ID: {fragment.get('fragment_id', 'N/A')}")
            print(f"📋 扩展上下文: {bool(fragment.get('extended_context'))}")
            print(f"📋 标准格式示例: {fragment.get('standard_format', 'N/A')[:50]}...")
            print(f"📋 修复置信度: {fragment.get('fix_confidence', 'N/A')}")
            print(f"📋 需要人工审核: {fragment.get('manual_review_required', 'N/A')}")
            
            return True
        else:
            print(f"❌ 问题片段详情API测试失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试问题片段详情失败: {str(e)}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始真实问题片段功能测试")
    print("=" * 60)
    
    # 1. 获取认证令牌
    print("🔐 步骤1: 获取认证令牌...")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        return
    
    # 2. 上传文档并创建任务
    print("\n📄 步骤2: 上传测试文档...")
    task_id = upload_document(token)
    if not task_id:
        print("❌ 无法上传文档，测试终止")
        return
    
    # 3. 等待任务完成
    print("\n⏳ 步骤3: 等待任务完成...")
    if not wait_for_task_completion(token, task_id):
        print("❌ 任务未完成，但继续测试问题片段API...")
    
    # 4. 测试问题片段API
    print("\n🔍 步骤4: 测试问题片段API...")
    if test_problem_fragments(token, task_id):
        print("\n✅ 问题片段API测试通过!")
        
        # 5. 测试问题片段详情API（使用第一个片段）
        print("\n🔍 步骤5: 测试问题片段详情API...")
        # 这里可以使用一个已知的fragment_id，或者从上一步获取
        test_fragment_id = "frag_001"  # 使用模拟的ID进行测试
        test_fragment_detail(token, task_id, test_fragment_id)
        
        print(f"\n🌐 前端统计页面: http://localhost:3000/document/{task_id}/statistics")
    else:
        print("\n❌ 问题片段API测试失败")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
