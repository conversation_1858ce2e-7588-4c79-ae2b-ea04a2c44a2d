"""
缓存工具函数

提供高级缓存功能，包括缓存装饰器、批量缓存、缓存预热等
"""

import json
import asyncio
import hashlib
from typing import Any, Optional, Dict, List, Callable, Union
from functools import wraps
from datetime import datetime, timedelta

from app.core.cache import cache_get, cache_set, cache_delete, is_redis_available
from app.core.logging import logger


class CacheManager:
    """高级缓存管理器"""
    
    def __init__(self, prefix: str = "word_service"):
        self.prefix = prefix
        self.default_ttl = 3600  # 1小时
        
    def _make_key(self, key: str) -> str:
        """生成带前缀的缓存键"""
        return f"{self.prefix}:{key}"
    
    def _serialize_value(self, value: Any) -> str:
        """序列化缓存值"""
        return json.dumps(value, ensure_ascii=False, separators=(',', ':'))
    
    def _deserialize_value(self, value: str) -> Any:
        """反序列化缓存值"""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        cache_key = self._make_key(key)
        try:
            cached_value = await cache_get(cache_key)
            if cached_value is not None:
                return self._deserialize_value(cached_value)
            return default
        except Exception as e:
            logger.warning(f"缓存获取失败 {cache_key}: {e}")
            return default
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        cache_key = self._make_key(key)
        ttl = ttl or self.default_ttl
        try:
            serialized_value = self._serialize_value(value)
            return await cache_set(cache_key, serialized_value, ttl)
        except Exception as e:
            logger.warning(f"缓存设置失败 {cache_key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        cache_key = self._make_key(key)
        try:
            return await cache_delete(cache_key)
        except Exception as e:
            logger.warning(f"缓存删除失败 {cache_key}: {e}")
            return False
    
    async def get_or_set(self, key: str, func: Callable, ttl: Optional[int] = None, *args, **kwargs) -> Any:
        """获取缓存值，如果不存在则执行函数并缓存结果"""
        # 先尝试从缓存获取
        cached_value = await self.get(key)
        if cached_value is not None:
            return cached_value
        
        # 缓存不存在，执行函数
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 缓存结果
            await self.set(key, result, ttl)
            return result
        except Exception as e:
            logger.error(f"缓存更新失败 {key}: {e}")
            raise
    
    async def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存值"""
        results = {}
        for key in keys:
            results[key] = await self.get(key)
        return results
    
    async def mset(self, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存值"""
        all_success = True
        for key, value in data.items():
            success = await self.set(key, value, ttl)
            if not success:
                all_success = False
        return all_success


def cache_key_generator(*args, **kwargs) -> str:
    """生成缓存键"""
    # 将参数转换为字符串
    key_parts = []
    
    # 处理位置参数
    for arg in args:
        if hasattr(arg, '__dict__'):
            # 对象参数，使用类名
            key_parts.append(f"{arg.__class__.__name__}")
        else:
            key_parts.append(str(arg))
    
    # 处理关键字参数
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}={v}")
    
    # 生成哈希
    key_string = ":".join(key_parts)
    return hashlib.md5(key_string.encode('utf-8')).hexdigest()


def cached(ttl: int = 3600, key_prefix: str = "", key_func: Optional[Callable] = None):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
        key_func: 自定义缓存键生成函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 跳过缓存如果Redis不可用
            if not await is_redis_available():
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                func_name = func.__name__
                arg_key = cache_key_generator(*args, **kwargs)
                cache_key = f"{key_prefix}:{func_name}:{arg_key}" if key_prefix else f"{func_name}:{arg_key}"
            
            cache_manager = CacheManager()
            
            try:
                # 尝试从缓存获取
                result = await cache_manager.get(cache_key)
                if result is not None:
                    logger.debug(f"缓存命中: {cache_key}")
                    return result
                
                # 执行原函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 缓存结果
                await cache_manager.set(cache_key, result, ttl)
                logger.debug(f"缓存更新: {cache_key}")
                
                return result
                
            except Exception as e:
                logger.error(f"缓存装饰器错误 {cache_key}: {e}")
                # 缓存失败时直接执行原函数
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
        
        return wrapper
    return decorator


def cache_invalidate(pattern: str):
    """
    缓存失效装饰器
    
    Args:
        pattern: 要失效的缓存键模式
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 执行原函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 失效相关缓存
            try:
                cache_manager = CacheManager()
                # 这里可以扩展为支持模式匹配的删除
                await cache_manager.delete(pattern)
                logger.debug(f"缓存失效: {pattern}")
            except Exception as e:
                logger.warning(f"缓存失效失败 {pattern}: {e}")
            
            return result
        return wrapper
    return decorator


class UserCacheManager(CacheManager):
    """用户相关缓存管理器"""
    
    def __init__(self):
        super().__init__("user")
    
    async def get_user_stats(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户统计信息缓存"""
        return await self.get(f"stats:{user_id}")
    
    async def set_user_stats(self, user_id: str, stats: Dict[str, Any], ttl: int = 1800) -> bool:
        """设置用户统计信息缓存"""
        return await self.set(f"stats:{user_id}", stats, ttl)
    
    async def invalidate_user_cache(self, user_id: str) -> bool:
        """清除用户相关缓存"""
        keys = [
            f"stats:{user_id}",
            f"tasks:{user_id}",
            f"documents:{user_id}"
        ]
        
        all_success = True
        for key in keys:
            success = await self.delete(key)
            if not success:
                all_success = False
        
        return all_success


class SystemCacheManager(CacheManager):
    """系统相关缓存管理器"""
    
    def __init__(self):
        super().__init__("system")
    
    async def get_system_stats(self) -> Optional[Dict[str, Any]]:
        """获取系统统计信息缓存"""
        return await self.get("stats")
    
    async def set_system_stats(self, stats: Dict[str, Any], ttl: int = 300) -> bool:
        """设置系统统计信息缓存（5分钟）"""
        return await self.set("stats", stats, ttl)
    
    async def get_task_stats(self) -> Optional[Dict[str, Any]]:
        """获取任务统计信息缓存"""
        return await self.get("task_stats")
    
    async def set_task_stats(self, stats: Dict[str, Any], ttl: int = 600) -> bool:
        """设置任务统计信息缓存（10分钟）"""
        return await self.set("task_stats", stats, ttl)


class DocumentCacheManager(CacheManager):
    """文档相关缓存管理器"""
    
    def __init__(self):
        super().__init__("document")
    
    async def get_document_analysis(self, document_id: str) -> Optional[Dict[str, Any]]:
        """获取文档分析结果缓存"""
        return await self.get(f"analysis:{document_id}")
    
    async def set_document_analysis(self, document_id: str, analysis: Dict[str, Any], ttl: int = 7200) -> bool:
        """设置文档分析结果缓存（2小时）"""
        return await self.set(f"analysis:{document_id}", analysis, ttl)
    
    async def get_document_content(self, document_id: str) -> Optional[Dict[str, Any]]:
        """获取文档内容缓存"""
        return await self.get(f"content:{document_id}")
    
    async def set_document_content(self, document_id: str, content: Dict[str, Any], ttl: int = 3600) -> bool:
        """设置文档内容缓存（1小时）"""
        return await self.set(f"content:{document_id}", content, ttl)


# 全局缓存管理器实例
user_cache = UserCacheManager()
system_cache = SystemCacheManager()
document_cache = DocumentCacheManager()


async def warm_up_cache():
    """缓存预热"""
    try:
        logger.info("开始缓存预热...")
        
        # 预热系统统计信息
        # 这里可以添加预热逻辑
        
        logger.info("缓存预热完成")
    except Exception as e:
        logger.error(f"缓存预热失败: {e}")


async def cache_health_check() -> Dict[str, Any]:
    """缓存健康检查"""
    try:
        test_cache = CacheManager("health_check")
        test_key = "test"
        test_value = {"timestamp": datetime.utcnow().isoformat()}
        
        # 测试写入
        write_success = await test_cache.set(test_key, test_value, 60)
        
        # 测试读取
        read_value = await test_cache.get(test_key)
        read_success = read_value == test_value
        
        # 测试删除
        delete_success = await test_cache.delete(test_key)
        
        return {
            "status": "healthy" if all([write_success, read_success, delete_success]) else "unhealthy",
            "write_success": write_success,
            "read_success": read_success,
            "delete_success": delete_success,
            "redis_available": await is_redis_available()
        }
        
    except Exception as e:
        logger.error(f"缓存健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "redis_available": False
        } 