<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport">
    <title>格式检测·统计报告</title>
    <style>
        * {
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
            '微软雅黑', Arial, sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        *::-webkit-scrollbar-track {
            background: #FAFBFF;
        }

        *::-webkit-scrollbar-thumb {
            background: #CBD0D8;
            border-radius: 6px;
        }

        body::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        body::-webkit-scrollbar-track {
            background: #FAFBFF;
        }

        body::-webkit-scrollbar-thumb {
            background: #CBD0D8;
            border-radius: 10px;
        }

        .clear::after {
            content: '';
            display: block;
            clear: both;
        }

        .header {
            width: 100%;
            height: 64px;
            padding: 17px 30px;
            background: #FFF;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 99999;
        }

        .logo {
            position: absolute;
            top: 50%;
            left: 30px;
            transform: translateY(-50%);
            width: 219px;
            height: 26px;
        }

        .logo > img {
            width: 100%;
            height: 100%;
        }


        .logo_btns {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            height: 48px;
            border-radius: 4px;
            background: #F7F8FA;
        }

        .logo_btns_line {
            position: absolute;
            top: 50%;
            right: 100px;
            transform: translateY(-50%);
            width: 1px;
            height: 16px;
            background: #E5E6EB;
        }

        .logo_single_btn {
            height: 40px;
            padding: 4px 12px;
            border-radius: 4px;
            float: left;
            cursor: pointer;
            margin-top: 4px;
            margin-left: 4px;
            line-height: 32px;
            text-align: center;
            width: 95px;
        }

        .logo_single_btn_active {
            background: #FFF;
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
            color: #28A781;
        }

        .operate_btns {
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translate(0, -50%);
        }

        .operate_btns_btn {
            border-radius: 4px;
            padding: 0 12px;
            float: left;
            height: 40px;
            cursor: pointer;
        }

        .operate_btns_btn > svg {
            margin-right: 6px;
            transform: translate(0, 2.5px);
        }

        .operate_verify {
            border: 1px solid #1D2129;
            color: #1D2129;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 39px;
            margin-right: 16px;
        }

        .operate_download {
            background: #1D2129;
            color: #FFF;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 39px;
            /* 100.013% */
        }

        .side_nav {
            position: absolute;
            display: none;
            overflow: auto;
        }

        .side_nav::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 1px;
            height: 99%;
            background: #E2E7EE;
        }

        .single_nav {
            color: #1D2129;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            letter-spacing: 0.7px;
            cursor: pointer;
            margin-bottom: 20px;
            position: relative;
            padding-left: 16px;
            white-space: nowrap;
            max-width: 150px;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .single_nav_child {
            padding-left: 30px;
            color: #4E5969;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }

        .child_color {
            color: #EA5035;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 16.002px;
            /* 133.35% */
            margin-left: 4px;
        }

        .single_nav.single_nav_active {
            color: #27B569;
        }

        .single_nav_active::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 3px;
            height: 85%;
            border-radius: 21px;
            background: #27B569;
        }

        .single_nav_child.single_nav_active::before {
            display: none;
        }

        .single_nav:last-child {
            margin-bottom: 0;
        }

        .content {
            background: #fff;
            margin-top: 64px;
            padding-top: 21px;
        }

        .report_main {
            width: 960px;
            padding: 25px 60px 30px;
            margin: auto;
            border-radius: 0px 18px 0px 0px;
            background: #F0F9FB;
            /* 报告阴影 */
            box-shadow: 0px 4px 14px 0px rgba(114, 114, 114, 0.04);
            position: relative;

        }

        .report_main::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 40px;
            height: 40px;
            transform: translate(-100%, 0%);
            background: url('data:image/png;base64,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') no-repeat;
            background-size: contain;
        }

        .report_header_right_icon {
            position: relative;
            top: -7px;
            right: 12px;
        }

        .report_header {
            position: relative;
            z-index: 1;
            margin-bottom: 24px;
        }


        .report_header_left_code {
            margin-bottom: 20px;
        }

        .report_header_left_code_detail {
            float: left;
            color: #4E5969;
            text-align: justify;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 16.002px;
            /* 114.3% */
        }


        .report_header_left_code_detail > .NO {
            display: inline-block;
            border-radius: 2px;
            background: linear-gradient(113deg, #1D2129 -19.56%, #65738F 96.33%);
            color: #FFF;
            text-align: justify;
            font-size: 10px;
            font-style: normal;
            font-weight: 500;
            line-height: 16.002px;
            text-transform: uppercase;
            transform: translateY(-2px);
            padding: 0.2px 1.5px;
        }

        .report_header_left_code_line {
            float: left;
            width: 1px;
            height: 16px;
            background: rgba(51, 51, 51, .1);
            margin: 0 8px;
        }

        .report_header_left_code_time {
            float: left;
            color: #4E5969;
            text-align: justify;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 16.002px;
            /* 114.3% */
        }

        .report_header_left_info {
            color: #4E5969;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 0.7px;
            position: relative;
            padding-left: 14px;

        }

        .report_header_left_info::before {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 0.75px;
            background: #1D2129;
            top: 7px;
            left: 0;
        }

        .report_header_left_info > div {
            float: left;
        }

        .report_header_left_info > div:last-child {
            max-width: 530px;
            word-break: break-all;
        }

        .report_header_left {
            float: left;
            width: 600px;
        }

        .report_header_left_icon {
            width: 184px;
            height: 37px;
            margin-bottom: 20px;
        }

        .report_header_left_icon > img {
            width: 100%;
            height: 100%;
        }

        .report_header_right {
            position: absolute;
            top: 0;
            right: 0;
        }

        .report_header_right_version {
            padding: 4px 12px;
            color: #1D2129;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-align: center;
            display: inline-block;
            transform: translateX(25%);
            position: relative;
            z-index: 2;
            border-radius: 19px 19px 19px 0px;
            border: 1px solid #8ECBCE;
            background: #FFF;
        }

        .report_item {
            border-radius: 6px;
            border: 1px solid #FFF;
            background: #fff;
            box-shadow: 0px 8px 12.3px 0px rgba(171, 197, 197, 0.29);
            backdrop-filter: blur(22.950000762939453px);
            margin-bottom: 20px;
            position: relative;
            padding: 58px 24px 24px;
            z-index: 2;
        }

        .report_item:last-child {
            margin-bottom: 0;
        }

        .item_name {
            position: absolute;
            top: 0;
            left: 0;
            height: 36px;
            line-height: 27px;
            text-align: center;
            padding: 4px 20px;
            border-radius: 0px 0px 26px 0px;
            background: linear-gradient(94deg, rgba(72, 236, 173, 0.15) 0.66%, rgba(85, 201, 255, 0.15) 105.08%);
            color: #1A9771;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            /* 136.122% */
            letter-spacing: 0.9px;
        }


        .detect_result_nums_left {
            padding: 10px 20px;
            float: left;
            width: 500px;
            border-radius: 6px;
            border: 1px solid #FFDCDC;
            background: linear-gradient(90deg, #FFF6F5 0%, #FFFBF9 100%);
        }

        .detect_result_nums_left_item {
            float: left;
        }

        .detect_result_nums_left_num {
            color: #EA5035;
            font-size: 22px;
            font-style: normal;
            font-weight: 500;
            line-height: 29.593px;
            /* 134.515% */
            letter-spacing: 1.1px;
        }

        .detect_result_nums_left_title {
            color: #4E5969;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 29.593px;
            /* 184.958% */
            letter-spacing: 0.8px;
        }

        .detect_result_nums_left_title > svg {
            transform: translate(0, 2px);
        }

        .detect_result_nums_right {
            float: right;
            border-radius: 6px;
            padding: 10px 20px;
            border-radius: 6px;
            border: 1px solid #E2EEF9;
            background: #F9FCFF;
            height: 81px;
        }

        .detect_result_nums_right > div:first-child {
            color: #86909C;
            font-family: Montserrat;
            font-size: 18px;
            font-style: normal;
            font-weight: 500;
            line-height: 29.593px;
            /* 164.407% */
            letter-spacing: 0.9px;
        }

        .detect_result_nums_right > div:last-child {
            color: #4E5969;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 204.183% */
            letter-spacing: 0.6px;
        }

        .detect_result_line {
            margin: 26px 0;
            height: 2px;
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABjEAAAADCAYAAADSmy35AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgB7corDsJAFIbRzhQ1Ao1AIVhBH9uva9olIDAYNAZDUkCQNBNM3Yhz3P3vV1UAAAAAAAAFCl3XnX/HPM+Xf9G6WdPr9fqtfdM0pxjjLt9TStdhGF56vV6v1+v1pfR1Xd/GcXzme9/3x2VZkl6v1+v1en3pfQjhPk3TI9/btj18fnu9vvT+28QKAAAAAACgQG/DpwrkqhtFEQAAAABJRU5ErkJggg==') no-repeat;
            background-size: contain;
        }

        .detect_result_item_title {
            padding-left: 9px;
            position: relative;
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24.502px;
            /* 153.138% */
            letter-spacing: 0.8px;
            margin-bottom: 8px;
        }

        .detect_result_item_title::before {
            content: '';
            width: 3px;
            height: 15px;
            border-radius: 21px;
            background: #28A781;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translate(0, -50%);
        }

        .detect_result_item_content {
            color: #4E5969;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: left;
        }

        .detect_result_item_content > div > svg {
            transform: translateY(6px);
        }

        .detect_result_item_content_other > span:first-child {
            color: #4E5969;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
        }

        .detect_result_item_content_other > span:last-child {
            color: #86909C;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 204.183% */
            letter-spacing: 0.6px;
        }

        .detect_result_item_content_other_line {
            width: 1px;
            height: 16px;
            background: rgba(51, 51, 51, 0.10);
            margin: 0 8px;
            display: inline-block;
            transform: translateY(3px);
        }

        .paper_info_item {
            float: left;
            color: #4E5969;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            width: 25%;
        }

        .summarize_row {
            border-radius: 4px;
            background: #F8FBFF;
            padding: 9px 16px;
            margin-bottom: 16px;
        }

        .summarize_row_has {
            color: #1D2129;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: left;
        }

        .summarize_row_tip {
            float: right;
            color: #165DFF;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
        }


        .summarize_row_danger {
            color: #A11901;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: right;
        }

        .fake_table_interval_head {
            border-radius: 4px 0px 0px 4px;
            background: #E2EAF2;
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            margin-bottom: 8px;
        }

        .fake_table_interval_head_item {
            float: left;
            padding: 4px 16px;
            height: 40px;
            line-height: 33px;
        }

        .fake_table_interval_tr {
            background: #F0F4F8;
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            margin-bottom: 8px;
            position: relative;
        }

        .fake_table_interval_tr:last-child {
            margin-bottom: 0;
        }

        .construct_analyse_status {
            border-radius: 3px;
            border: 1px solid rgba(40, 167, 129, 0.40);
            padding: 0px 6px;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 204.183% */
            letter-spacing: 0.6px;
            height: 25px;
            display: inline-block;
        }

        .construct_analyse_status > svg {
            transform: translate(0, 2px);
        }

        .construct_analyse_status_success {
            border: 1px solid rgba(40, 167, 129, 0.40);
            color: #28A781;
        }

        .construct_analyse_status_primary {
            border: 1px solid rgba(22, 93, 255, 0.40);
            color: #165DFF;

        }

        .construct_analyse_status_err {
            border: 1px solid rgba(161, 25, 1, 0.4);
            color: #A11901;
        }

        .construct_analyse_status_none {
            border: 1px solid rgba(134, 144, 156, 0.40);
            color: #86909C;
        }

        .show_tip {
            margin-left: 8px;
            cursor: pointer;
            position: relative;
        }

        .show_tip > svg {
            transform: translate(0, 2px);
        }

        .tip_box {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-30%, 105%);
            color: #FFF;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            border-radius: 6px;
            background: #1D2129;
            padding: 12px;
            z-index: 99;
            white-space: nowrap;
            display: none;
        }

        .tip_box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 29%;
            transform: translate(0%, -94%);
            width: 0;
            height: 0;
            border: 5px solid #262626;
            border-top-color: transparent;
            border-left-color: transparent;
            border-right-color: transparent;
        }

        .tip2_box {
            transform: translate(-50%, 105%);
        }

        .tip2_box::before {
            left: 48%;
        }

        .summarize_row_err {
            color: #E44548;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: right;
        }

        .summarize_row_normal {
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: left;
        }

        .page_setting_analyse {
            color: #EA5035;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            line-height: 33px;
        }

        .page_setting_analyse > .open_analyse > svg {
            transform: translate(0, 4px);
        }

        .open_analyse {
            transform-origin: 50%;
            display: inline-block;
            cursor: pointer;
            margin-left: 8px;
            transition: all .2s;
        }

        .open_analyse_active {
            transform: rotate(180deg);
        }

        .report_bot {
            padding: 24px 0px;
            border-radius: 6px;
            border: 1px solid #FFF;
            background: #fff;
            box-shadow: 0px 8px 12.3px 0px rgba(171, 197, 197, 0.29);
            backdrop-filter: blur(22.950000762939453px);
        }

        .report_bot_top {
            padding: 0 30px;
            margin-bottom: 32px;
            position: relative;
        }

        .report_bot_title {
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 15.002px;
            /* 83.344% */
            margin-bottom: 20px;
        }

        .bot_li {
            padding-left: 16px;
            max-width: 645px;
            margin-bottom: 12px;
            position: relative;
            font-size: 12px;
            font-weight: 400;
            color: #1D2129;
        }

        .bot_li:last-child {
            margin-bottom: 0;
        }

        .bot_li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 5.5px;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: #C4CED5;
        }

        .report_code {
            position: absolute;
            right: 30px;
            top: 28px;
        }

        .report_code_box {
            margin-bottom: 6px;
            width: 81px;
            height: 81px;
            border-radius: 6px;
            border: 1px solid #F3F3F3;
            background: #FFF;
            backdrop-filter: blur(2px);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .report_code_box::after {
            content: '';
            width: 90%;
            height: 90%;
            background: url("data:image/png;base64,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") no-repeat;
            background-size: contain;
        }


        .report_code_text {
            text-align: center;
            color: #1D2129;
            font-family: "Hiragino Sans GB";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18.885px;
            /* 157.374% */
            letter-spacing: 0.6px;
        }

        .report_bot_line {
            margin: 0 30px 20px;
            position: relative;
            height: 1px;
            background: #D5DCE1;
        }

        .report_bot_line_text {
            position: absolute;
            padding: 0 12px;
            background: #fff;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #4E5969;
            text-align: justify;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 15.002px;
            /* 115.4% */
        }

        .report_bot_info {
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .report_bot_info > div {
            color: #1D2129;
            text-align: center;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 15.002px;
            float: left;
        }

        .report_bot_info_line {
            width: 1px;
            height: 16px;
            background: rgba(51, 51, 51, 0.10);
        }

        .quetions_title {
            padding-left: 9px;
            color: #1D2129;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24.502px;
            /* 153.138% */
            letter-spacing: 0.8px;
            position: relative;
            margin-bottom: 16px;
        }

        .quetions_title::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            transform: translate(0, -50%);
            border-radius: 21px;
            background: #28A781;
            width: 3px;
            height: 15px;
        }

        .questions_title_err {
            color: #E44548;
            font-family: "Alibaba PuHuiTi 3.0";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 175.014% */
            letter-spacing: 0.7px;
            float: right;
        }

        .fake_table_head {
            background: #E2EAF2;
            height: 36px;
        }

        .fake_table_head_item {
            color: #1D2129;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            letter-spacing: 0.7px;
            padding: 4px 16px;
            float: left;
            width: 20%;
            line-height: 27px;
            border-right: 1px solid #D6E1EC;
        }

        .page_setting_detail_box {
            display: none;
            float: left;
            width: 100%;
            background: #fff;

        }

        .page_setting_detail_box_item {
            min-height: 32px;
            border-bottom: 1px solid #E5EDF5;
            position: relative;
        }

        .page_setting_detail_box_item:last-child {
            border-bottom: 0;
        }

        .page_setting_detail_box_item_title {
            position: absolute;
            left: 0;
            top: 0;
            width: 120px;
            height: 100%;
            border-right: 1px solid #E5EDF5;
            padding-left: 16px;
            color: #1D2129;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            letter-spacing: 0.7px;
            display: flex;
            align-items: center;
        }


        .page_setting_detail_box_item_content {
            padding-left: 120px;
            width: 100%;
        }

        .page_setting_detail_box_item_content_single {
            border-bottom: 1px solid #E5EDF5;
            width: 100%;
            line-height: 32px;
            display: flex;
            align-items: center;
        }

        .page_setting_detail_box_item_content > .page_setting_detail_box_item_content_single:last-child {
            border-bottom: 0;
        }

        .page_setting_detail_box_item_content_single_detail {
            display: inline-block;
            width: 32%;
            color: #1D2129;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            letter-spacing: 0.7px;
            padding-left: 16px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .fake_table_tr {
            position: relative;
            display: table;
            width: 100% ；
        }

        .fake_table_tr:nth-child(n) {
            background: #fff;
        }

        .fake_table_tr:nth-child(2n) {
            background: #F6F8FA;
        }

        .fake_table_tr_item,
        .fake_table_tr_item_more_column {
            min-height: 34px;
            padding: 0 16px;
            color: #1D2129;
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            letter-spacing: 0.7px;
        }

        .fake_table_tr_item_more_column {
            width: 554px !important;
            padding: 0;
        }

        .fake_table_tr_item_border {
            border-right: 1px solid #E5EDF5;
            border-bottom: 1px solid #E5EDF5;
        }

        .fake_table_tr_item1 {
            display: table-cell;
            vertical-align: middle;
            width: 60px !important;
        }

        .fake_table_tr_item2 {
            display: table-cell;
            vertical-align: middle;
            width: 170px !important;
        }

        .fake_table_tr_item2 > div {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            word-break: break-all;
        }

        .wordWrap {
            white-space: unset !important;
        }

        .fake_table_tr_item2 img {
            max-width: 123px !important;
            height: auto !important;
        }

        .fake_table_tr_item_more_column_item {
            width: 33.3%;
            float: left;
            border-right: 1px solid #E5EDF5;
            max-height: 600px;
            overflow: auto;
        }

        .fake_table_tr_item_more_column_item:last-child {
            border-right: 0;
        }

        .single_fake_table_tr_item_more_column {
            border-bottom: 1px solid #E5EDF5;
        }

        .single_fake_table_tr_item_more_column > .more_column_item:last-child {
            border-right: none;
        }

        .more_column_item {
            width: 190px !important;
            border-right: 1px solid #E5EDF5;
            display: table-cell;
            vertical-align: middle;
            position: relative;
        }

        .more_column_item > div {
            padding: 8px 0 8px 16px;
            width: 185px !important;
        }

        .more_column_item_label {
            position: absolute;
            top: 14px;
            left: 16px;
            width: 6px;
            height: 6px;
        }

        .err_label {
            background: #E44548;
        }

        .fatal_err_label {
            background: #A11901;
        }

        .alert_label {
            background: #165DFF;
        }

        .color_green {
            color: #28A781;
        }

        .fake_table_tbody {
            max-height: 600px;
            overflow: auto;
            overflow-x: hidden;
        }

        #word_count_analyse .fake_table_interval_head,
        #word_count_analyse .fake_table_interval_tr {
            margin-bottom: 0;
        }

        #word_count_analyse .fake_table_interval_tr {
            border-bottom: 1px solid #E5EDF5;
            background: #fff;
        }

        .constrction_body_title {
            height: 45px;
        }

        .constrction_body_title_left {
            border-radius: 8px 0px 0px 0px;
            background: linear-gradient(90deg, #8ED4DE 0%, #37C69B 100%);
            line-height: 45px;
            padding: 0 50px;
            color: #FFF;
            font-size: 15px;
            font-style: normal;
            font-weight: 500;
            letter-spacing: 0.75px;
            float: left;
            width: 50%;
            box-shadow: 11px 0px 13.6px 0px rgba(21, 21, 21, 0.04);
            position: relative;
            z-index: 2;
        }

        .constrction_body_title_right {
            border-radius: 0px 8px 0px 0px;
            background: #ABC;
            line-height: 45px;
            padding: 0 50px;
            color: #FFF;
            font-size: 15px;
            font-style: normal;
            font-weight: 500;
            letter-spacing: 0.75px;
            float: left;
            width: 50%;
            position: relative;
        }

        .constrction_body_main {
            display: table;
            width: 100%;
        }

        .constrction_body_left {
            display: table-cell;
            width: 50%;
            border-radius: 0px 0px 0px 8px;
            background: linear-gradient(201deg, #F9FFFC 13.83%, #F1FEFF 100.54%);
            padding: 0 50px;
            box-shadow: 11px 0px 13.6px 0px rgba(21, 21, 21, 0.04);
            position: relative;
            z-index: 2;
        }

        .constrction_body_right {
            display: table-cell;
            width: 50%;
            border-radius: 0px 0px 8px 0px;
            background: #F8FBFE;
            padding: 0 50px;
            position: relative;
        }

        .constrction_body_row {
            padding: 20px 0;
            color: #1D2129;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            letter-spacing: 0.7px;
            position: relative;
        }

        .constrction_body_row > svg {
            transform: translateY(3px);
            margin-right: 8px;
        }

        .constrction_body_row::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAlEAAAADCAYAAACzpVoLAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJBSURBVHgB7daxbtNAGMDxy9mOY9PSWqSlZkIRSxEbExsDEkICJipep+/DRlkq8QpZy1a2WGmtXp1Eri+O7fqatE18/qz7JEtd7hsS6fJLfLL+yrmz//3dy5xnlJQTfvg5JsfHOanMwZc3e4vcMqvrYeiGZDhMVb1lXUfBnyCurnufBjuGYbuqfu/o7VYxKbar653nnenlr7OZqs8yHrO/51F13f/qu2m6u6PqycfXvb7teNXlOenwyenZlaqndpJf/P4/lvwRMfqTw31lX07/86Fftx6e/gva8OvdbHigIdAjG4I8tiHIYxuCPLYhyGMbAj22IcCL0Q01e91QsxfTVkOgxzakz7670WffmldoSBgz4fm2ZRrW3acnJ2H5Kn2JG12HUvqsuu77o6jcVarqk8SRolh623VMY1fVJzeLXtfsSp7fzMVeZqreILZ4i2qua1HbUPZk6wUtCi55WmRi/1eqviCO2L8cxvl7WhwgvPis5n6uJmjDb3SzPkBDkMc2BHtcQ5DHNgR6ZEOQxzYEemxDgF+Nbojohu7nKRsCPbIhffY9XFefffej0lBpzK6djbM4WT6Zf/uRlU/X0m8x4oQeZ9INDNirtG5fkGfMqw0jttzITlis6qez+cwzF1LA00XOMZ71vLTOi+t6HhupeuIMec4Gkre8XobxFPKDYY7y5eQ8HhHEYP1GN+sDNAR5bEOQxzYEeWxDkMc2BHlsQ1ZbDQG+aXRDy9ENNfumae1/C9mQPvser6vPvtWoNFSaW8AcwIYAj0q7AAAAAElFTkSuQmCC') no-repeat;
            background-size: 100% 100%;
        }

        .constrction_body_left > .constrction_body_row:last-child::after,
        .constrction_body_right > .constrction_body_row:last-child::after {
            content: none;
        }

        .lack_icon {
            padding: 0px 6px;
            border-radius: 3px;
            border: 1px solid rgba(161, 25, 1, 0.40);
            margin-left: 12px;
            color: #A11901;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 204.183% */
            letter-spacing: 0.6px;
        }

        .unnecessary_icon {
            padding: 0px 6px;
            border-radius: 3px;
            border: 1px solid rgba(22, 93, 255, 0.40);
            color: #165DFF;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24.502px;
            /* 204.183% */
            letter-spacing: 0.6px;
            margin-left: 12px;
        }

        .constrction_body_row_unnecessary {
            border-radius: 31px;
            position: relative;
        }

        .constrction_body_row_unnecessary > svg:first-child {
            transform: translateY(3px);
            margin-right: 4px;
        }

        .constrction_body_row_tip {
            position: absolute;
            top: 5px;
            right: 0;
            cursor: pointer;
        }
    </style>
    <style>
        .ml50 {
            margin-left: 20px;
        }

        .tY2 {
            transform: translate(0, 2px);
        }

        .mb40 {
            margin-bottom: 40px;
        }

        .ml20 {
            margin-left: 20px;
        }
    </style>
    <style>
        body {
            min-width: 1300px;
        }

        .page_setting_detail_box_item_all_title {
            border-top: 1px solid #EAECEE;
            background: #F6F8FB;
        }

        .page_setting_detail_box_item_all_title > .page_setting_detail_box_item_title {
            font-weight: 600;
            color: #4E5969;
            font-size: 12px;
        }

        .tY3 {
            transform: translateY(3px);
        }

        .text_overflow {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            position: relative;
        }

        .text_overflow_tooltip {
            position: fixed;
            max-width: 300px;
            width: fit-content;
            background: #1D2129;
            border-radius: 4px;
            padding: 8px 16px;
            box-sizing: border-box;
            color: #fff;
            z-index: 99;
            white-space: wrap;
            font-size: 14px;
            word-break: break-all;

        }

        .text_overflow_tooltip::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -5px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #1D2129;
        }
    </style>
</head>

<body>
<div class="header clear">
    <div class="logo">
        <img src="data:image/png;base64,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"
             alt=""/>
    </div>
    <div class="logo_btns">
        <div id="reportTj" href="新媒体技术对舞蹈编导创作手法的影响研究_格式统计报告.html" class="logo_single_btn logo_single_btn_active"
             style="margin-right: 4px;">统计报告
        </div>
        <div id="reportBd" href="新媒体技术对舞蹈编导创作手法的影响研究_格式比对报告.html" class="logo_single_btn">比对报告</div>
        <div id="reportCd" href="新媒体技术对舞蹈编导创作手法的影响研究_格式存档报告.pdf" class="logo_single_btn">存档报告</div>
    </div>
    <div class="operate_btns">
        <div class="operate_verify operate_btns_btn" href="https://vpcs.fanyu.com/verify">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="15" viewBox="0 0 12 15" fill="none">
                <path
                        d="M0.522169 1.38398L6.00012 0.166664L11.4781 1.38398C11.7831 1.45177 12.0001 1.72231 12.0001 2.03478V8.6926C12.0001 10.03 11.3317 11.2789 10.2189 12.0208L6.00012 14.8333L1.78132 12.0208C0.668529 11.2789 0.00012207 10.03 0.00012207 8.6926V2.03478C0.00012207 1.72231 0.217142 1.45177 0.522169 1.38398ZM1.33346 2.56956V8.6926C1.33346 9.5842 1.77906 10.4168 2.52092 10.9114L6.00012 13.2309L9.47932 10.9114C10.2212 10.4168 10.6668 9.5842 10.6668 8.6926V2.56956L6.00012 1.53252L1.33346 2.56956ZM6.66679 6.16666H8.66679L5.33346 10.8333V7.5H3.33346L6.66679 2.83333V6.16666Z"
                        fill="black"/>
            </svg>
            <span>验证真伪</span>
        </div>
        <div class="operate_download operate_btns_btn"
             href="https://vpcs-report.fanyu.com/dldFormatCheckReport?reportNum=194c6ae6b76d9gcd">
            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                <path
                        d="M9.23909 6.66667H12.5724L8.57243 10.6667L4.57243 6.66667H7.90576V2H9.23909V6.66667ZM3.23909 12.6667H13.9058V8H15.2391V13.3333C15.2391 13.7015 14.9406 14 14.5724 14H2.57243C2.20424 14 1.90576 13.7015 1.90576 13.3333V8H3.23909V12.6667Z"
                        fill="white"/>
            </svg>
            <span>下载报告</span>
        </div>
    </div>
</div>
<div class="side_nav">
    <div class="single_nav single_nav_father single_nav_active" data-link="detect_result">检测结果</div>
    <div class="single_nav single_nav_father" data-link="paper_info">文档信息</div>
    <div class="single_nav single_nav_father" data-link="construct_analyse">结构分析</div>
    <div class="single_nav single_nav_father" data-link="word_count_analyse">字数分析</div>
    
    <div class="single_nav single_nav_father" data-link="quetions" style="margin-bottom: 8px">问题片段
    </div>
    <div class="single_nav single_nav_child" style="margin-bottom: 6px" data-link="中文关键词">
        <span data-link="中文关键词">中文关键词</span><span
            data-link="中文关键词" class="child_color">(1)</span>
    </div>
    <div class="single_nav single_nav_child" style="margin-bottom: 6px" data-link="英文关键词">
        <span data-link="英文关键词">英文关键词</span><span
            data-link="英文关键词" class="child_color">(1)</span>
    </div>
    <div class="single_nav single_nav_child" style="margin-bottom: 6px" data-link="正文">
        <span data-link="正文">正文</span><span
            data-link="正文" class="child_color">(15)</span>
    </div>
    <div class="single_nav single_nav_child" style="margin-bottom: 6px" data-link="参考文献">
        <span data-link="参考文献">参考文献</span><span
            data-link="参考文献" class="child_color">(7)</span>
    </div>
    <div class="single_nav single_nav_child" style="margin-bottom: 6px" data-link="致谢">
        <span data-link="致谢">致谢</span><span
            data-link="致谢" class="child_color">(1)</span>
    </div>
</div>
<div class="content">
    <div class="report_main">
        <div class="report_header clear">
            <div class="report_header_left clear">
                <div class="report_header_left_icon">
                    <img src="data:image/png;base64,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"
                         alt=""/>
                </div>
                <div class="report_header_left_code clear">
                    <div class="report_header_left_code_detail">
                        <span class="NO">NO.</span>
                        <span>194c6ae6b76d9gcd</span>
                    </div>
                    <div class="report_header_left_code_line"></div>
                    <div class="report_header_left_code_time">2025-06-07 10:00:37</div>
                </div>
                <div class="report_header_left_info clear" style="margin-bottom: 6px;">
                    <div>题目：</div>
                    <div style="color: #1D2129">新媒体技术对舞蹈编导创作手法的影响研究</div>
                </div>
                <div class="report_header_left_info clear" style="margin-bottom: 6px;">
                    <div>作者：</div>
                    <div style="color: #1D2129">李岩</div>
                </div>
                <div class="report_header_left_info clear" style="margin-bottom: 6px;">
                    <div>检测模板：</div>
                    <div style="color: #1D2129;max-width: 510px">河北科技学院-河北省通用模板</div>
                </div>
                <div class="report_header_left_info clear">
                    <div>所属单位：</div>
                    <div style="color: #1D2129">河北科技学院</div>
                </div>
            </div>
            <div class="report_header_right">
                <div class="report_header_right_version">统计报告 · 大学生版</div>
                <div class="report_header_right_icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="195" height="201" fill="none">
                        <g clip-path="url(#a)">
                            <g filter="url(#b)">
                                <g clip-path="url(#c)">
                                    <rect width="193" height="198" x="1.031" y=".985" fill="#DDF3F7" rx="6"/>
                                    <mask id="d" width="207" height="207" x=".031" y="-12.401" fill="#000"
                                          maskUnits="userSpaceOnUse">
                                        <path fill="#fff" d="M.031-12.401h207v207h-207z"/>
                                        <path fill-rule="evenodd"
                                              d="M13.85-11.271H1.16v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm0 .129h12.69v12.69H39.62V1.548ZM52.438-11.4H1.031V193.841H206.274V-11.401H52.439ZM1.161 142.563v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.129h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM13.851 91.156H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69Zm12.82 0H39.62v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.691v-12.69h12.691v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.129h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.691v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69H39.62v-12.69Zm-12.82 0h12.69v12.69H26.8v-12.69Zm-12.819 0h12.69v12.69H13.98v-12.69Zm-12.82 0h12.69v12.69H1.16v-12.69Zm204.984-51.408v-12.69h-12.69v12.69h12.69Zm-12.819 0v-12.69h-12.69v12.69h12.69Zm-12.82 0v-12.69h-12.69v12.69h12.69Zm-179.344.13v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.13h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm-.129-25.51v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69V1.548h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82V1.548h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.407-38.33h-12.69v12.69h12.69v-12.69Zm.129 38.33h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69V1.548h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 25.64v-12.69h-12.69v12.69h12.69Zm0-12.82V1.548h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69V1.548h12.69v12.69ZM1.161 27.058h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16V1.548h12.69v12.69Zm12.82 0H13.98V1.548h12.69v12.69Zm12.82 0H26.8V1.548h12.69v12.69Zm25.639-25.51h-12.69v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-12.82 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm38.458-12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.819 25.64h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69V1.548h-12.69v12.69Zm12.819 0h12.69V1.548h-12.69v12.69Zm38.459-12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-25.639 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm12.819 0h-12.69V1.548h12.69v12.69ZM39.619 52.826h12.69v12.69H39.62v-12.69Zm115.247 12.82v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 12.82v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69ZM1.161 78.336h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69ZM52.31 104.105H39.62v12.69h12.69v-12.69Zm102.556 25.509v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69Zm.129-12.69v12.69h12.691v-12.69h-12.691Zm0 12.819v12.69h12.691v-12.69h-12.691Zm-38.588 12.69h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69Zm-12.819.129v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69ZM52.31 129.614v-12.69H39.62v12.69h12.69Zm.129 0h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-38.588 12.819H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69ZM1.16 116.795h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm51.149 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm-25.51-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm38.458 12.819h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-.129-12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-38.329-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM39.619 155.383h12.69v12.69H39.62v-12.69Zm115.247 25.639v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.819v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-.129 25.639v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69ZM52.31 181.022v12.69H39.62v-12.69h12.69Zm0-12.82v12.69H39.62v-12.69h12.69Zm12.819 25.51h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69ZM1.161 180.892h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.69.13H26.8v12.69h12.69v-12.69Zm-12.82 0H13.98v12.69h12.69v-12.69Zm-12.82 0H1.16v12.69h12.69v-12.69Zm0-12.949H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.13h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm0-12.949h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.819 0h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Z"
                                              clip-rule="evenodd"/>
                                    </mask>
                                    <path fill="#B3E0E9" fill-rule="evenodd"
                                          d="M13.85-11.271H1.16v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm0 .129h12.69v12.69H39.62V1.548ZM52.438-11.4H1.031V193.841H206.274V-11.401H52.439ZM1.161 142.563v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.129h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM13.851 91.156H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69Zm12.82 0H39.62v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.691v-12.69h12.691v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.129h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.691v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69H39.62v-12.69Zm-12.82 0h12.69v12.69H26.8v-12.69Zm-12.819 0h12.69v12.69H13.98v-12.69Zm-12.82 0h12.69v12.69H1.16v-12.69Zm204.984-51.408v-12.69h-12.69v12.69h12.69Zm-12.819 0v-12.69h-12.69v12.69h12.69Zm-12.82 0v-12.69h-12.69v12.69h12.69Zm-179.344.13v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.13h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm-.129-25.51v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69V1.548h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82V1.548h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.407-38.33h-12.69v12.69h12.69v-12.69Zm.129 38.33h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69V1.548h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 25.64v-12.69h-12.69v12.69h12.69Zm0-12.82V1.548h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69V1.548h12.69v12.69ZM1.161 27.058h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16V1.548h12.69v12.69Zm12.82 0H13.98V1.548h12.69v12.69Zm12.82 0H26.8V1.548h12.69v12.69Zm25.639-25.51h-12.69v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-12.82 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm38.458-12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.819 25.64h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69V1.548h-12.69v12.69Zm12.819 0h12.69V1.548h-12.69v12.69Zm38.459-12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-25.639 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm12.819 0h-12.69V1.548h12.69v12.69ZM39.619 52.826h12.69v12.69H39.62v-12.69Zm115.247 12.82v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 12.82v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69ZM1.161 78.336h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69ZM52.31 104.105H39.62v12.69h12.69v-12.69Zm102.556 25.509v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69Zm.129-12.69v12.69h12.691v-12.69h-12.691Zm0 12.819v12.69h12.691v-12.69h-12.691Zm-38.588 12.69h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69Zm-12.819.129v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69ZM52.31 129.614v-12.69H39.62v12.69h12.69Zm.129 0h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-38.588 12.819H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69ZM1.16 116.795h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm51.149 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm-25.51-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm38.458 12.819h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-.129-12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-38.329-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM39.619 155.383h12.69v12.69H39.62v-12.69Zm115.247 25.639v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.819v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-.129 25.639v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69ZM52.31 181.022v12.69H39.62v-12.69h12.69Zm0-12.82v12.69H39.62v-12.69h12.69Zm12.819 25.51h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69ZM1.161 180.892h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.69.13H26.8v12.69h12.69v-12.69Zm-12.82 0H13.98v12.69h12.69v-12.69Zm-12.82 0H1.16v12.69h12.69v-12.69Zm0-12.949H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.13h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm0-12.949h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.819 0h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Z"
                                          clip-rule="evenodd"/>
                                    <path stroke="#B0D6DE" stroke-width=".2"
                                          d="M13.85-11.271H1.16v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm0 .129h12.69v12.69H39.62V1.548ZM52.438-11.4H1.031V193.841H206.274V-11.401H52.439ZM1.161 142.563v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.129h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM13.851 91.156H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69Zm12.82 0H39.62v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.691v-12.69h12.691v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.129h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.691v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.691v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm-12.82 0h12.69v12.69H39.62v-12.69Zm-12.82 0h12.69v12.69H26.8v-12.69Zm-12.819 0h12.69v12.69H13.98v-12.69Zm-12.82 0h12.69v12.69H1.16v-12.69Zm204.984-51.408v-12.69h-12.69v12.69h12.69Zm-12.819 0v-12.69h-12.69v12.69h12.69Zm-12.82 0v-12.69h-12.69v12.69h12.69Zm-179.344.13v12.69h12.69v-12.69H1.16Zm12.82 0v12.69h12.69v-12.69H13.98Zm12.819 0v12.69h12.69v-12.69H26.8Zm12.82 0v12.69h12.69v-12.69H39.62Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.691v-12.69h-12.691Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.82 0v12.69h12.69v-12.69h-12.69Zm12.819 0v12.69h12.69v-12.69h-12.69Zm-192.293-.13h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.82 0h12.69v-12.69H39.62v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.691v-12.69h-12.691v12.69Zm-.129-25.51v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69V1.548h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82V1.548h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.407-38.33h-12.69v12.69h12.69v-12.69Zm.129 38.33h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69V1.548h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 25.64v-12.69h-12.69v12.69h12.69Zm0-12.82V1.548h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69V1.548h12.69v12.69ZM1.161 27.058h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16V1.548h12.69v12.69Zm12.82 0H13.98V1.548h12.69v12.69Zm12.82 0H26.8V1.548h12.69v12.69Zm25.639-25.51h-12.69v12.69h12.69v-12.69Zm.13 12.69h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-12.82 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm38.458-12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.819 25.64h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69V1.548h-12.69v12.69Zm12.819 0h12.69V1.548h-12.69v12.69Zm38.459-12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-25.639 25.64h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69V1.548h12.69v12.69Zm12.82 0h-12.69V1.548h12.69v12.69Zm12.819 0h-12.69V1.548h12.69v12.69ZM39.619 52.826h12.69v12.69H39.62v-12.69Zm115.247 12.82v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm-.129 12.82v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm-51.278.13v12.69H39.62v-12.69h12.69Zm12.819 12.69h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69ZM1.161 78.336h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm-12.95-12.82H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm-.13-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.82h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-25.509-12.82h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.82h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-12.949-12.82h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69ZM52.31 104.105H39.62v12.69h12.69v-12.69Zm102.556 25.509v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69Zm.129-12.69v12.69h12.691v-12.69h-12.691Zm0 12.819v12.69h12.691v-12.69h-12.691Zm-38.588 12.69h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69Zm-12.819.129v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69ZM52.31 129.614v-12.69H39.62v12.69h12.69Zm.129 0h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-38.588 12.819H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.819 0H26.8v-12.69h12.69v12.69ZM1.16 116.795h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.82 0h12.69v-12.69H26.8v12.69Zm51.149 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm-25.51-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm38.458 12.819h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm-.129-12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-38.329-12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69ZM39.619 155.383h12.69v12.69H39.62v-12.69Zm115.247 25.639v12.69h-12.69v-12.69h12.69Zm0-12.82v12.69h-12.69v-12.69h12.69Zm0-12.819v12.69h-12.69v-12.69h12.69Zm.129 12.69v-12.69h12.691v12.69h-12.691Zm0 12.819v-12.69h12.691v12.69h-12.691Zm0 12.82v-12.69h12.691v12.69h-12.691Zm-51.278 0h12.69v-12.69h-12.69v12.69Zm0-12.82h12.69v-12.69h-12.69v12.69Zm0-12.819h12.69v-12.69h-12.69v12.69Zm-.129 25.639v-12.69h-12.69v12.69h12.69Zm0-12.82v-12.69h-12.69v12.69h12.69Zm0-12.819v-12.69h-12.69v12.69h12.69ZM52.31 181.022v12.69H39.62v-12.69h12.69Zm0-12.82v12.69H39.62v-12.69h12.69Zm12.819 25.51h-12.69v-12.69h12.69v12.69Zm0-12.82h-12.69v-12.69h12.69v12.69Zm0-12.819h-12.69v-12.69h12.69v12.69ZM1.161 180.892h12.69v-12.69H1.16v12.69Zm12.82 0h12.69v-12.69H13.98v12.69Zm12.819 0h12.69v-12.69H26.8v12.69Zm12.69.13H26.8v12.69h12.69v-12.69Zm-12.82 0H13.98v12.69h12.69v-12.69Zm-12.82 0H1.16v12.69h12.69v-12.69Zm0-12.949H1.16v-12.69h12.69v12.69Zm12.82 0H13.98v-12.69h12.69v12.69Zm12.82 0H26.8v-12.69h12.69v12.69Zm25.768 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm38.459 12.819h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Zm-12.69.13h12.69v12.69h-12.69v-12.69Zm-12.819 0h12.69v12.69h-12.69v-12.69Zm0-12.949h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm38.459 12.819h12.69v-12.69h-12.69v12.69Zm12.82 0h12.69v-12.69h-12.69v12.69Zm12.819 0h12.69v-12.69h-12.69v12.69Zm12.69.13h-12.69v12.69h12.69v-12.69Zm-12.819 0h-12.69v12.69h12.69v-12.69Zm-12.82 0h-12.69v12.69h12.69v-12.69Zm0-12.949h-12.69v-12.69h12.69v12.69Zm12.82 0h-12.69v-12.69h12.69v12.69Zm12.819 0h-12.69v-12.69h12.69v12.69Z"
                                          clip-rule="evenodd" mask="url(#d)"/>
                                </g>
                                <rect width="192" height="197" x="1.531" y="1.485" stroke="#fff" rx="5.5"/>
                            </g>
                            <g filter="url(#e)">
                                <path fill="#fff"
                                      d="M35.412 41.201a4 4 0 0 1 4-4h150.512a4 4 0 0 1 4 4v7.444H35.412V41.2Z"/>
                            </g>
                            <g filter="url(#f)">
                                <path fill="#F2FCFD"
                                      d="M35.412 48.645h157.365v146.34a4 4 0 0 1-4 4H39.412a4 4 0 0 1-4-4V48.645Z"/>
                            </g>
                            <rect width="123.485" height="137.919" x="1.136" y="60.865" fill="url(#g)"
                                  fill-opacity=".7" stroke="url(#h)" stroke-width=".4" rx="6.8"/>
                            <g filter="url(#i)">
                                <rect width="92.843" height="166.575" x="100.29" y="32.41" fill="url(#j)" rx="2"/>
                            </g>
                            <path fill="#fff"
                                  d="M110.342 106.707a1 1 0 0 1 1-1h6.698a1 1 0 0 1 1 1v4.214a1 1 0 0 1-1 1h-6.698a1 1 0 0 1-1-1v-4.214Z"/>
                            <rect width="8.284" height="5.799" x="110.522" y="105.915" stroke="#E44548"
                                  stroke-width=".414" rx=".793"/>
                            <path fill="#E44548"
                                  d="m114.842 112.97-1.014-1.259 1.022-.007-.008 1.266ZM115.051 110.238h-.568v-2.142a1.957 1.957 0 0 1-.734.431v-.515c.148-.049.309-.141.483-.276.174-.136.293-.294.358-.475h.461v2.977Z"/>
                            <path fill="#fff"
                                  d="M110.342 128.682a1 1 0 0 1 1-1h6.698a1 1 0 0 1 1 1v4.214a1 1 0 0 1-1 1h-6.698a1 1 0 0 1-1-1v-4.214Z"/>
                            <rect width="8.284" height="5.799" x="110.522" y="127.889" stroke="#E44548"
                                  stroke-width=".414" rx=".793"/>
                            <path fill="#E44548"
                                  d="m114.842 134.945-1.014-1.259 1.022-.007-.008 1.266ZM115.516 131.544v.528h-1.992a1.37 1.37 0 0 1 .194-.566c.108-.179.321-.417.639-.712.257-.239.414-.4.472-.485a.63.63 0 0 0 .117-.348c0-.127-.034-.224-.103-.292a.38.38 0 0 0-.281-.103.38.38 0 0 0-.284.108c-.07.071-.11.19-.121.355l-.566-.056c.034-.313.139-.537.317-.674.178-.136.401-.204.668-.204.292 0 .522.079.689.237a.772.772 0 0 1 .251.588c0 .134-.024.261-.073.383-.047.12-.122.246-.226.378a3.823 3.823 0 0 1-.372.378 6.15 6.15 0 0 0-.342.328.99.99 0 0 0-.115.157h1.128Z"/>
                            <path fill="#fff"
                                  d="M110.342 150.231a1 1 0 0 1 1-1h6.698a1 1 0 0 1 1 1v4.213a1 1 0 0 1-1 1h-6.698a1 1 0 0 1-1-1v-4.213Z"/>
                            <rect width="8.284" height="5.799" x="110.522" y="149.438" stroke="#E44548"
                                  stroke-width=".414" rx=".793"/>
                            <path fill="#E44548"
                                  d="m114.842 156.493-1.014-1.259 1.022-.007-.008 1.266ZM114.711 153.621v-.597h-1.213v-.497l1.286-1.883h.477v1.881h.369v.499h-.369v.597h-.55Zm0-1.096v-1.014l-.681 1.014h.681Z"/>
                            <path fill="#E4484B"
                                  d="M110.314 92.958a1 1 0 0 1 1-1h6.699a1 1 0 0 1 1 1v4.213a1 1 0 0 1-1 1h-6.699a1 1 0 0 1-1-1v-4.213Z"/>
                            <path fill="#E44548" fill-rule="evenodd"
                                  d="M118.599 93.372a1 1 0 0 0-1-1h-5.87a1 1 0 0 0-1 1v3.385a1 1 0 0 0 1 1h5.87a1 1 0 0 0 1-1v-3.385Zm-7.285-1.414a1 1 0 0 0-1 1v4.213a1 1 0 0 0 1 1h6.699a1 1 0 0 0 1-1v-4.213a1 1 0 0 0-1-1h-6.699Z"
                                  clip-rule="evenodd"/>
                            <path fill="#E44548" d="m114.842 99.22-1.014-1.258 1.022-.007-.008 1.266Z"/>
                            <path fill="#fff" fill-rule="evenodd"
                                  d="M114.249 93.2v2.072h.829V93.2h-.829Zm.828 2.486h-.828v.828h.828v-.828Z"
                                  clip-rule="evenodd"/>
                            <circle cx="42.181" cy="42.923" r="1.807" fill="#EF3939"/>
                            <circle cx="48.795" cy="42.923" r="1.807" fill="#FC5"/>
                            <circle cx="55.408" cy="42.923" r="1.807" fill="#52BF2E"/>
                            <mask id="k" width="18" height="16" x="108" y="41" maskUnits="userSpaceOnUse"
                                  style="mask-type:luminance">
                                <path fill="#fff" d="M125.472 41.658h-17.375v14.893h17.375V41.658Z"/>
                            </mask>
                            <g fill="#fff" mask="url(#k)">
                                <path
                                        d="m120.968 47.469-5.124-3.152 2.378-1.418 5.099 3.128-2.353 1.442ZM120.99 52.185l-9.045-5.522 2.549-1.515 9.046 5.498-2.55 1.539Z"/>
                                <path fill-opacity=".52"
                                      d="m117.118 54.482-9.02-5.522 2.549-1.54 9.045 5.523-2.574 1.539Z"/>
                            </g>
                            <path fill="#fff"
                                  d="m177.835 45.51-.387.527a6.423 6.423 0 0 0-.492-.485 10.176 10.176 0 0 0-.556-.486l.401-.527c.155.105.331.239.535.408.204.161.365.31.499.443v-.542h2.279v4.15h-.633V45.51h-1.027v3.51h-.619v-3.51Zm3.637 4.199v-5.114l.598.028v5.318c0 .647-.085.76-.767.816l-.394.028-.204-.69.352-.027c.358-.043.415-.057.415-.36Zm-.978-.5V45.2l.591.03v3.98h-.591Zm-1.815-3.404.598.028v1.815c0 .57-.049 1.034-.148 1.393a2.62 2.62 0 0 1-.464.942c-.211.26-.513.549-.914.851l-.437-.541c1.217-.943 1.337-1.231 1.365-2.645v-1.843Zm-.999 1.266-.436.619a6.109 6.109 0 0 0-.52-.514c-.211-.19-.415-.365-.605-.513l.443-.542c.162.113.344.26.563.45.218.184.408.352.555.5Zm-.759.957.33.105.317.106c-.057.267-.162.69-.324 1.273-.155.577-.274.985-.352 1.23l-.647-.231c.071-.211.19-.633.366-1.26.176-.632.281-1.04.31-1.223Zm2.082 1.491.415-.45c.373.337.689.682.963 1.034l-.45.5a8.549 8.549 0 0 0-.457-.592 5.89 5.89 0 0 0-.471-.492ZM171.446 47.83l-.429.409c-.134-.204-.225-.33-.26-.38v2.975h-.683v-2.827a8.18 8.18 0 0 1-.534 1.083l-.591-.43c.169-.26.351-.597.548-1.02.204-.421.373-.83.507-1.23h-.654v-.739h.724v-1.224l.683.035v1.19h.675v.583a5.227 5.227 0 0 0 1.456-1.878h.605c.408.851 1.104 1.639 2.089 2.35l-.436.618a9.363 9.363 0 0 1-.577-.457v.443h-2.743v-.422c-.162.141-.282.247-.366.31l-.464-.626.246-.183h-.485v.633l.126-.127c.204.204.408.45.612.739l.521-.169c.098.26.204.584.323.964.12.38.218.696.282.95l-.683.267c-.049-.24-.119-.542-.218-.9-.098-.36-.19-.676-.274-.936Zm.682-1.202h2.16c-.486-.471-.844-.95-1.084-1.45-.246.5-.604.979-1.076 1.45Zm2.16.837.682.218a39.47 39.47 0 0 1-.76 2.209h1.041v.731h-4.136v-.731h2.329c.091-.232.183-.464.267-.704l-.626.211a12.536 12.536 0 0 0-.232-.893 29.906 29.906 0 0 0-.275-.907l.641-.19c.07.19.154.457.26.809.105.344.19.654.253.928a36.35 36.35 0 0 0 .556-1.681ZM167.693 48.963l.641.31a5.506 5.506 0 0 1-.183.668c-.218.654-.45.88-.844.88-.394 0-.76-.233-1.091-.852-.344-.724-.647-1.983-.731-3.623h-3.25v-.752h3.229a13.788 13.788 0 0 0-.043-1.112l.802.029c0 .33.007.689.028 1.083h.943a14.74 14.74 0 0 0-.682-.64l.464-.5c.134.113.302.254.492.43.19.175.331.309.422.407l-.232.303h.521v.752h-1.892c.091 1.71.316 2.772.689 3.384.077.148.183.218.317.218.091 0 .147-.105.267-.443.056-.14.098-.338.133-.542Zm-2.068.26.007.416.007.344c-.133.007-.703.078-1.723.211-1.02.134-1.583.211-1.688.24l-.099-.76c.113 0 .577-.057 1.4-.162v-1.583h-1.076v-.745h2.912v.745h-1.048v1.477c.492-.063.929-.126 1.308-.182ZM161.367 47.578l-.232.71c-.781-.218-1.428-.485-1.934-.816a7.36 7.36 0 0 1-1.33.696h2.786v2.638h-.718v-.288h-1.66v.288h-.724v-2.511l-.246.084-.268-.647-.168.155-.254-.338v3.208h-.703v-2.694a8.691 8.691 0 0 1-.647 1.196l-.465-.648c.19-.274.38-.618.577-1.033.204-.416.366-.81.493-1.182h-.697v-.739h.739v-1.224l.703.028v1.196h.605v.535c.232-.254.45-.556.668-.908.219-.358.38-.675.5-.956l.71.246-.204.422h1.942v.682c-.289.514-.633.95-1.034 1.323.429.232.949.422 1.561.577Zm-1.371-1.9h-1.492l-.077.12c.218.31.471.57.746.788.302-.254.577-.556.823-.908Zm-2.61 1.738-.274.253a7.375 7.375 0 0 0 1.47-.661 4.813 4.813 0 0 1-.577-.633 4.927 4.927 0 0 1-.499.562l-.465-.541h-.422v.408l.134-.134c.204.218.415.464.633.746Zm.893 2.42h1.66v-.978h-1.66v.978ZM154.261 50.011l-.317.81c-1.217-.45-2.124-.922-2.828-1.485-.682.563-1.582 1.034-2.82 1.52l-.359-.795c1.147-.394 1.977-.788 2.603-1.26-.626-.668-1.062-1.498-1.372-2.595h-.992v-.753h2.568a14.89 14.89 0 0 0-.268-.914l.802-.113c.05.134.155.478.31 1.027h2.455v.753h-1.013c-.289 1.104-.711 1.934-1.351 2.61.64.47 1.463.85 2.582 1.195Zm-2.075-3.805h-2.195c.225.858.598 1.561 1.111 2.103.514-.542.859-1.21 1.084-2.103ZM146.055 48.858l.774.281a4.35 4.35 0 0 1-.057.464l-.049.31c-.098.612-.366.802-1.104.844-.246.007-.422.014-.535.014-.112 0-.288-.007-.534-.014-.809-.042-1.091-.246-1.091-1.16V47.19l-.366.288-.492-.675a7.356 7.356 0 0 0 1.175-1.09c.373-.43.661-.866.865-1.295h.598c.204.45.485.88.837 1.294.352.416.732.767 1.147 1.063l-.444.682a4.961 4.961 0 0 1-1.814-2.16 6.289 6.289 0 0 1-1.105 1.52l.38.014v1.104c.288-.148.598-.316.915-.506.316-.19.583-.359.801-.507l.387.753c-.225.148-.534.324-.935.535a36.1 36.1 0 0 1-1.168.583v.774c0 .352.035.38.429.422.169.007.289.014.366.014.084 0 .204-.007.352-.014.506-.063.485-.056.598-.5.035-.161.056-.372.07-.632Zm-3.116-3.11-.577.584a8.922 8.922 0 0 0-.507-.71 11.21 11.21 0 0 0-.527-.633l.577-.471c.471.513.816.928 1.034 1.23Zm-.45.873v2.701l.604-.5.268.753c-.253.19-.57.436-.943.746l-.323.267-.479-.506c.12-.106.127-.204.127-.45V47.43h-.718v-.81h1.464ZM138.794 46.944h1.287v.64h-6.169v-.64h1.266a5.317 5.317 0 0 0-.366-.436 7.2 7.2 0 0 0-.379-.394l.344-.344h-.548v-.648h1.385a2.776 2.776 0 0 0-.344-.443l.605-.373c.246.303.422.535.513.683l-.183.134h1.33c.218-.247.464-.605.584-.83l.626.386-.303.444h1.323v.647h-.577l.366.288c-.12.183-.373.478-.76.886Zm-2.863-1.174h-.872c.281.274.513.527.689.773l-.387.401h.57V45.77Zm.753 0v1.174h.64V45.77h-.64Zm1.378 1.174h.577l-.429-.351c.232-.218.542-.563.739-.823h-.887v1.174Zm-2.567 3.595v.31h-.725V47.95h4.453v2.891h-.725v-.302h-3.003Zm0-1.576h3.003v-.38h-3.003v.38Zm3.003.612h-3.003v.387h3.003v-.387ZM126.995 47.162l-.219-.738c.169-.056.24-.12.303-.232.091-.127.218-.373.394-.739.176-.366.337-.71.471-1.041l.675.26a9.753 9.753 0 0 1-.436.936 7.36 7.36 0 0 1-.443.76l.148-.008.345-.02c.204-.402.337-.683.415-.852l.605.303c-.169.344-.387.752-.662 1.238-.267.485-.471.837-.619 1.055.148-.021.317-.056.507-.092.197-.035.344-.07.436-.091l.007.725c-.049.007-.148.02-.289.056a27.48 27.48 0 0 1-1.048.197l-.267.056-.162.035-.19-.795a.563.563 0 0 0 .338-.253c.134-.19.317-.478.535-.872a5.768 5.768 0 0 0-.521.05 2.363 2.363 0 0 0-.323.062Zm4.769 2.568h1.308v.696h-2.947v.394h-.669v-3.524c-.063.106-.154.26-.288.464l-.45-.682c.556-.767.921-1.618 1.146-2.638l.697.113a9.421 9.421 0 0 1-.267.921h.865a9.682 9.682 0 0 0-.211-.809l.661-.105c.042.148.127.45.246.914h1.055v.704h-1.146v.703h1.027v.668h-1.027v.71h1.027v.683h-1.027v.788Zm-1.639-3.552v.703h.978v-.703h-.978Zm0 1.371v.71h.978v-.71h-.978Zm.978 2.18v-.787h-.978v.788h.978Zm-1.963-.513.035.38.021.352c-.147.014-.534.077-1.174.19-.634.12-1.035.197-1.196.24l-.113-.697c.113-.014.464-.078 1.062-.19l1.365-.275ZM180.682 54.705V52.55h.184l.881 1.482.24.435h.015a11.476 11.476 0 0 1-.024-.64V52.55h.169v2.156h-.184l-.879-1.485-.242-.432h-.015c.009.207.024.409.024.625v1.292h-.169ZM178.73 54.744c-.521 0-.894-.438-.894-1.124 0-.684.373-1.11.894-1.11.526 0 .896.426.896 1.11 0 .686-.37 1.124-.896 1.124Zm0-.16c.429 0 .713-.379.713-.964 0-.586-.284-.95-.713-.95-.426 0-.707.364-.707.95 0 .585.281.964.707.964ZM176.605 54.705V52.55h.178v2.156h-.178ZM174.773 54.705V52.7h-.678v-.151h1.539v.15h-.681v2.006h-.18ZM172.74 54.744c-.533 0-.911-.43-.911-1.118 0-.69.381-1.116.929-1.116.254 0 .452.125.568.26l-.101.116a.605.605 0 0 0-.467-.216c-.456 0-.743.367-.743.95 0 .588.281.964.734.964.219 0 .381-.092.535-.26l.101.112a.809.809 0 0 1-.645.308ZM169.705 54.705V52.55h1.192v.15h-1.015v.788h.855v.15h-.855v.915h1.048v.153h-1.225ZM167.875 54.705V52.7h-.678v-.151h1.539v.15h-.681v2.006h-.18ZM165.159 54.705V52.55h1.193v.15h-1.015v.788h.855v.15h-.855v.915h1.047v.153h-1.225ZM162.608 54.705V52.55h.509c.662 0 .988.41.988 1.07 0 .66-.326 1.086-.985 1.086h-.512Zm.177-.15h.314c.568 0 .82-.376.82-.935 0-.56-.252-.923-.82-.923h-.314v1.857ZM159.559 54.705V52.7h-.677v-.151h1.538v.15h-.68v2.006h-.181ZM157.133 53.839h.746l-.13-.391c-.089-.251-.16-.48-.237-.74h-.012c-.074.26-.148.489-.234.74l-.133.39Zm1.033.866-.24-.724h-.84l-.246.724h-.177l.754-2.156h.184l.754 2.156h-.189ZM154.061 54.705V52.55h.237l.443 1.24.169.473h.012c.056-.157.106-.317.16-.474l.444-1.24h.236v2.157h-.168V53.34c0-.178.011-.415.02-.595h-.012l-.168.482-.453 1.249h-.142l-.455-1.249-.169-.482h-.012c.009.18.021.417.021.595v1.366h-.163ZM151.876 52.697v.887h.411c.373 0 .58-.148.58-.458 0-.314-.207-.43-.58-.43h-.411Zm1.216 2.008h-.201l-.574-.973h-.441v.973h-.178V52.55h.631c.429 0 .719.156.719.577 0 .343-.21.541-.539.591l.583.988ZM149.748 54.744c-.521 0-.894-.438-.894-1.124 0-.684.373-1.11.894-1.11.527 0 .896.426.896 1.11 0 .686-.369 1.124-.896 1.124Zm0-.16c.429 0 .713-.379.713-.964 0-.586-.284-.95-.713-.95-.426 0-.707.364-.707.95 0 .585.281.964.707.964ZM146.885 54.705V52.55h1.189v.15h-1.011v.832h.855v.15h-.855v1.024h-.178ZM143.483 52.697v.887h.411c.373 0 .58-.148.58-.458 0-.314-.207-.43-.58-.43h-.411Zm1.216 2.008h-.201l-.574-.973h-.441v.973h-.178V52.55h.631c.429 0 .719.156.719.577 0 .343-.211.541-.539.591l.583.988ZM141.048 54.705V52.55h1.192v.15h-1.014v.788h.855v.15h-.855v.915h1.047v.153h-1.225ZM138.675 54.705V52.55h.559c.488 0 .787.16.787.606 0 .432-.296.64-.775.64h-.394v.91h-.177Zm.177-1.059h.358c.426 0 .63-.145.63-.49 0-.35-.213-.46-.642-.46h-.346v.95ZM136.556 53.839h.745l-.13-.391a13.29 13.29 0 0 1-.236-.74h-.012c-.074.26-.148.489-.234.74l-.133.39Zm1.032.866-.239-.724h-.84l-.246.724h-.178l.755-2.156h.183l.755 2.156h-.19ZM134.164 54.705V52.55h.559c.489 0 .787.16.787.606 0 .432-.295.64-.775.64h-.393v.91h-.178Zm.178-1.059h.358c.426 0 .63-.145.63-.49 0-.35-.213-.46-.642-.46h-.346v.95ZM130.572 54.705V52.55h.559c.488 0 .787.16.787.606 0 .432-.296.64-.775.64h-.393v.91h-.178Zm.178-1.059h.358c.426 0 .63-.145.63-.49 0-.35-.213-.46-.642-.46h-.346v.95ZM129.203 54.705V52.55h.178v2.156h-.178ZM127.42 54.705l-.707-2.156h.189l.388 1.24c.08.26.136.46.222.721h.012c.089-.26.145-.461.228-.722l.384-1.24h.181l-.702 2.157h-.195Z"/>
                            <g opacity=".58">
                                <g filter="url(#l)">
                                    <circle cx="70.778" cy="112.558" r="23.265" stroke="#72BAE7"/>
                                </g>
                                <g filter="url(#m)">
                                    <circle cx="70.779" cy="112.558" r="17.828" fill="#72BAE7"/>
                                </g>
                                <g filter="url(#n)">
                                    <circle cx="71.406" cy="112.558" r="31.179" stroke="#72BAE7"
                                            stroke-width=".427"/>
                                </g>
                            </g>
                            <g clip-path="url(#o)">
                                <rect width="44.176" height="44.176" x="29.126" y="99.992" fill="#fff"
                                      rx="22.088"/>
                                <rect width="44.176" height="44.176" x="29.126" y="100.985" fill="#fff"
                                      rx="22.088"/>
                                <g filter="url(#p)">
                                    <path fill="url(#q)" fill-rule="evenodd"
                                          d="m64.277 123.14 1.07 1.071a.672.672 0 0 1 .026.937l-1.446 1.446a.646.646 0 0 1-.937 0l-1.446-1.446a.65.65 0 0 1-.149-.724.646.646 0 0 1 .15-.213l.963-.964v-3.561c-1.922.812-3.449 1.453-4.682 1.971-2.836 1.193-4.118 1.731-5.089 2.179-1.392.642-2.41.641-3.802.107l-.455-.174c-2.023-.772-7.826-2.986-10.843-4.458-2.196-1.071-2.356-1.766.026-2.677a1119.55 1119.55 0 0 1 7.268-2.8c1.394-.534 2.683-1.028 3.656-1.404 1.607-.669 2.463-1.017 3.936-.268 1.233.529 3.217 1.318 5.282 2.138 2.33.926 4.762 1.893 6.338 2.575 2.195.963 1.317 1.401.548 1.785-.15.075-.296.148-.414.223v4.257Zm-17.738 1.305c.634.217 1.299.445 1.999.691 1.445.536 3.293.696 4.632-.027.77-.324 1.676-.757 2.673-1.234.98-.468 2.049-.979 3.163-1.47v7.977s-2.918 3.187-8.032 3.187c-5.515 0-8.487-3.187-8.487-3.187v-7.414c1.223.508 2.552.963 4.052 1.477Z"
                                          clip-rule="evenodd"/>
                                    <path stroke="#fff" stroke-width=".465"
                                          d="M65.318 124.68a.44.44 0 0 1-.113.307l-1.442 1.442-.004.004a.407.407 0 0 1-.3.129.417.417 0 0 1-.3-.129l-.004-.004-1.446-1.446-.005-.004a.415.415 0 0 1 0-.599l.005-.005.964-.964.068-.068v-4.008l-.323.137c-1.921.811-3.447 1.452-4.68 1.97l-.002.001-.008.003c-2.829 1.189-4.114 1.729-5.088 2.179-.676.312-1.247.461-1.809.473-.562.012-1.134-.112-1.812-.372a84.13 84.13 0 0 0-.456-.174c-2.027-.774-7.819-2.984-10.824-4.45-.545-.265-.948-.501-1.214-.714-.274-.221-.355-.38-.36-.485-.003-.093.053-.234.318-.43.26-.191.674-.396 1.264-.622h.001c1.97-.772 4.785-1.85 7.266-2.8a1494.05 1494.05 0 0 0 3.657-1.404l.005-.002c.812-.338 1.397-.578 1.959-.65.545-.07 1.08.018 1.782.375l.007.003.007.003c1.236.53 3.222 1.32 5.285 2.139l.003.002c2.33.926 4.76 1.891 6.33 2.571.542.238.874.435 1.063.595.***************.186.268-.005.038-.046.11-.194.213-.138.098-.32.19-.517.288l-.006.003c-.146.073-.3.15-.428.231l-.11.068V123.236l.069.068.674.674h-.015l.413.4c.082.08.13.188.134.302Zm-18.854-.015c.633.217 1.296.444 1.995.69 1.472.545 3.39.725 4.811-.036.773-.326 1.68-.759 2.67-1.232l.004-.002c.884-.422 1.837-.878 2.83-1.322v7.525l-.065.064a7.646 7.646 0 0 1-.389.351c-.345.291-.858.681-1.53 1.072a11.497 11.497 0 0 1-5.816 1.562c-2.711 0-4.794-.783-6.198-1.564a10.792 10.792 0 0 1-1.591-1.073 7.053 7.053 0 0 1-.465-.413v-6.973c1.14.459 2.372.881 3.74 1.349l.004.002Z"/>
                                </g>
                            </g>
                            <circle cx="51.214" cy="122.081" r="28.551" stroke="#fff" stroke-width="1.629"/>
                            <circle cx="51.214" cy="122.081" r="28.551" stroke="#fff" stroke-width="1.629"/>
                            <circle cx="51.214" cy="122.081" r="39.709" stroke="#fff" stroke-width=".582"/>
                            <circle cx="51.214" cy="122.081" r="39.709" stroke="#fff" stroke-width=".582"/>
                            <rect width="56.002" height="1.6" x="125.403" y="93.089" fill="#5BBAC4" rx=".8"/>
                            <rect width="56.002" height=".4" x="125.403" y="97.689" fill="#5BBAC4" rx=".2"/>
                            <rect width="56.002" height="2.587" x="125.403" y="106.089" fill="#BBFDFC" rx="1.294"/>
                            <rect width="56.002" height="2.587" x="125.403" y="111.676" fill="#BBFDFC" rx="1.294"/>
                            <rect width="23.003" height="2.587" x="125.403" y="117.264" fill="#BBFDFC" rx="1.294"/>
                            <rect width="56.002" height="2.587" x="125.403" y="127.851" fill="#BBFDFC" rx="1.294"/>
                            <rect width="56.002" height="2.587" x="125.403" y="133.438" fill="#BBFDFC" rx="1.294"/>
                            <rect width="23.003" height="2.587" x="125.403" y="139.025" fill="#BBFDFC" rx="1.294"/>
                            <rect width="56.002" height="2.587" x="125.403" y="149.613" fill="#BBFDFC" rx="1.294"/>
                            <rect width="56.002" height="2.587" x="125.403" y="155.2" fill="#BBFDFC" rx="1.294"/>
                            <rect width="23.003" height="2.587" x="125.403" y="160.787" fill="#BBFDFC" rx="1.294"/>
                            <path fill="#DAFFF7"
                                  d="M108.097 66.134h4.582v4.582h-4.582zM113.824 66.134h4.582v4.582h-4.582zM119.551 66.134h4.582v4.582h-4.582z"/>
                            <path fill="#3C746A"
                                  d="M108.856 73.452c.089.067.205.166.347.3.144.13.264.247.36.35l-.352.387c-.06-.087-.16-.2-.299-.339a6.02 6.02 0 0 0-.356-.347l.3-.35Zm.793.282h2.286v3.053c0 .17-.016.298-.048.382a.302.302 0 0 1-.169.182 1.14 1.14 0 0 1-.36.07l-.382.021-.147-.494.325-.013a.614.614 0 0 0 .187-.035.142.142 0 0 0 .086-.082.523.523 0 0 0 .022-.174v-2.433h-1.8v-.477Zm-1.136 3.652v-3.01l.49.017v2.993h-.49Zm.872-.863v-1.8h1.674v1.8h-1.674Zm1.205-1.375h-.737v.932h.737v-.932Zm2.444-1.496h1.496v1.526h-1.496v-1.526Zm2.242.746.078-.317h-.659v-.399h1.825v.4h-.75a6.287 6.287 0 0 1-.082.316h.707v1.622h-.386v-1.24h-.837v1.244h-.382v-1.626h.486Zm-1.158-.16v-.24h-.672v.24h.672Zm-.672.35v.226h.672v-.225h-.672Zm1.969.387.36.017v.56a1.6 1.6 0 0 1-.096.593 1.025 1.025 0 0 1-.29.412c-.131.113-.31.222-.538.326l-.234-.343c.231-.107.403-.21.516-.308a.734.734 0 0 0 .225-.316c.038-.116.057-.266.057-.451v-.49Zm-.217 1.934c.546 0 1.038-.018 1.474-.052l-.108.45a26.056 26.056 0 0 1-2.247.035 1.686 1.686 0 0 1-.598-.104.882.882 0 0 1-.395-.347c-.052.174-.115.35-.191.53l-.351-.204c.093-.214.162-.414.208-.599a3.88 3.88 0 0 0 .104-.676l.382.043-.039.373c.084.188.172.318.265.39v-.94h-.842v-.4h1.791v.4h-.542v.316h.495v.386h-.495v.373c.067.008.136.013.208.013.295.008.589.013.881.013Zm1.075-.044a7.48 7.48 0 0 1-.199-.169 5.871 5.871 0 0 0-.391-.312l.222-.33c.136.096.251.181.347.256l.242.182-.221.373Zm2.89-1.162h-1.47v-1.622h.694a6.354 6.354 0 0 0-.174-.239c-.063-.086-.101-.138-.112-.156l.373-.26.147.2c.124.164.203.271.234.32l-.156.135h.707c.075-.095.155-.207.239-.334.086-.13.149-.23.186-.3l.412.244a7.713 7.713 0 0 1-.273.39h.694v1.622h-1.431c.173.217.329.422.468.616l-.338.3c-.058-.09-.174-.252-.347-.487l-.152-.212.299-.217Zm1.028-1.17h-2.017v.74h2.017v-.74Zm.334 1.305c.093.135.204.31.334.524.13.211.231.386.304.525l-.373.26c-.067-.127-.165-.3-.295-.52-.13-.22-.243-.403-.339-.551l.369-.238Zm-3.352 1.14c.081-.142.17-.316.269-.525.101-.208.182-.394.242-.56l.434.144a12.409 12.409 0 0 1-.538 1.162l-.407-.22Zm2.628-.577.442.152c-.011.081-.033.195-.065.343a.802.802 0 0 1-.108.264.336.336 0 0 1-.174.13 1.153 1.153 0 0 1-.303.052 6.674 6.674 0 0 1-.837 0 .966.966 0 0 1-.313-.06.312.312 0 0 1-.16-.16.81.81 0 0 1-.052-.33v-.86l.473.018v.737c0 .09.013.15.039.178.028.03.089.048.182.057a2.995 2.995 0 0 0 .559 0c.098-.012.164-.032.195-.061.035-.029.061-.082.078-.16.026-.09.041-.19.044-.3Zm5.769.586-.251.455a4.16 4.16 0 0 1-.69-.759 5.18 5.18 0 0 1-.741.768l-.308-.395c.353-.277.616-.55.789-.82a3.927 3.927 0 0 1-.308-.936l-.143.243-.121-.243-.122.225a4.894 4.894 0 0 0-.611-.403v.33h-.417v-.373c-.179.17-.428.352-.746.546l-.208-.356c.321-.164.592-.347.811-.546h-.655v-.373h.798v-.824l.417.017v.807h.746v.373h-.746v.338l.147-.26c.139.06.295.149.469.264.135-.21.26-.462.373-.754.112-.295.195-.571.247-.828l.433.078a7.124 7.124 0 0 1-.16.59h.971v.468h-.229a6.453 6.453 0 0 1-.152.937c-.067.26-.156.497-.269.71.17.247.396.487.676.72Zm-1.734-3.2a4.033 4.033 0 0 1-.365.528l-.308-.217a2.837 2.837 0 0 0 .356-.525l.317.213Zm-1.622.51a2.716 2.716 0 0 0-.152-.242 3.18 3.18 0 0 0-.187-.265l.282-.203c.052.057.115.135.187.234.072.098.131.183.178.256l-.308.22Zm2.407 1.467c.127-.313.214-.694.26-1.145h-.486l-.035.078c.052.422.139.778.261 1.067Zm-1.202 1.548a12.196 12.196 0 0 0-.507-.312c-.26.168-.603.322-1.028.464l-.186-.39c.338-.102.605-.201.802-.3a15.31 15.31 0 0 0-.547-.273c.015-.023.071-.126.17-.308l.021-.043a.538.538 0 0 0 .022-.04h-.356v-.364h.529c.035-.078.071-.169.109-.273l.425.117-.061.156h.889v.347a2.05 2.05 0 0 1-.407.608l.342.169-.217.442Zm-.746-.902.239.108c.136-.112.241-.248.317-.407h-.577l-.039.082c-.041.09-.065.142-.074.156l.134.061Z"/>
                            <path fill="#DAFFF7"
                                  d="M133.206 66.834h3v3h-3zM137.167 66.834h3v3h-3zM141.128 66.834h3v3h-3z"/>
                            <path fill="#A11901" fill-rule="evenodd"
                                  d="M135.688 76.756a2.482 2.482 0 1 0 .037-4.963 2.482 2.482 0 0 0-.037 4.963Zm1.266-3.65a.217.217 0 0 0-.307-.003l-.939.926-.925-.939a.218.218 0 0 0-.31.305l.926.939-.94.926a.217.217 0 1 0 .305.31l.94-.927.926.94a.217.217 0 1 0 .309-.305l-.926-.94.939-.925a.218.218 0 0 0 .002-.307Z"
                                  clip-rule="evenodd"/>
                            <path fill="url(#r)" d="M139.148 72.325h7.365v4h-7.365z"/>
                            <path fill="#DAFFF7"
                                  d="M151.764 66.834h3v3h-3zM155.725 66.834h3v3h-3zM159.686 66.834h3v3h-3z"/>
                            <path fill="#E44548" fill-rule="evenodd"
                                  d="M154.246 76.756a2.482 2.482 0 1 0 .036-4.964 2.482 2.482 0 0 0-.036 4.964Zm1.266-3.65a.217.217 0 0 0-.307-.002l-.939.925-.925-.939a.217.217 0 0 0-.31.305l.926.939-.94.926a.217.217 0 1 0 .305.31l.94-.927.926.94a.217.217 0 1 0 .309-.305l-.926-.94.939-.925a.218.218 0 0 0 .002-.307Z"
                                  clip-rule="evenodd"/>
                            <path fill="url(#s)" d="M157.706 72.325h7.36v4h-7.36z"/>
                            <path fill="#DAFFF7"
                                  d="M170.312 66.816h3v3h-3zM174.273 66.816h3v3h-3zM178.234 66.816h3v3h-3z"/>
                            <g clip-path="url(#t)">
                                <path fill="#165DFF"
                                      d="M172.848 76.774a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Zm.234-3.828a.234.234 0 1 0-.469 0v1.719a.235.235 0 0 0 .469 0v-1.719Zm-.234 2.422a.235.235 0 1 0 0 .47.235.235 0 0 0 0-.47Z"/>
                            </g>
                            <path fill="url(#u)" d="M176.289 72.326h7.36v4h-7.36z"/>
                        </g>
                        <defs>
                            <filter id="b" width="201" height="206" x="-2.969" y=".985"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" result="hardAlpha"
                                               values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="2"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.65 0"/>
                                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2152_10130"/>
                                <feBlend in="SourceGraphic" in2="effect1_dropShadow_2152_10130" result="shape"/>
                            </filter>
                            <filter id="e" width="166.512" height="19.444" x="28.412" y="33.201"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" result="hardAlpha"
                                               values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                                <feOffset dx="-3"/>
                                <feGaussianBlur stdDeviation="2"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                        values="0 0 0 0 0.628206 0 0 0 0 0.767607 0 0 0 0 0.792952 0 0 0 0.27 0"/>
                                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2152_10130"/>
                                <feBlend in="SourceGraphic" in2="effect1_dropShadow_2152_10130" result="shape"/>
                            </filter>
                            <filter id="f" width="165.365" height="158.34" x="28.412" y="44.645"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" result="hardAlpha"
                                               values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                                <feOffset dx="-3"/>
                                <feGaussianBlur stdDeviation="2"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                        values="0 0 0 0 0.628206 0 0 0 0 0.767607 0 0 0 0 0.792952 0 0 0 0.27 0"/>
                                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2152_10130"/>
                                <feBlend in="SourceGraphic" in2="effect1_dropShadow_2152_10130" result="shape"/>
                            </filter>
                            <filter id="i" width="103.843" height="177.575" x="90.79" y="26.91"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" result="hardAlpha"
                                               values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                                <feOffset dx="-4"/>
                                <feGaussianBlur stdDeviation="2.75"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                        values="0 0 0 0 0.215463 0 0 0 0 0.47583 0 0 0 0 0.4564 0 0 0 0.1 0"/>
                                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2152_10130"/>
                                <feBlend in="SourceGraphic" in2="effect1_dropShadow_2152_10130" result="shape"/>
                            </filter>
                            <filter id="l" width="55.53" height="55.53" x="43.014" y="84.793"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                                <feGaussianBlur result="effect1_foregroundBlur_2152_10130" stdDeviation="2"/>
                            </filter>
                            <filter id="m" width="43.656" height="43.656" x="48.951" y="90.73"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                                <feGaussianBlur result="effect1_foregroundBlur_2152_10130" stdDeviation="2"/>
                            </filter>
                            <filter id="n" width="70.785" height="70.785" x="36.014" y="77.165"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                                <feGaussianBlur result="effect1_foregroundBlur_2152_10130" stdDeviation="2"/>
                            </filter>
                            <filter id="p" width="37.53" height="29.741" x="31.977" y="110.111"
                                    color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" result="hardAlpha"
                                               values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
                                <feOffset dy="2.327"/>
                                <feGaussianBlur stdDeviation="1.978"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                        values="0 0 0 0 0.449267 0 0 0 0 0.770269 0 0 0 0 0.919027 0 0 0 0.34 0"/>
                                <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2152_10130"/>
                                <feBlend in="SourceGraphic" in2="effect1_dropShadow_2152_10130" result="shape"/>
                            </filter>
                            <linearGradient id="g" x1="62.878" x2="62.878" y1="60.666" y2="198.985"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#D5EDFF"/>
                                <stop offset="1" stop-color="#BAE1FF"/>
                            </linearGradient>
                            <linearGradient id="h" x1=".936" x2="145.217" y1="95.005" y2="97.493"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#93D9FF"/>
                                <stop offset="1" stop-color="#fff"/>
                            </linearGradient>
                            <linearGradient id="j" x1="146.712" x2="146.712" y1="32.41" y2="198.985"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#A0ECD4"/>
                                <stop offset="1" stop-color="#B4F0FC"/>
                            </linearGradient>
                            <linearGradient id="q" x1="50.742" x2="50.742" y1="111.74" y2="134.277"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#64EDD8"/>
                                <stop offset="1" stop-color="#45ADFC"/>
                            </linearGradient>
                            <linearGradient id="r" x1="139.148" x2="146.513" y1="74.325" y2="74.325"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#DAFFF7"/>
                                <stop offset="1" stop-color="#fff" stop-opacity="0"/>
                            </linearGradient>
                            <linearGradient id="s" x1="157.706" x2="165.066" y1="74.325" y2="74.325"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#DAFFF7"/>
                                <stop offset="1" stop-color="#fff" stop-opacity="0"/>
                            </linearGradient>
                            <linearGradient id="u" x1="176.289" x2="183.649" y1="74.326" y2="74.326"
                                            gradientUnits="userSpaceOnUse">
                                <stop stop-color="#DAFFF7"/>
                                <stop offset="1" stop-color="#fff" stop-opacity="0"/>
                            </linearGradient>
                            <clipPath id="a">
                                <path fill="#fff" d="M.31.86h194v200H.31z"/>
                            </clipPath>
                            <clipPath id="c">
                                <rect width="193" height="198" x="1.031" y=".985" fill="#fff" rx="6"/>
                            </clipPath>
                            <clipPath id="o">
                                <rect width="44.176" height="44.176" x="29.126" y="99.992" fill="#fff"
                                      rx="22.088"/>
                            </clipPath>
                            <clipPath id="t">
                                <path fill="#fff" d="M170.348 71.774h5v5h-5z"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>

        <div id="detect_result" class="report_item location_item">
            <div class="item_name">检测结果</div>

            <div class="detect_result_nums clear">
                <div class="detect_result_nums_left">
                    <div class="detect_result_nums_left_item">
                        <div class="detect_result_nums_left_num">27</div>
                        <div class="detect_result_nums_left_title">
                            问题总数
                            <span style="cursor: pointer;position: relative;top: 2px" id="tip1">
                                    <div class="tip_box tip1_box">
                                        <div style='position: relative'>问题总数说明：</div>
                                        <div style='position: relative'>问题总数=结构分析问题种数+字数分析问题种数+页面设置问题种数+问题片段问题种数</div>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15"
                                         fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                              d="M7.02051 14.5544C3.16052 14.5544 0.0205078 11.4144 0.0205078 7.55444C0.0205078 3.69445 3.16052 0.554443 7.02051 0.554443C10.8805 0.554443 14.0205 3.69445 14.0205 7.55444C14.0205 11.4144 10.8805 14.5544 7.02051 14.5544ZM7.02051 1.55893C3.7145 1.55893 1.025 4.24844 1.025 7.55444C1.025 10.8599 3.7145 13.55 7.02051 13.55C10.326 13.55 13.016 10.86 13.016 7.55444C13.016 4.24844 10.326 1.55893 7.02051 1.55893ZM6.27051 11.8099C6.27051 12.2242 6.6063 12.5599 7.02051 12.5599C7.43473 12.5599 7.77052 12.2242 7.77052 11.8099C7.77052 11.3957 7.43473 11.0599 7.02051 11.0599C6.6063 11.0599 6.27051 11.3957 6.27051 11.8099ZM6.52051 9.58099C6.52051 9.85698 6.74451 10.081 7.02052 10.081C7.29653 10.081 7.52053 9.85649 7.52053 9.58048V8.73498C7.52053 8.33148 7.96953 7.88249 8.40453 7.44849L8.40565 7.44737C8.9538 6.89921 9.52053 6.33248 9.52053 5.58449C9.52053 4.19949 8.39905 3.073 7.02052 3.073C5.642 3.073 4.52051 4.18449 4.52051 5.55049C4.52051 5.82647 4.74451 6.05047 5.02052 6.05047C5.29653 6.05047 5.52053 5.8265 5.52053 5.55049C5.52053 4.722 6.17953 4.073 7.02052 4.073C7.84751 4.073 8.52051 4.751 8.52051 5.58449C8.52051 5.91696 8.10458 6.33341 7.70126 6.73724L7.6975 6.741C7.1455 7.29248 6.52051 7.91748 6.52051 8.73549V9.58099Z"
                                              fill="#28A781"/>
                                    </svg>
                                </span>
                        </div>
                    </div>

                    <div class="detect_result_nums_left_item" style="margin: 0 80px;">
                        <div class="detect_result_nums_left_num" style="color: #1D2129;font-size: 18px">2</div>
                        <div class="detect_result_nums_left_title" style="color: #A11901;font-size: 14px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15"
                                 fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7.34416 14.2374C11.1825 14.265 14.3166 11.1758 14.3442 7.33741C14.3718 3.49903 11.2825 0.365018 7.44415 0.337408C3.60576 0.309797 0.471758 3.39904 0.444147 7.23742C0.416537 11.0758 3.50578 14.2098 7.34416 14.2374ZM10.8886 4.01539C10.6528 3.7762 10.2678 3.77344 10.0286 4.00922L7.39976 6.60059L4.80871 3.97208C4.57293 3.73289 4.18788 3.73013 3.94869 3.96591C3.7095 4.2017 3.70673 4.58674 3.94252 4.82593L6.53357 7.45444L3.90359 10.0469C3.6644 10.2827 3.66163 10.6678 3.89742 10.907C4.1332 11.1462 4.51825 11.1489 4.75744 10.9131L7.38742 8.32063L9.98024 10.9509C10.216 11.1901 10.6011 11.1929 10.8403 10.9571C11.0795 10.7213 11.0822 10.3363 10.8464 10.0971L8.25361 7.46678L10.8824 4.87541C11.1216 4.63963 11.1244 4.25458 10.8886 4.01539Z"
                                      fill="#A11901"/>
                            </svg>
                            严重错误
                        </div>
                    </div>
                    <div class="detect_result_nums_left_item">
                        <div class="detect_result_nums_left_num" style="color: #1D2129;font-size: 18px">25</div>
                        <div class="detect_result_nums_left_title" style="color: #E44548;font-size: 14px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14"
                                 fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7.17993 14C11.0459 14 14.1799 10.866 14.1799 7C14.1799 3.13401 11.0459 0 7.17993 0C3.31394 0 0.179932 3.13401 0.179932 7C0.179932 10.866 3.31394 14 7.17993 14ZM10.6763 3.67917C10.4371 3.43997 10.0493 3.43998 9.81009 3.67918L7.18126 6.3081L4.55266 3.67959C4.31346 3.4404 3.92565 3.44041 3.68645 3.67961C3.44726 3.91881 3.44727 4.30662 3.68647 4.54582L6.31507 7.17432L3.68509 9.8044C3.44589 10.0436 3.4459 10.4314 3.6851 10.6706C3.9243 10.9098 4.31212 10.9098 4.55131 10.6706L7.18129 8.04051L9.81168 10.6708C10.0509 10.91 10.4387 10.91 10.6779 10.6708C10.9171 10.4316 10.9171 10.0438 10.6779 9.80459L8.04748 7.17429L10.6763 4.54537C10.9155 4.30617 10.9155 3.91836 10.6763 3.67917Z"
                                      fill="#E44548"/>
                            </svg>
                            错误
                        </div>
                    </div>
                </div>
                <div class="detect_result_nums_right">
                    <div>3</div>
                    <div>
                        <svg style="transform: translate(0,2px);" xmlns="http://www.w3.org/2000/svg" width="14"
                             height="14" viewBox="0 0 14 14" fill="none">
                            <g clip-path="url(#clip0_1761_8170)">
                                <path
                                        d="M6.99756 14C3.13159 14 -0.00244141 10.866 -0.00244141 7C-0.00244141 3.13403 3.13159 0 6.99756 0C10.8635 0 13.9976 3.13403 13.9976 7C13.9976 10.866 10.8635 14 6.99756 14ZM7.65381 3.28125C7.65381 2.91878 7.36003 2.625 6.99756 2.625C6.63509 2.625 6.34131 2.91878 6.34131 3.28125V8.09375C6.34131 8.45622 6.63509 8.75 6.99756 8.75C7.36003 8.75 7.65381 8.45622 7.65381 8.09375V3.28125ZM6.99756 10.0625C6.63509 10.0625 6.34131 10.3563 6.34131 10.7188C6.34131 11.0812 6.63509 11.375 6.99756 11.375C7.36003 11.375 7.65381 11.0812 7.65381 10.7188C7.65381 10.3563 7.36003 10.0625 6.99756 10.0625Z"
                                        fill="#165DFF"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_1761_8170">
                                    <rect width="14" height="14" fill="white" transform="translate(-0.00244141)"/>
                                </clipPath>
                            </defs>
                        </svg>
                        <span style="color: #165DFF;font-size: 14px;margin: 0 4px;">提醒</span>
                        <span>（提醒不计入总问题个数内）</span>
                    </div>
                </div>
            </div>
            <div class="detect_result_line"></div>
            <div class="detect_result_item clear">
                <div class="detect_result_item_title">问题详情</div>
                <div class="detect_result_item_content ">
                    <div style="margin-right: 4px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path
                                    d="M4.03192 7.60431H15.9919C16.2678 7.60431 16.4915 7.37752 16.4915 7.09776V5.07153C16.4915 4.79177 16.2678 4.56497 15.9919 4.56497H4.03192C3.75598 4.56497 3.53229 4.79177 3.53229 5.07153V7.09776C3.53229 7.37752 3.75598 7.60431 4.03192 7.60431ZM4.53155 5.57809H15.4922V6.5912H4.53155V5.57809ZM3.99963 10.3834H9.49676V10.9152C9.49676 11.195 9.72046 11.4218 9.99639 11.4218C10.2723 11.4218 10.496 11.195 10.496 10.9152V10.3834H15.9045C16.1805 10.3834 16.4042 10.1566 16.4042 9.87682C16.4042 9.59706 16.1805 9.37027 15.9045 9.37027H10.496V8.89157C10.496 8.61181 10.2723 8.38501 9.99639 8.38501C9.72046 8.38501 9.49676 8.61181 9.49676 8.89157V9.37027H3.99963C3.72369 9.37027 3.5 9.59706 3.5 9.87682C3.5 10.1566 3.72369 10.3834 3.99963 10.3834ZM6.49038 12.1619H4.02344C3.74751 12.1619 3.52381 12.3887 3.52381 12.6685V15.2013C3.52381 15.481 3.74751 15.7078 4.02344 15.7078H6.49038C6.76632 15.7078 6.99001 15.481 6.99001 15.2013V12.6685C6.99001 12.3887 6.76632 12.1619 6.49038 12.1619ZM5.99075 14.6947H4.52308V13.175H5.99075V14.6947ZM11.2454 12.1619H8.77843C8.50249 12.1619 8.2788 12.3887 8.2788 12.6685V15.2013C8.2788 15.481 8.50249 15.7078 8.77843 15.7078H11.2454C11.5213 15.7078 11.745 15.481 11.745 15.2013V12.6685C11.745 12.3887 11.5213 12.1619 11.2454 12.1619ZM10.7457 14.6947H9.27806V13.175H10.7457V14.6947ZM16.0004 12.1619H13.5334C13.2575 12.1619 13.0338 12.3887 13.0338 12.6685V15.2013C13.0338 15.481 13.2575 15.7078 13.5334 15.7078H16.0004C16.2763 15.7078 16.5 15.481 16.5 15.2013V12.6685C16.5 12.3887 16.2763 12.1619 16.0004 12.1619ZM15.5007 14.6947H14.0331V13.175H15.5007V14.6947Z"
                                    fill="#8493A9"/>
                        </svg>
                        结构问题：<span style="color: #1D2129;">1</span>种
                    </div>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path
                                    d="M8.00014 4.70259V16.5702H7.00019V4.70259H5.50028V16.5702H15.4997V4.70259H8.00014ZM5.0003 3.71363H15.9997C16.1457 3.71363 16.2657 3.76011 16.3597 3.85209C16.4527 3.94505 16.4997 4.06372 16.4997 4.20811V17.0646C16.5021 17.1303 16.491 17.1957 16.4669 17.257C16.4428 17.3182 16.4063 17.3739 16.3597 17.4207C16.3124 17.4668 16.2561 17.5029 16.1942 17.5267C16.1323 17.5505 16.0661 17.5616 15.9997 17.5591H5.0003C4.93393 17.5616 4.86775 17.5505 4.80584 17.5267C4.74393 17.5029 4.68761 17.4668 4.64032 17.4207C4.59367 17.3739 4.55719 17.3182 4.53311 17.257C4.50904 17.1957 4.49788 17.1303 4.50033 17.0646V4.20811C4.50033 4.06372 4.54733 3.94505 4.64032 3.85209C4.68761 3.80595 4.74393 3.76986 4.80584 3.74605C4.86775 3.72224 4.93393 3.71121 5.0003 3.71363ZM10 6.68052H12.9999C13.3328 6.68052 13.4998 6.84568 13.4998 7.175C13.4998 7.50433 13.3328 7.66949 12.9999 7.66949H10C9.66705 7.66949 9.50006 7.50433 9.50006 7.175C9.50006 6.84568 9.66705 6.68052 10 6.68052ZM10 9.64742H12.9999C13.3328 9.64742 13.4998 9.81257 13.4998 10.1419C13.4998 10.4712 13.3328 10.6364 12.9999 10.6364H10C9.66705 10.6364 9.50006 10.4712 9.50006 10.1419C9.50006 9.81257 9.66705 9.64742 10 9.64742ZM10 12.6143H12.9999C13.3328 12.6143 13.4998 12.7795 13.4998 13.1088C13.4998 13.4381 13.3328 13.6033 12.9999 13.6033H10C9.66705 13.6033 9.50006 13.4381 9.50006 13.1088C9.50006 12.7795 9.66705 12.6143 10 12.6143Z"
                                    fill="#8493A9"/>
                        </svg>
                        内容问题：<span style="color: #1D2129;">19</span>种
                    </div>

                </div>
                <div class="detect_result_item_content" style="margin-left: 50px;">
                    <div style="margin-right: 4px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M15.9885 4.14791C15.821 3.98047 15.5939 3.8864 15.3571 3.8864H4.64286C4.40606 3.8864 4.17895 3.98047 4.01151 4.14791C3.84407 4.31535 3.75 4.54246 3.75 4.77926V15.4935C3.75 15.7303 3.84407 15.9574 4.01151 16.1249C4.17895 16.2923 4.40606 16.3864 4.64286 16.3864H15.3571C15.5939 16.3864 15.821 16.2923 15.9885 16.1249C16.1559 15.9574 16.25 15.7303 16.25 15.4935V4.77926C16.25 4.54246 16.1559 4.31535 15.9885 4.14791ZM10.5 8.35068H15.3571V15.4935H10.5V8.35068ZM9.5 8.35068H4.64286V15.4935H9.5V8.35068ZM4.64286 4.77926H15.3571V7.45783H4.64286V4.77926Z"
                                  fill="#8493A9"/>
                        </svg>
                        页面设置问题：<span style="color: #1D2129;">0</span>种
                    </div>

                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path
                                    d="M7.14286 14.9364H7.72001C8.03201 14.9364 8.28572 15.2028 8.28572 15.5364C8.28573 15.6945 8.22631 15.8462 8.12038 15.9586C8.01445 16.0709 7.87057 16.1348 7.72001 16.1364H5.42287C5.34816 16.1362 5.27423 16.1204 5.20532 16.0901C5.13641 16.0598 5.07388 16.0155 5.02132 15.9598C4.96876 15.904 4.9272 15.8379 4.89903 15.7653C4.87086 15.6926 4.85663 15.6148 4.85715 15.5364C4.85714 15.3783 4.91656 15.2266 5.02249 15.1142C5.12843 15.0019 5.27231 14.938 5.42287 14.9364H6.00001V5.33641H3.14287V5.93041C3.14287 6.08954 3.08266 6.24215 2.9755 6.35467C2.86834 6.4672 2.72299 6.53041 2.57144 6.53041C2.41989 6.53041 2.27454 6.4672 2.16738 6.35467C2.06022 6.24215 2.00001 6.08954 2.00001 5.93041V4.74241C1.9995 4.66263 2.01414 4.58355 2.04309 4.50978C2.07203 4.43602 2.1147 4.36906 2.16858 4.31281C2.22115 4.2567 2.28386 4.21219 2.35305 4.18189C2.42223 4.1516 2.49648 4.13614 2.57144 4.13641H10.5703C10.6455 4.13586 10.7201 4.15099 10.7898 4.18093C10.8594 4.21087 10.9227 4.25502 10.9759 4.31083C11.0292 4.36664 11.0714 4.433 11.1 4.50605C11.1287 4.57911 11.1432 4.65741 11.1429 4.73641V5.93041C11.1429 6.08954 11.0827 6.24215 10.9755 6.35467C10.8683 6.4672 10.723 6.53041 10.5714 6.53041C10.4199 6.53041 10.2745 6.4672 10.1674 6.35467C10.0602 6.24215 10 6.08954 10 5.93041V5.33641H7.14286V14.9364ZM15.1429 14.9364H15.72C16.032 14.9364 16.2857 15.2028 16.2857 15.5364C16.2857 15.6945 16.2263 15.8462 16.1204 15.9586C16.0144 16.0709 15.8706 16.1348 15.72 16.1364H13.4229C13.3481 16.1362 13.2742 16.1204 13.2053 16.0901C13.1364 16.0598 13.0739 16.0155 13.0213 15.9598C12.9688 15.904 12.9272 15.8379 12.899 15.7653C12.8708 15.6926 12.8566 15.6148 12.8571 15.5364C12.8571 15.3783 12.9166 15.2266 13.0225 15.1142C13.1284 15.0019 13.2723 14.938 13.4229 14.9364H14V10.1364H12.2857V10.7304C12.2857 10.8895 12.2255 11.0421 12.1183 11.1547C12.0112 11.2672 11.8658 11.3304 11.7143 11.3304C11.5627 11.3304 11.4174 11.2672 11.3102 11.1547C11.2031 11.0421 11.1429 10.8895 11.1429 10.7304V9.54241C11.1423 9.46263 11.157 9.38354 11.1859 9.30978C11.2149 9.23601 11.2575 9.16905 11.3114 9.11281C11.364 9.05669 11.4267 9.01218 11.4959 8.98189C11.5651 8.95159 11.6393 8.93613 11.7143 8.93641H17.4274C17.5027 8.93585 17.5773 8.95098 17.6469 8.98092C17.7165 9.01086 17.7798 9.05502 17.8331 9.11083C17.8863 9.16664 17.9285 9.23299 17.9572 9.30605C17.9858 9.3791 18.0004 9.4574 18 9.53641V10.7304C18 10.8895 17.9398 11.0421 17.8326 11.1547C17.7255 11.2672 17.5801 11.3304 17.4286 11.3304C17.277 11.3304 17.1317 11.2672 17.0245 11.1547C16.9173 11.0421 16.8571 10.8895 16.8571 10.7304V10.1364H15.1429V14.9364Z"
                                    fill="#8493A9"/>
                        </svg>
                        字体问题：<span style="color: #1D2129;">4</span>种
                    </div>
                </div>
                <div class="detect_result_item_content" style="margin-left: 50px;">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M3.4 5.1364H16.6C16.8209 5.1364 17 5.31548 17 5.5364V14.7364C17 14.9573 16.8209 15.1364 16.6 15.1364H3.4C3.17909 15.1364 3 14.9573 3 14.7364V5.5364C3 5.31548 3.17909 5.1364 3.4 5.1364ZM2 5.5364C2 4.7632 2.6268 4.1364 3.4 4.1364H16.6C17.3732 4.1364 18 4.7632 18 5.5364V14.7364C18 15.5096 17.3732 16.1364 16.6 16.1364H3.4C2.6268 16.1364 2 15.5096 2 14.7364V5.5364ZM7.8793 12.8493C7.8793 13.0079 8.00784 13.1364 8.1664 13.1364H10.4325C10.7459 13.1364 10.9999 12.8824 10.9999 12.569C10.9999 12.2557 10.7459 12.0016 10.4325 12.0016H9.96149C9.68481 12.0016 9.50874 11.711 9.67373 11.4889C9.7945 11.3294 9.91868 11.1733 10.0463 11.0207C10.2263 10.8042 10.3915 10.5877 10.5419 10.3713C10.6923 10.1525 10.8131 9.91098 10.9042 9.64665C10.9953 9.38005 11.0409 9.07016 11.0409 8.71696C11.0409 8.19743 10.9019 7.79753 10.6239 7.51726C10.3482 7.23698 9.96084 7.09685 9.46181 7.09685C9.04254 7.09685 8.7167 7.1766 8.48428 7.33611C8.25413 7.49333 8.09349 7.72347 8.00234 8.02654C7.9112 8.32732 7.86562 8.69418 7.86562 9.12712C7.86562 9.18864 7.86676 9.25245 7.86904 9.31853C7.87589 9.41784 7.95778 9.49626 8.05733 9.49626H8.49788C8.85469 9.49626 9.14394 9.20701 9.14394 8.8502V8.7614C9.14394 8.40365 9.2442 8.22478 9.44473 8.22478C9.57689 8.22478 9.66348 8.27605 9.70449 8.37859C9.74779 8.47885 9.76943 8.59961 9.76943 8.74089C9.76943 9.03484 9.72386 9.3003 9.63271 9.53728C9.54385 9.77198 9.42764 9.99073 9.28408 10.1935C9.14281 10.394 8.99355 10.59 8.83633 10.7814C8.67454 10.9774 8.52073 11.1779 8.3749 11.383C8.22907 11.5858 8.10944 11.8068 8.01601 12.0461C7.92487 12.283 7.8793 12.5508 7.8793 12.8493ZM11.972 12.7843C12.2135 13.0646 12.5997 13.2048 13.1307 13.2048C13.714 13.2048 14.131 13.0418 14.3816 12.716C14.6346 12.3901 14.761 11.9264 14.761 11.3249C14.761 11.056 14.7485 10.827 14.7234 10.6379C14.6984 10.4487 14.6448 10.2904 14.5628 10.1628C14.4627 10.007 14.4739 9.70788 14.5727 9.55132C14.5937 9.51808 14.612 9.48264 14.6277 9.44499C14.7075 9.25131 14.7474 9.00635 14.7474 8.71013C14.7474 7.64372 14.2347 7.11052 13.2093 7.11052C12.6784 7.11052 12.2841 7.23926 12.0267 7.49675C11.7692 7.75424 11.6404 8.1587 11.6404 8.71013V8.82292C11.6437 8.87899 11.6905 8.92204 11.7466 8.92204H12.7103C12.8103 8.92204 12.8914 8.84094 12.8914 8.74089C12.8914 8.58822 12.913 8.4629 12.9563 8.36491C13.0019 8.26465 13.0885 8.21452 13.2161 8.21452C13.4121 8.21452 13.5101 8.38428 13.5101 8.7238C13.5101 8.94027 13.4554 9.09522 13.346 9.18864L13.339 9.19437C13.3243 9.20611 13.3095 9.2179 13.2945 9.22976C13.0917 9.39073 12.8709 9.56592 12.8709 9.82071V10.3574C12.8709 10.3826 12.8936 10.402 12.9187 10.402C12.9393 10.3997 12.962 10.3986 12.9871 10.3986C13.1899 10.3986 13.3255 10.4761 13.3938 10.631C13.4645 10.786 13.4998 10.9671 13.4998 11.1745V11.3249C13.4998 11.596 13.4804 11.792 13.4417 11.9128C13.403 12.0313 13.313 12.0905 13.1717 12.0905C13.0327 12.0905 12.9427 12.0358 12.9017 11.9264C12.8629 11.8148 12.8436 11.6792 12.8436 11.5197V11.5043C12.8436 11.1636 12.5673 10.8874 12.2266 10.8874C11.8859 10.8874 11.6097 11.1636 11.6097 11.5043V11.5163C11.6097 12.0814 11.7304 12.5041 11.972 12.7843ZM5.83057 8.9199C5.83057 8.77793 5.62268 8.68497 5.49219 8.74089C5.27828 8.82917 5.08545 8.64216 5.08545 8.41075V8.27699C5.08545 8.04261 5.25948 7.845 5.4751 7.7531C5.6141 7.69157 5.74284 7.6084 5.86133 7.50359C5.88492 7.48266 5.90743 7.46092 5.92885 7.43834C6.08962 7.26889 6.28989 7.11736 6.52348 7.11736C6.8449 7.11736 7.10547 7.37792 7.10547 7.69935V12.4989C7.10547 12.851 6.82007 13.1364 6.46802 13.1364C6.11596 13.1364 5.83057 12.851 5.83057 12.4989V8.9199Z"
                                  fill="#8493A9"/>
                        </svg>
                        字数问题：<span style="color: #1D2129;">1</span>种
                    </div>
                </div>
                <div class="detect_result_item_content" style="margin-left: 50px;">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21"
                             fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M13.3334 5.41423H4.44447C4.0741 5.41423 3.88892 5.59942 3.88892 5.96979C3.88892 6.34016 4.0741 6.52534 4.44447 6.52534H13.3334C13.7037 6.52534 13.8889 6.34016 13.8889 5.96979C13.8889 5.59942 13.7037 5.41423 13.3334 5.41423ZM15.5556 8.19206H4.44447C4.0741 8.19206 3.88892 8.37725 3.88892 8.74762C3.88892 9.11799 4.0741 9.30317 4.44447 9.30317H15.5556C15.926 9.30317 16.1111 9.11799 16.1111 8.74762C16.1111 8.37725 15.926 8.19206 15.5556 8.19206ZM4.44447 13.7475H15.5556C15.926 13.7475 16.1111 13.9327 16.1111 14.303C16.1111 14.6734 15.926 14.8586 15.5556 14.8586H4.44447C4.0741 14.8586 3.88892 14.6734 3.88892 14.303C3.88892 13.9327 4.0741 13.7475 4.44447 13.7475ZM10 10.9697H4.44447C4.0741 10.9697 3.88892 11.1548 3.88892 11.5252C3.88892 11.8956 4.0741 12.0808 4.44447 12.0808H10C10.3704 12.0808 10.5556 11.8956 10.5556 11.5252C10.5556 11.1548 10.3704 10.9697 10 10.9697Z"
                                  fill="#8493A9"/>
                        </svg>
                        段落问题：<span style="color: #1D2129;">2</span>种
                    </div>
                </div>
            </div>
            <div class="detect_result_line"></div>
            <div class="detect_result_item">
                <div class="detect_result_item_title">其他指标</div>
                <div class="detect_result_item_content_other">
                    <span>差错率：<span style="color: #E44548;font-size: 18px">13.99</span><span
                            style="font-size: 14px"> / 10000</span></span>
                    <span class="detect_result_item_content_other_line"></span>
                    <span>差错率计算方法:问题总数*10000/总字符数（每万字差错率）</span>
                </div>
            </div>
        </div>

        <div id="paper_info" class="report_item location_item">
            <div class="item_name">文档信息</div>
            <div class="clear">
                <div class="paper_info_item">
                    <div>文档页数：<span style="color: #1D2129;">32</span></div>
                    <div>公式数：<span style="color: #1D2129;">0</span>
                    </div>
                </div>
                <div class="paper_info_item">
                    <div>文档字符数：<span style="color: #1D2129;">19302</span>
                    </div>
                    <div>空格数：<span style="color: #1D2129;">2413</span></div>
                </div>
                <div class="paper_info_item">
                    <div>表格数：<span style="color: #1D2129;">8</span></div>
                    <div>脚注数：<span style="color: #1D2129;">2</span>
                    </div>
                </div>
                <div class="paper_info_item">
                    <div>图片数：<span style="color: #1D2129;">16</span>
                    </div>
                    <div>尾注数：<span style="color: #1D2129;">2</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="construct_analyse" class="report_item location_item">
            <div class="item_name">结构分析</div>
            <div class="mt20">
                <div class="summarize_row clear">
                    <div class="summarize_row_has">
                        
                        <span>结构是否准确：<span style="color: #E44548">否</span></span>
                    </div>

                    <div class="summarize_row_tip ml20">
                        <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                             viewBox="0 0 15 15" fill="none">
                            <g clip-path="url(#clip0_1761_8244)">
                                <path
                                        d="M7.17822 14.7083C3.31225 14.7083 0.178223 11.5742 0.178223 7.70825C0.178223 3.84228 3.31225 0.708252 7.17822 0.708252C11.0442 0.708252 14.1782 3.84228 14.1782 7.70825C14.1782 11.5742 11.0442 14.7083 7.17822 14.7083ZM7.83447 3.9895C7.83447 3.62703 7.54069 3.33325 7.17822 3.33325C6.81575 3.33325 6.52197 3.62703 6.52197 3.9895V8.802C6.52197 9.16447 6.81575 9.45825 7.17822 9.45825C7.54069 9.45825 7.83447 9.16447 7.83447 8.802V3.9895ZM7.17822 10.7708C6.81575 10.7708 6.52197 11.0645 6.52197 11.427C6.52197 11.7895 6.81575 12.0833 7.17822 12.0833C7.54069 12.0833 7.83447 11.7895 7.83447 11.427C7.83447 11.0645 7.54069 10.7708 7.17822 10.7708Z"
                                        fill="#165DFF"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_1761_8244">
                                    <rect width="14" height="14" fill="white"
                                          transform="translate(0.178223 0.708252)"/>
                                </clipPath>
                            </defs>
                        </svg>
                        提醒：<span>3</span>种
                    </div>
                    
                    <div class="summarize_row_danger ">
                        <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                             viewBox="0 0 15 15" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M7.34416 14.2374C11.1825 14.265 14.3166 11.1758 14.3442 7.33741C14.3718 3.49903 11.2825 0.365018 7.44415 0.337408C3.60576 0.309797 0.471758 3.39904 0.444147 7.23742C0.416537 11.0758 3.50578 14.2098 7.34416 14.2374ZM10.8886 4.01539C10.6528 3.7762 10.2678 3.77344 10.0286 4.00922L7.39976 6.60059L4.80871 3.97208C4.57293 3.73289 4.18788 3.73013 3.94869 3.96591C3.7095 4.2017 3.70673 4.58674 3.94252 4.82593L6.53357 7.45444L3.90359 10.0469C3.6644 10.2827 3.66163 10.6678 3.89742 10.907C4.1332 11.1462 4.51825 11.1489 4.75744 10.9131L7.38742 8.32063L9.98024 10.9509C10.216 11.1901 10.6011 11.1929 10.8403 10.9571C11.0795 10.7213 11.0822 10.3363 10.8464 10.0971L8.25361 7.46678L10.8824 4.87541C11.1216 4.63963 11.1244 4.25458 10.8886 4.01539Z"
                                  fill="#A11901"/>
                        </svg>
                        严重错误：<span>1</span>种
                    </div>
                </div>
                <div class="constrction_body clear">
                    <div class="constrction_body_title clear">
                        <div class="constrction_body_title_left">标准结构</div>
                        <div class="constrction_body_title_right">当前论文结构</div>
                    </div>
                    <div class="constrction_body_main clear">
                        <div class="constrction_body_left">
                            <div class="constrction_body_row">
                                
                                <span>封面</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>诚信声明</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>版权声明</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>中文摘要</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>中文关键词</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>英文摘要</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>英文关键词</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>目录</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>正文</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>参考文献</span>
                            </div>
                            <div class="constrction_body_row">
                                
                                <span>致谢</span>
                            </div>
                            <div class="constrction_body_row">
                                <svg xmlns="http://www.w3.org/2000/svg"
                                     width="15" height="15" viewBox="0 0 15 15" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.07209 14.1892C10.938 14.217 14.0945 11.1056 14.1223 7.23974C14.1501 3.37385 11.0387 0.217381 7.17279 0.189573C3.3069 0.161764 0.15043 3.27315 0.122622 7.13904C0.0948137 11.0049 3.2062 14.1614 7.07209 14.1892ZM10.6422 3.89399C10.4048 3.65308 10.017 3.6503 9.77605 3.88777L7.12838 6.49772L4.51876 3.85037C4.28128 3.60946 3.89348 3.60668 3.65257 3.84415C3.41167 4.08163 3.40888 4.46943 3.64636 4.71034L6.25598 7.35768L3.60715 9.96877C3.36624 10.2062 3.36346 10.594 3.60093 10.835C3.83841 11.0759 4.22621 11.0786 4.46712 10.8412L7.11595 8.23008L9.72735 10.8792C9.96483 11.1201 10.3526 11.1229 10.5935 10.8855C10.8344 10.648 10.8372 10.2602 10.5998 10.0193L7.98835 7.37011L10.636 4.76017C10.8769 4.5227 10.8797 4.13489 10.6422 3.89399Z"
                                          fill="#A11901"/>
                                </svg>
                                <span>附录</span><span class="lack_icon">缺失</span>
                            </div>
                        </div>
                        <div class="constrction_body_right">
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>封面</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div class="constrction_body_row_unnecessary">
                                    
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5699)">
                                            <path
                                                    d="M7.07227 14.1894C3.2063 14.1894 0.0722656 11.0554 0.0722656 7.18939C0.0722656 3.32342 3.2063 0.189392 7.07227 0.189392C10.9382 0.189392 14.0723 3.32342 14.0723 7.18939C14.0723 11.0554 10.9382 14.1894 7.07227 14.1894ZM7.72852 3.47064C7.72852 3.10817 7.43473 2.81439 7.07227 2.81439C6.7098 2.81439 6.41602 3.10817 6.41602 3.47064V8.28314C6.41602 8.64561 6.7098 8.93939 7.07227 8.93939C7.43473 8.93939 7.72852 8.64561 7.72852 8.28314V3.47064ZM7.07227 10.2519C6.7098 10.2519 6.41602 10.5457 6.41602 10.9081C6.41602 11.2706 6.7098 11.5644 7.07227 11.5644C7.43473 11.5644 7.72852 11.2706 7.72852 10.9081C7.72852 10.5457 7.43473 10.2519 7.07227 10.2519Z"
                                                    fill="#165DFF"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5699">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <span>资料_任务书</span><span
                                        class="unnecessary_icon">多余</span>
                                    <span class="constrction_body_row_tip tip2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                                 viewBox="0 0 17 17" fill="none">
                                                <g clip-path="url(#clip0_3829_5703)">
                                                    <path
                                                            d="M8.17773 11.6119C8.17773 12.0261 8.51352 12.3619 8.92774 12.3619C9.34196 12.3619 9.67775 12.0261 9.67775 11.6119C9.67775 11.1977 9.34196 10.8619 8.92774 10.8619C8.51352 10.8619 8.17773 11.1977 8.17773 11.6119Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92773 14.3564C5.06774 14.3564 1.92773 11.2164 1.92773 7.35638C1.92773 3.49639 5.06774 0.356384 8.92773 0.356384C12.7877 0.356384 15.9277 3.49639 15.9277 7.35638C15.9277 11.2164 12.7877 14.3564 8.92773 14.3564ZM8.92773 1.36087C5.62173 1.36087 2.93222 4.05038 2.93222 7.35638C2.93222 10.6619 5.62173 13.3519 8.92773 13.3519C12.2332 13.3519 14.9232 10.6619 14.9232 7.35638C14.9232 4.05038 12.2332 1.36087 8.92773 1.36087Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92775 9.88292C8.65174 9.88292 8.42773 9.65892 8.42773 9.38294V8.53743C8.42773 7.71942 9.05273 7.09443 9.60473 6.54294C10.0092 6.13794 10.4277 5.71993 10.4277 5.38643C10.4277 4.55294 9.75473 3.87494 8.92775 3.87494C8.08676 3.87494 7.42776 4.52394 7.42776 5.35243C7.42776 5.62844 7.20376 5.85242 6.92775 5.85242C6.65174 5.85242 6.42773 5.62841 6.42773 5.35243C6.42773 3.98643 7.54922 2.87494 8.92775 2.87494C10.3063 2.87494 11.4278 4.00143 11.4278 5.38643C11.4278 6.13493 10.8603 6.70192 10.3118 7.25043C9.87676 7.68443 9.42776 8.13343 9.42776 8.53692V9.38242C9.42776 9.65844 9.20376 9.88292 8.92775 9.88292Z"
                                                            fill="#28A781"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_3829_5703">
                                                        <rect width="16" height="16" fill="white"
                                                              transform="translate(0.0722656 0.189392)"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            <div class="tip_box tip2_box" style="display: none">
                                                <div>请检查结构标题与模板要求是否一致</div>
                                            </div>
                                        </span>

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div class="constrction_body_row_unnecessary">
                                    
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5699)">
                                            <path
                                                    d="M7.07227 14.1894C3.2063 14.1894 0.0722656 11.0554 0.0722656 7.18939C0.0722656 3.32342 3.2063 0.189392 7.07227 0.189392C10.9382 0.189392 14.0723 3.32342 14.0723 7.18939C14.0723 11.0554 10.9382 14.1894 7.07227 14.1894ZM7.72852 3.47064C7.72852 3.10817 7.43473 2.81439 7.07227 2.81439C6.7098 2.81439 6.41602 3.10817 6.41602 3.47064V8.28314C6.41602 8.64561 6.7098 8.93939 7.07227 8.93939C7.43473 8.93939 7.72852 8.64561 7.72852 8.28314V3.47064ZM7.07227 10.2519C6.7098 10.2519 6.41602 10.5457 6.41602 10.9081C6.41602 11.2706 6.7098 11.5644 7.07227 11.5644C7.43473 11.5644 7.72852 11.2706 7.72852 10.9081C7.72852 10.5457 7.43473 10.2519 7.07227 10.2519Z"
                                                    fill="#165DFF"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5699">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <span>资料_开题报告</span><span
                                        class="unnecessary_icon">多余</span>
                                    <span class="constrction_body_row_tip tip2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                                 viewBox="0 0 17 17" fill="none">
                                                <g clip-path="url(#clip0_3829_5703)">
                                                    <path
                                                            d="M8.17773 11.6119C8.17773 12.0261 8.51352 12.3619 8.92774 12.3619C9.34196 12.3619 9.67775 12.0261 9.67775 11.6119C9.67775 11.1977 9.34196 10.8619 8.92774 10.8619C8.51352 10.8619 8.17773 11.1977 8.17773 11.6119Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92773 14.3564C5.06774 14.3564 1.92773 11.2164 1.92773 7.35638C1.92773 3.49639 5.06774 0.356384 8.92773 0.356384C12.7877 0.356384 15.9277 3.49639 15.9277 7.35638C15.9277 11.2164 12.7877 14.3564 8.92773 14.3564ZM8.92773 1.36087C5.62173 1.36087 2.93222 4.05038 2.93222 7.35638C2.93222 10.6619 5.62173 13.3519 8.92773 13.3519C12.2332 13.3519 14.9232 10.6619 14.9232 7.35638C14.9232 4.05038 12.2332 1.36087 8.92773 1.36087Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92775 9.88292C8.65174 9.88292 8.42773 9.65892 8.42773 9.38294V8.53743C8.42773 7.71942 9.05273 7.09443 9.60473 6.54294C10.0092 6.13794 10.4277 5.71993 10.4277 5.38643C10.4277 4.55294 9.75473 3.87494 8.92775 3.87494C8.08676 3.87494 7.42776 4.52394 7.42776 5.35243C7.42776 5.62844 7.20376 5.85242 6.92775 5.85242C6.65174 5.85242 6.42773 5.62841 6.42773 5.35243C6.42773 3.98643 7.54922 2.87494 8.92775 2.87494C10.3063 2.87494 11.4278 4.00143 11.4278 5.38643C11.4278 6.13493 10.8603 6.70192 10.3118 7.25043C9.87676 7.68443 9.42776 8.13343 9.42776 8.53692V9.38242C9.42776 9.65844 9.20376 9.88292 8.92775 9.88292Z"
                                                            fill="#28A781"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_3829_5703">
                                                        <rect width="16" height="16" fill="white"
                                                              transform="translate(0.0722656 0.189392)"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            <div class="tip_box tip2_box" style="display: none">
                                                <div>请检查结构标题与模板要求是否一致</div>
                                            </div>
                                        </span>

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>诚信声明</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>版权声明</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>中文摘要</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>中文关键词</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>英文摘要</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>英文关键词</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>目录</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>正文</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>参考文献</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div>
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5573)">
                                            <path
                                                    d="M7.06509 0.196228C3.20306 0.196228 0.0722656 3.32703 0.0722656 7.18905C0.0722656 11.0511 3.20306 14.1819 7.06509 14.1819C10.9271 14.1819 14.0579 11.0511 14.0579 7.18905C14.0579 3.32703 10.9271 0.196228 7.06509 0.196228ZM11.076 5.37519L6.55338 9.89784C6.55338 9.89784 6.55332 9.8979 6.55324 9.89798C6.34474 10.1065 6.02682 10.1391 5.78411 9.99574C5.73916 9.96917 5.69679 9.93656 5.65819 9.89798C5.65816 9.89793 5.6581 9.8979 5.6581 9.8979L3.0542 7.294C2.80705 7.04684 2.80705 6.64607 3.0542 6.39886C3.30135 6.15171 3.70213 6.15171 3.94928 6.39886L6.10576 8.55533L10.1809 4.48014C10.4281 4.23299 10.8289 4.23299 11.0761 4.48014C11.3232 4.7273 11.3232 5.12804 11.076 5.37519Z"
                                                    fill="#28A781"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5573">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    
                                    <span>致谢</span>
                                    

                                </div>
                            </div>
                            <div class="constrction_body_row">
                                <div class="constrction_body_row_unnecessary">
                                    
                                    <svg class="tY3" style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                                         viewBox="0 0 15 15" fill="none">
                                        <g clip-path="url(#clip0_3829_5699)">
                                            <path
                                                    d="M7.07227 14.1894C3.2063 14.1894 0.0722656 11.0554 0.0722656 7.18939C0.0722656 3.32342 3.2063 0.189392 7.07227 0.189392C10.9382 0.189392 14.0723 3.32342 14.0723 7.18939C14.0723 11.0554 10.9382 14.1894 7.07227 14.1894ZM7.72852 3.47064C7.72852 3.10817 7.43473 2.81439 7.07227 2.81439C6.7098 2.81439 6.41602 3.10817 6.41602 3.47064V8.28314C6.41602 8.64561 6.7098 8.93939 7.07227 8.93939C7.43473 8.93939 7.72852 8.64561 7.72852 8.28314V3.47064ZM7.07227 10.2519C6.7098 10.2519 6.41602 10.5457 6.41602 10.9081C6.41602 11.2706 6.7098 11.5644 7.07227 11.5644C7.43473 11.5644 7.72852 11.2706 7.72852 10.9081C7.72852 10.5457 7.43473 10.2519 7.07227 10.2519Z"
                                                    fill="#165DFF"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_3829_5699">
                                                <rect width="14" height="14" fill="white"
                                                      transform="translate(0.0722656 0.189392)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                    <span>脚注</span><span
                                        class="unnecessary_icon">多余</span>
                                    <span class="constrction_body_row_tip tip2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17"
                                                 viewBox="0 0 17 17" fill="none">
                                                <g clip-path="url(#clip0_3829_5703)">
                                                    <path
                                                            d="M8.17773 11.6119C8.17773 12.0261 8.51352 12.3619 8.92774 12.3619C9.34196 12.3619 9.67775 12.0261 9.67775 11.6119C9.67775 11.1977 9.34196 10.8619 8.92774 10.8619C8.51352 10.8619 8.17773 11.1977 8.17773 11.6119Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92773 14.3564C5.06774 14.3564 1.92773 11.2164 1.92773 7.35638C1.92773 3.49639 5.06774 0.356384 8.92773 0.356384C12.7877 0.356384 15.9277 3.49639 15.9277 7.35638C15.9277 11.2164 12.7877 14.3564 8.92773 14.3564ZM8.92773 1.36087C5.62173 1.36087 2.93222 4.05038 2.93222 7.35638C2.93222 10.6619 5.62173 13.3519 8.92773 13.3519C12.2332 13.3519 14.9232 10.6619 14.9232 7.35638C14.9232 4.05038 12.2332 1.36087 8.92773 1.36087Z"
                                                            fill="#28A781"/>
                                                    <path
                                                            d="M8.92775 9.88292C8.65174 9.88292 8.42773 9.65892 8.42773 9.38294V8.53743C8.42773 7.71942 9.05273 7.09443 9.60473 6.54294C10.0092 6.13794 10.4277 5.71993 10.4277 5.38643C10.4277 4.55294 9.75473 3.87494 8.92775 3.87494C8.08676 3.87494 7.42776 4.52394 7.42776 5.35243C7.42776 5.62844 7.20376 5.85242 6.92775 5.85242C6.65174 5.85242 6.42773 5.62841 6.42773 5.35243C6.42773 3.98643 7.54922 2.87494 8.92775 2.87494C10.3063 2.87494 11.4278 4.00143 11.4278 5.38643C11.4278 6.13493 10.8603 6.70192 10.3118 7.25043C9.87676 7.68443 9.42776 8.13343 9.42776 8.53692V9.38242C9.42776 9.65844 9.20376 9.88292 8.92775 9.88292Z"
                                                            fill="#28A781"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_3829_5703">
                                                        <rect width="16" height="16" fill="white"
                                                              transform="translate(0.0722656 0.189392)"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            <div class="tip_box tip2_box" style="display: none">
                                                <div>请检查结构标题与模板要求是否一致</div>
                                            </div>
                                        </span>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="word_count_analyse" class="report_item location_item">
            <div class="item_name">字数分析</div>
            <div class="mt20">
                <div class="summarize_row clear">
                    <div class="summarize_row_has">
                        <span>字数是否达标：<span
                                style="color: #E44548;">否</span></span>
                        
                    </div>
                    
                    
                    <div class="summarize_row_danger ">
                        <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="15" height="15"
                             viewBox="0 0 15 15" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M7.34416 14.2374C11.1825 14.265 14.3166 11.1758 14.3442 7.33741C14.3718 3.49903 11.2825 0.365018 7.44415 0.337408C3.60576 0.309797 0.471758 3.39904 0.444147 7.23742C0.416537 11.0758 3.50578 14.2098 7.34416 14.2374ZM10.8886 4.01539C10.6528 3.7762 10.2678 3.77344 10.0286 4.00922L7.39976 6.60059L4.80871 3.97208C4.57293 3.73289 4.18788 3.73013 3.94869 3.96591C3.7095 4.2017 3.70673 4.58674 3.94252 4.82593L6.53357 7.45444L3.90359 10.0469C3.6644 10.2827 3.66163 10.6678 3.89742 10.907C4.1332 11.1462 4.51825 11.1489 4.75744 10.9131L7.38742 8.32063L9.98024 10.9509C10.216 11.1901 10.6011 11.1929 10.8403 10.9571C11.0795 10.7213 11.0822 10.3363 10.8464 10.0971L8.25361 7.46678L10.8824 4.87541C11.1216 4.63963 11.1244 4.25458 10.8886 4.01539Z"
                                  fill="#A11901"/>
                        </svg>
                        严重错误：<span>1</span>种
                    </div>
                </div>
                <div class="fake_table_interval_head clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">结构</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">字数标准</div>
                    <div class="fake_table_interval_head_item" style="width: 220px;">实际</div>
                    <div class="fake_table_interval_head_item" style="width: 105px;">分析结果</div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">封面</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">65字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">资料_任务书</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">766字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">资料_开题报告</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">3211字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">诚信声明</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">192字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">版权声明</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">190字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">中文摘要</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="color: #1A9771">300-500字</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">357字</div>
                    </div>
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_success">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1790_11822)">
                                        <path
                                                d="M6.49385 0.422302C3.18354 0.422302 0.5 3.10584 0.5 6.41615C0.5 9.72648 3.18354 12.41 6.49385 12.41C9.80418 12.41 12.4877 9.72648 12.4877 6.41615C12.4877 3.10584 9.80415 0.422302 6.49385 0.422302ZM9.93177 4.86142L6.05524 8.73797C6.05524 8.73797 6.05519 8.73802 6.05512 8.73809C5.87641 8.91685 5.6039 8.94476 5.39587 8.82189C5.35734 8.79911 5.32102 8.77116 5.28793 8.73809C5.28791 8.73804 5.28786 8.73802 5.28786 8.73802L3.05594 6.5061C2.8441 6.29426 2.8441 5.95074 3.05594 5.73884C3.26779 5.527 3.61131 5.527 3.82316 5.73884L5.67156 7.58725L9.16459 4.09423C9.37646 3.88238 9.71997 3.88238 9.93182 4.09423C10.1436 4.30608 10.1436 4.64957 9.93177 4.86142Z"
                                                fill="#28A781"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1790_11822">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416504)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>达标</span>
                            </span>
                    </div>
                    
                    
                    
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">中文关键词</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="color: #1A9771">3-5个</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">5个</div>
                    </div>
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_success">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1790_11822)">
                                        <path
                                                d="M6.49385 0.422302C3.18354 0.422302 0.5 3.10584 0.5 6.41615C0.5 9.72648 3.18354 12.41 6.49385 12.41C9.80418 12.41 12.4877 9.72648 12.4877 6.41615C12.4877 3.10584 9.80415 0.422302 6.49385 0.422302ZM9.93177 4.86142L6.05524 8.73797C6.05524 8.73797 6.05519 8.73802 6.05512 8.73809C5.87641 8.91685 5.6039 8.94476 5.39587 8.82189C5.35734 8.79911 5.32102 8.77116 5.28793 8.73809C5.28791 8.73804 5.28786 8.73802 5.28786 8.73802L3.05594 6.5061C2.8441 6.29426 2.8441 5.95074 3.05594 5.73884C3.26779 5.527 3.61131 5.527 3.82316 5.73884L5.67156 7.58725L9.16459 4.09423C9.37646 3.88238 9.71997 3.88238 9.93182 4.09423C10.1436 4.30608 10.1436 4.64957 9.93177 4.86142Z"
                                                fill="#28A781"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1790_11822">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416504)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>达标</span>
                            </span>
                    </div>
                    
                    
                    
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">英文摘要</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="color: #1A9771">300-500字</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">195字</div>
                    </div>
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_err">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M6.45686 12.4164C9.77049 12.4402 12.476 9.77332 12.4999 6.4597C12.5237 3.14607 9.8568 0.440529 6.54318 0.416694C3.22956 0.392858 0.524012 3.05976 0.500176 6.37338C0.476341 9.687 3.14324 12.3925 6.45686 12.4164ZM9.51723 3.59186C9.31368 3.38537 8.98128 3.38299 8.77479 3.58654L6.50536 5.82363L4.26854 3.55448C4.06499 3.34799 3.73259 3.3456 3.5261 3.54915C3.3196 3.7527 3.31722 4.0851 3.52077 4.29159L5.75759 6.56075L3.48716 8.79882C3.28067 9.00237 3.27828 9.33477 3.48183 9.54126C3.68538 9.74776 4.01779 9.75014 4.22428 9.54659L6.4947 7.30852L8.73305 9.57922C8.9366 9.78571 9.269 9.7881 9.4755 9.58455C9.68199 9.381 9.68437 9.0486 9.48082 8.84211L7.24247 6.5714L9.51191 4.33431C9.7184 4.13076 9.72078 3.79836 9.51723 3.59186Z"
                                          fill="#A11901"/>
                                </svg>
                                <span>不足</span>
                            </span>
                    </div>
                    
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">英文关键词</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="color: #1A9771">3-5个</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">5个</div>
                    </div>
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_success">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1790_11822)">
                                        <path
                                                d="M6.49385 0.422302C3.18354 0.422302 0.5 3.10584 0.5 6.41615C0.5 9.72648 3.18354 12.41 6.49385 12.41C9.80418 12.41 12.4877 9.72648 12.4877 6.41615C12.4877 3.10584 9.80415 0.422302 6.49385 0.422302ZM9.93177 4.86142L6.05524 8.73797C6.05524 8.73797 6.05519 8.73802 6.05512 8.73809C5.87641 8.91685 5.6039 8.94476 5.39587 8.82189C5.35734 8.79911 5.32102 8.77116 5.28793 8.73809C5.28791 8.73804 5.28786 8.73802 5.28786 8.73802L3.05594 6.5061C2.8441 6.29426 2.8441 5.95074 3.05594 5.73884C3.26779 5.527 3.61131 5.527 3.82316 5.73884L5.67156 7.58725L9.16459 4.09423C9.37646 3.88238 9.71997 3.88238 9.93182 4.09423C10.1436 4.30608 10.1436 4.64957 9.93177 4.86142Z"
                                                fill="#28A781"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1790_11822">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416504)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>达标</span>
                            </span>
                    </div>
                    
                    
                    
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">目录</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">519字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">正文</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">11517字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">参考文献</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">中文11条;外文3条</div>
                    </div>
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">致谢</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">237字</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
                <div class="fake_table_interval_tr clear">
                    <div class="fake_table_interval_head_item" style="width: 230px;">脚注</div>
                    <div class="fake_table_interval_head_item" style="width: 230px;">
                        <div class="text_overflow" style="#1D2129">-</div>
                    </div>
                    <div class="fake_table_interval_head_item"
                         style="width: 220px;">
                        <div class="text_overflow">2个</div>
                    </div>
                    
                    
                    
                    
                    <div class="fake_table_interval_head_item" style="width: 105px;">
                            <span class="construct_analyse_status construct_analyse_status_none">
                                <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13"
                                     fill="none">
                                    <g clip-path="url(#clip0_1786_10463)">
                                        <path
                                                d="M6.5 0.416565C3.18629 0.416565 0.5 3.10285 0.5 6.41656C0.5 9.72942 3.18629 12.4166 6.5 12.4166C9.81286 12.4166 12.5 9.72942 12.5 6.41656C12.5 3.10285 9.81286 0.416565 6.5 0.416565ZM9.44171 6.99942H3.48714C3.17086 6.99942 2.91629 6.75171 2.91629 6.44828C2.91371 6.14485 3.17171 5.89799 3.48629 5.89799H9.43914C9.75543 5.89799 10.0117 6.14485 10.01 6.44999C10.0126 6.75342 9.75629 6.99942 9.44171 6.99942Z"
                                                fill="#86909C"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1786_10463">
                                            <rect width="12" height="12" fill="white"
                                                  transform="translate(0.5 0.416565)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                                <span>无要求</span>
                            </span>
                    </div>
                </div>
            </div>
        </div>


        

        <div id="quetions" class="report_item location_item">
            <div class="item_name">问题片段</div>
            <div class="mt20">
                <div class="quetions_single mb40 location_item" id="中文关键词">
                    <div class="quetions_title">
                        <span>中文关键词</span>
                        
                        <div class="summarize_row_err ml20">
                            <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"
                                 viewBox="0 0 14 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7 14.7083C10.866 14.7083 14 11.5743 14 7.70827C14 3.84228 10.866 0.708275 7 0.708275C3.13401 0.708275 0 3.84228 0 7.70827C0 11.5743 3.13401 14.7083 7 14.7083ZM10.4964 4.38744C10.2572 4.14825 9.86935 4.14826 9.63015 4.38746L7.00133 7.01638L4.37273 4.38787C4.13353 4.14868 3.74571 4.14868 3.50652 4.38788C3.26733 4.62708 3.26734 5.0149 3.50654 5.25409L6.13514 7.8826L3.50515 10.5127C3.26596 10.7519 3.26597 11.1397 3.50517 11.3789C3.74437 11.6181 4.13218 11.6181 4.37138 11.3789L7.00136 8.74879L9.63175 11.3791C9.87095 11.6183 10.2588 11.6183 10.498 11.3791C10.7372 11.1399 10.7371 10.7521 10.4979 10.5129L7.86755 7.88257L10.4964 5.25365C10.7356 5.01445 10.7356 4.62663 10.4964 4.38744Z"
                                      fill="#E44548"/>
                            </svg>
                            错误：<span>1</span>种
                        </div>
                        

                        <!--                            <div class="questions_title_err">-->
                        <!--                                <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"-->
                        <!--                                    viewBox="0 0 14 15" fill="none">-->
                        <!--                                    <path fill-rule="evenodd" clip-rule="evenodd"-->
                        <!--                                        d="M7 14.5C10.866 14.5 14 11.366 14 7.5C14 3.63401 10.866 0.5 7 0.5C3.13401 0.5 0 3.63401 0 7.5C0 11.366 3.13401 14.5 7 14.5ZM10.4964 4.17917C10.2572 3.93997 9.86935 3.93998 9.63015 4.17918L7.00133 6.8081L4.37273 4.17959C4.13353 3.9404 3.74571 3.94041 3.50652 4.17961C3.26733 4.41881 3.26734 4.80662 3.50654 5.04582L6.13514 7.67432L3.50515 10.3044C3.26596 10.5436 3.26597 10.9314 3.50517 11.1706C3.74437 11.4098 4.13218 11.4098 4.37138 11.1706L7.00136 8.54051L9.63175 11.1708C9.87095 11.41 10.2588 11.41 10.498 11.1708C10.7372 10.9316 10.7371 10.5438 10.4979 10.3046L7.86755 7.67429L10.4964 5.04537C10.7356 4.80617 10.7356 4.41836 10.4964 4.17917Z"-->
                        <!--                                        fill="#E44548" />-->
                        <!--                                </svg>-->
                        <!--                                错误：<span th:text="${sel.strucErrorCount}">12</span>种-->
                        <!--                            </div>-->
                    </div>
                    <div class="fake_table_head">
                        <div class="fake_table_head_item" style="width: 60px;padding-right: 0">序号</div>
                        <div class="fake_table_head_item" style="width: 170px">原文片段</div>
                        <div class="fake_table_head_item" style="width: 150px">问题详情</div>
                        <div class="fake_table_head_item" style="width: 190px">标准</div>
                        <div class="fake_table_head_item" style="border: none;padding-left: 16px">问题描述</div>
                    </div>
                    <div class="fake_table_tbody">
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">1</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">对齐方式问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">左对齐</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">两端对齐</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quetions_single mb40 location_item" id="英文关键词">
                    <div class="quetions_title">
                        <span>英文关键词</span>
                        
                        <div class="summarize_row_err ml20">
                            <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"
                                 viewBox="0 0 14 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7 14.7083C10.866 14.7083 14 11.5743 14 7.70827C14 3.84228 10.866 0.708275 7 0.708275C3.13401 0.708275 0 3.84228 0 7.70827C0 11.5743 3.13401 14.7083 7 14.7083ZM10.4964 4.38744C10.2572 4.14825 9.86935 4.14826 9.63015 4.38746L7.00133 7.01638L4.37273 4.38787C4.13353 4.14868 3.74571 4.14868 3.50652 4.38788C3.26733 4.62708 3.26734 5.0149 3.50654 5.25409L6.13514 7.8826L3.50515 10.5127C3.26596 10.7519 3.26597 11.1397 3.50517 11.3789C3.74437 11.6181 4.13218 11.6181 4.37138 11.3789L7.00136 8.74879L9.63175 11.3791C9.87095 11.6183 10.2588 11.6183 10.498 11.3791C10.7372 11.1399 10.7371 10.7521 10.4979 10.5129L7.86755 7.88257L10.4964 5.25365C10.7356 5.01445 10.7356 4.62663 10.4964 4.38744Z"
                                      fill="#E44548"/>
                            </svg>
                            错误：<span>1</span>种
                        </div>
                        

                        <!--                            <div class="questions_title_err">-->
                        <!--                                <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"-->
                        <!--                                    viewBox="0 0 14 15" fill="none">-->
                        <!--                                    <path fill-rule="evenodd" clip-rule="evenodd"-->
                        <!--                                        d="M7 14.5C10.866 14.5 14 11.366 14 7.5C14 3.63401 10.866 0.5 7 0.5C3.13401 0.5 0 3.63401 0 7.5C0 11.366 3.13401 14.5 7 14.5ZM10.4964 4.17917C10.2572 3.93997 9.86935 3.93998 9.63015 4.17918L7.00133 6.8081L4.37273 4.17959C4.13353 3.9404 3.74571 3.94041 3.50652 4.17961C3.26733 4.41881 3.26734 4.80662 3.50654 5.04582L6.13514 7.67432L3.50515 10.3044C3.26596 10.5436 3.26597 10.9314 3.50517 11.1706C3.74437 11.4098 4.13218 11.4098 4.37138 11.1706L7.00136 8.54051L9.63175 11.1708C9.87095 11.41 10.2588 11.41 10.498 11.1708C10.7372 10.9316 10.7371 10.5438 10.4979 10.3046L7.86755 7.67429L10.4964 5.04537C10.7356 4.80617 10.7356 4.41836 10.4964 4.17917Z"-->
                        <!--                                        fill="#E44548" />-->
                        <!--                                </svg>-->
                        <!--                                错误：<span th:text="${sel.strucErrorCount}">12</span>种-->
                        <!--                            </div>-->
                    </div>
                    <div class="fake_table_head">
                        <div class="fake_table_head_item" style="width: 60px;padding-right: 0">序号</div>
                        <div class="fake_table_head_item" style="width: 170px">原文片段</div>
                        <div class="fake_table_head_item" style="width: 150px">问题详情</div>
                        <div class="fake_table_head_item" style="width: 190px">标准</div>
                        <div class="fake_table_head_item" style="border: none;padding-left: 16px">问题描述</div>
                    </div>
                    <div class="fake_table_tbody">
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">2</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">Key words:dance creation;virtual reality (VR); augmented reality (AR); short video communication; choreography and creation techniques</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">标题错误</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">Key（1个半角空格）Words：（中文）</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">Key（1个半角空格）words:（英文）</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quetions_single mb40 location_item" id="正文">
                    <div class="quetions_title">
                        <span>正文</span>
                        
                        <div class="summarize_row_err ml20">
                            <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"
                                 viewBox="0 0 14 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7 14.7083C10.866 14.7083 14 11.5743 14 7.70827C14 3.84228 10.866 0.708275 7 0.708275C3.13401 0.708275 0 3.84228 0 7.70827C0 11.5743 3.13401 14.7083 7 14.7083ZM10.4964 4.38744C10.2572 4.14825 9.86935 4.14826 9.63015 4.38746L7.00133 7.01638L4.37273 4.38787C4.13353 4.14868 3.74571 4.14868 3.50652 4.38788C3.26733 4.62708 3.26734 5.0149 3.50654 5.25409L6.13514 7.8826L3.50515 10.5127C3.26596 10.7519 3.26597 11.1397 3.50517 11.3789C3.74437 11.6181 4.13218 11.6181 4.37138 11.3789L7.00136 8.74879L9.63175 11.3791C9.87095 11.6183 10.2588 11.6183 10.498 11.3791C10.7372 11.1399 10.7371 10.7521 10.4979 10.5129L7.86755 7.88257L10.4964 5.25365C10.7356 5.01445 10.7356 4.62663 10.4964 4.38744Z"
                                      fill="#E44548"/>
                            </svg>
                            错误：<span>15</span>种
                        </div>
                        

                        <!--                            <div class="questions_title_err">-->
                        <!--                                <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"-->
                        <!--                                    viewBox="0 0 14 15" fill="none">-->
                        <!--                                    <path fill-rule="evenodd" clip-rule="evenodd"-->
                        <!--                                        d="M7 14.5C10.866 14.5 14 11.366 14 7.5C14 3.63401 10.866 0.5 7 0.5C3.13401 0.5 0 3.63401 0 7.5C0 11.366 3.13401 14.5 7 14.5ZM10.4964 4.17917C10.2572 3.93997 9.86935 3.93998 9.63015 4.17918L7.00133 6.8081L4.37273 4.17959C4.13353 3.9404 3.74571 3.94041 3.50652 4.17961C3.26733 4.41881 3.26734 4.80662 3.50654 5.04582L6.13514 7.67432L3.50515 10.3044C3.26596 10.5436 3.26597 10.9314 3.50517 11.1706C3.74437 11.4098 4.13218 11.4098 4.37138 11.1706L7.00136 8.54051L9.63175 11.1708C9.87095 11.41 10.2588 11.41 10.498 11.1708C10.7372 10.9316 10.7371 10.5438 10.4979 10.3046L7.86755 7.67429L10.4964 5.04537C10.7356 4.80617 10.7356 4.41836 10.4964 4.17917Z"-->
                        <!--                                        fill="#E44548" />-->
                        <!--                                </svg>-->
                        <!--                                错误：<span th:text="${sel.strucErrorCount}">12</span>种-->
                        <!--                            </div>-->
                    </div>
                    <div class="fake_table_head">
                        <div class="fake_table_head_item" style="width: 60px;padding-right: 0">序号</div>
                        <div class="fake_table_head_item" style="width: 170px">原文片段</div>
                        <div class="fake_table_head_item" style="width: 150px">问题详情</div>
                        <div class="fake_table_head_item" style="width: 190px">标准</div>
                        <div class="fake_table_head_item" style="border: none;padding-left: 16px">问题描述</div>
                    </div>
                    <div class="fake_table_tbody">
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">3</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">1.2.1　国内发展现状</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">字号问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">13</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">小四</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">4</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">1.2.2　国外发展现状</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">字号问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">13</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">小四</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">5</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">1.3.1　研究内容</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">字号问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">13</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">小四</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">6</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">1.3.2　研究方法</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">字号问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">13</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">小四</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">7</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">表2.1 传统与新媒体编导方式对比分析</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">表题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">表1.1（1个全角空格）表名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">表2.1（1个半角空格）表名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">8</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">图4.1舞蹈《蛇来运转》</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图4.1图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">9</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">表4.1 具体舞蹈动作与新媒体技术的应用</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">表题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">表1.1（1个全角空格）表名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">表4.1（1个半角空格）表名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">10</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">图4.2春晚舞蹈《喜上枝头》</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图4.2图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">11</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">表4.2 新媒体技术介入下的舞蹈动作语汇变形</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">表题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">表1.1（1个全角空格）表名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">表4.2（1个半角空格）表名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">12</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">图5.1“小心头-小意头”抖音视频             图5.2“旦小怡”抖音视频</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图5.1图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图5.2图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">13</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">表5.1观众需求与反馈</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">表题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">表1.1（1个全角空格）表名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">表5.1表名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">14</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">表5.2 短视频平台对舞蹈节奏结构影响模型</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">表题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">表1.1（1个全角空格）表名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">表5.2（1个半角空格）表名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">15</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">图6.1舞蹈诗剧《只此青绿》编排</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图6.1图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">16</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">图6.2新媒体编导优化路径流程图</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">行距问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">单倍行距</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">固定值</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">图题问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">图1.1（1个全角空格）图名</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">图6.2图名</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quetions_single mb40 location_item" id="参考文献">
                    <div class="quetions_title">
                        <span>参考文献</span>
                        
                        <div class="summarize_row_err ml20">
                            <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"
                                 viewBox="0 0 14 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7 14.7083C10.866 14.7083 14 11.5743 14 7.70827C14 3.84228 10.866 0.708275 7 0.708275C3.13401 0.708275 0 3.84228 0 7.70827C0 11.5743 3.13401 14.7083 7 14.7083ZM10.4964 4.38744C10.2572 4.14825 9.86935 4.14826 9.63015 4.38746L7.00133 7.01638L4.37273 4.38787C4.13353 4.14868 3.74571 4.14868 3.50652 4.38788C3.26733 4.62708 3.26734 5.0149 3.50654 5.25409L6.13514 7.8826L3.50515 10.5127C3.26596 10.7519 3.26597 11.1397 3.50517 11.3789C3.74437 11.6181 4.13218 11.6181 4.37138 11.3789L7.00136 8.74879L9.63175 11.3791C9.87095 11.6183 10.2588 11.6183 10.498 11.3791C10.7372 11.1399 10.7371 10.7521 10.4979 10.5129L7.86755 7.88257L10.4964 5.25365C10.7356 5.01445 10.7356 4.62663 10.4964 4.38744Z"
                                      fill="#E44548"/>
                            </svg>
                            错误：<span>7</span>种
                        </div>
                        

                        <!--                            <div class="questions_title_err">-->
                        <!--                                <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"-->
                        <!--                                    viewBox="0 0 14 15" fill="none">-->
                        <!--                                    <path fill-rule="evenodd" clip-rule="evenodd"-->
                        <!--                                        d="M7 14.5C10.866 14.5 14 11.366 14 7.5C14 3.63401 10.866 0.5 7 0.5C3.13401 0.5 0 3.63401 0 7.5C0 11.366 3.13401 14.5 7 14.5ZM10.4964 4.17917C10.2572 3.93997 9.86935 3.93998 9.63015 4.17918L7.00133 6.8081L4.37273 4.17959C4.13353 3.9404 3.74571 3.94041 3.50652 4.17961C3.26733 4.41881 3.26734 4.80662 3.50654 5.04582L6.13514 7.67432L3.50515 10.3044C3.26596 10.5436 3.26597 10.9314 3.50517 11.1706C3.74437 11.4098 4.13218 11.4098 4.37138 11.1706L7.00136 8.54051L9.63175 11.1708C9.87095 11.41 10.2588 11.41 10.498 11.1708C10.7372 10.9316 10.7371 10.5438 10.4979 10.3046L7.86755 7.67429L10.4964 5.04537C10.7356 4.80617 10.7356 4.41836 10.4964 4.17917Z"-->
                        <!--                                        fill="#E44548" />-->
                        <!--                                </svg>-->
                        <!--                                错误：<span th:text="${sel.strucErrorCount}">12</span>种-->
                        <!--                            </div>-->
                    </div>
                    <div class="fake_table_head">
                        <div class="fake_table_head_item" style="width: 60px;padding-right: 0">序号</div>
                        <div class="fake_table_head_item" style="width: 170px">原文片段</div>
                        <div class="fake_table_head_item" style="width: 150px">问题详情</div>
                        <div class="fake_table_head_item" style="width: 190px">标准</div>
                        <div class="fake_table_head_item" style="border: none;padding-left: 16px">问题描述</div>
                    </div>
                    <div class="fake_table_tbody">
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">17</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[1]高绿苑.“沉浸式”舞蹈剧场中观演关系研究[D].山东师范大学,2023.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">18</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[2]廖伟.舞台表演视角下新媒体舞蹈的发展策略研究[D].华侨大学,2023.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">19</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[5]王炳臻.数字影像对民族舞蹈创作路径拓展[D].中央民族大学,2023.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">20</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[6]尹素雯.新媒体舞蹈艺术研究[D].华南理工大学,2022.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">21</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[7]张涵.新媒体视域下舞蹈创作的发展研究[D].天津体育学院,2022.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">22</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[10]Georgia Luckhurst.Campaign calls for creativity to be put at heart of curriculum[J].The Stage,2025,5.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">期刊文献页码问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.4.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">页码缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">23</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">[14]常周烁子.人数关系下的身体技术在编创作品中的运用[D].北京舞蹈学院,2021.</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">元素问题</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">请参考国标GB/T 7714-2015中4.1.2小节</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">保存地点或保存单位缺失</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="quetions_single mb40 location_item" id="致谢">
                    <div class="quetions_title">
                        <span>致谢</span>
                        
                        <div class="summarize_row_err ml20">
                            <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"
                                 viewBox="0 0 14 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M7 14.7083C10.866 14.7083 14 11.5743 14 7.70827C14 3.84228 10.866 0.708275 7 0.708275C3.13401 0.708275 0 3.84228 0 7.70827C0 11.5743 3.13401 14.7083 7 14.7083ZM10.4964 4.38744C10.2572 4.14825 9.86935 4.14826 9.63015 4.38746L7.00133 7.01638L4.37273 4.38787C4.13353 4.14868 3.74571 4.14868 3.50652 4.38788C3.26733 4.62708 3.26734 5.0149 3.50654 5.25409L6.13514 7.8826L3.50515 10.5127C3.26596 10.7519 3.26597 11.1397 3.50517 11.3789C3.74437 11.6181 4.13218 11.6181 4.37138 11.3789L7.00136 8.74879L9.63175 11.3791C9.87095 11.6183 10.2588 11.6183 10.498 11.3791C10.7372 11.1399 10.7371 10.7521 10.4979 10.5129L7.86755 7.88257L10.4964 5.25365C10.7356 5.01445 10.7356 4.62663 10.4964 4.38744Z"
                                      fill="#E44548"/>
                            </svg>
                            错误：<span>1</span>种
                        </div>
                        

                        <!--                            <div class="questions_title_err">-->
                        <!--                                <svg class="tY2" xmlns="http://www.w3.org/2000/svg" width="14" height="15"-->
                        <!--                                    viewBox="0 0 14 15" fill="none">-->
                        <!--                                    <path fill-rule="evenodd" clip-rule="evenodd"-->
                        <!--                                        d="M7 14.5C10.866 14.5 14 11.366 14 7.5C14 3.63401 10.866 0.5 7 0.5C3.13401 0.5 0 3.63401 0 7.5C0 11.366 3.13401 14.5 7 14.5ZM10.4964 4.17917C10.2572 3.93997 9.86935 3.93998 9.63015 4.17918L7.00133 6.8081L4.37273 4.17959C4.13353 3.9404 3.74571 3.94041 3.50652 4.17961C3.26733 4.41881 3.26734 4.80662 3.50654 5.04582L6.13514 7.67432L3.50515 10.3044C3.26596 10.5436 3.26597 10.9314 3.50517 11.1706C3.74437 11.4098 4.13218 11.4098 4.37138 11.1706L7.00136 8.54051L9.63175 11.1708C9.87095 11.41 10.2588 11.41 10.498 11.1708C10.7372 10.9316 10.7371 10.5438 10.4979 10.3046L7.86755 7.67429L10.4964 5.04537C10.7356 4.80617 10.7356 4.41836 10.4964 4.17917Z"-->
                        <!--                                        fill="#E44548" />-->
                        <!--                                </svg>-->
                        <!--                                错误：<span th:text="${sel.strucErrorCount}">12</span>种-->
                        <!--                            </div>-->
                    </div>
                    <div class="fake_table_head">
                        <div class="fake_table_head_item" style="width: 60px;padding-right: 0">序号</div>
                        <div class="fake_table_head_item" style="width: 170px">原文片段</div>
                        <div class="fake_table_head_item" style="width: 150px">问题详情</div>
                        <div class="fake_table_head_item" style="width: 190px">标准</div>
                        <div class="fake_table_head_item" style="border: none;padding-left: 16px">问题描述</div>
                    </div>
                    <div class="fake_table_tbody">
                        <div class="fake_table_tr ">
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item1">24</div>
                            
                            <div class="fake_table_tr_item fake_table_tr_item_border fake_table_tr_item2">
                                <div class="text_overflow wordWrap">致　　谢</div>
                            </div>
                            <div class="fake_table_tr_item_more_column clear">
                                <div class="single_fake_table_tr_item_more_column clear">
                                    <div class="more_column_item" style="width: 150px !important;">
                                        <div class="text_overflow" style="width: 149px !important;">标题错误</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow color_green">致谢</div>
                                    </div>
                                    <div class="more_column_item">
                                        <div class="text_overflow" style="padding-left: 30px;width: 205px !important;">致（2个全角空格）谢</div>
                                        <!--错误类型--err_label，严重错误--fatal_err_label，提醒--alert_label-->
                                        
                                        <span class="more_column_item_label err_label"></span>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="report_bot">
            <div class="report_bot_top ">
                <div class="report_bot_title">阅读说明：</div>
                <div class="bot_li">
                    检测依据:《GB7713学位论文编写格式》《GB7714参考文献著录规则》《GB15834标点符号用法》《GB15835出版物上数字用法》《GB3100国际单位制及其应用》《GB3101有关量单位符号的一般原则》《GB3102空间和时间的量和单位》。
                </div>
                <div class="bot_li">
                    差错率计算方法:问题总数*10000/总字符数(每万字差错率)，参考《图书质量管理规定》《报纸期刊质量管理规定》。
                </div>
                <div class="bot_li">
                    报告编号系送检论文检测报告在本系统中的唯一编号。
                </div>
                <div class="bot_li">
                    格式检测依据相关条例和学校规范进行，结果仅供参考。
                </div>

                <div class="report_code">
                    <div class="report_code_box">
                    </div>
                    <div class="report_code_text">微信公众号</div>
                </div>
            </div>
            <div class="report_bot_line">
                <div class="report_bot_line_text">如果对检测报告有任何疑问，请联系客服</div>
            </div>
            <div class="report_bot_info">
                <div>唯一官网：https://vpcs.fanyu.com</div>
                <div class="report_bot_info_line"></div>
                <div>客服邮箱：<EMAIL></div>
                <div class="report_bot_info_line"></div>
                <div>客服热线：************</div>
                <div class="report_bot_info_line"></div>
                <div>客服QQ：4006075550</div>
            </div>
        </div>
    </div>
</div>
</body>
<script>


    const setNavPosition = () => {
        const nav = document.getElementsByClassName('side_nav')[0];
        const main = document.getElementsByClassName('report_main')[0];
        nav.style.left = main.clientWidth + main.offsetLeft + 16 + 'px';
        if (document.body.clientWidth > 1300) {
            nav.style.top = '50%'
            nav.style.transform = 'translateY(-50%)'
            nav.style.position = 'fixed'
        } else {
            nav.style.top = main.getBoundingClientRect().y > 0 ? nav.clientHeight / 2 + 'px' : Math.abs(main.getBoundingClientRect().y) + nav.clientHeight / 2 + 'px';
            nav.style.transform = 'unset'
            nav.style.position = 'absolute'
        }
        nav.style.display = 'block'
        nav.style['max-height'] = window.innerHeight - 124 + 'px';
    }

    window.addEventListener('resize', function () {
        setNavPosition();
    })


    const computedHeight = () => {
        const allColumns = Array.from(document.getElementsByClassName('fake_table_tr_item_more_column'));
        allColumns.forEach(item => {
            const neighbor = item.parentNode.querySelector('.fake_table_tr_item2');
            const children = Array.from(item.children);
            // 设置超出省略的行数
            Array.from(neighbor.children)[0].style['-webkit-line-clamp'] = children.length;
            // 如果是图片 设置高度，超出滚动条展示
            if (Array.from(neighbor.children)[0].querySelector('img') !== null) {
                Array.from(neighbor.children)[0].style.height = 34 * children.length + 'px';
                Array.from(neighbor.children)[0].style.overflow = 'auto';
                Array.from(neighbor.children)[0].classList.remove('text_overflow')
            }
        })
    }

    const setTableBody = () => {
        const bodys = Array.from(document.querySelectorAll('.fake_table_tbody'))
        bodys.forEach(item => {
            if (item.clientHeight < 600) {
                item.parentNode.style['padding-right'] = '6px'
            }
        })
    }
    const setPageProblem = () => {
        const page = document.getElementById('page_problem')
        const head = document.getElementsByClassName('page_problem_head').item(0)
        if (page && page.scrollHeight > 750) {
            head.style['padding-right'] = '6px'
        }
    }
    window.onload = function () {
        computedHeight()
        setNavPosition()
        setTableBody()
        setPageProblem()
        window.scroll(0, 1)
        window.scroll(0, 0)
        // 文字溢出鼠标悬浮提示框
        const needTooltips = Array.from(document.querySelectorAll('.text_overflow'));
        needTooltips.forEach(item => {
            item.addEventListener('mouseenter', function () {
                // 删除类名text_overflow_tooltip的节点
                const doms = Array.from(document.getElementsByClassName('text_overflow_tooltip'));
                if (doms.length) {
                    doms.map(d => {
                        d.remove();
                    })
                }
                if (item.scrollWidth > item.clientWidth || item.scrollHeight - item.clientHeight > 3) {
                    // 文本溢出
                    const tooltip = document.createElement('div');
                    tooltip.classList.add('text_overflow_tooltip');
                    tooltip.innerText = item.innerText;

                    tooltip.style.bottom = '-10000px';
                    tooltip.style.left = '-10000px';
                    document.body.appendChild(tooltip);
                    const {x, y} = item.getBoundingClientRect();

                    const bodyH = document.documentElement.clientHeight;

                    tooltip.style.bottom = bodyH - y + 'px';

                    const tooltipW = tooltip.clientWidth;
                    tooltip.style.left = x - tooltipW / 2 + item.clientWidth / 2 + 'px';
                    // document.body.removeChild(tooltip);
                    // item.appendChild(tooltip);
                }
            })

            item.addEventListener('mouseleave', function () {
                const dom = Array.from(document.getElementsByClassName('text_overflow_tooltip'));
                if (dom.length) {
                    dom.map(d => {
                        d.remove();
                    })
                }
            })
        })
    }

    const tipBtn1 = document.getElementById('tip1');
    tipBtn1.addEventListener('mouseover', function () {
        tipBtn1.getElementsByClassName('tip1_box').item(0).style.display = 'block';
    })
    tipBtn1.addEventListener('mouseleave', function () {
        tipBtn1.getElementsByClassName('tip1_box').item(0).style.display = 'none';
    })
    const tipBtn2 = Array.from(document.getElementsByClassName('tip2'));
    if (tipBtn2 && tipBtn2.length) {
        tipBtn2.forEach(item => {
            item.addEventListener('mouseover', function () {
                item.getElementsByClassName('tip2_box').item(0).style.display = 'block';
            })
            item.addEventListener('mouseleave', function () {
                item.getElementsByClassName('tip2_box').item(0).style.display = 'none';
            })
        })
    }
    // 顶部切换报告
    const reportTj = document.getElementById('reportTj');
    const reportBd = document.getElementById('reportBd');
    const reportCd = document.getElementById('reportCd');
    reportTj.onclick = function () {
        document.getElementsByClassName('logo_single_btn_active').item(0).classList.remove('logo_single_btn_active');
        reportTj.classList.add('logo_single_btn_active');

        // 直接跳转
        window.location.href = encodeURIComponent(this.getAttribute("href"));
    }
    reportBd.onclick = function () {
        document.getElementsByClassName('logo_single_btn_active').item(0).classList.remove('logo_single_btn_active');
        reportBd.classList.add('logo_single_btn_active');

        // 直接跳转
        window.location.href = encodeURIComponent(this.getAttribute("href"));
    }
    reportCd.onclick = function () {
        // 新打开页面
        window.open(encodeURIComponent(this.getAttribute("href")));
    }

    // 顶部按钮点击事件
    const operate_btns_btn = Array.from(document.getElementsByClassName('operate_btns_btn'));
    operate_btns_btn.forEach(item => {
        item.onclick = function () {
            // 新打开页面
            window.open(item.getAttribute("href"));
        }
    })

    let needWatch = true;
    let timer = null;
    // 菜单栏切换
    const sideNavBtns = Array.from(document.getElementsByClassName('single_nav'));
    sideNavBtns.map(s => {
        s.onclick = function (e) {
            needWatch = false;
            clearTimeout(timer);
            timer = null;
            const id = event.target.getAttribute('data-link');
            if (!id) {
                return
            }
            const target = document.getElementById(id);
            const targetRect = target.getBoundingClientRect();
            window.scrollTo({
                top: targetRect.top + window.scrollY - 90,
                behavior: 'smooth'
            });

            const nowActive = Array.from(document.querySelectorAll('.single_nav_active'))
            if (nowActive.length) {
                nowActive.forEach(item => {
                    item.classList.remove('single_nav_active');
                })
            }
            s.classList.add('single_nav_active');
            const afterActive = Array.from(document.querySelectorAll('.single_nav_active'));
            afterActive.forEach((item) => {
                if (item.className.includes('single_nav_child')) {
                    const father = findClosestPreviousSiblingWithClass(item, 'single_nav_father');
                    if (father) {
                        father.classList.add('single_nav_active')
                    }
                }
            })
            timer = setTimeout(() => {
                needWatch = true;
            }, 800)
        }
    })

    // 提示按钮
    const tipBtns = Array.from(document.getElementsByClassName('constrction_body_row_unnecessary'));
    tipBtns.map(tipBtn => {
        tipBtn.onmouseenter = function () {
            const div = document.createElement('div');
            div.innerHTML = '请检查结构标题与模板要求是否一致'
            div.classList.add('tip_box');
            tipBtn.appendChild(div);
        }
        tipBtn.onmouseleave = function () {
            tipBtn.removeChild(tipBtn.lastChild);
        }
    })


    // 页面设置中的打开按钮
    // const pageSettingDetailBox = document.getElementsByClassName('page_setting_detail_box');
    const openAnalyseBtns = Array.from(document.getElementsByClassName('open_analyse'));
    openAnalyseBtns.map(openAnalyseBtn => {
        openAnalyseBtn.onclick = function (e) {
            const closestParent = findClosestParent(e.target, 'fake_table_interval_tr');
            if (openAnalyseBtn.classList.contains('open_analyse_active')) {
                openAnalyseBtn.classList.remove('open_analyse_active')
                const pageSettingDetailBox = Array.from(closestParent.getElementsByClassName('page_setting_detail_box'));
                pageSettingDetailBox.map(pageSettingDetail => {
                    pageSettingDetail.style.display = 'none';
                });
            } else {
                // const isOtherOpen = Array.from(document.getElementsByClassName('open_analyse_active'));
                // if (isOtherOpen.length) {
                //     isOtherOpen[0].classList.remove('open_analyse_active')
                // }
                openAnalyseBtn.classList.add('open_analyse_active')
                const pageSettingDetailBox = Array.from(closestParent.getElementsByClassName('page_setting_detail_box'));
                pageSettingDetailBox.map(pageSettingDetail => {
                    pageSettingDetail.style.display = 'block';
                });
            }
        }
    })

    const findClosestParent = (element, className) => {
        while (element && element !== document) {
            if (element.classList && element.classList.contains(className)) {
                return element;
            }
            element = element.parentElement;
        }
        return null;
    }

    window.addEventListener('scroll', function () {
        setNavPosition()
        const navList = Array.from(document.getElementsByClassName('location_item'));
        const nowActive = Array.from(document.querySelectorAll('.single_nav_active'))
        navList.map(n => {
            if (!needWatch) {
                return;
            }
            const rect = n.getBoundingClientRect();
            if (rect.top > 200 && rect.top < 400) {
                if (nowActive.length) {
                    nowActive.forEach(item => {
                        item.classList.remove('single_nav_active');
                    })
                }
                const sideNavBtn = sideNavBtns.find(s => s.getAttribute('data-link') === n.id)
                if (sideNavBtn) {
                    sideNavBtn.classList.add('single_nav_active');
                    const afterActive = Array.from(document.querySelectorAll('.single_nav_active'));
                    afterActive.forEach((item) => {
                        if (item.className.includes('single_nav_child')) {
                            const father = findClosestPreviousSiblingWithClass(item, 'single_nav_father');
                            if (father) {
                                father.classList.add('single_nav_active')
                            }
                        }
                    })
                }
            }
        })
    })

    function findClosestPreviousSiblingWithClass(element, className) {
        let sibling = element.previousElementSibling;
        while (sibling) {
            if (sibling.classList.contains(className)) {
                return sibling;
            }
            sibling = sibling.previousElementSibling;
        }
        return null;
    }


</script>
</html>
