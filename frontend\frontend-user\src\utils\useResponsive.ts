import { ref, onMounted, onUnmounted } from 'vue'

// 断点定义
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const

export type Breakpoint = keyof typeof breakpoints

// 设备类型
export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
  orientation: 'portrait' | 'landscape'
  width: number
  height: number
}

// 响应式检测 Hook
export function useResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isDesktop = ref(false)
  const isTouch = ref(false)
  const orientation = ref<'portrait' | 'landscape'>('portrait')

  // 更新设备信息
  const updateDeviceInfo = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    
    // 基于屏幕宽度判断设备类型
    isMobile.value = windowWidth.value < breakpoints.md
    isTablet.value = windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg
    isDesktop.value = windowWidth.value >= breakpoints.lg
    
    // 检测触摸设备
    isTouch.value = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    // 检测屏幕方向
    orientation.value = windowWidth.value > windowHeight.value ? 'landscape' : 'portrait'
  }

  // 监听窗口大小变化
  let resizeTimeout: number
  const handleResize = () => {
    clearTimeout(resizeTimeout)
    resizeTimeout = window.setTimeout(() => {
      updateDeviceInfo()
    }, 100) // 防抖100ms
  }

  // 生命周期
  onMounted(() => {
    updateDeviceInfo()
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleResize)
    if (resizeTimeout) {
      clearTimeout(resizeTimeout)
    }
  })

  // 断点检查函数
  const isBreakpoint = (bp: Breakpoint) => {
    return windowWidth.value >= breakpoints[bp]
  }

  const isBreakpointOnly = (bp: Breakpoint) => {
    const bpKeys = Object.keys(breakpoints) as Breakpoint[]
    const currentIndex = bpKeys.indexOf(bp)
    const nextBp = bpKeys[currentIndex + 1]
    
    if (!nextBp) {
      return windowWidth.value >= breakpoints[bp]
    }
    
    return windowWidth.value >= breakpoints[bp] && windowWidth.value < breakpoints[nextBp]
  }

  // 设备信息对象
  const deviceInfo = ref<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouch: false,
    orientation: 'landscape',
    width: 0,
    height: 0
  })

  // 监听变化并更新设备信息
  const updateComputedDeviceInfo = () => {
    deviceInfo.value = {
      isMobile: isMobile.value,
      isTablet: isTablet.value,
      isDesktop: isDesktop.value,
      isTouch: isTouch.value,
      orientation: orientation.value,
      width: windowWidth.value,
      height: windowHeight.value
    }
  }

  // 初始更新
  updateComputedDeviceInfo()

  return {
    // 响应式数据
    windowWidth,
    windowHeight,
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
    orientation,
    deviceInfo,
    
    // 工具函数
    isBreakpoint,
    isBreakpointOnly,
    updateDeviceInfo: () => {
      updateDeviceInfo()
      updateComputedDeviceInfo()
    }
  }
}

// 媒体查询Hook
export function useMediaQuery(query: string) {
  const matches = ref(false)
  let mediaQuery: MediaQueryList | null = null

  const updateMatches = () => {
    if (mediaQuery) {
      matches.value = mediaQuery.matches
    }
  }

  onMounted(() => {
    if (typeof window !== 'undefined') {
      mediaQuery = window.matchMedia(query)
      matches.value = mediaQuery.matches
      
      // 添加监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', updateMatches)
      } else {
        // 兼容旧版本浏览器
        mediaQuery.addListener(updateMatches)
      }
    }
  })

  onUnmounted(() => {
    if (mediaQuery) {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', updateMatches)
      } else {
        // 兼容旧版本浏览器
        mediaQuery.removeListener(updateMatches)
      }
    }
  })

  return {
    matches
  }
}

// 常用媒体查询预设
export const useCommonMediaQueries = () => {
  const isMobileQuery = useMediaQuery(`(max-width: ${breakpoints.md - 1}px)`)
  const isTabletQuery = useMediaQuery(`(min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`)
  const isDesktopQuery = useMediaQuery(`(min-width: ${breakpoints.lg}px)`)
  const isPortraitQuery = useMediaQuery('(orientation: portrait)')
  const isLandscapeQuery = useMediaQuery('(orientation: landscape)')
  const isPrefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)')
  const isDarkMode = useMediaQuery('(prefers-color-scheme: dark)')

  return {
    isMobile: isMobileQuery.matches,
    isTablet: isTabletQuery.matches,
    isDesktop: isDesktopQuery.matches,
    isPortrait: isPortraitQuery.matches,
    isLandscape: isLandscapeQuery.matches,
    prefersReducedMotion: isPrefersReducedMotion.matches,
    prefersDarkMode: isDarkMode.matches
  }
}

// 获取视口信息
export function useViewport() {
  const { windowWidth, windowHeight, orientation } = useResponsive()
  
  const viewportInfo = ref({
    width: 0,
    height: 0,
    ratio: 0,
    isSquare: false,
    isWide: false,
    isTall: false
  })

  const updateViewportInfo = () => {
    const width = windowWidth.value
    const height = windowHeight.value
    const ratio = width / height

    viewportInfo.value = {
      width,
      height,
      ratio,
      isSquare: Math.abs(ratio - 1) < 0.1,
      isWide: ratio > 1.2,
      isTall: ratio < 0.8
    }
  }

  // 监听变化
  onMounted(() => {
    updateViewportInfo()
    window.addEventListener('resize', updateViewportInfo)
    window.addEventListener('orientationchange', updateViewportInfo)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateViewportInfo)
    window.removeEventListener('orientationchange', updateViewportInfo)
  })

  return {
    viewportInfo,
    windowWidth,
    windowHeight,
    orientation
  }
} 