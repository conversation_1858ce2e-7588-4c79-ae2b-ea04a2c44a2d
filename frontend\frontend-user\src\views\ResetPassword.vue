<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <AppNavbar :isAuthPage="true" />

    <!-- 主要内容 -->
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div class="text-center">
          <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
            <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-bold text-gray-900 dark:text-white">
            设置新密码
          </h2>
          <p class="mt-2 text-center text-gray-600 dark:text-gray-300">
            请为您的账户设置一个新的安全密码
          </p>
        </div>

        <!-- 重置密码表单 -->
        <BaseCard>
          <form class="space-y-6" @submit.prevent="handleResetPassword">
            <!-- 新密码 -->
            <div>
              <BaseInput
                v-model="formData.newPassword"
                type="password"
                label="新密码"
                placeholder="8-20个字符，包含字母和数字"
                :error="errors.newPassword"
                required
                @input="validatePassword"
              />
              
              <!-- 密码强度指示器 -->
              <div v-if="formData.newPassword" class="mt-2">
                <div class="flex justify-between items-center text-sm mb-1">
                  <span class="text-gray-600 dark:text-gray-300">密码强度</span>
                  <span :class="getPasswordStrengthColor()">{{ getPasswordStrengthText() }}</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div :class="['h-2 rounded-full transition-all', getPasswordStrengthColor()]" 
                       :style="{ width: `${passwordStrength * 25}%` }"></div>
                </div>
              </div>
            </div>

            <!-- 确认密码 -->
            <BaseInput
              v-model="formData.confirmPassword"
              type="password"
              label="确认新密码"
              placeholder="请再次输入新密码"
              :error="errors.confirmPassword"
              required
              @input="validateConfirmPassword"
            />

            <!-- 提交按钮 -->
            <BaseButton 
              type="submit" 
              :disabled="isLoading || !isFormValid"
              :loading="isLoading"
              variant="primary"
              class="w-full"
            >
              {{ isLoading ? '重置中...' : '确认重置密码' }}
            </BaseButton>
          </form>
        </BaseCard>

        <!-- 安全提示 -->
        <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                密码安全建议
              </h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <ul class="list-disc list-inside space-y-1">
                  <li>使用8位以上的强密码，包含字母、数字和特殊字符</li>
                  <li>不要使用容易猜测的个人信息作为密码</li>
                  <li>定期更换密码，提高账户安全性</li>
                  <li>不要在多个网站使用相同的密码</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-500 dark:text-gray-400">
        <p>&copy; 2024 Word文档分析服务. 保留所有权利.</p>
      </div>
    </footer>

    <!-- 重置成功模态框 -->
    <div v-if="showSuccessModal" class="modal show">
      <BaseCard class="modal-content">
        <div class="text-center">
          <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-4">
            <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">密码重置成功</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-6">
            您的密码已成功重置，请使用新密码登录。
          </p>
          <BaseButton @click="goToLogin" variant="primary">
            前往登录
          </BaseButton>
        </div>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import BaseCard from '@/components/BaseCard.vue'
import BaseInput from '@/components/BaseInput.vue'
import BaseButton from '@/components/BaseButton.vue'
import AppNavbar from '@/components/AppNavbar.vue'
import { $notify } from '@/utils/useNotifications'

const router = useRouter()
const themeStore = useThemeStore()

// 表单状态
const isLoading = ref(false)
const showSuccessModal = ref(false)
const passwordStrength = ref(0)

// 密码显示状态
const showPassword = reactive({
  new: false,
  confirm: false
})

// 表单数据
const formData = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 错误信息
const errors = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证状态
const isFormValid = computed(() => {
  return formData.newPassword.length >= 8 && 
         formData.confirmPassword === formData.newPassword &&
         !errors.newPassword && 
         !errors.confirmPassword
})

// 切换密码可见性
const togglePassword = (type: 'new' | 'confirm') => {
  showPassword[type] = !showPassword[type]
}

// 计算密码强度
const calculatePasswordStrength = (password: string) => {
  let strength = 0
  
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/\d/.test(password)) strength++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++
  
  return Math.min(strength, 4)
}

// 获取密码强度文本
const getPasswordStrengthText = () => {
  const texts = ['很弱', '弱', '中等', '强', '很强']
  return texts[passwordStrength.value] || '很弱'
}

// 获取密码强度颜色
const getPasswordStrengthColor = () => {
  const colors = [
    'text-red-500 bg-red-500',
    'text-orange-500 bg-orange-500', 
    'text-yellow-500 bg-yellow-500',
    'text-blue-500 bg-blue-500',
    'text-green-500 bg-green-500'
  ]
  return colors[passwordStrength.value] || colors[0]
}

// 验证新密码
const validatePassword = () => {
  errors.newPassword = ''
  
  if (!formData.newPassword) {
    errors.newPassword = '请输入新密码'
    passwordStrength.value = 0
    return
  }
  
  if (formData.newPassword.length < 8) {
    errors.newPassword = '密码长度不能少于8位'
  } else if (formData.newPassword.length > 20) {
    errors.newPassword = '密码长度不能超过20位'
  }
  
  passwordStrength.value = calculatePasswordStrength(formData.newPassword)
  
  // 如果确认密码已填写，重新验证
  if (formData.confirmPassword) {
    validateConfirmPassword()
  }
}

// 验证确认密码
const validateConfirmPassword = () => {
  errors.confirmPassword = ''
  
  if (!formData.confirmPassword) {
    errors.confirmPassword = '请确认新密码'
    return
  }
  
  if (formData.newPassword !== formData.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  }
}

// 处理重置密码
const handleResetPassword = async () => {
  // 重新验证
  validatePassword()
  validateConfirmPassword()
  
  if (!isFormValid.value) {
    return
  }
  
  isLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 显示成功模态框
    showSuccessModal.value = true
    
  } catch (error) {
    console.error('重置密码失败:', error)
    $notify.error('重置失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 前往登录页面
const goToLogin = () => {
  router.push('/auth')
}
</script> 