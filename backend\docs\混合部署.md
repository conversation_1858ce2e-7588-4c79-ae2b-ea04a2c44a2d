# Word文档分析服务 - Windows云服务器混合部署指南

## 概述

混合部署架构结合了容器化和原生部署的优势，在Windows云服务器上实现最佳的性能与稳定性平衡。

### 部署架构说明

**容器化服务 (Docker)**:
- PostgreSQL 17.5 数据库
- Redis 7.x 缓存
- Nginx 反向代理

**原生服务 (Windows)**:
- FastAPI 应用
- Word COM 接口
- 文档处理服务

### 架构优势
- **性能优化**: 95%的原生性能表现
- **运维便利**: 容器化管理数据库和代理
- **稳定可靠**: Word COM接口原生运行
- **成本效益**: 资源使用最优化

## 第一部分：系统环境准备

### 1.1 Windows系统配置

#### 1.1.1 启用Windows容器功能
```powershell
# 启用Hyper-V（如果使用Windows容器）
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# 启用容器功能
Enable-WindowsOptionalFeature -Online -FeatureName Containers -All

# 重启系统
Restart-Computer
```

#### 1.1.2 防火墙配置
```powershell
# 允许Docker相关端口
New-NetFirewallRule -DisplayName "PostgreSQL-Docker" -Direction Inbound -Protocol TCP -LocalPort 5432 -Action Allow
New-NetFirewallRule -DisplayName "Redis-Docker" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow
New-NetFirewallRule -DisplayName "Nginx-Docker" -Direction Inbound -Protocol TCP -LocalPort 80,443 -Action Allow
New-NetFirewallRule -DisplayName "FastAPI-Native" -Direction Inbound -Protocol TCP -LocalPort 8000 -Action Allow
```

### 1.2 创建项目目录结构
```powershell
# 创建项目根目录
New-Item -ItemType Directory -Path "C:\Services\WordService" -Force
cd C:\Services\WordService

# 创建子目录
New-Item -ItemType Directory -Path "docker" -Force
New-Item -ItemType Directory -Path "docker\postgres" -Force
New-Item -ItemType Directory -Path "docker\redis" -Force
New-Item -ItemType Directory -Path "docker\nginx" -Force
New-Item -ItemType Directory -Path "data" -Force
New-Item -ItemType Directory -Path "logs" -Force
New-Item -ItemType Directory -Path "backups" -Force
```

## 第二部分：Docker环境安装

### 2.1 安装Docker Desktop

#### 2.1.1 下载Docker Desktop
```powershell
# 下载Docker Desktop for Windows
$dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
$dockerOutput = "C:\temp\DockerDesktopInstaller.exe"
Invoke-WebRequest -Uri $dockerUrl -OutFile $dockerOutput
```

#### 2.1.2 安装Docker Desktop
```powershell
# 静默安装Docker Desktop
Start-Process -FilePath "C:\temp\DockerDesktopInstaller.exe" -ArgumentList "install", "--quiet" -Wait

# 启动Docker Desktop（需要重启后）
Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
```

#### 2.1.3 验证Docker安装
```powershell
# 验证Docker版本
docker --version
docker-compose --version

# 测试Docker运行
docker run hello-world
```

## 第三部分：容器化服务部署

### 3.1 创建Docker Compose配置

#### 3.1.1 主配置文件
创建 `C:\Services\WordService\docker-compose.yml`：

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:17.5
    container_name: wordservice_postgres
    restart: always
    environment:
      POSTGRES_DB: word_service
      POSTGRES_USER: word_service_user
      POSTGRES_PASSWORD: word_service_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U word_service_user -d word_service"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: wordservice_redis
    restart: always
    command: >
      redis-server
      --requirepass redis_password_here
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "redis_password_here", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: wordservice_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
      - ./data/ssl:/etc/nginx/ssl:ro
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  default:
    name: wordservice_network
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
```

### 3.2 PostgreSQL配置

#### 3.2.1 初始化脚本
创建 `C:\Services\WordService\docker\postgres\init\01-init.sql`：

```sql
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档表
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    status VARCHAR(20) DEFAULT 'uploaded',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建分析结果表
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL,
    result_data JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_analysis_results_document_id ON analysis_results(document_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_status ON analysis_results(status);
CREATE INDEX IF NOT EXISTS idx_analysis_results_type ON analysis_results(analysis_type);
```

### 3.3 Nginx配置

#### 3.3.1 主配置文件
创建 `C:\Services\WordService\docker\nginx\nginx.conf`：

```nginx
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                   '$status $body_bytes_sent "$http_referer" '
                   '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上传限制
    client_max_body_size 50M;
    client_body_buffer_size 1M;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # 超时配置
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
```

#### 3.3.2 站点配置
创建 `C:\Services\WordService\docker\nginx\conf.d\wordservice.conf`：

```nginx
# 上游服务器配置（原生FastAPI应用）
upstream wordservice_backend {
    server host.docker.internal:8000;
    keepalive 32;
}

# HTTP服务器配置
server {
    listen 80;
    server_name localhost;
    
    # 安全头
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 健康检查端点
    location /health {
        proxy_pass http://wordservice_backend/health;
        access_log off;
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://wordservice_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文档上传代理
    location /upload {
        proxy_pass http://wordservice_backend/upload;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 上传优化
        proxy_request_buffering off;
        proxy_buffering off;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 静态文件处理
    location /static/ {
        root /var/www;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 根路径重定向
    location / {
        proxy_pass http://wordservice_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 第四部分：原生服务部署

### 4.1 Python环境配置

#### 4.1.1 安装Python 3.12
```powershell
# 下载Python 3.12
$pythonUrl = "https://www.python.org/ftp/python/3.12.0/python-3.12.0-amd64.exe"
$pythonOutput = "C:\temp\python-3.12.0-amd64.exe"
Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonOutput

# 静默安装
$pythonArgs = @("/quiet", "InstallAllUsers=1", "PrependPath=1", "Include_pip=1")
Start-Process -FilePath $pythonOutput -ArgumentList $pythonArgs -Wait
```

#### 4.1.2 创建虚拟环境
```powershell
# 切换到项目目录
cd C:\Services\WordService

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 升级pip
python -m pip install --upgrade pip
```

### 4.2 Word COM接口配置

#### 4.2.1 安装Microsoft Word
确保安装完整的Microsoft Office套件或至少Word组件，以支持COM接口。

#### 4.2.2 验证Word COM接口
```powershell
# 验证Word COM接口
$word = New-Object -ComObject Word.Application
$word.Visible = $false
$doc = $word.Documents.Add()
$doc.Content.Text = "Test Document"
$doc.Close()
$word.Quit()
Write-Host "Word COM interface is working correctly"
```

### 4.3 应用配置

#### 4.3.1 配置文件
编辑 `backend/config.yaml`：

```yaml
# 应用配置
app:
  name: "Word Document Analysis Service"
  version: "1.0.0"
  debug: false
  secret_key: "your-secret-key-here"
  
# 服务器配置
server:
  host: "127.0.0.1"  # 只监听本地，通过Nginx代理
  port: 8000
  workers: 1  # Windows COM接口建议单进程
  
# 数据库配置（连接Docker容器）
database:
  url: "postgresql+asyncpg://word_service_user:word_service_password@localhost:5432/word_service"
  pool_size: 10
  max_overflow: 20
  
# Redis配置（连接Docker容器）
redis:
  url: "redis://:redis_password_here@localhost:6379/0"
  max_connections: 10
  
# 文件配置
upload:
  max_size: 52428800  # 50MB
  allowed_extensions: [".docx", ".doc"]
  upload_dir: "C:\\Services\\WordService\\data\\uploads"
  
# Word COM配置
word:
  visible: false
  display_alerts: false
  timeout: 60
```

## 第五部分：启动和管理

### 5.1 启动容器化服务

#### 5.1.1 启动Docker服务
```powershell
# 切换到项目目录
cd C:\Services\WordService

# 启动所有容器服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
```

#### 5.1.2 验证容器服务
```powershell
# 验证PostgreSQL
docker exec wordservice_postgres pg_isready -U word_service_user -d word_service

# 验证Redis
docker exec wordservice_redis redis-cli -a redis_password_here ping

# 验证Nginx
curl http://localhost/health
```

### 5.2 启动原生应用

#### 5.2.1 启动FastAPI应用
```powershell
# 激活虚拟环境
cd C:\Services\WordService
.\venv\Scripts\Activate.ps1

# 切换到backend目录
cd backend

# 启动应用
python -m uvicorn app.main:app --host 127.0.0.1 --port 8000
```

#### 5.2.2 创建Windows服务
```powershell
# 安装nssm
$nssmUrl = "https://nssm.cc/release/nssm-2.24.zip"
$nssmOutput = "C:\temp\nssm-2.24.zip"
Invoke-WebRequest -Uri $nssmUrl -OutFile $nssmOutput
Expand-Archive -Path $nssmOutput -DestinationPath "C:\temp\"
Copy-Item "C:\temp\nssm-2.24\win64\nssm.exe" "C:\Windows\System32\"

# 创建WordService服务
nssm install WordService "C:\Services\WordService\venv\Scripts\python.exe"
nssm set WordService Parameters "-m uvicorn app.main:app --host 127.0.0.1 --port 8000"
nssm set WordService AppDirectory "C:\Services\WordService\backend"
nssm set WordService Description "Word Document Analysis Service"
nssm set WordService Start SERVICE_AUTO_START

# 启动服务
Start-Service WordService
```

## 第六部分：监控和维护

### 6.1 健康检查

#### 6.1.1 服务健康检查脚本
创建 `C:\Services\WordService\scripts\health_check.ps1`：

```powershell
# 健康检查脚本
$services = @{
    "PostgreSQL" = "http://localhost:5432"
    "Redis" = "localhost:6379"
    "Nginx" = "http://localhost"
    "FastAPI" = "http://localhost:8000/health"
}

foreach ($service in $services.GetEnumerator()) {
    try {
        if ($service.Value -like "http*") {
            $response = Invoke-RestMethod -Uri $service.Value -TimeoutSec 5
            Write-Host "$($service.Key): OK"
        } else {
            $connection = Test-NetConnection -ComputerName localhost -Port ($service.Value -split ":")[-1] -WarningAction SilentlyContinue
            Write-Host "$($service.Key): $(if($connection.TcpTestSucceeded){'OK'}else{'FAIL'})"
        }
    } catch {
        Write-Host "$($service.Key): FAIL - $($_.Exception.Message)"
    }
}
```

### 6.2 备份策略

#### 6.2.1 数据库备份
```powershell
# 数据库备份脚本
$backupDir = "C:\Services\WordService\backups"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# Docker PostgreSQL备份
docker exec wordservice_postgres pg_dump -U word_service_user -d word_service > "$backupDir\db_backup_$timestamp.sql"

# 压缩备份
Compress-Archive -Path "$backupDir\db_backup_$timestamp.sql" -DestinationPath "$backupDir\db_backup_$timestamp.zip"
Remove-Item "$backupDir\db_backup_$timestamp.sql"
```

## 第七部分：故障排除

### 7.1 常见问题

#### 7.1.1 容器启动问题
```powershell
# 检查Docker状态
docker info

# 检查容器日志
docker-compose logs postgres
docker-compose logs redis
docker-compose logs nginx

# 重启容器
docker-compose restart
```

#### 7.1.2 网络连接问题
```powershell
# 检查端口占用
netstat -an | findstr ":8000"
netstat -an | findstr ":5432"
netstat -an | findstr ":6379"

# 测试容器网络
docker network ls
docker network inspect wordservice_network
```

## 总结

混合部署架构提供了最佳的性能与管理平衡：

### 优势
- **高性能**: Word COM接口原生运行，无性能损失
- **易管理**: 数据库和代理容器化，便于升级和维护
- **高可用**: 容器自动重启，服务稳定性高
- **安全性**: 容器隔离，降低安全风险

### 适用场景
- 生产环境部署
- 需要Word COM接口的Windows服务器
- 要求高性能和稳定性的企业应用
- 混合云部署架构

按照本指南部署，您将获得一个稳定、高性能的Word文档分析服务。 