"""
Word文档分析服务 - 主应用程序入口
"""

import sys
import os
from pathlib import Path

# 将项目根目录添加到sys.path
# 这对于直接运行此文件进行调试非常重要
root_dir = Path(__file__).resolve().parent.parent
if str(root_dir) not in sys.path:
    sys.path.append(str(root_dir))

import asyncio
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException
import time

# 设置时区，确保所有时间操作都基于正确的时区
# 这对于数据库时间戳、日志记录和API响应至关重要
os.environ['TZ'] = 'Asia/Shanghai'
if sys.platform != 'win32':
    time.tzset()

# 导入核心模块
from app.core.config import settings
from app.core.logging import setup_logging, logger
from app.core.exceptions import (
    DocumentAnalysisException,
    COMInterfaceException,
    TaskException
)

# 导入API路由
from app.api.v1 import health, tasks, documents, images, system, auth, websocket, payments, paper_check


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Word文档分析服务启动中...")
    
    # 初始化数据库连接
    try:
        from app.database.connection import init_database, get_database_session
        from app.database.init_db import create_all_tables
        
        await init_database()
        
        # 创建表结构
        session = await get_database_session()
        try:
            await create_all_tables(session)
        finally:
            await session.close()
        
        logger.info("数据库初始化和表结构验证完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise
    
    # 初始化Redis连接
    try:
        from app.core.cache import init_redis
        await init_redis()
        logger.info("Redis连接初始化完成")
    except Exception as e:
        logger.warning(f"Redis连接初始化失败，将在无缓存模式下运行: {str(e)}")
        # 不抛出异常，允许应用继续启动
    
    # 初始化任务管理器
    try:
        from app.tasks.manager import task_manager
        await task_manager.initialize()
        logger.info("任务管理器初始化完成")
    except Exception as e:
        logger.error(f"任务管理器初始化失败: {str(e)}")
        raise
    
    logger.info("Word文档分析服务启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("Word文档分析服务关闭中...")
    
    # 停止任务管理器
    try:
        from app.tasks.manager import task_manager
        await task_manager.shutdown()
        logger.info("任务管理器已停止")
    except Exception as e:
        logger.error(f"任务管理器停止失败: {str(e)}")
    
    # 关闭数据库连接
    try:
        from app.database.connection import close_database
        await close_database()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"数据库连接关闭失败: {str(e)}")
    
    # 关闭Redis连接
    try:
        from app.core.cache import close_redis
        await close_redis()
        logger.info("Redis连接已关闭")
    except Exception as e:
        logger.error(f"Redis连接关闭失败: {str(e)}")
    
    logger.info("Word文档分析服务已关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title="Word文档分析服务",
    description="基于COM接口的Word文档内容提取和论文检测API服务",
    version="1.0.0",
    docs_url="/docs" if settings.enable_api_docs else None,
    redoc_url="/redoc" if settings.enable_api_docs else None,
    lifespan=lifespan
)

# 中间件配置
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def request_timing_middleware(request: Request, call_next):
    """请求时间统计中间件"""
    start_time = time.time()
    
    # 记录请求开始
    logger.info(
        "请求开始",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else None
    )
    
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # 记录请求完成
    logger.info(
        "请求完成",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
        client_ip=request.client.host if request.client else None
    )
    
    return response


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.warning(
        "HTTP异常",
        status_code=exc.status_code,
        detail=exc.detail,
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "code": exc.status_code,
            "message": exc.detail,
            "data": None,
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


def _serialize_error_for_json(obj):
    """将错误对象序列化为JSON安全的格式"""
    if isinstance(obj, bytes):
        try:
            return obj.decode('utf-8')
        except UnicodeDecodeError:
            return str(obj)[2:-1]  # 移除 b'...' 格式
    elif isinstance(obj, dict):
        return {k: _serialize_error_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_serialize_error_for_json(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(_serialize_error_for_json(item) for item in obj)
    else:
        return obj


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    # 安全序列化错误信息
    safe_errors = _serialize_error_for_json(exc.errors())
    
    logger.warning(
        "请求验证失败",
        errors=safe_errors,
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "code": 422,
            "message": "请求参数验证失败",
            "data": {"errors": safe_errors},
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(DocumentAnalysisException)
async def document_analysis_exception_handler(request: Request, exc: DocumentAnalysisException):
    """文档分析异常处理器"""
    logger.error(
        "文档分析异常",
        error_code=exc.code,
        message=str(exc),
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "code": exc.code,
            "message": str(exc),
            "data": None,
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(COMInterfaceException)
async def com_interface_exception_handler(request: Request, exc: COMInterfaceException):
    """COM接口异常处理器"""
    logger.error(
        "COM接口异常",
        error_code=exc.code,
        message=str(exc),
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=503,
        content={
            "success": False,
            "code": exc.code,
            "message": str(exc),
            "data": None,
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(TaskException)
async def task_exception_handler(request: Request, exc: TaskException):
    """任务异常处理器"""
    logger.error(
        "任务异常",
        error_code=exc.code,
        message=str(exc),
        url=str(request.url),
        method=request.method
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "code": exc.code,
            "message": str(exc),
            "data": None,
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(
        "未知异常",
        error_type=type(exc).__name__,
        message=str(exc),
        url=str(request.url),
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "code": 50000,
            "message": "服务器内部错误",
            "data": None,
            "timestamp": int(time.time()),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


# 注册API路由
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["用户认证"]
)

app.include_router(
    health.router,
    prefix="/health",
    tags=["健康检查"]
)

app.include_router(
    tasks.router,
    prefix="/api/v1/tasks",
    tags=["任务管理"]
)

app.include_router(
    documents.router,
    prefix="/api/v1/documents",
    tags=["文档处理"]
)

app.include_router(
    images.router,
    prefix="/api/v1/images",
    tags=["图片资源"]
)

app.include_router(
    system.router,
    prefix="/api/v1/system",
    tags=["系统管理"]
)

app.include_router(
    websocket.router,
    tags=["WebSocket"]
)

app.include_router(
    payments.router,
    prefix="/api/v1/payments",
    tags=["支付"]
)

app.include_router(
    paper_check.router,
    prefix="/api/v1",
    tags=["论文检测"]
)


@app.get("/", include_in_schema=False)
async def root():
    """根路径重定向到API文档"""
    return {
        "message": "Word文档分析服务",
        "version": "1.0.0",
        "docs_url": "/docs",
        "health_check": "/health"
    }


if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 运行应用
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_config=None,  # 使用自定义日志配置
        access_log=False  # 禁用默认访问日志
    ) 