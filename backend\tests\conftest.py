"""Test configuration"""
import pytest
import json
import tempfile
from pathlib import Path
from fastapi.testclient import TestClient
from app.main import app

@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)

@pytest.fixture
def rule_engine():
    """Rule engine fixture"""
    from app.checkers.rule_engine import RuleEngine
    from app.services.document_analyzer import CHECK_FUNCTIONS
    return RuleEngine(CHECK_FUNCTIONS)

@pytest.fixture
def sample_rule_config():
    """Sample rule configuration for testing"""
    return {
        "metadata": {
            "standard_id": "test_standard",
            "name": "测试标准",
            "version": "1.0.0",
            "description": "用于测试的规则配置"
        },
        "definitions": {
            "properties": {
                "font_sizes": {
                    "body_text": {
                        "value": "小四",
                        "point_size": 12,
                        "tolerance": 0.5,
                        "required": True,
                        "errorMessage": "正文字号应为小四号(12pt)",
                        "severity": "error"
                    }
                },
                "font_families": {
                    "chinese_body": {
                        "value": "宋体",
                        "alternatives": ["SimSun"],
                        "required": True,
                        "errorMessage": "正文中文字体应使用宋体",
                        "severity": "error"
                    }
                }
            },
            "styles": {
                "body_text_default": {
                    "font_family_chinese": {"$ref": "#/definitions/properties/font_families/chinese_body"},
                    "font_size": {"$ref": "#/definitions/properties/font_sizes/body_text"},
                    "alignment": {"value": "justify"}
                }
            }
        },
        "rules": {
            "format": {
                "body_text": {
                    "name": "正文格式检查",
                    "severity": "error",
                    "check_function": "check_text_format",
                    "parameters": {"$ref": "#/definitions/styles/body_text_default"}
                }
            }
        },
        "execution_plan": [
            {
                "phase": "format_check",
                "description": "格式检查阶段",
                "rules": [{"$ref": "#/rules/format/body_text"}]
            }
        ]
    }

@pytest.fixture
def sample_document_data():
    """Sample document data for testing"""
    from app.services.document_processor import DocumentData
    return DocumentData(
        file_path="test.docx",
        doc_info={
            "page_count": 1,
            "word_count": 20,
            "creation_time": "2024-01-01T00:00:00"
        },
        content_stats={
            "total_paragraphs": 2,
            "total_headings": 1
        },
        elements=[
            {"type": "paragraph", "text": "第一段内容", "style": {"font_family": "宋体", "font_size": 12}},
            {"type": "paragraph", "text": "第二段内容", "style": {"font_family": "宋体", "font_size": 12}},
            {"type": "heading", "text": "第一章 引言", "level": 1, "style": {"font_family": "黑体", "font_size": 16}}
        ]
    )

@pytest.fixture
def test_docx_path():
    """Get path to test document"""
    # 使用现有的测试文档
    test_files = list(Path("backend/data/uploads").rglob("*test.docx"))
    if test_files:
        return str(test_files[0])
    return None