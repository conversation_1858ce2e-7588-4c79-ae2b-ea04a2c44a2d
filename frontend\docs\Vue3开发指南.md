# Word文档分析服务 - Vue 3前端开发指南

## 📋 项目概述

基于已完成的后端API服务，使用Vue 3 + TypeScript技术栈开发前端应用。后端服务已经100%完成验证，具备生产环境部署能力，为前端开发提供了稳定的API基础。

## 🎯 推荐技术栈

### 核心技术栈
```
Vue 3.3+ + TypeScript + Vite
├── 状态管理: Pinia
├── UI组件库: 自定义组件库 (Tailwind CSS)
├── 路由管理: Vue Router v4
├── HTTP客户端: Axios + VueUse
├── 样式方案: Tailwind CSS
├── 表单处理: VeeValidate + Yup
├── 图表库: Chart.js
├── 文件上传: 自定义上传组件
├── 构建工具: Vite 5.x
└── 开发工具: Vue DevTools
```

### 详细依赖包

#### 核心依赖
```json
{
  "dependencies": {
    "vue": "^3.5.17",
    "vue-router": "^4.5.1",
    "pinia": "^3.0.3",
    "axios": "^1.10.0",
    "@vueuse/core": "^10.5.0",
    "tailwindcss": "^3.4.0",
    "chart.js": "^4.5.0",
    "vee-validate": "^4.11.8",
    "yup": "^1.3.3",
    "dayjs": "^1.11.10"
  }
}
```

#### 开发依赖
```json
{
  "devDependencies": {
    "@vitejs/plugin-vue": "^6.0.0",
    "@vue/tsconfig": "^0.7.0",
    "typescript": "~5.8.0",
    "vite": "^7.0.0",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.21",
    "postcss": "^8.5.6",
    "@tailwindcss/forms": "^0.5.7",
    "@tailwindcss/typography": "^0.5.10",
    "@tailwindcss/aspect-ratio": "^0.4.2",
    "@types/node": "^22.15.32",
    "eslint": "^9.29.0",
    "@vue/eslint-config-typescript": "^14.5.1",
    "prettier": "3.5.3",
    "prettier-plugin-tailwindcss": "^0.5.7",
    "vitest": "^0.34.6",
    "@vue/test-utils": "^2.4.2"
  }
}
```

## 🏗️ 项目结构

```
frontend/
├── public/                     # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/                    # API接口层
│   │   ├── client.ts          # HTTP客户端配置
│   │   ├── auth.ts            # 认证相关API
│   │   ├── documents.ts       # 文档相关API
│   │   ├── tasks.ts           # 任务相关API
│   │   └── types.ts           # API类型定义
│   ├── components/             # 通用组件
│   │   ├── ui/                # 基础UI组件
│   │   ├── layout/            # 布局组件
│   │   ├── forms/             # 表单组件
│   │   └── charts/            # 图表组件
│   ├── views/                  # 页面组件
│   │   ├── auth/              # 认证页面
│   │   ├── dashboard/         # 仪表盘
│   │   ├── documents/         # 文档管理
│   │   └── tasks/             # 任务管理
│   ├── composables/            # 组合式函数
│   │   ├── useAuth.ts         # 认证相关
│   │   ├── useDocuments.ts    # 文档相关
│   │   ├── useTasks.ts        # 任务相关
│   │   └── useUpload.ts       # 文件上传
│   ├── stores/                 # Pinia状态管理
│   │   ├── auth.ts            # 认证状态
│   │   ├── documents.ts       # 文档状态
│   │   ├── tasks.ts           # 任务状态
│   │   └── app.ts             # 应用状态
│   ├── router/                 # 路由配置
│   ├── utils/                  # 工具函数
│   ├── styles/                 # 样式文件
│   │   ├── main.css           # 主样式文件
│   │   └── tailwind.css       # Tailwind CSS入口
│   ├── types/                  # TypeScript类型
│   ├── App.vue                 # 根组件
│   └── main.ts                # 入口文件
├── tests/                      # 测试文件
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js          # Tailwind CSS配置
├── postcss.config.js           # PostCSS配置
└── README.md
```

## 🚀 快速开始

### 1. 项目初始化

```bash
# 创建Vue 3项目
npm create vue@latest word-analysis-frontend

# 选择配置项：
# ✅ TypeScript
# ✅ Router
# ✅ Pinia
# ✅ ESLint
# ✅ Prettier

cd word-analysis-frontend
npm install
```

### 2. 安装核心依赖

```bash
# UI组件库
npm install ant-design-vue @ant-design/icons-vue

# HTTP客户端和工具
npm install axios @vueuse/core

# 表单处理
npm install vee-validate yup

# 图表库
npm install vue-echarts echarts

# 文件上传
npm install vue-upload-component

# 日期处理
npm install dayjs

# CSS框架 - Tailwind CSS
npm install -D tailwindcss autoprefixer postcss
npm install -D @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio

# 自动导入
npm install -D unplugin-vue-components unplugin-auto-import

# Prettier插件
npm install -D prettier-plugin-tailwindcss
```

### 3. 配置文件设置

#### Vite配置 (vite.config.ts)
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [AntDesignVueResolver()],
      imports: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
      dts: true
    }),
    Components({
      resolvers: [AntDesignVueResolver({ importStyle: false })],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    }
  }
})
```

#### Tailwind CSS配置 (tailwind.config.js)
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 与Element Plus主题色保持一致
        primary: {
          50: '#eff6ff',
          100: '#dbeafe', 
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#409eff', // Element Plus主色
          600: '#337ecc',
          700: '#2d70b3',
          800: '#1e40af',
          900: '#1e3a8a'
        },
        success: '#67c23a',
        warning: '#e6a23c',
        danger: '#f56c6c',
        info: '#909399'
      },
      fontFamily: {
        sans: [
          'PingFang SC',
          'Helvetica Neue', 
          'Helvetica',
          'Arial',
          'sans-serif'
        ]
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio')
  ],
  // 避免与Element Plus样式冲突
  corePlugins: {
    preflight: false
  }
}
```

#### PostCSS配置 (postcss.config.js)
```javascript
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

#### 样式入口文件 (src/styles/tailwind.css)
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .card-hover {
    @apply hover:shadow-lg transition-shadow duration-200;
  }
}

/* 工具类扩展 */
@layer utilities {
  .text-ellipsis {
    @apply truncate;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
```

#### 主样式文件 (src/styles/main.css)
```css
@import './tailwind.css';

/* 全局样式 */
html, body {
  @apply h-full bg-gray-50;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#app {
  @apply h-full;
}

/* Element Plus样式重写 */
.el-button--primary {
  @apply !bg-primary-500 hover:!bg-primary-600 focus:!bg-primary-600;
}

.el-button--success {
  @apply !bg-green-500 hover:!bg-green-600;
}

.el-button--warning {
  @apply !bg-yellow-500 hover:!bg-yellow-600;
}

.el-button--danger {
  @apply !bg-red-500 hover:!bg-red-600;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  @apply w-2;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded hover:bg-gray-500;
}
```

#### Prettier配置 (.prettierrc)
```json
{
  "singleQuote": true,
  "semi": false,
  "trailingComma": "es5",
  "tabWidth": 2,
  "printWidth": 80,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## 💻 核心功能开发

### 1. API客户端封装

#### HTTP客户端 (src/api/client.ts)
```typescript
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

class ApiClient {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const authStore = useAuthStore()
        if (authStore.token) {
          config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      (error) => {
        if (error.response?.status === 401) {
          const authStore = useAuthStore()
          authStore.logout()
          ElMessage.error('登录已过期，请重新登录')
        } else if (error.response?.data?.message) {
          ElMessage.error(error.response.data.message)
        }
        return Promise.reject(error)
      }
    )
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

export const apiClient = new ApiClient()
```

### 2. 主入口文件配置 (src/main.ts)
```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 样式导入
import './styles/main.css'
import 'element-plus/dist/index.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
```

### 3. 文档上传组件 (使用Tailwind CSS)

#### 文档上传组件 (src/components/forms/DocumentUpload.vue)
```vue
<template>
  <div class="max-w-2xl mx-auto">
    <!-- 上传区域 -->
    <el-upload
      ref="uploadRef"
      class="upload-container"
      drag
      :auto-upload="false"
      :accept="'.doc,.docx'"
      :on-change="handleFileChange"
      :file-list="fileList"
      :limit="1"
      :on-exceed="handleExceed"
    >
      <div class="flex flex-col items-center justify-center py-12 px-6">
        <el-icon class="text-4xl text-gray-400 mb-4">
          <UploadFilled />
        </el-icon>
        <div class="text-lg text-gray-600 mb-2">
          将Word文档拖到此处，或<span class="text-primary-500 cursor-pointer">点击上传</span>
        </div>
        <div class="text-sm text-gray-400">
          仅支持.doc/.docx格式，文件大小不超过10MB
        </div>
      </div>
    </el-upload>

    <!-- 分析类型选择 -->
    <div v-if="fileList.length > 0" class="mt-6 p-6 bg-white rounded-lg shadow-sm border">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">选择分析类型</h3>
      <el-radio-group 
        v-model="analysisType" 
        class="flex flex-col space-y-3"
      >
        <el-radio 
          label="paper_check" 
          class="!flex !items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div class="ml-3">
            <div class="font-medium text-gray-800">论文检测</div>
            <div class="text-sm text-gray-500">检测论文格式、引用规范等</div>
          </div>
        </el-radio>
        <el-radio 
          label="format_check"
          class="!flex !items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div class="ml-3">
            <div class="font-medium text-gray-800">格式检查</div>
            <div class="text-sm text-gray-500">检查文档格式规范</div>
          </div>
        </el-radio>
        <el-radio 
          label="structure_check"
          class="!flex !items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div class="ml-3">
            <div class="font-medium text-gray-800">结构分析</div>
            <div class="text-sm text-gray-500">分析文档结构和章节</div>
          </div>
        </el-radio>
      </el-radio-group>
    </div>

    <!-- 操作按钮 -->
    <div v-if="fileList.length > 0" class="mt-6 flex justify-center space-x-4">
      <el-button 
        type="primary" 
        size="large"
        :loading="uploading"
        @click="handleUpload"
        class="px-8 py-3 !bg-primary-500 hover:!bg-primary-600"
      >
        <el-icon v-if="!uploading" class="mr-2">
          <Upload />
        </el-icon>
        {{ uploading ? '上传中...' : '开始分析' }}
      </el-button>
      
      <el-button 
        size="large" 
        @click="handleClear"
        class="px-6 py-3"
      >
        清空文件
      </el-button>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploading" class="mt-6 p-4 bg-blue-50 rounded-lg">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-blue-800">上传进度</span>
        <span class="text-sm text-blue-600">{{ uploadProgress }}%</span>
      </div>
      <el-progress 
        :percentage="uploadProgress" 
        :show-text="false"
        :stroke-width="8"
        color="#409eff"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Upload } from '@element-plus/icons-vue'
import type { UploadFile, UploadFiles } from 'element-plus'
import { useDocuments } from '@/composables/useDocuments'

const emit = defineEmits<{
  success: [result: any]
  error: [error: any]
}>()

const { uploadDocument } = useDocuments()

const uploadRef = ref()
const fileList = ref<UploadFiles>([])
const analysisType = ref('paper_check')
const uploading = ref(false)
const uploadProgress = ref(0)

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}

const handleExceed = () => {
  ElMessage.warning('最多只能上传一个文件')
}

const handleUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  const file = fileList.value[0].raw as File

  uploading.value = true
  uploadProgress.value = 0

  // 模拟上传进度
  const progressTimer = setInterval(() => {
    if (uploadProgress.value < 90) {
      uploadProgress.value += 10
    }
  }, 200)

  try {
    const result = await uploadDocument(file, analysisType.value)
    
    clearInterval(progressTimer)
    uploadProgress.value = 100

    ElMessage.success('文档上传成功，开始分析')
    emit('success', result)
    
    // 延迟重置
    setTimeout(() => {
      handleClear()
    }, 1500)
  } catch (error) {
    clearInterval(progressTimer)
    ElMessage.error('文档上传失败')
    emit('error', error)
  } finally {
    uploading.value = false
  }
}

const handleClear = () => {
  fileList.value = []
  uploadProgress.value = 0
  analysisType.value = 'paper_check'
  uploadRef.value?.clearFiles()
}
</script>

<style scoped>
.upload-container :deep(.el-upload-dragger) {
  @apply border-2 border-dashed border-gray-300 hover:border-primary-400 transition-colors;
}

.upload-container :deep(.el-upload-dragger:hover) {
  @apply bg-gray-50;
}
</style>
```

### 4. 文档列表组件示例
```vue
<template>
  <div class="space-y-6">
    <!-- 筛选器 -->
    <div class="flex flex-wrap gap-4 p-4 bg-white rounded-lg shadow-sm">
      <el-select v-model="filters.status" placeholder="状态筛选" class="w-40">
        <el-option label="全部" value="" />
        <el-option label="已上传" value="uploaded" />
        <el-option label="处理中" value="processing" />
        <el-option label="已完成" value="completed" />
        <el-option label="失败" value="failed" />
      </el-select>
      
      <el-button type="primary" @click="fetchDocuments">
        刷新列表
      </el-button>
    </div>

    <!-- 文档网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div 
        v-for="doc in documents" 
        :key="doc.id"
        class="card card-hover cursor-pointer border border-gray-200"
        @click="viewDocument(doc.id)"
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800 truncate">
            {{ doc.filename }}
          </h3>
          <el-tag :type="getStatusType(doc.status)">
            {{ getStatusText(doc.status) }}
          </el-tag>
        </div>
        
        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex justify-between">
            <span>文件大小:</span>
            <span>{{ formatFileSize(doc.file_size) }}</span>
          </div>
          <div class="flex justify-between">
            <span>上传时间:</span>
            <span>{{ formatDate(doc.uploaded_at) }}</span>
          </div>
          <div class="flex justify-between">
            <span>分析类型:</span>
            <span>{{ getAnalysisTypeText(doc.analysis_type) }}</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="mt-4 flex space-x-2">
          <el-button size="small" type="primary" @click.stop="viewDocument(doc.id)">
            查看详情
          </el-button>
          <el-button size="small" type="danger" @click.stop="deleteDocument(doc.id)">
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <el-icon class="animate-spin text-2xl text-primary-500">
        <Loading />
      </el-icon>
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-else-if="documents.length === 0" class="text-center py-12">
      <div class="text-gray-400 text-lg mb-4">暂无文档</div>
      <el-button type="primary" @click="$router.push('/upload')">
        上传第一个文档
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useDocuments } from '@/composables/useDocuments'
import { Loading } from '@element-plus/icons-vue'
import { formatFileSize, formatDate } from '@/utils/formatters'

const { documents, loading, fetchDocuments, deleteDocument: deleteDoc } = useDocuments()

const filters = ref({
  status: ''
})

const getStatusType = (status: string) => {
  const types = {
    uploaded: '',
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return types[status as keyof typeof types] || ''
}

const getStatusText = (status: string) => {
  const texts = {
    uploaded: '已上传',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status as keyof typeof texts] || status
}

const getAnalysisTypeText = (type: string) => {
  const texts = {
    paper_check: '论文检测',
    format_check: '格式检查',
    structure_check: '结构分析'
  }
  return texts[type as keyof typeof texts] || type
}

const viewDocument = (id: string) => {
  // 跳转到文档详情页
  console.log('View document:', id)
}

const deleteDocument = async (id: string) => {
  try {
    await deleteDoc(id)
    fetchDocuments()
  } catch (error) {
    console.error('删除文档失败:', error)
  }
}

onMounted(() => {
  fetchDocuments()
})
</script>
```

## 🔗 重要文档链接

- **API接口文档**: `./API接口文档.md`
- **技术栈建议**: `./技术栈建议.md`
- **后端项目**: `../backend/`
- **在线API文档**: http://localhost:8000/docs

## 📋 开发建议

1. **使用TypeScript**提高代码质量和开发体验
2. **组合式API**优于选项式API，便于逻辑复用
3. **Pinia状态管理**简单直观，推荐使用
4. **Element Plus + Tailwind CSS**组合使用，发挥各自优势
5. **响应式设计**使用Tailwind CSS的响应式工具类

## 🎨 Tailwind CSS最佳实践

1. **使用@apply指令**提取重复的工具类组合
2. **配置自定义颜色**与Element Plus主题保持一致
3. **避免与Element Plus冲突**，关闭preflight
4. **使用组件类**定义可复用的样式模式
5. **响应式优先**，移动端优先设计

---

这个指南提供了完整的Vue 3 + Tailwind CSS前端开发框架，可以直接开始开发工作。 