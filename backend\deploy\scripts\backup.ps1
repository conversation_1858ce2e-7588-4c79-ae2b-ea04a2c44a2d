# ==================================================
# Word文档分析服务 - 数据备份脚本
# ==================================================

param(
    [string]$BackupType = "full",           # full, data, config, logs
    [string]$BackupPath = ".\data\backups", # 备份目录
    [switch]$Compress = $true,               # 是否压缩
    [int]$RetentionDays = 30,               # 保留天数
    [switch]$AutoCleanup = $true,           # 自动清理旧备份
    [switch]$Verify = $true,                # 验证备份完整性
    [switch]$Schedule = $false,             # 是否为计划任务调用
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
Word文档分析服务 - 数据备份脚本

用法:
    .\backup.ps1 [参数]

参数:
    -BackupType     备份类型 (full/data/config/logs) [默认: full]
                   full:   完整备份(数据+配置+日志)
                   data:   仅备份数据文件
                   config: 仅备份配置文件  
                   logs:   仅备份日志文件
    -BackupPath     备份存储路径 [默认: .\data\backups]
    -Compress       是否压缩备份 [默认: true]
    -RetentionDays  备份保留天数 [默认: 30]
    -AutoCleanup    自动清理过期备份 [默认: true]
    -Verify         验证备份完整性 [默认: true]
    -Schedule       计划任务模式(静默运行) [默认: false]
    -Help           显示此帮助信息

示例:
    .\backup.ps1                                    # 完整备份
    .\backup.ps1 -BackupType data                   # 仅备份数据
    .\backup.ps1 -BackupPath "D:\Backups" -Compress  # 指定路径并压缩
    .\backup.ps1 -Schedule -RetentionDays 7         # 计划任务模式，保留7天
"@ -ForegroundColor Green
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    if (!$Schedule) {
        switch ($Level) {
            "INFO"  { Write-Host $logMessage -ForegroundColor Green }
            "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
            "ERROR" { Write-Host $logMessage -ForegroundColor Red }
            default { Write-Host $logMessage }
        }
    }
    
    # 写入日志文件
    $logFile = Join-Path $BackupPath "backup.log"
    Add-Content -Path $logFile -Value $logMessage -ErrorAction SilentlyContinue
}

# 开始备份
$startTime = Get-Date
$backupTimestamp = $startTime.ToString("yyyyMMdd-HHmmss")

if (!$Schedule) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Word文档分析服务 - 数据备份" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "备份类型: $BackupType" -ForegroundColor Yellow
    Write-Host "备份路径: $BackupPath" -ForegroundColor Yellow
    Write-Host "压缩: $Compress" -ForegroundColor Yellow
    Write-Host "保留天数: $RetentionDays" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Green
}

Write-Log "开始备份操作 - 类型: $BackupType"

try {
    # 创建备份目录
    if (!(Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
        Write-Log "创建备份目录: $BackupPath"
    }

    # 临时备份目录
    $tempBackupDir = Join-Path $BackupPath "temp_$backupTimestamp"
    New-Item -ItemType Directory -Path $tempBackupDir -Force | Out-Null

    # 备份计数器
    $backupItems = @()

    # 根据备份类型选择备份内容
    switch ($BackupType) {
        "full" {
            Write-Log "执行完整备份..."
            $backupItems = @(
                @{ Source = ".\data"; Target = "data"; Description = "数据文件" },
                @{ Source = ".\config"; Target = "config"; Description = "配置文件" },
                @{ Source = ".\logs"; Target = "logs"; Description = "日志文件" },
                @{ Source = ".\config.yaml"; Target = "config.yaml"; Description = "主配置文件" },
                @{ Source = ".\docker-compose.yml"; Target = "docker-compose.yml"; Description = "Docker编排文件" }
            )
        }
        "data" {
            Write-Log "执行数据备份..."
            $backupItems = @(
                @{ Source = ".\data"; Target = "data"; Description = "数据文件" }
            )
        }
        "config" {
            Write-Log "执行配置备份..."
            $backupItems = @(
                @{ Source = ".\config"; Target = "config"; Description = "配置文件" },
                @{ Source = ".\config.yaml"; Target = "config.yaml"; Description = "主配置文件" },
                @{ Source = ".\docker-compose.yml"; Target = "docker-compose.yml"; Description = "Docker编排文件" },
                @{ Source = ".\deploy"; Target = "deploy"; Description = "部署文件" }
            )
        }
        "logs" {
            Write-Log "执行日志备份..."
            $backupItems = @(
                @{ Source = ".\logs"; Target = "logs"; Description = "日志文件" }
            )
        }
    }

    # 执行备份
    $totalSize = 0
    foreach ($item in $backupItems) {
        $sourcePath = $item.Source
        $targetPath = Join-Path $tempBackupDir $item.Target
        $description = $item.Description
        
        if (Test-Path $sourcePath) {
            Write-Log "备份 $description : $sourcePath -> $targetPath"
            
            if (Test-Path $sourcePath -PathType Container) {
                # 复制目录
                Copy-Item -Path $sourcePath -Destination $targetPath -Recurse -Force
            } else {
                # 复制文件
                Copy-Item -Path $sourcePath -Destination $targetPath -Force
            }
            
            # 计算大小
            $size = (Get-ChildItem -Path $targetPath -Recurse | Measure-Object -Property Length -Sum).Sum
            $totalSize += $size
            Write-Log "完成备份 $description : $(Format-FileSize $size)"
        } else {
            Write-Log "跳过不存在的路径: $sourcePath" "WARN"
        }
    }

    # 创建备份元数据
    $metadata = @{
        timestamp = $backupTimestamp
        type = $BackupType
        total_size = $totalSize
        items_count = $backupItems.Count
        created_by = $env:USERNAME
        machine = $env:COMPUTERNAME
        version = "1.0.0"
    }
    
    $metadataJson = $metadata | ConvertTo-Json -Depth 3
    $metadataFile = Join-Path $tempBackupDir "backup_metadata.json"
    Set-Content -Path $metadataFile -Value $metadataJson -Encoding UTF8

    # 压缩备份（如果启用）
    $finalBackupPath = ""
    if ($Compress) {
        Write-Log "压缩备份文件..."
        $zipFileName = "backup_${BackupType}_${backupTimestamp}.zip"
        $finalBackupPath = Join-Path $BackupPath $zipFileName
        
        # 使用.NET压缩
        Add-Type -AssemblyName "System.IO.Compression.FileSystem"
        [System.IO.Compression.ZipFile]::CreateFromDirectory($tempBackupDir, $finalBackupPath)
        
        $compressedSize = (Get-Item $finalBackupPath).Length
        $compressionRatio = [math]::Round((1 - $compressedSize / $totalSize) * 100, 2)
        Write-Log "压缩完成: $(Format-FileSize $compressedSize), 压缩率: ${compressionRatio}%"
    } else {
        # 不压缩，直接重命名目录
        $dirName = "backup_${BackupType}_${backupTimestamp}"
        $finalBackupPath = Join-Path $BackupPath $dirName
        Move-Item -Path $tempBackupDir -Destination $finalBackupPath
        Write-Log "备份保存为目录: $finalBackupPath"
    }

    # 清理临时目录
    if (Test-Path $tempBackupDir) {
        Remove-Item -Path $tempBackupDir -Recurse -Force
    }

    # 验证备份（如果启用）
    if ($Verify) {
        Write-Log "验证备份完整性..."
        if (Test-Path $finalBackupPath) {
            if ($Compress) {
                # 验证ZIP文件
                try {
                    $zip = [System.IO.Compression.ZipFile]::OpenRead($finalBackupPath)
                    $entryCount = $zip.Entries.Count
                    $zip.Dispose()
                    Write-Log "备份验证通过: $entryCount 个文件"
                } catch {
                    Write-Log "备份验证失败: $($_.Exception.Message)" "ERROR"
                    throw
                }
            } else {
                # 验证目录
                $fileCount = (Get-ChildItem -Path $finalBackupPath -Recurse -File).Count
                Write-Log "备份验证通过: $fileCount 个文件"
            }
        } else {
            Write-Log "备份文件不存在，验证失败" "ERROR"
            throw "备份文件不存在"
        }
    }

    # 自动清理旧备份（如果启用）
    if ($AutoCleanup) {
        Write-Log "清理过期备份..."
        $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
        $oldBackups = Get-ChildItem -Path $BackupPath -Filter "backup_*" | Where-Object { 
            $_.CreationTime -lt $cutoffDate 
        }
        
        $cleanedCount = 0
        $cleanedSize = 0
        foreach ($oldBackup in $oldBackups) {
            try {
                $cleanedSize += $oldBackup.Length
                Remove-Item -Path $oldBackup.FullName -Recurse -Force
                $cleanedCount++
                Write-Log "删除过期备份: $($oldBackup.Name)"
            } catch {
                Write-Log "删除备份失败: $($oldBackup.Name) - $($_.Exception.Message)" "WARN"
            }
        }
        
        if ($cleanedCount -gt 0) {
            Write-Log "清理完成: 删除 $cleanedCount 个过期备份, 释放空间 $(Format-FileSize $cleanedSize)"
        } else {
            Write-Log "无需清理过期备份"
        }
    }

    # 计算总耗时
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-Log "备份操作完成"
    Write-Log "备份文件: $finalBackupPath"
    Write-Log "备份大小: $(Format-FileSize (Get-Item $finalBackupPath).Length)"
    Write-Log "总耗时: $([math]::Round($duration, 2)) 秒"

    if (!$Schedule) {
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "备份完成！" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "备份文件: $finalBackupPath" -ForegroundColor Cyan
        Write-Host "备份大小: $(Format-FileSize (Get-Item $finalBackupPath).Length)" -ForegroundColor Cyan
        Write-Host "总耗时: $([math]::Round($duration, 2)) 秒" -ForegroundColor Cyan
    }

    exit 0

} catch {
    Write-Log "备份失败: $($_.Exception.Message)" "ERROR"
    
    # 清理失败的临时文件
    if (Test-Path $tempBackupDir) {
        try {
            Remove-Item -Path $tempBackupDir -Recurse -Force
            Write-Log "清理临时文件"
        } catch {
            Write-Log "清理临时文件失败" "WARN"
        }
    }
    
    if (!$Schedule) {
        Write-Host "备份失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    exit 1
}

# 辅助函数：格式化文件大小
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size Bytes"
    }
} 