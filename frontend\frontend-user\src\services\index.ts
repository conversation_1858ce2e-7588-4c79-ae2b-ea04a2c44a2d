// 基础API服务
import apiService from './api'
export { default as apiService } from './api'

// 认证相关API
import authApi, { AuthApi } from './authApi'
export { default as authApi, AuthApi } from './authApi'

// 文档相关API
import documentApi, { 
  DocumentApi,
  type DocumentUploadOptions,
  type DocumentListParams,
  type AnalysisResult
} from './documentApi'
export { 
  default as documentApi, 
  DocumentApi,
  type DocumentUploadOptions,
  type DocumentListParams,
  type AnalysisResult
} from './documentApi'

// 任务相关API
import taskApi, { 
  TaskApi,
  type TaskListParams,
  type TaskDetail
} from './taskApi'
export { 
  default as taskApi, 
  TaskApi,
  type TaskListParams,
  type TaskDetail
} from './taskApi'

// 管理端API
import adminApi, { 
  AdminApi,
  type UserManagementParams,
  type SystemStats,
  type OrderInfo,
  type SystemConfig
} from './adminApi'
export { 
  default as adminApi, 
  AdminApi,
  type UserManagementParams,
  type SystemStats,
  type OrderInfo,
  type SystemConfig
} from './adminApi'

// 支付相关API
import { paymentApi, PaymentApi } from './paymentApi'
export { 
  paymentApi, 
  PaymentApi,
  type CreateOrderRequest,
  type PaymentInfo,
  type OrderStatus,
  type PaymentPlan,
  type PaymentHistoryResponse,
  type PaymentStatistics,
  type PaymentHistoryParams
} from './paymentApi'

// 统一API接口类
export class ApiClient {
  // 基础服务
  public readonly base = apiService
  
  // 业务服务
  public readonly auth = authApi
  public readonly documents = documentApi
  public readonly tasks = taskApi
  public readonly admin = adminApi
  public readonly payments = paymentApi

  constructor() {
    // 监听认证登出事件
    window.addEventListener('auth:logout', this.handleAuthLogout.bind(this))
  }

  /**
   * 处理认证登出事件
   */
  private handleAuthLogout() {
    // 清理所有本地存储的认证信息
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    
    // 跳转到登录页
    if (window.location.pathname.startsWith('/admin')) {
      window.location.href = '/admin/login'
    } else {
      window.location.href = '/auth'
    }
  }

  /**
   * 初始化API客户端
   */
  async initialize(): Promise<void> {
    // 检查API服务健康状态
    const isHealthy = await this.base.healthCheck()
    if (!isHealthy) {
      console.warn('API service is not healthy')
    }

    // 如果有token，验证其有效性
    const token = this.auth.getStoredToken()
    if (token) {
      try {
        await this.auth.getCurrentUser()
      } catch (error) {
        console.warn('Token validation failed, clearing stored auth data')
        this.handleAuthLogout()
      }
    }
  }

  /**
   * 获取API基础URL
   */
  getBaseURL(): string {
    return this.base.getAxiosInstance().defaults.baseURL || ''
  }

  /**
   * 检查API连接状态
   */
  async checkConnection(): Promise<boolean> {
    return this.base.healthCheck()
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

// 默认导出
export default apiClient

// 兼容性导出 - 保持向后兼容
export const api = {
  auth: authApi,
  documents: documentApi,
  tasks: taskApi,
  admin: adminApi,
  payments: paymentApi,
  base: apiService
}

/**
 * API初始化函数
 * 建议在应用启动时调用
 */
export async function initializeApi(): Promise<void> {
  await apiClient.initialize()
}

/**
 * 认证检查函数
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  return authApi.isLoggedIn()
}

/**
 * 管理员认证检查函数
 * 检查管理员是否已登录
 */
export function isAdminAuthenticated(): boolean {
  return adminApi.isAdminLoggedIn()
}

/**
 * 获取当前用户信息
 */
export function getCurrentUser() {
  return authApi.getStoredUser()
}

/**
 * 获取当前管理员信息
 */
export function getCurrentAdmin() {
  return adminApi.getStoredAdmin()
} 