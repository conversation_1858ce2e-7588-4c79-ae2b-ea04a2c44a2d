# ==================================================
# Word文档分析服务 - 生产环境配置示例
# ==================================================

ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# 数据库配置
DATABASE_URL=postgresql+asyncpg://word_user:YOUR_DB_PASSWORD@postgres:5432/word_service
DB_PASSWORD=YOUR_SUPER_SECURE_DB_PASSWORD

# Redis配置
REDIS_URL=redis://:YOUR_REDIS_PASSWORD@redis:6379/0
REDIS_PASSWORD=YOUR_REDIS_PASSWORD

# 安全配置
SECRET_KEY=YOUR_SUPER_SECRET_JWT_KEY_MINIMUM_32_CHARACTERS_LONG
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# 文件配置
MAX_FILE_SIZE=104857600
UPLOAD_PATH=/app/data/uploads

# 监控配置
GRAFANA_PASSWORD=YOUR_GRAFANA_PASSWORD

# SSL配置
SSL_ENABLED=true
SSL_CERT_PATH=/app/ssl/cert.pem
SSL_KEY_PATH=/app/ssl/key.pem

# CORS配置
CORS_ORIGINS=https://your-domain.com
TRUSTED_HOSTS=your-domain.com 