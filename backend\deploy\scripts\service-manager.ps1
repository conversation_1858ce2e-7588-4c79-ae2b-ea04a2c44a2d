# ==================================================
# Word文档分析服务 - 服务管理脚本
# ==================================================

param(
    [string]$Action = "status",             # start, stop, restart, update, status, logs, cleanup
    [string]$Service = "all",               # all, word-service, redis, nginx, monitoring
    [string]$Environment = "production",    # production, development, testing
    [switch]$Force = $false,                # 强制操作
    [switch]$Follow = $false,               # 跟踪日志输出
    [int]$Lines = 100,                      # 显示日志行数
    [string]$Since = "",                    # 日志起始时间
    [switch]$Timestamps = $true,            # 显示时间戳
    [switch]$Help = $false
)

# 显示帮助信息
if ($Help) {
    Write-Host @"
Word文档分析服务 - 服务管理脚本

用法:
    .\service-manager.ps1 [参数]

参数:
    -Action         操作类型 [默认: status]
                   start:    启动服务
                   stop:     停止服务
                   restart:  重启服务
                   update:   更新服务(拉取最新镜像并重启)
                   status:   查看服务状态
                   logs:     查看服务日志
                   cleanup:  清理服务资源(停止的容器、未使用的镜像等)
    
    -Service        服务名称 [默认: all]
                   all:          所有服务
                   word-service: 主应用服务
                   redis:        Redis缓存服务
                   nginx:        Nginx代理服务
                   monitoring:   监控服务(Prometheus + Grafana)
    
    -Environment    环境 [默认: production]
    -Force          强制操作(跳过确认)
    -Follow         跟踪日志输出(仅用于logs操作)
    -Lines          显示日志行数 [默认: 100]
    -Since          日志起始时间(如: "1h", "30m", "2024-12-19T10:00:00")
    -Timestamps     显示日志时间戳 [默认: true]
    -Help           显示此帮助信息

示例:
    .\service-manager.ps1                                   # 查看所有服务状态
    .\service-manager.ps1 -Action start                     # 启动所有服务
    .\service-manager.ps1 -Action stop -Service word-service  # 停止主服务
    .\service-manager.ps1 -Action restart -Force           # 强制重启所有服务
    .\service-manager.ps1 -Action logs -Service word-service -Follow  # 跟踪主服务日志
    .\service-manager.ps1 -Action update                    # 更新所有服务
    .\service-manager.ps1 -Action cleanup                   # 清理资源
"@ -ForegroundColor Green
    exit 0
}

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "INFO"  { Write-Host $logMessage -ForegroundColor Green }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "DEBUG" { Write-Host $logMessage -ForegroundColor Gray }
        default { Write-Host $logMessage }
    }
}

# 服务定义
$Services = @{
    "word-service" = @{
        Name = "word-service-app"
        Description = "Word文档分析主服务"
        Required = $true
        DependsOn = @("redis")
    }
    "redis" = @{
        Name = "word-service-redis"
        Description = "Redis缓存服务"
        Required = $true
        DependsOn = @()
    }
    "nginx" = @{
        Name = "word-service-nginx"
        Description = "Nginx反向代理"
        Required = $false
        DependsOn = @("word-service")
    }
    "prometheus" = @{
        Name = "word-service-prometheus"
        Description = "Prometheus监控"
        Required = $false
        DependsOn = @()
    }
    "grafana" = @{
        Name = "word-service-grafana"
        Description = "Grafana仪表板"
        Required = $false
        DependsOn = @("prometheus")
    }
}

# 获取服务列表
function Get-ServiceList {
    param([string]$ServiceFilter)
    
    if ($ServiceFilter -eq "all") {
        if ($Environment -eq "development") {
            return @("word-service", "redis")
        } else {
            return @("word-service", "redis", "nginx")
        }
    } elseif ($ServiceFilter -eq "monitoring") {
        return @("prometheus", "grafana")
    } else {
        return @($ServiceFilter)
    }
}

# 检查Docker环境
function Test-DockerEnvironment {
    try {
        $null = docker --version
        $null = docker-compose --version
        return $true
    } catch {
        Write-Log "Docker或Docker Compose未安装或未正确配置" "ERROR"
        return $false
    }
}

# 获取容器状态
function Get-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker ps -a --filter "name=$ContainerName" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Select-Object -Skip 1
        if ($status) {
            return $status.Trim()
        } else {
            return "不存在"
        }
    } catch {
        return "未知"
    }
}

# 显示服务状态
function Show-ServicesStatus {
    param([array]$ServiceList)
    
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "服务状态概览" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    
    foreach ($serviceName in $ServiceList) {
        if ($Services.ContainsKey($serviceName)) {
            $service = $Services[$serviceName]
            $containerName = $service.Name
            $description = $service.Description
            $status = Get-ContainerStatus $containerName
            
            Write-Host "$description ($containerName):" -ForegroundColor Cyan
            if ($status -like "*Up*") {
                Write-Host "  状态: $status" -ForegroundColor Green
            } elseif ($status -like "*Exited*") {
                Write-Host "  状态: $status" -ForegroundColor Red
            } else {
                Write-Host "  状态: $status" -ForegroundColor Yellow
            }
            Write-Host ""
        }
    }
    
    # 显示Docker Compose状态
    Write-Host "Docker Compose 服务状态:" -ForegroundColor Cyan
    try {
        docker-compose ps
    } catch {
        Write-Log "无法获取Docker Compose状态" "WARN"
    }
}

# 启动服务
function Start-Services {
    param([array]$ServiceList, [bool]$ForceAction)
    
    Write-Log "启动服务: $($ServiceList -join ', ')"
    
    # 构建Docker Compose命令
    $composeArgs = @("up", "-d")
    
    # 添加服务名称
    foreach ($serviceName in $ServiceList) {
        if ($Services.ContainsKey($serviceName)) {
            $composeArgs += $Services[$serviceName].Name.Replace("word-service-", "")
        }
    }
    
    # 如果包含监控服务，添加profile
    if ($ServiceList -contains "prometheus" -or $ServiceList -contains "grafana") {
        $composeArgs = @("--profile", "monitoring") + $composeArgs
    }
    
    try {
        Write-Log "执行命令: docker-compose $($composeArgs -join ' ')"
        & docker-compose $composeArgs
        Write-Log "服务启动命令执行完成" "INFO"
        
        # 等待服务启动
        Write-Log "等待服务启动..."
        Start-Sleep -Seconds 10
        
        # 检查服务状态
        Show-ServicesStatus $ServiceList
        
    } catch {
        Write-Log "启动服务失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 停止服务
function Stop-Services {
    param([array]$ServiceList, [bool]$ForceAction)
    
    Write-Log "停止服务: $($ServiceList -join ', ')"
    
    if (!$ForceAction) {
        $confirm = Read-Host "确认停止服务? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Log "操作已取消"
            return
        }
    }
    
    try {
        if ($ServiceList.Count -eq 1 -and $ServiceList[0] -ne "all") {
            # 停止单个服务
            $serviceName = $ServiceList[0]
            if ($Services.ContainsKey($serviceName)) {
                $containerName = $Services[$serviceName].Name
                docker-compose stop $containerName.Replace("word-service-", "")
            }
        } else {
            # 停止所有服务
            docker-compose down
        }
        
        Write-Log "服务停止完成"
        
    } catch {
        Write-Log "停止服务失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 重启服务
function Restart-Services {
    param([array]$ServiceList, [bool]$ForceAction)
    
    Write-Log "重启服务: $($ServiceList -join ', ')"
    
    try {
        Stop-Services $ServiceList $ForceAction
        Start-Sleep -Seconds 5
        Start-Services $ServiceList $ForceAction
        
    } catch {
        Write-Log "重启服务失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 更新服务
function Update-Services {
    param([array]$ServiceList, [bool]$ForceAction)
    
    Write-Log "更新服务: $($ServiceList -join ', ')"
    
    if (!$ForceAction) {
        $confirm = Read-Host "确认更新服务? 这将拉取最新镜像并重启服务 (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Log "操作已取消"
            return
        }
    }
    
    try {
        # 拉取最新镜像
        Write-Log "拉取最新镜像..."
        docker-compose pull
        
        # 重新构建本地镜像
        Write-Log "重新构建本地镜像..."
        docker-compose build --no-cache
        
        # 重启服务
        Write-Log "重启服务..."
        Restart-Services $ServiceList $true
        
        Write-Log "服务更新完成"
        
    } catch {
        Write-Log "更新服务失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 查看日志
function Show-ServiceLogs {
    param([array]$ServiceList, [bool]$FollowLogs, [int]$LogLines, [string]$SinceTime, [bool]$ShowTimestamps)
    
    $serviceName = $ServiceList[0]  # 日志查看只支持单个服务
    
    if (!$Services.ContainsKey($serviceName)) {
        Write-Log "无效的服务名称: $serviceName" "ERROR"
        return
    }
    
    $containerName = $Services[$serviceName].Name.Replace("word-service-", "")
    $logArgs = @("logs")
    
    if ($ShowTimestamps) {
        $logArgs += "--timestamps"
    }
    
    if ($FollowLogs) {
        $logArgs += "--follow"
    }
    
    if ($LogLines -gt 0) {
        $logArgs += "--tail"
        $logArgs += $LogLines.ToString()
    }
    
    if (![string]::IsNullOrEmpty($SinceTime)) {
        $logArgs += "--since"
        $logArgs += $SinceTime
    }
    
    $logArgs += $containerName
    
    Write-Log "显示 $($Services[$serviceName].Description) 日志"
    Write-Log "执行命令: docker-compose $($logArgs -join ' ')"
    
    try {
        & docker-compose $logArgs
    } catch {
        Write-Log "获取日志失败: $($_.Exception.Message)" "ERROR"
    }
}

# 清理资源
function Cleanup-Resources {
    param([bool]$ForceAction)
    
    Write-Log "清理Docker资源"
    
    if (!$ForceAction) {
        $confirm = Read-Host "确认清理资源? 这将删除未使用的容器、镜像和网络 (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Log "操作已取消"
            return
        }
    }
    
    try {
        Write-Log "清理停止的容器..."
        docker container prune -f
        
        Write-Log "清理未使用的镜像..."
        docker image prune -f
        
        Write-Log "清理未使用的网络..."
        docker network prune -f
        
        Write-Log "清理未使用的数据卷..."
        docker volume prune -f
        
        Write-Log "资源清理完成"
        
        # 显示清理后的状态
        Write-Log "Docker资源使用情况:"
        docker system df
        
    } catch {
        Write-Log "清理资源失败: $($_.Exception.Message)" "ERROR"
        throw
    }
}

# 主程序
Write-Host "========================================" -ForegroundColor Green
Write-Host "Word文档分析服务 - 服务管理" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host "服务: $Service" -ForegroundColor Yellow
Write-Host "环境: $Environment" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

# 检查Docker环境
if (!(Test-DockerEnvironment)) {
    exit 1
}

# 获取要操作的服务列表
$serviceList = Get-ServiceList $Service

try {
    switch ($Action.ToLower()) {
        "start" {
            Start-Services $serviceList $Force
        }
        "stop" {
            Stop-Services $serviceList $Force
        }
        "restart" {
            Restart-Services $serviceList $Force
        }
        "update" {
            Update-Services $serviceList $Force
        }
        "status" {
            Show-ServicesStatus $serviceList
        }
        "logs" {
            if ($serviceList.Count -ne 1) {
                Write-Log "日志查看只支持单个服务" "ERROR"
                exit 1
            }
            Show-ServiceLogs $serviceList $Follow $Lines $Since $Timestamps
        }
        "cleanup" {
            Cleanup-Resources $Force
        }
        default {
            Write-Log "无效的操作: $Action" "ERROR"
            Write-Host "使用 -Help 参数查看帮助信息" -ForegroundColor Yellow
            exit 1
        }
    }
    
    Write-Log "操作完成"
    
} catch {
    Write-Log "操作失败: $($_.Exception.Message)" "ERROR"
    exit 1
} 