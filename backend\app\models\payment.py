import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import BaseModel, Field

class PaymentStatus(str, Enum):
    PENDING = "pending"
    PAID = "paid"
    FAILED = "failed"
    CANCELLED = "cancelled"

class PaymentMethod(str, Enum):
    WECHAT = "wechat"
    ALIPAY = "alipay"
    CARD = "card"

class OrderBase(BaseModel):
    user_id: str
    plan_id: str = Field(..., description="购买的套餐ID, e.g., 'basic', 'standard'")
    amount: float = Field(..., gt=0, description="订单金额")
    payment_method: PaymentMethod
    
class OrderCreate(OrderBase):
    pass

class Order(OrderBase):
    order_id: str = Field(default_factory=lambda: f"order_{uuid.uuid4().hex}")
    status: PaymentStatus = PaymentStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    paid_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class PaymentInfo(BaseModel):
    order_id: str
    payment_url: Optional[str] = None
    qr_code_url: Optional[str] = None
    message: str 