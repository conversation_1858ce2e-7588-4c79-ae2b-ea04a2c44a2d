"""
文件清理机制模块

提供临时文件自动清理、定期清理任务、存储空间管理等功能
"""

import os
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
import structlog
import threading
import time

from app.core.config import settings
from app.core.exceptions import CleanupError
from app.services.storage import get_storage_manager, StorageManager

logger = structlog.get_logger()

# 注意：所有清理相关的路径和保留时间配置已移至 app.core.config.py
# 请勿在此处添加硬编码配置。

# 清理任务调度配置
CLEANUP_SCHEDULE_HOURS = getattr(settings, 'CLEANUP_SCHEDULE_HOURS', 6)  # 每6小时执行一次

class FileCleanupManager:
    """文件清理管理器"""
    
    def __init__(self, storage_manager: StorageManager = None):
        """
        初始化文件清理管理器
        
        Args:
            storage_manager: 存储管理器实例
        """
        self.storage_manager = storage_manager or get_storage_manager()
        
        # 清理统计
        self.cleanup_stats = {
            'total_cleanups': 0,
            'files_deleted': 0,
            'bytes_freed': 0,
            'errors': 0,
            'last_cleanup': None,
            'last_deep_cleanup': None
        }
        
        # 调度器状态
        self.scheduler_running = False
        self.scheduler_thread = None
        
        # 清理目录配置
        self.cleanup_directories = self._get_cleanup_directories()
    
    def _get_cleanup_directories(self) -> Dict[str, Dict[str, Any]]:
        """从统一配置中获取需要清理的目录"""
        return {
            'temp_files': {
                'path': settings.files.temp_path,
                'max_age_hours': settings.files.temp_retention_hours,
                'description': '临时处理文件'
            },
            'temp_images': {
                'path': settings.files.images_path,
                'max_age_hours': settings.files.temp_retention_hours,
                'description': '临时图片文件 (共享临时文件保留策略)'
            },
            'log_files': {
                'path': settings.logging.file.parent,
                'max_age_days': settings.logging.log_retention_days,
                'description': '日志文件',
                'pattern': '*.log*'
            }
        }
    
    def cleanup_temp_files(self) -> Dict[str, Any]:
        """
        清理临时文件
        
        Returns:
            清理结果统计
        """
        try:
            logger.info("开始清理临时文件")
            
            cleanup_result = {
                'directories_cleaned': 0,
                'files_deleted': 0,
                'bytes_freed': 0,
                'errors': [],
                'details': {}
            }
            
            # 清理各个临时目录
            for dir_name, dir_config in self.cleanup_directories.items():
                if 'max_age_hours' in dir_config:
                    result = self._cleanup_directory_by_hours(
                        directory=dir_config['path'],
                        max_age_hours=dir_config['max_age_hours'],
                        description=dir_config['description'],
                        pattern=dir_config.get('pattern', '*')
                    )
                elif 'max_age_days' in dir_config:
                    result = self._cleanup_directory_by_days(
                        directory=dir_config['path'],
                        max_age_days=dir_config['max_age_days'],
                        description=dir_config['description'],
                        pattern=dir_config.get('pattern', '*')
                    )
                else:
                    continue

                cleanup_result['details'][dir_name] = result
                cleanup_result['directories_cleaned'] += 1
                cleanup_result['files_deleted'] += result['files_deleted']
                cleanup_result['bytes_freed'] += result['bytes_freed']
                cleanup_result['errors'].extend(result['errors'])
            
            # 更新统计
            self.cleanup_stats['total_cleanups'] += 1
            self.cleanup_stats['files_deleted'] += cleanup_result['files_deleted']
            self.cleanup_stats['bytes_freed'] += cleanup_result['bytes_freed']
            self.cleanup_stats['errors'] += len(cleanup_result['errors'])
            self.cleanup_stats['last_cleanup'] = datetime.utcnow().isoformat()
            
            logger.info(f"临时文件清理完成: 删除 {cleanup_result['files_deleted']} 个文件，释放 {cleanup_result['bytes_freed']} 字节")
            
            return cleanup_result
            
        except Exception as e:
            logger.error(f"临时文件清理失败: {str(e)}")
            raise CleanupError(f"临时文件清理失败: {str(e)}")
    
    def _cleanup_directory_by_days(
        self,
        directory: Path,
        max_age_days: int,
        description: str,
        pattern: str = '*'
    ) -> Dict[str, Any]:
        """
        按天数清理目录
        
        Args:
            directory: 目录路径
            max_age_days: 最大保留时间（天）
            description: 目录描述
            pattern: 文件匹配模式
            
        Returns:
            清理结果
        """
        return self._cleanup_directory_by_hours(
            directory=directory,
            max_age_hours=max_age_days * 24,
            description=description,
            pattern=pattern
        )

    def _cleanup_directory_by_hours(
        self,
        directory: Path,
        max_age_hours: int,
        description: str,
        pattern: str = '*'
    ) -> Dict[str, Any]:
        """
        按小时清理目录
        
        Args:
            directory: 目录路径
            max_age_hours: 最大保留时间（小时）
            description: 目录描述
            pattern: 文件匹配模式
            
        Returns:
            清理结果
        """
        try:
            if not directory.exists():
                return {
                    'files_deleted': 0,
                    'bytes_freed': 0,
                    'errors': [],
                    'message': f'{description}目录不存在: {directory}'
                }
            
            cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            cutoff_timestamp = cutoff_time.timestamp()
            
            files_deleted = 0
            bytes_freed = 0
            errors = []
            
            # 遍历目录中的文件
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    try:
                        file_mtime = file_path.stat().st_mtime
                        if file_mtime < cutoff_timestamp:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            files_deleted += 1
                            bytes_freed += file_size
                            logger.debug(f"删除过期文件: {file_path}")
                    except Exception as e:
                        errors.append(f"删除文件 {file_path} 失败: {str(e)}")
            
            return {
                'files_deleted': files_deleted,
                'bytes_freed': bytes_freed,
                'errors': errors,
                'message': f'{description}清理完成: 删除 {files_deleted} 个文件'
            }
            
        except Exception as e:
            return {
                'files_deleted': 0,
                'bytes_freed': 0,
                'errors': [str(e)],
                'message': f'{description}清理失败: {str(e)}'
            }
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取清理统计信息"""
        stats = self.cleanup_stats.copy()
        
        # 添加调度器状态
        stats['scheduler_status'] = {
            'running': self.scheduler_running,
            'cleanup_interval_hours': CLEANUP_SCHEDULE_HOURS
        }
        
        # 添加目录配置
        stats['cleanup_directories'] = {}
        for name, config in self.cleanup_directories.items():
            stats['cleanup_directories'][name] = {
                'path': str(config['path']),
                'description': config['description'],
                'exists': config['path'].exists(),
                'max_age_hours': config.get('max_age_hours'),
                'max_age_days': config.get('max_age_days')
            }
        
        return stats

# 全局文件清理管理器实例
_cleanup_manager = None

def get_cleanup_manager() -> FileCleanupManager:
    """获取全局文件清理管理器实例"""
    global _cleanup_manager
    if _cleanup_manager is None:
        _cleanup_manager = FileCleanupManager()
    return _cleanup_manager

def cleanup_temp_files() -> Dict[str, Any]:
    """
    快捷函数：清理临时文件
    
    Returns:
        清理结果
    """
    manager = get_cleanup_manager()
    return manager.cleanup_temp_files()
