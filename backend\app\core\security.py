"""
文件安全检查模块

提供文件类型验证、安全扫描、病毒检测等功能
"""

import os
import hashlib
import mimetypes
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import structlog
from datetime import datetime

from app.core.config import settings
from app.core.exceptions import SecurityError, ValidationError

logger = structlog.get_logger()

# 支持的文件类型配置
ALLOWED_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # .docx
    'application/msword',  # .doc
    'application/pdf',  # .pdf (可选支持)
    'text/plain',  # .txt (可选支持)
}

ALLOWED_EXTENSIONS = {'.docx', '.doc', '.pdf', '.txt'}

# 危险文件类型黑名单
DANGEROUS_MIME_TYPES = {
    'application/x-executable',
    'application/x-msdownload',
    'application/x-msdos-program',
    'application/x-winexe',
    'application/x-dosexec',
    'application/vnd.microsoft.portable-executable',
    'application/x-sharedlib',
    'application/x-shellscript',
    'text/x-shellscript',
    'application/javascript',
    'text/javascript',
    'application/x-javascript',
}

DANGEROUS_EXTENSIONS = {
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.app', '.deb', '.pkg', '.dmg', '.rpm', '.msi', '.run', '.bin'
}

# 文件大小限制 (字节)
MAX_FILE_SIZE = getattr(settings, 'MAX_FILE_SIZE', 50 * 1024 * 1024)  # 50MB
MIN_FILE_SIZE = getattr(settings, 'MIN_FILE_SIZE', 1024)  # 1KB

class FileSecurityChecker:
    """文件安全检查器"""
    
    def __init__(self):
        """初始化文件安全检查器"""
        self.check_stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'failed_checks': 0,
            'blocked_files': 0,
            'suspicious_files': 0
        }
        
    def check_file_security(self, file_path: str, original_filename: str = None) -> Dict[str, Any]:
        """
        执行完整的文件安全检查
        
        Args:
            file_path: 文件路径
            original_filename: 原始文件名
            
        Returns:
            检查结果字典
            
        Raises:
            SecurityError: 安全检查失败
            ValidationError: 验证失败
        """
        self.check_stats['total_checks'] += 1
        
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise ValidationError(f"文件不存在: {file_path}")
            
            original_filename = original_filename or file_path.name
            
            logger.info(f"开始文件安全检查: {original_filename}")
            
            # 执行各项检查
            checks = {
                'file_exists': self._check_file_exists(file_path),
                'file_size': self._check_file_size(file_path),
                'file_extension': self._check_file_extension(original_filename),
                'mime_type': self._check_mime_type(file_path),
                'file_content': self._check_file_content(file_path),
                'file_hash': self._calculate_file_hash(file_path),
                'dangerous_patterns': self._check_dangerous_patterns(file_path),
            }
            
            # 汇总检查结果
            result = self._compile_check_result(file_path, original_filename, checks)
            
            if result['is_safe']:
                self.check_stats['passed_checks'] += 1
                logger.info(f"文件安全检查通过: {original_filename}")
            else:
                self.check_stats['failed_checks'] += 1
                self.check_stats['blocked_files'] += 1
                logger.warning(f"文件安全检查失败: {original_filename}, 原因: {result['risk_reasons']}")
                raise SecurityError(f"文件安全检查失败: {', '.join(result['risk_reasons'])}")
            
            return result
            
        except Exception as e:
            self.check_stats['failed_checks'] += 1
            logger.error(f"文件安全检查异常: {str(e)}")
            raise e
    
    def _check_file_exists(self, file_path: Path) -> Dict[str, Any]:
        """检查文件是否存在"""
        exists = file_path.exists() and file_path.is_file()
        return {
            'passed': exists,
            'message': '文件存在' if exists else '文件不存在或不是文件',
            'details': {
                'path': str(file_path),
                'exists': exists,
                'is_file': file_path.is_file() if file_path.exists() else False
            }
        }
    
    def _check_file_size(self, file_path: Path) -> Dict[str, Any]:
        """检查文件大小"""
        try:
            file_size = file_path.stat().st_size
            
            size_ok = MIN_FILE_SIZE <= file_size <= MAX_FILE_SIZE
            
            if file_size < MIN_FILE_SIZE:
                message = f"文件太小: {file_size} bytes < {MIN_FILE_SIZE} bytes"
            elif file_size > MAX_FILE_SIZE:
                message = f"文件太大: {file_size} bytes > {MAX_FILE_SIZE} bytes"
            else:
                message = f"文件大小正常: {file_size} bytes"
            
            return {
                'passed': size_ok,
                'message': message,
                'details': {
                    'file_size': file_size,
                    'min_size': MIN_FILE_SIZE,
                    'max_size': MAX_FILE_SIZE,
                    'size_mb': round(file_size / (1024 * 1024), 2)
                }
            }
            
        except Exception as e:
            return {
                'passed': False,
                'message': f"无法获取文件大小: {str(e)}",
                'details': {'error': str(e)}
            }
    
    def _check_file_extension(self, filename: str) -> Dict[str, Any]:
        """检查文件扩展名"""
        file_ext = Path(filename).suffix.lower()
        
        # 检查是否为允许的扩展名
        is_allowed = file_ext in ALLOWED_EXTENSIONS
        
        # 检查是否为危险扩展名
        is_dangerous = file_ext in DANGEROUS_EXTENSIONS
        
        passed = is_allowed and not is_dangerous
        
        if is_dangerous:
            message = f"危险的文件扩展名: {file_ext}"
        elif not is_allowed:
            message = f"不支持的文件扩展名: {file_ext}"
        else:
            message = f"文件扩展名检查通过: {file_ext}"
        
        return {
            'passed': passed,
            'message': message,
            'details': {
                'extension': file_ext,
                'is_allowed': is_allowed,
                'is_dangerous': is_dangerous,
                'allowed_extensions': list(ALLOWED_EXTENSIONS)
            }
        }
    
    def _check_mime_type(self, file_path: Path) -> Dict[str, Any]:
        """检查MIME类型"""
        try:
            # 使用mimetypes模块
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if not mime_type:
                mime_type = 'application/octet-stream'
            
            # 检查是否为允许的MIME类型
            is_allowed = mime_type in ALLOWED_MIME_TYPES
            
            # 检查是否为危险的MIME类型
            is_dangerous = mime_type in DANGEROUS_MIME_TYPES
            
            passed = is_allowed and not is_dangerous
            
            if is_dangerous:
                message = f"危险的MIME类型: {mime_type}"
            elif not is_allowed:
                message = f"不支持的MIME类型: {mime_type}"
            else:
                message = f"MIME类型检查通过: {mime_type}"
            
            return {
                'passed': passed,
                'message': message,
                'details': {
                    'mime_type': mime_type,
                    'is_allowed': is_allowed,
                    'is_dangerous': is_dangerous,
                    'detection_method': 'mimetypes'
                }
            }
            
        except Exception as e:
            return {
                'passed': False,
                'message': f"MIME类型检查失败: {str(e)}",
                'details': {'error': str(e)}
            }
    
    def _check_file_content(self, file_path: Path) -> Dict[str, Any]:
        """检查文件内容"""
        try:
            # 读取文件头部进行基础检查
            with open(file_path, 'rb') as f:
                header = f.read(1024)  # 读取前1KB
            
            # 检查文件头部特征
            content_checks = {
                'has_content': len(header) > 0,
                'is_binary': self._is_binary_content(header),
                'has_suspicious_patterns': self._has_suspicious_patterns(header),
            }
            
            # Word文档特征检查
            if file_path.suffix.lower() == '.docx':
                content_checks['is_valid_docx'] = self._check_docx_signature(header)
            elif file_path.suffix.lower() == '.doc':
                content_checks['is_valid_doc'] = self._check_doc_signature(header)
            
            passed = (
                content_checks['has_content'] and
                not content_checks['has_suspicious_patterns']
            )
            
            message = "文件内容检查通过"
            if not content_checks['has_content']:
                message = "文件内容为空"
            elif content_checks['has_suspicious_patterns']:
                message = "文件包含可疑内容"
            
            return {
                'passed': passed,
                'message': message,
                'details': content_checks
            }
            
        except Exception as e:
            return {
                'passed': False,
                'message': f"文件内容检查失败: {str(e)}",
                'details': {'error': str(e)}
            }
    
    def _is_binary_content(self, content: bytes) -> bool:
        """检查是否为二进制内容"""
        # 简单的二进制检测：检查是否包含空字节
        return b'\x00' in content
    
    def _has_suspicious_patterns(self, content: bytes) -> bool:
        """检查是否包含可疑模式"""
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror=',
            b'eval(',
            b'exec(',
            b'system(',
            b'shell_exec',
        ]
        
        content_lower = content.lower()
        return any(pattern in content_lower for pattern in suspicious_patterns)
    
    def _check_docx_signature(self, header: bytes) -> bool:
        """检查DOCX文件签名"""
        # DOCX文件是ZIP格式，应该以PK开头
        return header.startswith(b'PK')
    
    def _check_doc_signature(self, header: bytes) -> bool:
        """检查DOC文件签名"""
        # DOC文件的OLE签名
        ole_signature = b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1'
        return header.startswith(ole_signature)
    
    def _calculate_file_hash(self, file_path: Path) -> Dict[str, Any]:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            hash_sha256 = hashlib.sha256()
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
                    hash_sha256.update(chunk)
            
            return {
                'passed': True,
                'message': '文件哈希计算成功',
                'details': {
                    'md5': hash_md5.hexdigest(),
                    'sha256': hash_sha256.hexdigest()
                }
            }
            
        except Exception as e:
            return {
                'passed': False,
                'message': f"文件哈希计算失败: {str(e)}",
                'details': {'error': str(e)}
            }
    
    def _check_dangerous_patterns(self, file_path: Path) -> Dict[str, Any]:
        """检查危险模式"""
        try:
            dangerous_found = []
            
            # 检查文件名中的危险模式
            filename = file_path.name.lower()
            dangerous_name_patterns = [
                'autorun', 'setup', 'install', 'update', 'patch',
                'crack', 'keygen', 'serial', 'activator'
            ]
            
            for pattern in dangerous_name_patterns:
                if pattern in filename:
                    dangerous_found.append(f"文件名包含危险模式: {pattern}")
            
            passed = len(dangerous_found) == 0
            message = "未发现危险模式" if passed else f"发现危险模式: {len(dangerous_found)}个"
            
            return {
                'passed': passed,
                'message': message,
                'details': {
                    'dangerous_patterns': dangerous_found,
                    'pattern_count': len(dangerous_found)
                }
            }
            
        except Exception as e:
            return {
                'passed': False,
                'message': f"危险模式检查失败: {str(e)}",
                'details': {'error': str(e)}
            }
    
    def _compile_check_result(self, file_path: Path, original_filename: str, checks: Dict[str, Dict]) -> Dict[str, Any]:
        """编译检查结果"""
        # 统计通过的检查
        passed_checks = [name for name, result in checks.items() if result['passed']]
        failed_checks = [name for name, result in checks.items() if not result['passed']]
        
        # 收集风险原因
        risk_reasons = []
        for name, result in checks.items():
            if not result['passed']:
                risk_reasons.append(result['message'])
        
        # 计算风险等级
        risk_level = self._calculate_risk_level(failed_checks)
        
        # 判断文件是否安全
        is_safe = len(failed_checks) == 0
        
        return {
            'file_path': str(file_path),
            'original_filename': original_filename,
            'is_safe': is_safe,
            'risk_level': risk_level,
            'risk_reasons': risk_reasons,
            'checks_passed': len(passed_checks),
            'checks_failed': len(failed_checks),
            'total_checks': len(checks),
            'passed_check_names': passed_checks,
            'failed_check_names': failed_checks,
            'detailed_results': checks,
            'file_hash': checks.get('file_hash', {}).get('details', {}),
            'checked_at': datetime.utcnow().isoformat()
        }
    
    def _calculate_risk_level(self, failed_checks: List[str]) -> str:
        """计算风险等级"""
        if not failed_checks:
            return 'safe'
        
        high_risk_checks = {'dangerous_patterns', 'mime_type', 'file_content'}
        medium_risk_checks = {'file_extension', 'file_size'}
        
        high_risk_count = sum(1 for check in failed_checks if check in high_risk_checks)
        medium_risk_count = sum(1 for check in failed_checks if check in medium_risk_checks)
        
        if high_risk_count > 0:
            return 'high'
        elif medium_risk_count > 1:
            return 'medium'
        else:
            return 'low'
    
    def get_security_stats(self) -> Dict[str, Any]:
        """获取安全检查统计信息"""
        return {
            'statistics': self.check_stats.copy(),
            'success_rate': (
                self.check_stats['passed_checks'] / max(self.check_stats['total_checks'], 1) * 100
            ),
            'configuration': {
                'max_file_size_mb': MAX_FILE_SIZE / (1024 * 1024),
                'min_file_size_kb': MIN_FILE_SIZE / 1024,
                'allowed_extensions': list(ALLOWED_EXTENSIONS),
            }
        }

# 全局安全检查器实例
_security_checker = None

def get_security_checker() -> FileSecurityChecker:
    """获取全局安全检查器实例"""
    global _security_checker
    if _security_checker is None:
        _security_checker = FileSecurityChecker()
    return _security_checker

def check_file_security(file_path: str, original_filename: str = None) -> Dict[str, Any]:
    """
    快捷函数：执行文件安全检查
    
    Args:
        file_path: 文件路径
        original_filename: 原始文件名
        
    Returns:
        检查结果字典
    """
    checker = get_security_checker()
    return checker.check_file_security(file_path, original_filename)

def is_file_safe(file_path: str, original_filename: str = None) -> bool:
    """
    快捷函数：检查文件是否安全
    
    Args:
        file_path: 文件路径
        original_filename: 原始文件名
        
    Returns:
        是否安全
    """
    try:
        result = check_file_security(file_path, original_filename)
        return result['is_safe']
    except Exception:
        return False 