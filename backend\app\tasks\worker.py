"""
Word文档分析服务 - 异步任务执行器

实现高性能的工作线程池系统，支持：
- 多线程任务并发执行
- 动态工作线程管理
- 任务负载均衡
- 工作线程健康检查
- 优雅关闭和资源清理
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any, Set
from enum import Enum
import threading
import uuid
from concurrent.futures import ThreadPoolExecutor, Future

from app.core.logging import logger
from app.core.exceptions import TaskException, WorkerError
from app.tasks.queue import TaskQueue, QueuedTask, get_task_queue
from app.tasks.progress import ProgressTracker, get_progress_tracker


class WorkerStatus(str, Enum):
    """工作线程状态"""
    IDLE = "idle"           # 空闲
    BUSY = "busy"           # 忙碌
    ERROR = "error"         # 错误
    STOPPING = "stopping"   # 停止中


class WorkerInfo:
    """工作线程信息"""
    
    def __init__(self, worker_id: str, max_tasks: int = 100):
        self.worker_id = worker_id
        self.status = WorkerStatus.IDLE
        self.current_task_id: Optional[str] = None
        self.tasks_processed = 0
        self.tasks_failed = 0
        self.start_time = datetime.utcnow()
        self.last_heartbeat = datetime.utcnow()
        self.max_tasks = max_tasks
        self.processing_time_total = 0.0
        self.error_message: Optional[str] = None
    
    def update_heartbeat(self):
        """更新心跳时间"""
        self.last_heartbeat = datetime.utcnow()
    
    def is_healthy(self, timeout: int = 60) -> bool:
        """检查工作线程是否健康"""
        time_since_heartbeat = (datetime.utcnow() - self.last_heartbeat).total_seconds()
        return time_since_heartbeat < timeout
    
    def get_uptime(self) -> float:
        """获取运行时间（秒）"""
        return (datetime.utcnow() - self.start_time).total_seconds()
    
    def get_average_processing_time(self) -> float:
        """获取平均处理时间"""
        if self.tasks_processed == 0:
            return 0.0
        return self.processing_time_total / self.tasks_processed
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total_tasks = self.tasks_processed + self.tasks_failed
        if total_tasks == 0:
            return 1.0
        return self.tasks_processed / total_tasks


class TaskWorker:
    """单个任务工作线程"""
    
    def __init__(self, worker_info: WorkerInfo, task_processors: Dict[str, Callable]):
        self.worker_info = worker_info
        self.task_processors = task_processors
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        self.task_queue: Optional[TaskQueue] = None
        self.progress_tracker: Optional[ProgressTracker] = None
    
    async def start(self):
        """启动工作线程"""
        self.is_running = True
        self.task_queue = await get_task_queue()
        self.progress_tracker = await get_progress_tracker()
        
        logger.info(f"工作线程启动: {self.worker_info.worker_id}")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 更新心跳
                self.worker_info.update_heartbeat()
                
                # 检查是否达到最大任务数
                if self.worker_info.tasks_processed >= self.worker_info.max_tasks:
                    logger.info(f"工作线程达到最大任务数，准备重启: {self.worker_info.worker_id}")
                    break
                
                # 从队列获取任务
                task = await self._get_next_task()
                if task is None:
                    continue
                
                # 处理任务
                await self._process_task(task)
                
            except asyncio.CancelledError:
                logger.info(f"工作线程被取消: {self.worker_info.worker_id}")
                break
            except Exception as e:
                self.worker_info.status = WorkerStatus.ERROR
                self.worker_info.error_message = str(e)
                logger.error(f"工作线程发生错误: {self.worker_info.worker_id} - {str(e)}")
                
                # 错误恢复等待
                await asyncio.sleep(5)
                self.worker_info.status = WorkerStatus.IDLE
                self.worker_info.error_message = None
        
        self.worker_info.status = WorkerStatus.STOPPING
        logger.info(f"工作线程停止: {self.worker_info.worker_id}")
    
    async def stop(self):
        """停止工作线程"""
        self.is_running = False
        self.shutdown_event.set()
        self.worker_info.status = WorkerStatus.STOPPING
    
    async def _get_next_task(self) -> Optional[QueuedTask]:
        """从队列获取下一个任务"""
        try:
            if self.task_queue is None:
                return None
            
            # 使用较短的超时时间，以便能够及时响应停止信号
            task = await self.task_queue.dequeue(timeout=1)
            return task
            
        except Exception as e:
            logger.error(f"获取任务失败: {self.worker_info.worker_id} - {str(e)}")
            return None
    
    async def _process_task(self, task: QueuedTask):
        """处理单个任务"""
        start_time = time.time()
        
        try:
            self.worker_info.status = WorkerStatus.BUSY
            self.worker_info.current_task_id = task.task_id
            
            logger.info(f"开始处理任务: {task.task_id} (工作线程: {self.worker_info.worker_id})")
            
            # 初始化进度跟踪
            if self.progress_tracker:
                await self.progress_tracker.start_task(task.task_id, "开始处理任务")
            
            # 查找任务处理器
            processor = self.task_processors.get(task.task_type)
            if processor is None:
                raise TaskException(f"未找到任务类型处理器: {task.task_type}")
            
            # 执行任务处理器
            if asyncio.iscoroutinefunction(processor):
                result = await processor(task)
            else:
                # 在线程池中执行同步任务
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, processor, task)
            
            # 标记任务完成
            if self.task_queue:
                await self.task_queue.complete_task(task.task_id)
            
            if self.progress_tracker:
                await self.progress_tracker.complete_task(task.task_id, "任务完成")
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self.worker_info.tasks_processed += 1
            self.worker_info.processing_time_total += processing_time
            
            logger.info(f"任务处理完成: {task.task_id}, 耗时: {processing_time:.2f}秒")
            
        except Exception as e:
            # 任务处理失败
            processing_time = time.time() - start_time
            self.worker_info.tasks_failed += 1
            
            error_msg = f"任务处理失败: {task.task_id} - {str(e)}"
            logger.error(error_msg)
            
            # 标记任务失败
            if self.task_queue:
                await self.task_queue.fail_task(task.task_id, str(e), retry=True)
            
            if self.progress_tracker:
                await self.progress_tracker.fail_task(task.task_id, str(e))
            
        finally:
            # 重置工作线程状态
            self.worker_info.status = WorkerStatus.IDLE
            self.worker_info.current_task_id = None


class WorkerPool:
    """工作线程池管理器"""
    
    def __init__(
        self,
        min_workers: int = 2,
        max_workers: int = 10,
        max_tasks_per_worker: int = 100
    ):
        self.min_workers = min_workers
        self.max_workers = max_workers
        self.max_tasks_per_worker = max_tasks_per_worker
        
        self.workers: Dict[str, WorkerInfo] = {}
        self.task_processors: Dict[str, Callable] = {}
        self.is_running = False
        
        # 统计信息
        self.stats = {
            'workers_created': 0,
            'workers_destroyed': 0,
            'total_tasks_processed': 0,
            'total_tasks_failed': 0,
            'pool_start_time': None
        }
    
    def register_processor(self, task_type: str, processor: Callable):
        """注册任务处理器"""
        self.task_processors[task_type] = processor
        logger.info(f"注册任务处理器: {task_type}")
    
    async def start(self):
        """启动工作线程池"""
        if self.is_running:
            return
        
        self.is_running = True
        self.stats['pool_start_time'] = datetime.utcnow()
        
        # 创建最小数量的工作线程
        for i in range(self.min_workers):
            await self._create_worker()
        
        logger.info(f"工作线程池启动完成: {len(self.workers)} 个工作线程")
    
    async def stop(self):
        """停止工作线程池"""
        self.is_running = False
        self.workers.clear()
        logger.info("工作线程池已停止")
    
    async def get_pool_stats(self) -> Dict[str, Any]:
        """获取线程池统计信息"""
        worker_stats = []
        total_tasks_processed = 0
        total_tasks_failed = 0
        
        for worker_id, worker_info in self.workers.items():
            worker_stats.append({
                'worker_id': worker_id,
                'status': worker_info.status.value,
                'current_task_id': worker_info.current_task_id,
                'tasks_processed': worker_info.tasks_processed,
                'tasks_failed': worker_info.tasks_failed,
                'uptime': worker_info.get_uptime(),
                'is_healthy': worker_info.is_healthy()
            })
            
            total_tasks_processed += worker_info.tasks_processed
            total_tasks_failed += worker_info.tasks_failed
        
        return {
            'pool_status': 'running' if self.is_running else 'stopped',
            'worker_count': len(self.workers),
            'min_workers': self.min_workers,
            'max_workers': self.max_workers,
            'registered_processors': list(self.task_processors.keys()),
            'total_tasks_processed': total_tasks_processed,
            'total_tasks_failed': total_tasks_failed,
            'workers': worker_stats,
            **self.stats
        }
    
    async def _create_worker(self) -> str:
        """创建新的工作线程"""
        worker_id = f"worker-{uuid.uuid4().hex[:8]}"
        worker_info = WorkerInfo(worker_id, self.max_tasks_per_worker)
        
        self.workers[worker_id] = worker_info
        self.stats['workers_created'] += 1
        
        logger.info(f"创建工作线程: {worker_id}")
        return worker_id


# 全局工作线程池实例
_worker_pool: Optional[WorkerPool] = None


async def get_worker_pool() -> WorkerPool:
    """获取全局工作线程池实例"""
    global _worker_pool
    
    if _worker_pool is None:
        _worker_pool = WorkerPool()
        await _worker_pool.start()
    
    return _worker_pool 