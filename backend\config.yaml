# ==================================================
# Word文档分析服务 - 配置文件
# ==================================================

# 服务信息
service:
  name: "Word Document Analysis Service"
  version: "0.1.0"
  description: "基于FastAPI的Word文档分析服务"
  author: "Word Service Team"
  
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  max_connections: 1000
  keepalive_timeout: 5
  graceful_timeout: 30
  
# 应用配置
app:
  debug: false
  reload: false
  title: "Word Document Analysis API"
  description: "专业的Word文档解析和论文检测服务"
  version: "v1"
  docs_url: "/docs"
  redoc_url: "/redoc"
  openapi_url: "/openapi.json"
  
# 数据库配置 (PostgreSQL)
database:
  url: "postgresql+asyncpg://postgres:zr3800855@localhost:5432/word_service"
  echo: false
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  pool_recycle: 3600
  
# Redis配置
redis:
  url: "redis://localhost:6379/0"
  max_connections: 10
  timeout: 5
  retry_on_timeout: true
  encoding: "utf-8"
  decode_responses: true
  
# 任务处理配置 (PostgreSQL优化)
tasks:
  max_concurrent: 20  # PostgreSQL支持更高并发
  timeout: 300
  queue_name: "word_analysis_tasks"
  retry_attempts: 3
  retry_delay: 5
  batch_size: 10
  worker_count: 3
  
# Word COM接口配置
word_com:
  startup_timeout: 30
  operation_timeout: 120
  restart_interval: 100
  pool_size: 3
  visible: false
  display_alerts: false
  enable_events: false
  
# 文件处理配置 (PostgreSQL优化)
files:
  max_size: 104857600  # 100MB (PostgreSQL支持更大文件)
  allowed_extensions:
    - ".docx"
  upload_path: "data/uploads"
  temp_path: "data/temp"
  images_path: "data/images"
  reports_path: "data/reports"
  backup_path: "data/backups"
  temp_retention_hours: 24
  image_quality: 100
  
# 安全配置
security:
  secret_key: "your-secret-key-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 1440
  refresh_token_expire_days: 7
  password_min_length: 8
  bcrypt_rounds: 12
  
# 限流配置
rate_limit:
  per_user_minute: 100
  per_ip_minute: 200
  daily_task_limit: 50
  burst_limit: 10
  
# 日志配置
logging:
  level: "INFO"
  format: "json"
  file: "logs/word_service.log"
  max_size: 10485760  # 10MB
  backup_count: 5
  rotation: "midnight"
  console_output: true
  
# 监控配置 (PostgreSQL优化)
monitoring:
  enabled: true
  stats_retention_days: 90  # PostgreSQL支持更长保留期
  health_check_interval: 60
  performance_tracking: true
  metrics_endpoint: "/metrics"
  
# 论文检测配置
paper_check:
  enabled: true
  standards_config: "config/paper_standards.json"
  rules_directory: "config/rules"
  auto_fix_suggestions: true
  default_standard: "undergraduate"
  custom_rules: true
  
# 功能开关 (PostgreSQL优化)
features:
  image_extraction: true
  pdf_report: true
  batch_processing: true  # PostgreSQL支持批量处理
  email_notifications: false
  api_docs: true
  
# 邮件配置 (可选)
email:
  smtp_host: "smtp.example.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-email-password"
  from_email: "<EMAIL>"
  use_tls: true
  
# 开发配置
development:
  hot_reload: false
  show_error_details: false
  log_sql_queries: false
  skip_auth: false
  mock_com_interface: false
  test_mode: false
  
# 生产配置
production:
  compress_responses: true
  cors_origins:
    - "https://your-domain.com"
  trusted_hosts:
    - "your-domain.com"
  max_request_size: 52428800
  request_timeout: 300
  
# API版本配置
api:
  v1:
    enabled: true
    prefix: "/api/v1"
    description: "Word Document Analysis API v1"
    deprecation_notice: null
    
# 缓存配置
cache:
  default_ttl: 3600
  max_entries: 10000
  document_analysis_ttl: 7200
  user_session_ttl: 1800
  system_stats_ttl: 300
  
# 错误处理配置
error_handling:
  include_traceback: false
  log_exceptions: true
  custom_error_pages: true
  error_notification: false
  
# 性能配置
performance:
  enable_compression: true
  compression_level: 6
  enable_caching: true
  optimize_images: true
  lazy_loading: true
  
# 数据清理配置
cleanup:
  temp_files:
    enabled: true
    interval_hours: 1
    retention_hours: 24
  log_files:
    enabled: true
    interval_days: 1
    retention_days: 30
  old_tasks:
    enabled: true
    interval_hours: 6
    retention_days: 7 