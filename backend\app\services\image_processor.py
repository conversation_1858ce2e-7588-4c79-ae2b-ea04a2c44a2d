"""
图片提取处理模块

提供Word文档图片提取、格式转换、存储管理等功能
"""

import os
import uuid
import zipfile
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
import structlog
import io

from app.core.config import settings
from app.core.exceptions import ImageProcessorError, ValidationError
from app.services.storage import get_storage_manager, StorageManager

logger = structlog.get_logger()

# 图片处理配置
SUPPORTED_IMAGE_FORMATS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.emf', '.wmf'}
OUTPUT_IMAGE_FORMAT = getattr(settings, 'OUTPUT_IMAGE_FORMAT', 'PNG')
MAX_IMAGE_SIZE = getattr(settings, 'MAX_IMAGE_SIZE', 10 * 1024 * 1024)  # 10MB

class ImageProcessor:
    """图片处理器"""
    
    def __init__(self, storage_manager: StorageManager = None):
        """
        初始化图片处理器
        
        Args:
            storage_manager: 存储管理器实例
        """
        self.storage_manager = storage_manager or get_storage_manager()
        
        # 处理统计
        self.processing_stats = {
            'documents_processed': 0,
            'images_extracted': 0,
            'images_stored': 0,
            'processing_errors': 0,
            'total_image_size': 0
        }
    
    def extract_images_from_docx(self, file_path: str, file_id: str) -> Dict[str, Any]:
        """
        从DOCX文档中提取图片
        
        Args:
            file_path: DOCX文件路径
            file_id: 文件ID
            
        Returns:
            提取结果字典
            
        Raises:
            ImageProcessorError: 图片提取失败
        """
        try:
            self.processing_stats['documents_processed'] += 1
            
            file_path = Path(file_path)
            if not file_path.exists():
                raise ValidationError(f"文件不存在: {file_path}")
            
            if file_path.suffix.lower() != '.docx':
                raise ValidationError(f"不支持的文件格式: {file_path.suffix}")
            
            logger.info(f"开始从DOCX文档提取图片: {file_path.name}")
            
            extracted_images = []
            
            # DOCX文件实际上是ZIP文件
            with zipfile.ZipFile(file_path, 'r') as docx_zip:
                # 查找媒体文件夹中的图片
                media_files = [f for f in docx_zip.namelist() if f.startswith('word/media/')]
                
                for media_file in media_files:
                    try:
                        # 获取文件扩展名
                        file_ext = Path(media_file).suffix.lower()
                        if file_ext not in SUPPORTED_IMAGE_FORMATS:
                            logger.debug(f"跳过不支持的图片格式: {media_file}")
                            continue
                        
                        # 读取图片数据
                        image_data = docx_zip.read(media_file)
                        
                        if len(image_data) > MAX_IMAGE_SIZE:
                            logger.warning(f"图片文件过大，跳过: {media_file} ({len(image_data)} bytes)")
                            continue
                        
                        # 生成图片信息
                        image_info = self._process_extracted_image(
                            image_data=image_data,
                            original_name=Path(media_file).name,
                            file_id=file_id,
                            source_path=media_file
                        )
                        
                        if image_info:
                            extracted_images.append(image_info)
                            self.processing_stats['images_extracted'] += 1
                            self.processing_stats['total_image_size'] += len(image_data)
                        
                    except Exception as e:
                        self.processing_stats['processing_errors'] += 1
                        logger.error(f"处理图片失败 {media_file}: {str(e)}")
                        continue
            
            result = {
                'file_id': file_id,
                'source_file': str(file_path),
                'images_found': len(extracted_images),
                'images_extracted': len(extracted_images),
                'images': extracted_images,
                'extracted_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"DOCX图片提取完成: {file_path.name}, 提取了 {len(extracted_images)} 张图片")
            
            return result
            
        except Exception as e:
            self.processing_stats['processing_errors'] += 1
            logger.error(f"DOCX图片提取失败: {str(e)}")
            raise ImageProcessorError(f"DOCX图片提取失败: {str(e)}")
    
    def _process_extracted_image(
        self,
        image_data: bytes,
        original_name: str,
        file_id: str,
        source_path: str
    ) -> Optional[Dict[str, Any]]:
        """
        处理提取的图片数据
        
        Args:
            image_data: 图片二进制数据
            original_name: 原始文件名
            file_id: 文件ID
            source_path: 源路径
            
        Returns:
            图片信息字典，如果处理失败返回None
        """
        try:
            # 生成图片ID
            image_id = str(uuid.uuid4())
            
            # 分析图片信息
            image_info = self._analyze_image(image_data, original_name)
            
            # 创建临时文件
            temp_dir = Path('./data/temp_images')
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            temp_file_path = temp_dir / f"{image_id}.{OUTPUT_IMAGE_FORMAT.lower()}"
            
            with open(temp_file_path, 'wb') as f:
                f.write(image_data)
            
            # 存储图片
            storage_result = self.storage_manager.store_processed_file(
                source_path=str(temp_file_path),
                file_id=file_id,
                file_type='image',
                metadata={
                    'image_id': image_id,
                    'original_name': original_name,
                    'source_path': source_path,
                    'format': image_info.get('format', 'unknown'),
                    'width': image_info.get('width'),
                    'height': image_info.get('height'),
                    'size_bytes': len(image_data)
                }
            )
            
            # 清理临时文件
            try:
                temp_file_path.unlink()
            except Exception:
                pass
            
            self.processing_stats['images_stored'] += 1
            
            return {
                'image_id': image_id,
                'file_id': storage_result['file_id'],
                'original_name': original_name,
                'source_path': source_path,
                'storage_path': storage_result['storage_path'],
                'relative_path': storage_result['relative_path'],
                'format': image_info.get('format', 'unknown'),
                'width': image_info.get('width'),
                'height': image_info.get('height'),
                'size_bytes': len(image_data),
                'extracted_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"处理图片数据失败: {str(e)}")
            return None
    
    def _analyze_image(self, image_data: bytes, filename: str) -> Dict[str, Any]:
        """
        分析图片信息
        
        Args:
            image_data: 图片数据
            filename: 文件名
            
        Returns:
            图片信息字典
        """
        try:
            # 基础信息
            info = {
                'size_bytes': len(image_data),
                'filename': filename
            }
            
            # 尝试检测图片格式
            if image_data.startswith(b'\x89PNG'):
                info['format'] = 'PNG'
            elif image_data.startswith(b'\xff\xd8\xff'):
                info['format'] = 'JPEG'
            elif image_data.startswith(b'GIF8'):
                info['format'] = 'GIF'
            elif image_data.startswith(b'BM'):
                info['format'] = 'BMP'
            else:
                info['format'] = 'unknown'
            
            return info
                
        except Exception as e:
            logger.warning(f"图片分析失败: {str(e)}")
            return {
                'format': 'unknown',
                'size_bytes': len(image_data),
                'error': str(e)
            }
    
    def extract_images_from_document(self, file_path: str, file_id: str) -> Dict[str, Any]:
        """
        从文档中提取图片（自动检测格式）
        
        Args:
            file_path: 文档文件路径
            file_id: 文件ID
            
        Returns:
            提取结果字典
        """
        try:
            file_path = Path(file_path)
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.docx':
                return self.extract_images_from_docx(str(file_path), file_id)
            elif file_ext == '.doc':
                # DOC格式图片提取需要COM接口，这里先返回空结果
                logger.warning("DOC格式图片提取暂未实现，需要COM接口支持")
                return {
                    'file_id': file_id,
                    'source_file': str(file_path),
                    'images_found': 0,
                    'images_extracted': 0,
                    'images': [],
                    'extracted_at': datetime.utcnow().isoformat(),
                    'note': 'DOC格式图片提取暂未实现'
                }
            else:
                raise ValidationError(f"不支持的文档格式: {file_ext}")
                
        except Exception as e:
            logger.error(f"文档图片提取失败: {str(e)}")
            raise ImageProcessorError(f"文档图片提取失败: {str(e)}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        # 计算平均值
        if stats['documents_processed'] > 0:
            stats['avg_images_per_document'] = stats['images_extracted'] / stats['documents_processed']
        else:
            stats['avg_images_per_document'] = 0
        
        if stats['images_extracted'] > 0:
            stats['avg_image_size'] = stats['total_image_size'] / stats['images_extracted']
        else:
            stats['avg_image_size'] = 0
        
        # 添加配置信息
        stats['configuration'] = {
            'supported_formats': list(SUPPORTED_IMAGE_FORMATS),
            'output_format': OUTPUT_IMAGE_FORMAT,
            'max_image_size_mb': MAX_IMAGE_SIZE / (1024 * 1024)
        }
        
        return stats

# 全局图片处理器实例
_image_processor = None

def get_image_processor() -> ImageProcessor:
    """获取全局图片处理器实例"""
    global _image_processor
    if _image_processor is None:
        _image_processor = ImageProcessor()
    return _image_processor

    async def generate_thumbnail(self, image_path: str, size: int = 200) -> bytes:
        """
        生成图片缩略图
        
        Args:
            image_path: 图片文件路径
            size: 缩略图大小(像素)
            
        Returns:
            缩略图二进制数据
        """
        try:
            from PIL import Image
            
            # 打开图片
            with Image.open(image_path) as img:
                # 计算缩略图尺寸，保持纵横比
                width, height = img.size
                if width > height:
                    new_width = size
                    new_height = int(height * size / width)
                else:
                    new_height = size
                    new_width = int(width * size / height)
                
                # 生成缩略图
                thumbnail = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 转换为字节
                thumbnail_bytes = io.BytesIO()
                thumbnail.save(thumbnail_bytes, format='PNG')
                
                return thumbnail_bytes.getvalue()
                
        except Exception as e:
            logger.error(f"生成缩略图失败: {str(e)}")
            raise ImageProcessorError(f"生成缩略图失败: {str(e)}")

def extract_images_from_document(file_path: str, file_id: str) -> Dict[str, Any]:
    """
    快捷函数：从文档中提取图片
    
    Args:
        file_path: 文档路径
        file_id: 文件ID
        
    Returns:
        提取结果
    """
    processor = get_image_processor()
    return processor.extract_images_from_document(file_path, file_id)
