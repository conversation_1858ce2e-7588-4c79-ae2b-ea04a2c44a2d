"""
错误消息格式化工具

提供统一的错误消息格式化和用户友好的错误提示功能。
"""

from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass


class ErrorCategory(Enum):
    """错误类别"""
    STRUCTURE = "structure"
    FORMAT = "format"
    CONTENT = "content"
    REFERENCE = "reference"
    STYLE = "style"


class ErrorSeverity(Enum):
    """错误严重程度"""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ErrorDetail:
    """错误详情"""
    category: ErrorCategory
    severity: ErrorSeverity
    code: str
    message: str
    suggestion: Optional[str] = None
    position: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None


class ErrorFormatter:
    """错误消息格式化器"""
    
    def __init__(self):
        self.error_templates = self._load_error_templates()
        self.suggestion_templates = self._load_suggestion_templates()
    
    def format_error(self, error_code: str, **kwargs) -> ErrorDetail:
        """
        格式化错误消息。
        
        Args:
            error_code: 错误代码
            **kwargs: 格式化参数
            
        Returns:
            ErrorDetail: 格式化后的错误详情
        """
        template = self.error_templates.get(error_code)
        if not template:
            return self._create_generic_error(error_code, kwargs)
        
        # 格式化消息
        try:
            message = template["message"].format(**kwargs)
        except (KeyError, ValueError):
            message = template["message"]
        
        # 格式化建议
        suggestion = None
        if "suggestion" in template:
            try:
                suggestion = template["suggestion"].format(**kwargs)
            except (KeyError, ValueError):
                suggestion = template["suggestion"]
        
        return ErrorDetail(
            category=ErrorCategory(template["category"]),
            severity=ErrorSeverity(template["severity"]),
            code=error_code,
            message=message,
            suggestion=suggestion,
            position=kwargs.get("position"),
            context=kwargs.get("context")
        )
    
    def format_multiple_errors(self, errors: List[Dict[str, Any]]) -> List[ErrorDetail]:
        """
        格式化多个错误。
        
        Args:
            errors: 错误列表
            
        Returns:
            List[ErrorDetail]: 格式化后的错误详情列表
        """
        formatted_errors = []
        for error in errors:
            error_code = error.pop("code", "GENERIC_ERROR")
            formatted_error = self.format_error(error_code, **error)
            formatted_errors.append(formatted_error)
        
        return formatted_errors
    
    def create_summary_message(self, errors: List[ErrorDetail]) -> str:
        """
        创建错误摘要消息。
        
        Args:
            errors: 错误详情列表
            
        Returns:
            str: 摘要消息
        """
        if not errors:
            return "检查通过，未发现问题。"
        
        # 按严重程度分组
        critical_errors = [e for e in errors if e.severity == ErrorSeverity.CRITICAL]
        errors_list = [e for e in errors if e.severity == ErrorSeverity.ERROR]
        warnings = [e for e in errors if e.severity == ErrorSeverity.WARNING]
        
        summary_parts = []
        
        if critical_errors:
            summary_parts.append(f"发现{len(critical_errors)}个严重问题")
        
        if errors_list:
            summary_parts.append(f"发现{len(errors_list)}个错误")
        
        if warnings:
            summary_parts.append(f"发现{len(warnings)}个警告")
        
        summary = "，".join(summary_parts) + "。"
        
        # 添加主要问题描述
        main_issues = (critical_errors + errors_list)[:3]
        if main_issues:
            issue_descriptions = [self._get_short_description(error) for error in main_issues]
            summary += f" 主要问题：{'; '.join(issue_descriptions)}"
            if len(critical_errors + errors_list) > 3:
                summary += "等"
        
        return summary
    
    def create_detailed_report(self, errors: List[ErrorDetail]) -> str:
        """
        创建详细的错误报告。
        
        Args:
            errors: 错误详情列表
            
        Returns:
            str: 详细报告
        """
        if not errors:
            return "✅ 检查通过，文档格式符合要求。"
        
        report_lines = []
        report_lines.append("📋 检测报告")
        report_lines.append("=" * 40)
        
        # 按类别分组
        by_category = {}
        for error in errors:
            category = error.category.value
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(error)
        
        category_names = {
            "structure": "📚 结构问题",
            "format": "🎨 格式问题", 
            "content": "📝 内容问题",
            "reference": "📖 引用问题",
            "style": "✨ 样式问题"
        }
        
        for category, category_errors in by_category.items():
            report_lines.append(f"\n{category_names.get(category, category)}:")
            
            for i, error in enumerate(category_errors, 1):
                severity_icon = self._get_severity_icon(error.severity)
                report_lines.append(f"  {i}. {severity_icon} {error.message}")
                
                if error.suggestion:
                    report_lines.append(f"     💡 建议：{error.suggestion}")
                
                if error.position:
                    pos_info = self._format_position(error.position)
                    if pos_info:
                        report_lines.append(f"     📍 位置：{pos_info}")
        
        # 添加总结
        report_lines.append(f"\n📊 总结：共发现{len(errors)}个问题")
        
        return "\n".join(report_lines)
    
    def _load_error_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载错误模板"""
        return {
            "MISSING_SECTION": {
                "category": "structure",
                "severity": "error",
                "message": "缺少必需章节：{section_name}",
                "suggestion": "请在文档中添加{section_name}部分，确保章节完整性。"
            },
            "SECTION_ORDER_ERROR": {
                "category": "structure", 
                "severity": "error",
                "message": "章节顺序错误：{current_section}应该在{expected_section}之前",
                "suggestion": "请调整章节顺序，确保符合标准结构要求。"
            },
            "FONT_FAMILY_ERROR": {
                "category": "format",
                "severity": "error", 
                "message": "{element_type}字体不正确，期望：{expected}，实际：{actual}",
                "suggestion": "请将{element_type}字体设置为{expected}。"
            },
            "FONT_SIZE_ERROR": {
                "category": "format",
                "severity": "error",
                "message": "{element_type}字号不正确，期望：{expected}，实际：{actual}",
                "suggestion": "请将{element_type}字号调整为{expected}。"
            },
            "ALIGNMENT_ERROR": {
                "category": "format",
                "severity": "error",
                "message": "{element_type}对齐方式不正确，期望：{expected}，实际：{actual}",
                "suggestion": "请将{element_type}对齐方式设置为{expected}。"
            },
            "CONTENT_LENGTH_ERROR": {
                "category": "content",
                "severity": "error",
                "message": "{content_type}长度不符合要求，期望：{min_length}-{max_length}{unit}，实际：{actual_length}{unit}",
                "suggestion": "请调整{content_type}长度至{min_length}-{max_length}{unit}范围内。"
            },
            "KEYWORD_COUNT_ERROR": {
                "category": "content",
                "severity": "error",
                "message": "关键词数量不符合要求，期望：{min_count}-{max_count}个，实际：{actual_count}个",
                "suggestion": "请调整关键词数量至{min_count}-{max_count}个。"
            },
            "REFERENCE_FORMAT_ERROR": {
                "category": "reference",
                "severity": "error",
                "message": "参考文献格式错误：{error_detail}",
                "suggestion": "请按照标准格式调整参考文献，确保编号连续且格式正确。"
            },
            "PAGE_SETUP_ERROR": {
                "category": "format",
                "severity": "warning",
                "message": "页面设置不符合建议：{setting_name}为{actual_value}，建议：{recommended_value}",
                "suggestion": "建议将{setting_name}调整为{recommended_value}以获得更好的效果。"
            }
        }
    
    def _load_suggestion_templates(self) -> Dict[str, str]:
        """加载建议模板"""
        return {
            "font_family": "建议使用{recommended_font}字体以确保文档的专业性和可读性。",
            "font_size": "建议使用{recommended_size}字号以保持文档的层次结构清晰。",
            "alignment": "建议使用{recommended_alignment}对齐以符合学术论文的标准格式。",
            "content_length": "建议调整内容长度以满足学术要求，确保内容充实且重点突出。",
            "structure": "建议按照标准论文结构组织内容，确保逻辑清晰、层次分明。"
        }
    
    def _create_generic_error(self, error_code: str, kwargs: Dict[str, Any]) -> ErrorDetail:
        """创建通用错误"""
        return ErrorDetail(
            category=ErrorCategory.FORMAT,
            severity=ErrorSeverity.ERROR,
            code=error_code,
            message=f"检测到问题：{error_code}",
            suggestion="请检查相关内容并按照标准要求进行调整。"
        )
    
    def _get_short_description(self, error: ErrorDetail) -> str:
        """获取错误的简短描述"""
        message = error.message
        if len(message) > 30:
            return message[:27] + "..."
        return message
    
    def _get_severity_icon(self, severity: ErrorSeverity) -> str:
        """获取严重程度图标"""
        icons = {
            ErrorSeverity.CRITICAL: "🚨",
            ErrorSeverity.ERROR: "❌", 
            ErrorSeverity.WARNING: "⚠️",
            ErrorSeverity.INFO: "ℹ️"
        }
        return icons.get(severity, "❓")
    
    def _format_position(self, position: Dict[str, Any]) -> str:
        """格式化位置信息"""
        parts = []
        
        if "page" in position:
            parts.append(f"第{position['page']}页")
        
        if "paragraph" in position:
            parts.append(f"第{position['paragraph']}段")
        
        if "line" in position:
            parts.append(f"第{position['line']}行")
        
        return "，".join(parts) if parts else ""


# 全局错误格式化器实例
error_formatter = ErrorFormatter()
