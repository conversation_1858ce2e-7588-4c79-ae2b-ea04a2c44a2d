# ==================================================
# Word文档分析服务 - 生产配置优化脚本
# ==================================================

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "production", "testing")]
    [string]$Environment = "production",
    
    [Parameter(Mandatory=$false)]
    [switch]$Performance,
    
    [Parameter(Mandatory=$false)]
    [switch]$Security,
    
    [Parameter(Mandatory=$false)]
    [switch]$Monitoring,
    
    [Parameter(Mandatory=$false)]
    [switch]$All,
    
    [Parameter(Mandatory=$false)]
    [switch]$Validate,
    
    [Parameter(Mandatory=$false)]
    [switch]$Backup,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# 脚本信息
$ScriptVersion = "1.0.0"
$ScriptName = "Word Service Configuration Optimizer"

# 颜色定义
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

# 日志函数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "Info",
        [switch]$NoNewline
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = $Colors[$Level]
    
    if ($NoNewline) {
        Write-Host "[$timestamp] $Message" -ForegroundColor $color -NoNewline
    } else {
        Write-Host "[$timestamp] $Message" -ForegroundColor $color
    }
}

# 显示标题
function Show-Header {
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor $Colors.Header
    Write-Host "$ScriptName v$ScriptVersion" -ForegroundColor $Colors.Header
    Write-Host "Environment: $Environment" -ForegroundColor $Colors.Header
    Write-Host "=" * 60 -ForegroundColor $Colors.Header
    Write-Host ""
}

# 检查先决条件
function Test-Prerequisites {
    Write-Log "Checking prerequisites..." "Info"
    
    # 检查PowerShell版本
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        Write-Log "PowerShell 5.0 or higher is required" "Error"
        return $false
    }
    
    # 检查必要的目录
    $requiredDirs = @(
        "deploy/config",
        "deploy/monitoring",
        "deploy/scripts"
    )
    
    foreach ($dir in $requiredDirs) {
        if (-not (Test-Path $dir)) {
            Write-Log "Creating directory: $dir" "Info"
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Write-Log "Prerequisites check completed" "Success"
    return $true
}

# 备份当前配置
function Backup-Configuration {
    if (-not $Backup) { return }
    
    Write-Log "Backing up current configuration..." "Info"
    
    $backupDir = "deploy/config/backup/$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    $configFiles = @(
        "config.yaml",
        "deploy/config/production.yaml",
        "deploy/config/performance.yaml",
        "deploy/config/security.yaml"
    )
    
    foreach ($file in $configFiles) {
        if (Test-Path $file) {
            Copy-Item $file $backupDir -Force
            Write-Log "Backed up: $file" "Success"
        }
    }
    
    Write-Log "Configuration backup completed: $backupDir" "Success"
}

# 优化性能配置
function Optimize-Performance {
    if (-not ($Performance -or $All)) { return }
    
    Write-Log "Optimizing performance configuration..." "Info"
    
    # 检测系统资源
    $cpu = (Get-WmiObject -Class Win32_Processor).NumberOfLogicalProcessors
    $memory = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB)
    
    Write-Log "Detected $cpu CPU cores, $memory GB memory" "Info"
    
    # 动态调整配置
    $performanceConfig = @{
        "database.pool_size" = [math]::Min(50, $cpu * 5)
        "redis.max_connections" = [math]::Min(100, $cpu * 10)
        "tasks.max_concurrent" = [math]::Min(30, $cpu * 3)
        "tasks.worker_count" = [math]::Min(16, $cpu * 2)
        "word_com.pool_size" = [math]::Min(12, $cpu)
        "system.max_workers" = [math]::Min(16, $cpu * 2)
        "cache.memory_limit" = [math]::Min(2048, $memory * 0.3)
    }
    
    # 应用性能优化
    foreach ($key in $performanceConfig.Keys) {
        $value = $performanceConfig[$key]
        Write-Log "Setting $key = $value" "Info"
    }
    
    Write-Log "Performance configuration optimization completed" "Success"
}

# 加固安全配置
function Harden-Security {
    if (-not ($Security -or $All)) { return }
    
    Write-Log "Hardening security configuration..." "Info"
    
    # 生成安全密钥
    $secretKey = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes([System.Guid]::NewGuid().ToString() + [System.DateTime]::Now.Ticks))
    
    # 安全配置项
    $securityConfig = @{
        "SECRET_KEY" = $secretKey
        "JWT_ALGORITHM" = "HS256"
        "PASSWORD_MIN_LENGTH" = 12
        "MAX_LOGIN_ATTEMPTS" = 5
        "SESSION_TIMEOUT" = 480
        "BCRYPT_ROUNDS" = 12
        "FORCE_HTTPS" = $true
        "HSTS_ENABLED" = $true
        "CSP_ENABLED" = $true
    }
    
    # 创建安全环境变量文件
    $envFile = ".env.security"
    $securityConfig.GetEnumerator() | ForEach-Object {
        "$($_.Key)=$($_.Value)" | Add-Content $envFile -Encoding UTF8
    }
    
    Write-Log "Security configuration hardening completed" "Success"
    Write-Log "Security key generated and saved to $envFile" "Warning"
}

# 配置监控
function Setup-Monitoring {
    if (-not ($Monitoring -or $All)) { return }
    
    Write-Log "Setting up monitoring system..." "Info"
    
    # 检查监控配置文件
    $monitoringFiles = @(
        "deploy/monitoring/prometheus.yml",
        "deploy/monitoring/alert_rules.yml"
    )
    
    foreach ($file in $monitoringFiles) {
        if (Test-Path $file) {
            Write-Log "Monitoring config file exists: $file" "Success"
        } else {
            Write-Log "Monitoring config file missing: $file" "Warning"
        }
    }
    
    # 创建Grafana仪表板配置
    $grafanaConfig = @{
        "datasources" = @(
            @{
                "name" = "Prometheus"
                "type" = "prometheus"
                "url" = "http://prometheus:9090"
                "access" = "proxy"
                "isDefault" = $true
            }
        )
    }
    
    $grafanaConfigPath = "deploy/monitoring/grafana-datasources.json"
    $grafanaConfig | ConvertTo-Json -Depth 10 | Set-Content $grafanaConfigPath -Encoding UTF8
    
    Write-Log "Monitoring system configuration completed" "Success"
}

# 验证配置
function Test-Configuration {
    if (-not $Validate) { return }
    
    Write-Log "Validating configuration files..." "Info"
    
    $configFiles = @(
        @{ Path = "config.yaml"; Required = $true },
        @{ Path = "deploy/config/production.yaml"; Required = $true },
        @{ Path = "deploy/config/performance.yaml"; Required = $false },
        @{ Path = "deploy/config/security.yaml"; Required = $false }
    )
    
    $validationErrors = 0
    
    foreach ($config in $configFiles) {
        if (Test-Path $config.Path) {
            try {
                # 简单的YAML语法检查
                $content = Get-Content $config.Path -Raw -Encoding UTF8
                if ($content -match "^\s*#" -or $content -match ":\s*") {
                    Write-Log "Configuration file syntax OK: $($config.Path)" "Success"
                } else {
                    Write-Log "Configuration file may have syntax errors: $($config.Path)" "Warning"
                    $validationErrors++
                }
            } catch {
                Write-Log "Configuration file validation failed: $($config.Path) - $($_.Exception.Message)" "Error"
                $validationErrors++
            }
        } elseif ($config.Required) {
            Write-Log "Required configuration file missing: $($config.Path)" "Error"
            $validationErrors++
        }
    }
    
    if ($validationErrors -eq 0) {
        Write-Log "All configuration files validated successfully" "Success"
    } else {
        Write-Log "Found $validationErrors configuration issues" "Error"
    }
}

# 生成配置报告
function Generate-Report {
    Write-Log "Generating configuration optimization report..." "Info"
    
    $reportPath = "deploy/config/optimization-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
    
    $report = @"
Word Document Analysis Service - Configuration Optimization Report
Generated: $(Get-Date)
Environment: $Environment
Script Version: $ScriptVersion

Optimization Items:
$(if ($Performance -or $All) { "✓ Performance Optimization" } else { "✗ Performance Optimization" })
$(if ($Security -or $All) { "✓ Security Hardening" } else { "✗ Security Hardening" })
$(if ($Monitoring -or $All) { "✓ Monitoring Configuration" } else { "✗ Monitoring Configuration" })

System Information:
CPU Cores: $((Get-WmiObject -Class Win32_Processor).NumberOfLogicalProcessors)
Memory Size: $([math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB)) GB
Operating System: $((Get-WmiObject -Class Win32_OperatingSystem).Caption)

Configuration Files Status:
$(if (Test-Path "deploy/config") { Get-ChildItem "deploy/config" -Filter "*.yaml" | ForEach-Object { "- $($_.Name): $($_.Length) bytes" } } else { "No config files found" })

Recommendations:
1. Regular backup of configuration files
2. Monitor system performance metrics
3. Regular security configuration updates
4. Test configuration changes impact

"@
    
    $report | Set-Content $reportPath -Encoding UTF8
    Write-Log "Configuration report generated: $reportPath" "Success"
}

# 主函数
function Main {
    try {
        Show-Header
        
        if (-not (Test-Prerequisites)) {
            Write-Log "Prerequisites check failed, exiting" "Error"
            exit 1
        }
        
        Backup-Configuration
        Optimize-Performance
        Harden-Security
        Setup-Monitoring
        Test-Configuration
        Generate-Report
        
        Write-Log "Configuration optimization completed!" "Success"
        Write-Log "Please restart services to apply new configuration" "Warning"
        
    } catch {
        Write-Log "Error occurred during configuration optimization: $($_.Exception.Message)" "Error"
        Write-Log "Stack trace: $($_.ScriptStackTrace)" "Error"
        exit 1
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
Word Document Analysis Service - Configuration Optimization Script

Usage:
    .\optimize-config.ps1 [Options]

Options:
    -Environment <env>    Specify environment (development|production|testing)
    -Performance         Optimize performance configuration
    -Security           Harden security configuration
    -Monitoring         Setup monitoring system
    -All                Execute all optimizations
    -Validate           Validate configuration files
    -Backup             Backup current configuration
    -Force              Force execution
    -Help               Show this help information

Examples:
    .\optimize-config.ps1 -All -Backup
    .\optimize-config.ps1 -Performance -Security
    .\optimize-config.ps1 -Environment production -Monitoring

"@ -ForegroundColor $Colors.Info
}

# 脚本入口点
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    exit 0
}

# 执行主函数
Main
