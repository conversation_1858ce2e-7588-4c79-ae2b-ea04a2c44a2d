"""
Word文档分析服务 - 自定义异常定义
"""

from typing import Optional, Dict, Any


class WordServiceException(Exception):
    """服务基础异常类"""
    
    def __init__(self, message: str, code: int = 50000, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class DocumentAnalysisException(WordServiceException):
    """文档分析异常"""
    
    def __init__(self, message: str, code: int = 10000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class COMInterfaceException(WordServiceException):
    """COM接口异常"""
    
    def __init__(self, message: str, code: int = 20000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class TaskException(WordServiceException):
    """任务管理异常"""
    
    def __init__(self, message: str, code: int = 30000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class FileHandlingException(WordServiceException):
    """文件处理异常"""
    
    def __init__(self, message: str, code: int = 40000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class PaperCheckException(WordServiceException):
    """论文检测异常"""
    
    def __init__(self, message: str, code: int = 50000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


# 具体异常定义
class FileNotSupportedException(DocumentAnalysisException):
    """文件格式不支持异常"""
    
    def __init__(self, file_type: str):
        super().__init__(f"不支持的文件格式: {file_type}", 10001)


class FileSizeExceededException(DocumentAnalysisException):
    """文件大小超限异常"""
    
    def __init__(self, file_size: int, max_size: int):
        super().__init__(f"文件大小超限: {file_size} > {max_size}", 10002)


class FileCorruptedException(DocumentAnalysisException):
    """文件损坏异常"""
    
    def __init__(self, filename: str):
        super().__init__(f"文件损坏无法读取: {filename}", 10003)


class COMInitializationException(COMInterfaceException):
    """COM接口初始化失败异常"""
    
    def __init__(self, error: str):
        super().__init__(f"COM接口初始化失败: {error}", 20001)


class WordApplicationCrashException(COMInterfaceException):
    """Word应用程序崩溃异常"""
    
    def __init__(self, error: str):
        super().__init__(f"Word应用程序崩溃: {error}", 20002)


class DocumentParsingTimeoutException(COMInterfaceException):
    """文档解析超时异常"""
    
    def __init__(self, timeout: int):
        super().__init__(f"文档解析超时: {timeout}秒", 20003)


class SystemResourceInsufficientException(TaskException):
    """系统资源不足异常"""
    
    def __init__(self, resource_type: str):
        super().__init__(f"系统资源不足: {resource_type}", 30001)


class ConcurrentTaskLimitException(TaskException):
    """并发任务数量超限异常"""
    
    def __init__(self, current: int, max_limit: int):
        super().__init__(f"并发任务数量超限: {current} > {max_limit}", 30002)


# 阶段5新增异常类型
class SecurityError(WordServiceException):
    """安全检查异常"""
    
    def __init__(self, message: str, code: int = 60000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ValidationError(WordServiceException):
    """验证异常"""
    
    def __init__(self, message: str, code: int = 61000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class StorageError(WordServiceException):
    """存储异常"""
    
    def __init__(self, message: str, code: int = 62000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class FileHandlerError(WordServiceException):
    """文件处理异常"""
    
    def __init__(self, message: str, code: int = 63000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ImageProcessorError(WordServiceException):
    """图片处理异常"""
    
    def __init__(self, message: str, code: int = 64000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class CleanupError(WordServiceException):
    """清理异常"""
    
    def __init__(self, message: str, code: int = 65000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


# 阶段6新增异常类型
class WorkerError(WordServiceException):
    """工作线程异常"""
    
    def __init__(self, message: str, code: int = 66000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class QueueError(WordServiceException):
    """队列异常"""
    
    def __init__(self, message: str, code: int = 67000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ConcurrencyError(WordServiceException):
    """并发控制异常"""
    
    def __init__(self, message: str, code: int = 68000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ProgressTrackingError(WordServiceException):
    """进度跟踪异常"""
    
    def __init__(self, message: str, code: int = 69000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


# 阶段7新增异常类型
class DocumentAnalysisError(WordServiceException):
    """文档分析引擎异常"""
    
    def __init__(self, message: str, code: int = 70000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ContentParsingError(WordServiceException):
    """内容解析异常"""
    
    def __init__(self, message: str, code: int = 71000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class FormatAnalysisError(WordServiceException):
    """格式分析异常"""
    
    def __init__(self, message: str, code: int = 72000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class OrderManagementError(WordServiceException):
    """顺序管理异常"""
    
    def __init__(self, message: str, code: int = 73000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class JSONFormattingError(WordServiceException):
    """JSON格式化异常"""
    
    def __init__(self, message: str, code: int = 74000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ProcessorError(WordServiceException):
    """文档处理器异常"""

    def __init__(self, message: str, code: int = 75000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)


class ConfigurationError(WordServiceException):
    """配置错误异常"""

    def __init__(self, message: str, code: int = 76000, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, code, details)