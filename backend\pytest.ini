[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    error_handling: Error handling tests
    slow: Slow running tests
    requires_word: Tests that require Microsoft Word
    requires_redis: Tests that require Redis connection
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function 