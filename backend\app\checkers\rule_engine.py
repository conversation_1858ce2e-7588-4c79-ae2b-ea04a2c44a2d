"""
论文检测规则引擎

该模块实现了灵活的规则解析和执行引擎，支持：
- 分层规则定义和解析
- JSO<PERSON> Pointer (`$ref`) 引用解析
- 规则执行计划
- 结果收集和分析
"""

import asyncio
import json
import re
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable, Union, Set, Tuple
import structlog
import time

from ..models.check_result import CheckResult, CheckIssue, CheckSeverity
from ..core.exceptions import DocumentAnalysisException, ConfigurationError
from ..services.document_processor import DocumentData

logger = structlog.get_logger(__name__)

class RuleEngine:
    """
    新版检测规则引擎，支持分层结构、引用和执行计划。
    """
    
    def __init__(self, check_functions: Dict[str, Callable]):
        """
        初始化规则引擎。

        Args:
            check_functions: 一个将函数名映射到可调用检查函数的字典。
        """
        self.logger = structlog.get_logger(__name__)
        self.check_functions = check_functions
        self.rules: Dict[str, Any] = {}
        self.definitions: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        self.execution_plan: List[Dict[str, Any]] = []
        
        self.execution_stats = {
            "total_rules_executed": 0,
            "total_execution_time": 0.0,
            "rules_passed": 0,
            "rules_failed": 0,
            "rule_execution_times": {},
        }

    def load_rules_from_file(self, rule_file_path: str):
        """
        从单个JSON文件加载整个规则集。

        Args:
            rule_file_path: 规则文件的路径。
        
        Raises:
            ConfigurationError: 如果规则文件格式不正确或包含无效配置。
        """
        self.logger.info("开始从文件加载规则集...", file=rule_file_path)
        try:
            with open(rule_file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证顶级键
            required_keys = {"metadata", "definitions", "rules", "execution_plan"}
            if not required_keys.issubset(config.keys()):
                raise ConfigurationError(f"规则文件缺少顶级键: {required_keys - set(config.keys())}")
            
            self.metadata = config.get("metadata", {})
            self.definitions = config.get("definitions", {})
            self.rules = self._flatten_rules(config.get("rules", {}))
            self.execution_plan = config.get("execution_plan", [])
            
            self._validate_execution_plan()

            self.logger.info("规则集加载完成", 
                             standard=self.metadata.get("name"), 
                             version=self.metadata.get("version"),
                             total_rules=len(self.rules))

        except FileNotFoundError:
            self.logger.error("规则文件未找到", path=rule_file_path)
            raise ConfigurationError(f"规则文件未找到: {rule_file_path}")
        except json.JSONDecodeError as e:
            self.logger.error("规则文件JSON解析失败", error=str(e))
            raise ConfigurationError(f"规则文件JSON解析失败: {e.msg} (line {e.lineno} column {e.colno})")
        except Exception as e:
            self.logger.error("加载规则时发生未知错误", error=str(e))
            raise e

    def _flatten_rules(self, rules_config: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """将分层的规则对象扁平化为ID到规则内容的映射。"""
        flattened_rules = {}
        for rule_type, rules_in_type in rules_config.items():
            for rule_name, rule_body in rules_in_type.items():
                rule_id = f"{rule_type}.{rule_name}"
                if "check_function" not in rule_body:
                    raise ConfigurationError(f"规则 '{rule_id}' 缺少 'check_function' 字段。")
                flattened_rules[rule_id] = rule_body
        return flattened_rules

    def _validate_execution_plan(self):
        """验证执行计划中的所有规则引用都存在。"""
        all_rule_ids = set(self.rules.keys())
        for phase in self.execution_plan:
            for rule_ref in phase.get("rules", []):
                ref_path = rule_ref.get("$ref")
                if not ref_path:
                    raise ConfigurationError(f"执行计划中的规则引用格式错误: {rule_ref}")
                
                rule_id = self._ref_to_rule_id(ref_path)
                if rule_id not in all_rule_ids:
                    raise ConfigurationError(f"执行计划引用了不存在的规则: '{rule_id}'")

    async def execute_check(self, document_data: DocumentData) -> List[CheckResult]:
        """
        根据加载的执行计划，对文档数据执行所有检查。

        Args:
            document_data: 从 document_processor 获取的文档分析数据。

        Returns:
            一个包含所有检查结果的列表。
        """
        all_results: List[CheckResult] = []
        self.logger.info("开始执行检测计划...", standard=self.metadata.get("name"))

        for phase in self.execution_plan:
            phase_name = phase.get("description", "未命名阶段")
            self.logger.info(f"正在执行阶段: {phase_name}")
            
            phase_rules = phase.get("rules", [])
            for rule_ref in phase_rules:
                rule_id = self._ref_to_rule_id(rule_ref["$ref"])
                result = await self.execute_rule(rule_id, document_data)
                all_results.append(result)

        self.logger.info("检测计划执行完毕", total_results=len(all_results))
        return all_results

    async def execute_rule(self, rule_id: str, document_data: DocumentData) -> CheckResult:
        """
        执行单个规则，包括解析其参数中的所有引用。

        Args:
            rule_id: 规则ID (例如 "format.page_setup")
            document_data: 文档分析数据。

        Returns:
            单个规则的检查结果。
        """
        start_time = time.perf_counter()
        
        rule_config = self.rules.get(rule_id)
        if not rule_config:
            return self._create_error_result(rule_id, "未知规则", f"规则ID '{rule_id}' 未在规则集中定义。")

        rule_name = rule_config.get("name", rule_id)
        
        try:
            # 1. 解析参数，这是新引擎的核心
            resolved_params = self._resolve_params(rule_config.get("parameters", {}))
            
            # 2. 获取检查函数
            check_func_name = rule_config["check_function"]
            check_function = self.check_functions.get(check_func_name)
            if not check_function:
                raise ConfigurationError(f"规则 '{rule_id}' 指定的检查函数 '{check_func_name}' 未注册。")

            # 3. 执行检查
            if asyncio.iscoroutinefunction(check_function):
                result = await check_function(document_data, resolved_params)
            else:
                result = check_function(document_data, resolved_params)

            # 确保返回的是CheckResult对象
            if isinstance(result, dict):
                result = CheckResult(rule_id=rule_id, rule_name=rule_name, **result)
            elif not isinstance(result, CheckResult):
                raise TypeError(f"检查函数 '{check_func_name}' 返回了非法的类型: {type(result)}")
            
            # 更新统计
            self._update_stats(time.perf_counter() - start_time, result.passed)
            result.execution_time = time.perf_counter() - start_time
            return result

        except (ConfigurationError, DocumentAnalysisException) as e:
            self.logger.error("规则执行期间发生可预见的错误", rule_id=rule_id, error=str(e))
            return self._create_error_result(rule_id, rule_name, str(e), severity=CheckSeverity.ERROR)
        except Exception as e:
            self.logger.exception("规则执行期间发生意外错误", rule_id=rule_id)
            return self._create_error_result(rule_id, rule_name, f"内部错误: {str(e)}", severity=CheckSeverity.CRITICAL)

    def _resolve_params(self, params: Any) -> Any:
        """
        递归解析参数字典/列表中的所有 `$ref` 引用。

        Args:
            params: 包含可能$ref的参数部分。

        Returns:
            完全解析后的参数。
        """
        return self._resolve_ref_recursive(params, [])

    def _resolve_ref_recursive(self, node: Any, path_stack: List[str]) -> Any:
        """
        递归下降解析器，处理字典、列表和引用。

        Args:
            node: 当前正在访问的JSON节点。
            path_stack: 用于检测循环引用的路径堆栈。

        Returns:
            解析后的节点。
        """
        if isinstance(node, dict):
            if "$ref" in node:
                ref_path = node["$ref"]
                
                if ref_path in path_stack:
                    raise ConfigurationError(f"检测到循环引用: {' -> '.join(path_stack)} -> {ref_path}")

                path_stack.append(ref_path)
                
                # 引用合并策略：本地值覆盖引用值
                resolved_obj = self._resolve_ref_recursive(self._get_ref_value(ref_path), path_stack)
                
                # 创建一个副本以应用本地覆盖
                merged_obj = resolved_obj.copy() if isinstance(resolved_obj, dict) else resolved_obj
                
                for key, value in node.items():
                    if key != "$ref":
                        # 对本地值也进行递归解析
                        merged_obj[key] = self._resolve_ref_recursive(value, path_stack)

                path_stack.pop()
                return merged_obj
            else:
                return {key: self._resolve_ref_recursive(value, path_stack) for key, value in node.items()}

        elif isinstance(node, list):
            return [self._resolve_ref_recursive(item, path_stack) for item in node]
        
        return node
    
    def _get_ref_value(self, ref: str) -> Any:
        """
        根据JSON Pointer路径从`definitions`中获取值。

        Args:
            ref: JSON Pointer字符串 (e.g., "#/definitions/styles/level_1_title")

        Returns:
            引用的值。
        
        Raises:
            ConfigurationError: 如果引用路径无效。
        """
        if not ref.startswith("#/"):
            raise ConfigurationError(f"不支持的引用格式 (仅支持本地引用): {ref}")
        
        parts = ref[2:].split('/')
        current = {"definitions": self.definitions, "rules": self.rules} # 允许引用其他规则
        
        try:
            for part in parts:
                if isinstance(current, list):
                    current = current[int(part)]
                elif isinstance(current, dict):
                    current = current[part]
                else:
                    # 如果当前值不是list或dict，无法进一步深入路径
                    raise KeyError
            return current
        except (KeyError, IndexError, TypeError, ValueError):
            raise ConfigurationError(f"无效的引用路径: {ref}")

    def _ref_to_rule_id(self, ref: str) -> str:
        """将`#/rules/format/some_rule`格式的引用转换为`format.some_rule`"""
        if not ref.startswith("#/rules/"):
            raise ConfigurationError(f"无效的规则引用格式: {ref}")
        return ref[8:].replace("/", ".")

    def _create_error_result(self, rule_id: str, rule_name: str, message: str, severity: CheckSeverity = CheckSeverity.CRITICAL) -> CheckResult:
        """创建一个表示执行错误的CheckResult。"""
        return CheckResult(
            rule_id=rule_id,
            rule_name=rule_name,
            passed=False,
            severity=severity,
            message=message,
            metadata={"error": True}
        )
    
    def _update_stats(self, execution_time: float, passed: bool):
        """更新执行统计。"""
        self.execution_stats["total_rules_executed"] += 1
        self.execution_stats["total_execution_time"] += execution_time
        if passed:
            self.execution_stats["rules_passed"] += 1
        else:
            self.execution_stats["rules_failed"] += 1 