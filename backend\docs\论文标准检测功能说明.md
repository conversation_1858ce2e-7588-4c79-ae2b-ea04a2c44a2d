# 论文标准检测功能详细说明

## 概述

基于Word文档分析服务的论文标准检测功能，专门针对学术论文的格式规范要求，提供全面的检测和分析能力。

## 检测范围

### 1. 结构问题检测

#### 1.1 标题结构检测
- **层级规范**: 检查标题层级是否连续（如不能从H1直接跳到H3）
- **编号一致性**: 验证标题编号格式的一致性
- **格式规范**: 检查标题字体、字号、加粗等格式要求

```json
"heading_analysis": {
  "heading_numbering_consistent": true,
  "heading_levels_proper": true,
  "missing_levels": [],
  "numbering_issues": [
    {
      "level": 2,
      "expected": "1.1",
      "actual": "2.1",
      "position": 150,
      "issue_type": "numbering_skip"
    }
  ]
}
```

#### 1.2 目录结构检测
- **目录存在性**: 检查是否包含目录
- **页码准确性**: 验证目录页码与实际页码的一致性
- **格式一致性**: 检查目录格式规范

#### 1.3 章节结构检测
- **章节完整性**: 检查论文基本结构（摘要、引言、正文、结论、参考文献等）
- **章节顺序**: 验证章节出现顺序的合理性

### 2. 内容问题检测

#### 2.1 参考文献检测
- **引用格式**: 检查引用格式的一致性（APA、MLA、GB/T等）
- **引用完整性**: 验证文内引用与参考文献列表的对应关系
- **格式规范**: 检查参考文献的格式细节

```json
"references": {
  "total_count": 25,
  "in_text_citations": 28,
  "unmatched_citations": ["张三, 2023"],
  "format_analysis": {
    "consistent_style": false,
    "detected_style": "mixed",
    "style_issues": [
      {
        "reference_number": 5,
        "issue": "期刊名称格式不一致",
        "expected_format": "[5] 作者. 标题[J]. 期刊名, 年份, 卷(期): 页码.",
        "actual_format": "[5] 作者. 标题. 期刊名, 年份, 卷(期): 页码.",
        "position": 5680
      }
    ]
  }
}
```

#### 2.2 图表标题和编号
- **图表编号**: 检查图表编号的连续性和格式
- **标题规范**: 验证图表标题的格式和位置
- **引用完整性**: 检查正文中图表引用的完整性

### 3. 页面设置问题检测

#### 3.1 页面布局检测
- **页边距**: 检查页边距是否符合要求
- **页面方向**: 验证页面方向设置
- **页面大小**: 检查页面大小规格

```json
"page_format": {
  "margin_compliance": {
    "compliant": false,
    "issues": [
      {
        "margin_type": "top",
        "required": 25.4,
        "actual": 20.0,
        "unit": "mm"
      }
    ]
  },
  "page_size_compliance": true,
  "orientation_compliance": true
}
```

#### 3.2 页眉页脚检测
- **格式一致性**: 检查页眉页脚格式的一致性
- **内容规范**: 验证页眉页脚内容是否符合要求
- **页码格式**: 检查页码格式和位置

### 4. 字体问题检测

#### 4.1 字体一致性检测
- **正文字体**: 检查正文字体的一致性
- **标题字体**: 验证各级标题字体规范
- **特殊内容字体**: 检查图表标题、脚注等特殊内容的字体

```json
"font_usage_analysis": [
  {
    "font_name": "宋体",
    "font_size": 12,
    "usage_count": 450,
    "usage_percentage": 75.0,
    "contexts": ["正文", "脚注"],
    "compliance": true
  },
  {
    "font_name": "Arial",
    "font_size": 12,
    "usage_count": 15,
    "usage_percentage": 2.5,
    "contexts": ["正文"],
    "compliance": false,
    "issue": "正文应使用宋体"
  }
]
```

#### 4.2 字号规范检测
- **层级字号**: 检查各级标题字号是否符合规范
- **正文字号**: 验证正文字号的一致性
- **特殊内容字号**: 检查脚注、图表标题等字号规范

### 5. 字数问题检测

#### 5.1 字数统计
- **总字数**: 统计文档总字数（含/不含空格）
- **正文字数**: 统计正文部分字数
- **各章节字数**: 分别统计各章节字数

```json
"word_count_analysis": {
  "total_words": 8500,
  "main_text_words": 7200,
  "abstract_words": 280,
  "references_words": 850,
  "chapter_word_counts": [
    {"chapter": "引言", "words": 800},
    {"chapter": "文献综述", "words": 1500},
    {"chapter": "研究方法", "words": 1200}
  ]
}
```

#### 5.2 字数规范检测
- **摘要字数**: 检查摘要字数是否在规定范围内
- **总字数限制**: 验证总字数是否符合要求
- **章节字数平衡**: 检查各章节字数分配的合理性

### 6. 段落问题检测

#### 6.1 段落格式检测
- **首行缩进**: 检查段落首行缩进是否一致
- **行间距**: 验证行间距设置的一致性
- **段间距**: 检查段落间距的规范性

```json
"paragraph_format_analysis": {
  "first_line_indent_compliance": {
    "compliant": false,
    "required": 24,
    "issues": [
      {
        "paragraph_position": 15,
        "actual_indent": 0,
        "expected_indent": 24
      }
    ]
  },
  "line_spacing_compliance": {
    "compliant": true,
    "standard": 1.5,
    "variance_issues": []
  },
  "paragraph_spacing_compliance": {
    "compliant": false,
    "issues": [
      {
        "position": 120,
        "issue": "段前距不一致",
        "actual": 6,
        "expected": 0
      }
    ]
  }
}
```

#### 6.2 段落对齐检测
- **对齐方式**: 检查段落对齐方式的一致性
- **特殊对齐**: 验证标题、图表标题等特殊内容的对齐方式

## 统计数据详细说明

### 核心统计指标

| 统计项目 | 说明 | 用途 |
|---------|------|------|
| 文档页数 | 总页数统计 | 篇幅规范检查 |
| 公式数 | 数学公式总数 | 理工科论文规范检查 |
| 文档字符数 | 包含空格的总字符数 | 详细字数统计 |
| 空格数 | 空格字符总数 | 格式规范检查 |
| 表格数 | 表格总数量 | 图表规范检查 |
| 脚注数 | 脚注总数量 | 引用规范检查 |
| 图片数 | 图片总数量 | 图表规范检查 |
| 尾注数 | 尾注总数量 | 引用规范检查 |

### 扩展统计指标

```json
"detailed_statistics": {
  "content_distribution": {
    "abstract_percentage": 3.2,
    "introduction_percentage": 12.5,
    "main_body_percentage": 75.8,
    "conclusion_percentage": 5.5,
    "references_percentage": 3.0
  },
  "formatting_compliance": {
    "overall_score": 85.5,
    "structure_score": 90.0,
    "format_score": 82.0,
    "content_score": 88.0
  }
}
```

## 检测规则配置

### 可配置的检测标准

```json
"detection_rules": {
  "paper_type": "undergraduate_thesis",
  "standards": {
    "word_count": {
      "min": 8000,
      "max": 12000,
      "abstract_max": 300
    },
    "font_requirements": {
      "main_text": {"family": "宋体", "size": 12},
      "headings": {
        "level_1": {"family": "宋体", "size": 18, "bold": true},
        "level_2": {"family": "宋体", "size": 16, "bold": true}
      }
    },
    "spacing_requirements": {
      "line_spacing": 1.5,
      "first_line_indent": 24,
      "paragraph_spacing": 0
    },
    "margin_requirements": {
      "top": 25.4,
      "bottom": 25.4,
      "left": 30.0,
      "right": 20.0
    }
  }
}
```

## 检测报告生成

### 问题分级

- **严重问题**: 影响论文基本规范的问题
- **一般问题**: 格式不完全符合要求的问题  
- **建议优化**: 可以改进的格式问题

### 修改建议

每个检测到的问题都会提供具体的修改建议和标准要求说明。

```json
"issue_suggestions": [
  {
    "issue_id": "FONT_001",
    "severity": "medium",
    "description": "正文字体不一致",
    "location": "第3页第2段",
    "suggestion": "请将Arial字体改为宋体12号",
    "standard_reference": "本科毕业论文格式规范第3.2条"
  }
]
```

这个论文标准检测功能为学术写作提供了全面的格式规范检查，确保论文符合相关标准要求。 