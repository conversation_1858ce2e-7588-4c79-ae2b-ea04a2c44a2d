"""
配置管理系统

提供统一的配置管理功能，支持：
- 环境变量配置
- 配置验证和类型转换
- 开发/生产环境自动切换
"""

import os
from typing import List, Optional, Union
from pathlib import Path
from pydantic import Field, validator, FilePath
from pydantic_settings import BaseSettings
from functools import lru_cache


# 项目根目录 (backend)
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# 公共配置基类
class BaseConfig(BaseSettings):
    """配置基类"""
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "allow"
    }


class DatabaseSettings(BaseConfig):
    """数据库配置"""
    url: str = Field(default="postgresql+asyncpg://postgres:zr3800855@localhost:5432/word_service", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DB_ECHO")
    pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DB_POOL_RECYCLE")
    pool_pre_ping: bool = Field(default=True, env="DB_POOL_PRE_PING")


class RedisSettings(BaseConfig):
    """Redis配置"""
    url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    max_connections: int = Field(default=10, env="REDIS_MAX_CONNECTIONS")
    timeout: int = Field(default=5, env="REDIS_TIMEOUT")
    encoding: str = Field(default="utf-8")
    decode_responses: bool = Field(default=True)


class TaskSettings(BaseConfig):
    """任务处理配置"""
    max_concurrent: int = Field(default=5, env="MAX_CONCURRENT_TASKS")
    timeout: int = Field(default=300, env="TASK_TIMEOUT")
    retry_attempts: int = Field(default=3)
    retry_delay: int = Field(default=5)


class FileSettings(BaseConfig):
    """文件处理配置"""
    max_size: int = Field(default=52428800, env="MAX_FILE_SIZE")  # 50MB
    allowed_extensions: List[str] = Field(default=[".docx"])
    
    # 统一使用绝对路径
    upload_path: Path = Field(default=BASE_DIR / "uploads", env="UPLOAD_PATH")
    processed_path: Path = Field(default=BASE_DIR / "data" / "processed", env="PROCESSED_PATH")
    temp_path: Path = Field(default=BASE_DIR / "data" / "temp", env="TEMP_PATH")
    images_path: Path = Field(default=BASE_DIR / "data" / "images", env="IMAGES_PATH")
    reports_path: Path = Field(default=BASE_DIR / "data" / "reports", env="REPORTS_PATH")
    backup_path: Path = Field(default=BASE_DIR / "data" / "backups", env="BACKUP_PATH")
    temp_retention_hours: int = Field(default=24, env="TEMP_RETENTION_HOURS")

    @validator('allowed_extensions', pre=True)
    def parse_extensions(cls, v) -> List[str]:
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(',')]
        return v

    @validator('upload_path', 'processed_path', 'temp_path', 'images_path', 'reports_path', 'backup_path', pre=True)
    def resolve_paths(cls, v: Union[str, Path]) -> Path:
        """确保所有路径都是绝对路径"""
        path = Path(v)
        if not path.is_absolute():
            return (BASE_DIR / path).resolve()
        return path


class SecuritySettings(BaseConfig):
    """安全配置"""
    secret_key: str = Field(default="dev-secret-key-change-in-production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=1440, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    password_min_length: int = Field(default=8, env="PASSWORD_MIN_LENGTH")
    bcrypt_rounds: int = Field(default=12)


class LoggingSettings(BaseConfig):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="json", env="LOG_FORMAT")
    file: Path = Field(default=BASE_DIR / "logs" / "word_service.log", env="LOG_FILE")
    max_size: int = Field(default=10485760, env="LOG_MAX_SIZE")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    console_output: bool = Field(default=True)
    log_retention_days: int = Field(default=7, env="LOG_RETENTION_DAYS")

    @validator('file', pre=True)
    def resolve_log_path(cls, v: Union[str, Path]) -> Path:
        """确保日志文件路径是绝对路径"""
        path = Path(v)
        if not path.is_absolute():
            return (BASE_DIR / path).resolve()
        return path


class Settings(BaseConfig):
    """主配置类"""
    # 基础配置
    app_name: str = Field(default="Word Document Analysis Service")
    version: str = Field(default="0.1.0")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    allowed_hosts: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    @validator('allowed_hosts', pre=True)
    def parse_allowed_hosts(cls, v) -> List[str]:
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v
    
    # API配置
    api_v1_prefix: str = Field(default="/api/v1")
    docs_url: str = Field(default="/docs")
    redoc_url: str = Field(default="/redoc")
    openapi_url: str = Field(default="/openapi.json")
    
    # 功能开关
    enable_api_docs: bool = Field(default=True, env="ENABLE_API_DOCS")
    enable_image_extraction: bool = Field(default=True, env="ENABLE_IMAGE_EXTRACTION")
    enable_pdf_report: bool = Field(default=True, env="ENABLE_PDF_REPORT")
    enable_batch_processing: bool = Field(default=False, env="ENABLE_BATCH_PROCESSING")
    
    # 论文检测配置 (简化合并到主配置)
    paper_check_enabled: bool = Field(default=True, env="ENABLE_PAPER_CHECK")
    paper_standards_config: str = Field(default="config/paper_standards.json", env="PAPER_STANDARDS_CONFIG")
    paper_rules_directory: str = Field(default="config/rules", env="PAPER_RULES_DIRECTORY")
    
    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    tasks: TaskSettings = Field(default_factory=TaskSettings)
    files: FileSettings = Field(default_factory=FileSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保必要的目录存在
        self._ensure_directories()
        
    def _ensure_directories(self) -> None:
        """确保所有配置的目录都存在"""
        # 统一从配置中获取路径
        directories_to_ensure = [
            self.files.upload_path,
            self.files.processed_path,
            self.files.temp_path,
            self.files.images_path,
            self.files.reports_path,
            self.files.backup_path,
            self.logging.file.parent,
        ]
        
        for directory in directories_to_ensure:
            directory.mkdir(parents=True, exist_ok=True)


@lru_cache()
def get_settings() -> Settings:
    """获取全局配置实例"""
    return Settings()


# 全局配置实例
settings = get_settings()


def is_development() -> bool:
    """判断是否为开发环境"""
    return settings.environment.lower() == "development"


def is_production() -> bool:
    """判断是否为生产环境"""
    return settings.environment.lower() == "production"


def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database.url


def get_redis_url() -> str:
    """获取Redis连接URL"""
    return settings.redis.url


# 检测标准名称映射
DETECTION_STANDARD_NAMES = {
    'gbt_7713_1_2006': 'GB/T 7713.1-2006（学位论文编写规则）',
    'gbt_7714_2015': 'GB/T 7714-2015（参考文献著录规则）',
    'hbkj_bachelor_2024': '河北科技学院学士论文检测标准 (2024版)',
    'default': '标准检测'
}


if __name__ == "__main__":
    # 测试配置加载
    print("配置加载测试:")
    print(f"应用名称: {settings.app_name}")
    print(f"版本: {settings.version}")
    print(f"环境: {settings.environment}")
    print(f"调试模式: {settings.debug}")
    print(f"数据库URL: {settings.database.url}")
    print(f"Redis URL: {settings.redis.url}")
    print(f"上传路径: {settings.files.upload_path}")
    print(f"论文检测启用: {settings.paper_check_enabled}") 