"""
检查test.docx文档的中文关键词格式
"""

import sys
import os
sys.path.append('backend')

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def inspect_document():
    """检查文档内容"""
    
    doc_path = "docs/test.docx"
    if not os.path.exists(doc_path):
        print(f"❌ 文档不存在: {doc_path}")
        return
    
    try:
        doc = Document(doc_path)
        
        print("🔍 检查test.docx文档内容")
        print("=" * 50)
        
        print(f"📄 文档基本信息:")
        print(f"   段落数量: {len(doc.paragraphs)}")
        print(f"   表格数量: {len(doc.tables)}")
        
        print(f"\n🔍 查找中文关键词相关内容:")
        
        keywords_found = False
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            # 查找包含"关键词"的段落
            if "关键词" in text:
                keywords_found = True
                
                print(f"\n📌 段落 {i+1}:")
                print(f"   内容: {text}")
                print(f"   长度: {len(text)} 字符")
                
                # 检查对齐方式
                alignment = paragraph.alignment
                alignment_name = "未设置"
                
                if alignment == WD_ALIGN_PARAGRAPH.LEFT:
                    alignment_name = "左对齐"
                elif alignment == WD_ALIGN_PARAGRAPH.CENTER:
                    alignment_name = "居中"
                elif alignment == WD_ALIGN_PARAGRAPH.RIGHT:
                    alignment_name = "右对齐"
                elif alignment == WD_ALIGN_PARAGRAPH.JUSTIFY:
                    alignment_name = "两端对齐"
                elif alignment is None:
                    alignment_name = "默认（通常为左对齐）"
                
                print(f"   对齐方式: {alignment_name}")
                
                # 检查字体信息
                if paragraph.runs:
                    first_run = paragraph.runs[0]
                    font = first_run.font
                    print(f"   字体: {font.name if font.name else '默认'}")
                    print(f"   字号: {font.size if font.size else '默认'}")
                    print(f"   加粗: {font.bold if font.bold is not None else '默认'}")
                
                # 检查格式是否符合要求
                is_left_aligned = alignment == WD_ALIGN_PARAGRAPH.LEFT or alignment is None
                has_correct_format = text.startswith("关键词：") or text.startswith("关键词:")
                
                print(f"   ✅ 左对齐: {'是' if is_left_aligned else '否'}")
                print(f"   ✅ 格式正确: {'是' if has_correct_format else '否'}")
                
                if not is_left_aligned:
                    print(f"   🚨 问题: 中文关键词应该左对齐，当前为{alignment_name}")
                
                if not has_correct_format:
                    print(f"   🚨 问题: 中文关键词格式不正确，应以'关键词：'开头")
        
        if not keywords_found:
            print("❌ 未找到包含'关键词'的段落")
            
            # 显示前10个段落的内容，帮助调试
            print(f"\n📋 前10个段落内容:")
            for i, paragraph in enumerate(doc.paragraphs[:10]):
                text = paragraph.text.strip()
                if text:  # 只显示非空段落
                    print(f"   {i+1}: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        print(f"\n📊 总结:")
        if keywords_found:
            print("✅ 找到中文关键词相关内容")
        else:
            print("❌ 未找到中文关键词相关内容")
            
    except Exception as e:
        print(f"❌ 检查文档失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    inspect_document()
