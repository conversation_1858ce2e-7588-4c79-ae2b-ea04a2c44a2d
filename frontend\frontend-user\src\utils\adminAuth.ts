import type { Router } from 'vue-router'

/**
 * 管理员认证工具函数
 * 提供统一的管理员登录跳转和认证检查
 */

// 管理员访问令牌（与路由配置保持一致）
const ADMIN_ACCESS_TOKEN = 'wms_secure_2024'

/**
 * 跳转到管理员登录页面
 * @param router Vue Router 实例
 * @param redirectPath 登录成功后的重定向路径（可选）
 */
export const redirectToAdminLogin = (router: Router, redirectPath?: string) => {
  const query: any = { token: ADMIN_ACCESS_TOKEN }
  
  if (redirectPath) {
    query.redirect = redirectPath
  }
  
  router.push({
    name: 'admin-login',
    query
  })
}

/**
 * 检查管理员登录状态
 * @returns 是否已登录
 */
export const isAdminLoggedIn = (): boolean => {
  return !!localStorage.getItem('adminToken')
}

/**
 * 管理员登出
 * @param router Vue Router 实例
 */
export const adminLogout = (router: Router) => {
  // 清除管理员登录状态
  localStorage.removeItem('adminToken')
  localStorage.removeItem('adminRememberMe')
  
  // 跳转到管理员登录页面
  redirectToAdminLogin(router)
}

/**
 * 验证管理员访问权限
 * 如果未登录则自动跳转到登录页面
 * @param router Vue Router 实例
 * @param currentPath 当前页面路径（用于登录后重定向）
 * @returns 是否已登录
 */
export const validateAdminAccess = (router: Router, currentPath?: string): boolean => {
  if (!isAdminLoggedIn()) {
    redirectToAdminLogin(router, currentPath)
    return false
  }
  return true
}

/**
 * 获取管理员登录URL（用于外部链接）
 * @param redirectPath 登录成功后的重定向路径（可选）
 * @returns 完整的登录URL
 */
export const getAdminLoginUrl = (redirectPath?: string): string => {
  const baseUrl = window.location.origin
  let url = `${baseUrl}/secure-mgmt-x7k9p2w/auth?token=${ADMIN_ACCESS_TOKEN}`
  
  if (redirectPath) {
    url += `&redirect=${encodeURIComponent(redirectPath)}`
  }
  
  return url
} 