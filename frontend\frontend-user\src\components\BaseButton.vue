<template>
  <component
    :is="componentType"
    :to="to"
    :href="href"
    :type="buttonType"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
    v-bind="$attrs"
  >
    <!-- 加载图标 -->
    <svg 
      v-if="loading" 
      class="animate-spin -ml-1 h-4 w-4" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        class="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        stroke-width="4"
      ></circle>
      <path 
        class="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
    
    <!-- 前置图标 -->
    <svg 
      v-else-if="prependIcon" 
      :class="['h-4 w-4', $slots.default ? 'mr-2' : '']"
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="prependIcon"/>
    </svg>
    
    <!-- 按钮内容 -->
    <span v-if="$slots.default">
      <slot></slot>
    </span>
    
    <!-- 后置图标 -->
    <svg 
      v-if="appendIcon && !loading" 
      :class="['h-4 w-4', $slots.default ? 'ml-2' : '']"
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="appendIcon"/>
    </svg>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  // 按钮变体
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 'link' | 'outline-primary' | 'outline-secondary'
  // 按钮大小
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  // 按钮类型
  type?: 'button' | 'submit' | 'reset'
  // 是否禁用
  disabled?: boolean
  // 是否加载中
  loading?: boolean
  // 是否块级按钮
  block?: boolean
  // 路由链接
  to?: string | object
  // 外部链接
  href?: string
  // 前置图标
  prependIcon?: string
  // 后置图标
  appendIcon?: string
  // 自定义样式类
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  type: 'button'
})

const emit = defineEmits<{
  click: [event: Event]
}>()

// 计算组件类型
const componentType = computed(() => {
  if (props.to) return 'router-link'
  if (props.href) return 'a'
  return 'button'
})

// 计算按钮类型
const buttonType = computed(() => {
  if (componentType.value === 'button') return props.type
  return undefined
})

// 计算按钮样式类
const buttonClasses = computed(() => {
  const classes = [
    'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200'
  ]
  
  // 大小样式
  switch (props.size) {
    case 'xs':
      classes.push('px-2.5 py-1.5 text-xs')
      break
    case 'sm':
      classes.push('px-3 py-2 text-sm')
      break
    case 'md':
      classes.push('px-4 py-2 text-sm')
      break
    case 'lg':
      classes.push('px-4 py-2 text-base')
      break
    case 'xl':
      classes.push('px-6 py-3 text-base')
      break
  }
  
  // 变体样式
  switch (props.variant) {
    case 'primary':
      classes.push('text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600')
      break
    case 'secondary':
      classes.push('text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-blue-500')
      break
    case 'success':
      classes.push('text-white bg-green-600 hover:bg-green-700 focus:ring-green-500 dark:bg-green-500 dark:hover:bg-green-600')
      break
    case 'danger':
      classes.push('text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-500 dark:hover:bg-red-600')
      break
    case 'warning':
      classes.push('text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 dark:bg-yellow-500 dark:hover:bg-yellow-600')
      break
    case 'info':
      classes.push('text-white bg-cyan-600 hover:bg-cyan-700 focus:ring-cyan-500 dark:bg-cyan-500 dark:hover:bg-cyan-600')
      break
    case 'light':
      classes.push('text-gray-900 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500 dark:text-gray-100 dark:bg-gray-600 dark:hover:bg-gray-500')
      break
    case 'dark':
      classes.push('text-white bg-gray-800 hover:bg-gray-900 focus:ring-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600')
      break
    case 'link':
      classes.push('text-blue-600 dark:text-blue-400 bg-transparent hover:text-blue-700 dark:hover:text-blue-300 focus:ring-blue-500 underline-offset-4 hover:underline')
      break
    case 'outline-primary':
      classes.push('text-blue-600 dark:text-blue-400 bg-transparent border border-blue-600 dark:border-blue-400 hover:bg-blue-600 hover:text-white dark:hover:bg-blue-400 dark:hover:text-gray-900 focus:ring-blue-500')
      break
    case 'outline-secondary':
      classes.push('text-gray-600 dark:text-gray-300 bg-transparent border border-gray-300 dark:border-gray-600 hover:bg-gray-600 hover:text-white dark:hover:bg-gray-600 dark:hover:text-white focus:ring-gray-500')
      break
  }
  
  // 块级按钮
  if (props.block) {
    classes.push('w-full')
  }
  
  // 禁用状态
  if (props.disabled || props.loading) {
    classes.push('opacity-50 cursor-not-allowed')
  } else {
    // 正常状态显示手型光标
    classes.push('cursor-pointer')
  }
  
  // 自定义类
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes.join(' ')
})

// 点击事件处理
const handleClick = (event: Event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<script lang="ts">
export default {
  name: 'BaseButton'
}
</script> 