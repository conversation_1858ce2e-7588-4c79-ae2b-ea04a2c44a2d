import requests
import json

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "he<PERSON><PERSON>n5112"
        }

        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def test_problem_fragments_api():
    """测试问题片段API"""

    # 获取认证令牌
    print("🔐 获取认证令牌...")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌，跳过API测试")
        return

    print("✅ 认证令牌获取成功")

    # 测试URL
    base_url = "http://localhost:8000"
    task_id = "task_145daa3cebcd4f8c81db456e92cd1135"

    # 测试获取问题片段列表
    url = f"{base_url}/api/v1/paper-check/problem-fragments/{task_id}"

    try:
        print(f"🔍 测试问题片段API: {url}")

        # 发送请求（使用认证）
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 20
        })
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API响应成功!")
            print(f"📋 问题片段总数: {data['data']['total_count']}")
            print(f"📄 当前页: {data['data']['page']}")
            print(f"📦 每页数量: {data['data']['limit']}")
            print(f"🔍 片段数量: {len(data['data']['fragments'])}")
            
            # 显示前几个问题片段
            fragments = data['data']['fragments']
            for i, fragment in enumerate(fragments[:3]):
                print(f"\n📌 问题片段 {i+1}:")
                print(f"   ID: {fragment['fragment_id']}")
                print(f"   结构: {fragment['structure']}")
                print(f"   严重程度: {fragment['severity']}")
                print(f"   原文: {fragment['original_text'][:50]}...")
                print(f"   问题描述: {fragment['problem_description']}")
                
        elif response.status_code == 404:
            print("❌ API接口未找到 (404)")
            print("可能的原因:")
            print("1. 后端服务器未启动")
            print("2. API路由未正确注册")
            print("3. URL路径错误")
            
        elif response.status_code == 401:
            print("❌ 认证失败 (401)")
            print("需要添加认证头信息")
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到后端服务器")
        print("请确保后端服务器在 http://localhost:8001 上运行")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_server_health():
    """测试服务器是否运行"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("✅ 后端服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
    except:
        print("❌ 无法连接到后端服务器")
        return False

if __name__ == "__main__":
    print("🚀 开始测试问题片段API")
    print("=" * 50)
    
    # 先测试服务器健康状态
    if test_server_health():
        print("\n" + "=" * 50)
        test_problem_fragments_api()
    else:
        print("\n请先启动后端服务器:")
        print("cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000")
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
