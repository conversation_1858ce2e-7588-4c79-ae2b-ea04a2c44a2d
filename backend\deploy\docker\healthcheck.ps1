# ==================================================
# Word文档分析服务 - Docker健康检查脚本
# ==================================================

param(
    [string]$Host = "localhost",
    [int]$Port = 8000,
    [int]$Timeout = 10
)

# 设置错误处理
$ErrorActionPreference = "Stop"

try {
    # 检查基础健康端点
    $healthUrl = "http://${Host}:${Port}/health"
    
    Write-Host "检查健康状态: $healthUrl" -ForegroundColor Yellow
    
    # 创建HTTP请求
    $response = Invoke-RestMethod -Uri $healthUrl -Method Get -TimeoutSec $Timeout -ErrorAction Stop
    
    # 检查响应状态
    if ($response.success -eq $true -and $response.data.status -eq "healthy") {
        Write-Host "健康检查通过" -ForegroundColor Green
        Write-Host "服务状态: $($response.data.status)" -ForegroundColor Green
        Write-Host "版本: $($response.data.version)" -ForegroundColor Green
        Write-Host "运行时间: $($response.data.uptime)秒" -ForegroundColor Green
        
        # 检查系统资源
        if ($response.data.system_info) {
            $sysInfo = $response.data.system_info
            Write-Host "CPU使用率: $($sysInfo.cpu_percent)%" -ForegroundColor Cyan
            Write-Host "内存使用率: $($sysInfo.memory_percent)%" -ForegroundColor Cyan
            Write-Host "磁盘使用率: $($sysInfo.disk_percent)%" -ForegroundColor Cyan
            
            # 检查资源使用率是否过高
            if ($sysInfo.cpu_percent -gt 90) {
                Write-Warning "CPU使用率过高: $($sysInfo.cpu_percent)%"
            }
            if ($sysInfo.memory_percent -gt 90) {
                Write-Warning "内存使用率过高: $($sysInfo.memory_percent)%"
            }
            if ($sysInfo.disk_percent -gt 90) {
                Write-Warning "磁盘使用率过高: $($sysInfo.disk_percent)%"
            }
        }
        
        exit 0
    } else {
        Write-Error "健康检查失败: 服务状态异常"
        exit 1
    }
    
} catch [System.Net.WebException] {
    Write-Error "健康检查失败: 无法连接到服务 ($($_.Exception.Message))"
    exit 1
} catch [System.TimeoutException] {
    Write-Error "健康检查失败: 请求超时"
    exit 1
} catch {
    Write-Error "健康检查失败: $($_.Exception.Message)"
    exit 1
}

# 额外检查：验证关键端点
try {
    Write-Host "检查系统信息端点..." -ForegroundColor Yellow
    $sysInfoUrl = "http://${Host}:${Port}/api/v1/system/info"
    $sysResponse = Invoke-RestMethod -Uri $sysInfoUrl -Method Get -TimeoutSec 5 -ErrorAction Stop
    
    if ($sysResponse.success -eq $true) {
        Write-Host "系统信息端点正常" -ForegroundColor Green
    } else {
        Write-Warning "系统信息端点异常"
    }
} catch {
    Write-Warning "系统信息端点检查失败: $($_.Exception.Message)"
}

Write-Host "健康检查完成" -ForegroundColor Green
exit 0
