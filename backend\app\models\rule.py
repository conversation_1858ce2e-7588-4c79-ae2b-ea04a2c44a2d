"""
规则数据模型

定义论文检测规则的数据结构
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from pydantic import BaseModel, Field


class RuleType(Enum):
    """规则类型枚举"""
    FORMAT = "format"           # 格式规则
    STRUCTURE = "structure"     # 结构规则
    CONTENT = "content"         # 内容规则
    REFERENCE = "reference"     # 引用规则
    LAYOUT = "layout"          # 布局规则
    STYLE = "style"            # 样式规则


class CheckSeverity(Enum):
    """检查严重程度"""
    ERROR = "error"         # 错误 - 必须修复
    WARNING = "warning"     # 警告 - 建议修复
    INFO = "info"          # 信息 - 提示性
    SUGGESTION = "suggestion"  # 建议 - 优化建议


class RuleStatus(Enum):
    """规则状态"""
    ACTIVE = "active"       # 激活
    INACTIVE = "inactive"   # 未激活
    DEPRECATED = "deprecated"  # 已废弃


@dataclass
class CheckRule:
    """检查规则数据类"""
    rule_id: str                           # 规则唯一标识
    name: str                              # 规则名称
    description: str                       # 规则描述
    rule_type: RuleType                    # 规则类型
    severity: CheckSeverity                # 默认严重程度
    status: RuleStatus = RuleStatus.ACTIVE # 规则状态
    
    # 规则条件和参数
    conditions: Dict[str, Any] = field(default_factory=dict)  # 规则条件
    parameters: Dict[str, Any] = field(default_factory=dict)  # 规则参数
    
    # 规则依赖和组合
    dependencies: List[str] = field(default_factory=list)     # 依赖规则
    conflicts: List[str] = field(default_factory=list)        # 冲突规则
    
    # 规则元数据
    tags: List[str] = field(default_factory=list)             # 标签
    version: str = "1.0.0"                                    # 版本
    author: str = ""                                           # 作者
    created_at: Optional[datetime] = None                     # 创建时间
    updated_at: Optional[datetime] = None                     # 更新时间
    
    # 执行函数
    check_function: Optional[Callable] = None                 # 检查函数
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def is_active(self) -> bool:
        """检查规则是否激活"""
        return self.status == RuleStatus.ACTIVE
    
    def has_dependencies(self) -> bool:
        """检查是否有依赖"""
        return len(self.dependencies) > 0
    
    def has_conflicts(self) -> bool:
        """检查是否有冲突"""
        return len(self.conflicts) > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "rule_id": self.rule_id,
            "name": self.name,
            "description": self.description,
            "rule_type": self.rule_type.value,
            "severity": self.severity.value,
            "status": self.status.value,
            "conditions": self.conditions,
            "parameters": self.parameters,
            "dependencies": self.dependencies,
            "conflicts": self.conflicts,
            "tags": self.tags,
            "version": self.version,
            "author": self.author,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


# Pydantic 模型用于API交互
class CheckRuleModel(BaseModel):
    """检查规则Pydantic模型"""
    rule_id: str = Field(..., description="规则唯一标识")
    name: str = Field(..., description="规则名称")
    description: str = Field(..., description="规则描述")
    rule_type: str = Field(..., description="规则类型")
    severity: str = Field(..., description="默认严重程度")
    status: str = Field(default="active", description="规则状态")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="规则条件")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="规则参数")
    dependencies: List[str] = Field(default_factory=list, description="依赖规则")
    conflicts: List[str] = Field(default_factory=list, description="冲突规则")
    tags: List[str] = Field(default_factory=list, description="标签")
    version: str = Field(default="1.0.0", description="版本")
    author: str = Field(default="", description="作者")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class RuleSetModel(BaseModel):
    """规则集Pydantic模型"""
    set_id: str = Field(..., description="规则集ID")
    name: str = Field(..., description="规则集名称")
    description: str = Field(..., description="规则集描述")
    rules: List[CheckRuleModel] = Field(..., description="规则列表")
    version: str = Field(default="1.0.0", description="版本")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


# 辅助函数
def create_check_rule(
    rule_id: str,
    name: str,
    description: str,
    rule_type: RuleType,
    severity: CheckSeverity,
    **kwargs
) -> CheckRule:
    """创建检查规则实例"""
    return CheckRule(
        rule_id=rule_id,
        name=name,
        description=description,
        rule_type=rule_type,
        severity=severity,
        **kwargs
    )


def load_rule_from_dict(rule_data: Dict[str, Any]) -> CheckRule:
    """从字典数据加载规则"""
    # 转换枚举类型
    if isinstance(rule_data.get("rule_type"), str):
        rule_data["rule_type"] = RuleType(rule_data["rule_type"])
    
    if isinstance(rule_data.get("severity"), str):
        rule_data["severity"] = CheckSeverity(rule_data["severity"])
    
    if isinstance(rule_data.get("status"), str):
        rule_data["status"] = RuleStatus(rule_data["status"])
    
    # 转换时间字段
    if isinstance(rule_data.get("created_at"), str):
        rule_data["created_at"] = datetime.fromisoformat(rule_data["created_at"])
    
    if isinstance(rule_data.get("updated_at"), str):
        rule_data["updated_at"] = datetime.fromisoformat(rule_data["updated_at"])
    
    return CheckRule(**rule_data) 