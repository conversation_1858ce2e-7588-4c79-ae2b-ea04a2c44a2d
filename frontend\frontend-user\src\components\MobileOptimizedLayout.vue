<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 移动端头部 -->
    <header class="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
      <div class="flex items-center justify-between h-16 px-4">
        <button
          @click="toggleSidebar"
          class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        
        <h1 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
          {{ title }}
        </h1>
        
        <div class="flex items-center space-x-2">
          <!-- 通知按钮 -->
          <button class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 relative">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.405-3.405A2.032 2.032 0 0117 12.28V11a5 5 0 00-4.584-4.992 1.5 1.5 0 00-2.832 0A5 5 0 005 11v1.28c0 .596-.227 1.169-.594 1.615L1 17h5m8 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <span v-if="hasNotifications" class="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
          </button>
          
          <!-- 用户头像 -->
          <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
            <span class="text-blue-600 dark:text-blue-400 font-bold text-sm">
              {{ userInitial }}
            </span>
          </div>
        </div>
      </div>
    </header>

    <!-- 侧边栏遮罩 -->
    <div
      v-show="sidebarOpen"
      @click="sidebarOpen = false"
      class="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50 transition-opacity"
    ></div>

    <!-- 侧边栏 -->
    <aside
      :class="[
        'lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      ]"
    >
      <!-- 侧边栏内容 -->
      <div class="flex flex-col h-full">
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">菜单</h2>
          <button
            @click="sidebarOpen = false"
            class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <nav class="flex-1 overflow-y-auto p-4">
          <div class="space-y-2">
            <router-link
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              @click="sidebarOpen = false"
              :class="[
                'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                route.path === item.href
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              ]"
            >
              <component :is="item.icon" class="h-5 w-5 mr-3" />
              {{ item.name }}
            </router-link>
          </div>
        </nav>
      </div>
    </aside>

    <!-- 主要内容区域 -->
    <main class="lg:pl-0">
      <div class="px-4 py-6 lg:px-8">
        <!-- 移动端面包屑 -->
        <nav v-if="breadcrumbs.length > 0" class="mb-6 lg:hidden">
          <ol class="flex items-center space-x-2 text-sm">
            <li v-for="(crumb, index) in breadcrumbs" :key="index" class="flex items-center">
              <router-link
                v-if="crumb.href && index < breadcrumbs.length - 1"
                :to="crumb.href"
                class="text-blue-600 dark:text-blue-400 hover:underline"
              >
                {{ crumb.name }}
              </router-link>
              <span
                v-else
                class="text-gray-500 dark:text-gray-400 font-medium"
              >
                {{ crumb.name }}
              </span>
              <svg
                v-if="index < breadcrumbs.length - 1"
                class="h-4 w-4 mx-2 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </li>
          </ol>
        </nav>

        <!-- 页面标题（移动端） -->
        <div class="mb-6 lg:hidden">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ title }}</h1>
          <p v-if="description" class="mt-1 text-sm text-gray-600 dark:text-gray-400">{{ description }}</p>
        </div>

        <!-- 内容插槽 -->
        <div class="space-y-6">
          <slot />
        </div>
      </div>
    </main>

    <!-- 底部导航栏（移动端） -->
    <nav class="lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-30">
      <div class="grid grid-cols-5 h-16">
        <router-link
          v-for="item in bottomNavigation"
          :key="item.name"
          :to="item.href"
          :class="[
            'flex flex-col items-center justify-center space-y-1 transition-colors',
            route.path === item.href
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-600 dark:text-gray-400'
          ]"
        >
          <component :is="item.icon" class="h-5 w-5" />
          <span class="text-xs font-medium">{{ item.name }}</span>
        </router-link>
      </div>
    </nav>

    <!-- 移动端内容底部间距 -->
    <div class="lg:hidden h-16"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

interface NavigationItem {
  name: string
  href: string
  icon: string
}

interface Breadcrumb {
  name: string
  href?: string
}

interface Props {
  title: string
  description?: string
  breadcrumbs?: Breadcrumb[]
}

const props = withDefaults(defineProps<Props>(), {
  breadcrumbs: () => []
})

const route = useRoute()
const userStore = useUserStore()
const sidebarOpen = ref(false)

const userInitial = computed(() => {
  return userStore.currentUser?.username?.charAt(0).toUpperCase() || 'U'
})

const hasNotifications = ref(true)

const navigation: NavigationItem[] = [
  { name: '首页', href: '/', icon: 'HomeIcon' },
  { name: '仪表盘', href: '/dashboard', icon: 'ChartBarIcon' },
  { name: '文档管理', href: '/documents', icon: 'DocumentIcon' },
  { name: '上传文档', href: '/upload', icon: 'UploadIcon' },
  { name: '任务中心', href: '/tasks', icon: 'ClipboardListIcon' },
  { name: '订单管理', href: '/orders', icon: 'ShoppingCartIcon' },
  { name: '个人中心', href: '/profile', icon: 'UserIcon' }
]

const bottomNavigation: NavigationItem[] = [
  { name: '首页', href: '/', icon: 'HomeIcon' },
  { name: '文档', href: '/documents', icon: 'DocumentIcon' },
  { name: '上传', href: '/upload', icon: 'PlusIcon' },
  { name: '任务', href: '/tasks', icon: 'ClipboardListIcon' },
  { name: '我的', href: '/profile', icon: 'UserIcon' }
]

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}
</script>

<style scoped>
/* 确保移动端滚动流畅 */
@media (max-width: 1024px) {
  body {
    overflow-x: hidden;
  }
  
  .router-link-active {
    @apply bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300;
  }
}

/* 优化触摸目标大小 */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}
</style> 