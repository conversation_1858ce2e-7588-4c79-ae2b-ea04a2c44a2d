import { ref, reactive } from 'vue'

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  persistent?: boolean
}

export interface NotificationOptions {
  title?: string
  duration?: number
  closable?: boolean
  persistent?: boolean
}

// 全局通知状态
const notifications = ref<Notification[]>([])

// 默认配置
const defaultDuration = 4000 // 4秒
const maxNotifications = 5

let notificationId = 0

// 生成唯一ID
const generateId = (): string => {
  return `notification-${Date.now()}-${++notificationId}`
}

// 添加通知
const addNotification = (notification: Omit<Notification, 'id'>): string => {
  const id = generateId()
  const newNotification: Notification = {
    id,
    closable: true,
    duration: defaultDuration,
    ...notification
  }

  notifications.value.unshift(newNotification)

  // 限制最大通知数量
  if (notifications.value.length > maxNotifications) {
    notifications.value = notifications.value.slice(0, maxNotifications)
  }

  // 设置自动关闭
  if (newNotification.duration && newNotification.duration > 0 && !newNotification.persistent) {
    setTimeout(() => {
      closeNotification(id)
    }, newNotification.duration)
  }

  return id
}

// 关闭通知
const closeNotification = (id: string): void => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// 清空所有通知
const clearNotifications = (): void => {
  notifications.value = []
}

// 成功通知
const success = (message: string, options?: NotificationOptions): string => {
  return addNotification({
    type: 'success',
    message,
    ...options
  })
}

// 错误通知
const error = (message: string, options?: NotificationOptions): string => {
  return addNotification({
    type: 'error',
    message,
    duration: 6000, // 错误消息显示更久
    ...options
  })
}

// 警告通知
const warning = (message: string, options?: NotificationOptions): string => {
  return addNotification({
    type: 'warning',
    message,
    duration: 5000, // 警告消息显示稍久
    ...options
  })
}

// 信息通知
const info = (message: string, options?: NotificationOptions): string => {
  return addNotification({
    type: 'info',
    message,
    ...options
  })
}

// 确认对话框样式的通知（带操作按钮）
const confirm = (
  message: string, 
  options?: NotificationOptions & { 
    onConfirm?: () => void
    onCancel?: () => void
    confirmText?: string
    cancelText?: string
  }
): string => {
  return addNotification({
    type: 'warning',
    message,
    duration: 0, // 需要手动关闭
    persistent: true,
    ...options
  })
}

// 替代原生alert的函数
const alert = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void => {
  addNotification({
    type,
    message,
    title: getAlertTitle(type),
    duration: type === 'error' ? 6000 : defaultDuration
  })
}

// 获取Alert标题
const getAlertTitle = (type: string): string => {
  const titles = {
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '提示'
  }
  return titles[type as keyof typeof titles] || '提示'
}

// 批量通知方法
const batch = (notifications: Omit<Notification, 'id'>[]): string[] => {
  return notifications.map(notification => addNotification(notification))
}

// 更新通知
const updateNotification = (id: string, updates: Partial<Omit<Notification, 'id'>>): void => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    Object.assign(notification, updates)
  }
}

// 获取通知数量
const getNotificationCount = (): number => {
  return notifications.value.length
}

// 检查是否有特定类型的通知
const hasNotificationType = (type: Notification['type']): boolean => {
  return notifications.value.some(n => n.type === type)
}

export const useNotifications = () => {
  return {
    // 状态
    notifications: notifications,
    
    // 基础方法
    addNotification,
    closeNotification,
    clearNotifications,
    
    // 快捷方法
    success,
    error,
    warning,
    info,
    confirm,
    alert,
    
    // 高级方法
    batch,
    updateNotification,
    getNotificationCount,
    hasNotificationType
  }
}

// 全局通知实例（用于在组件外部调用）
export const $notify = {
  success,
  error,
  warning,
  info,
  confirm,
  alert,
  clear: clearNotifications
}

// Vue插件形式的全局通知
export const NotificationPlugin = {
  install(app: any) {
    app.config.globalProperties.$notify = $notify
    app.provide('notifications', useNotifications())
  }
} 