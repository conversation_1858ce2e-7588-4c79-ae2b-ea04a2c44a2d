import { apiService } from './api'

export interface CreateOrderRequest {
  plan_id: string;
  payment_method: string;
}

export interface PaymentInfo {
  order_id: string;
  qr_code_url?: string;
  message: string;
}

export interface OrderStatus {
  order_id: string;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  plan_id: string;
  amount: number;
  created_at: string;
  paid_at?: string;
  user_id: string;
  payment_method: string;
}

export interface PaymentPlan {
  plan_id: string;
  name: string;
  price: number;
  checks: number;
  description: string;
}

export interface PaymentHistoryResponse {
  orders: OrderStatus[];
  total: number;
  skip: number;
  limit: number;
}

export interface PaymentStatistics {
  completed_orders: number;
  pending_orders: number;
  total_spent: number;
  current_plan: string;
}

export interface PaymentHistoryParams {
  skip?: number;
  limit?: number;
  status?: string;
  search?: string;
  date_range?: string;
}

export class PaymentApi {
  /**
   * 创建支付订单
   */
  async createOrder(data: CreateOrderRequest): Promise<PaymentInfo> {
    return await apiService.post('/v1/payments/create-order', data);
  }

  /**
   * 获取订单状态
   */
  async getOrderStatus(orderId: string): Promise<OrderStatus> {
    return await apiService.get(`/v1/payments/order/${orderId}`);
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string): Promise<OrderStatus> {
    return await apiService.post(`/v1/payments/order/${orderId}/cancel`);
  }

  /**
   * 获取支付历史（支持筛选）
   */
  async getPaymentHistory(params: PaymentHistoryParams = {}): Promise<PaymentHistoryResponse> {
    const { skip = 0, limit = 20, ...filters } = params;
    return await apiService.get(`/v1/payments/`, {
      params: { skip, limit, ...filters }
    });
  }

  /**
   * 获取可用的支付套餐
   */
  async getPaymentPlans(): Promise<PaymentPlan[]> {
    const response = await apiService.get('/v1/payments/plans');
    return response.plans;
  }

  /**
   * 获取支付统计信息
   */
  async getPaymentStatistics(): Promise<PaymentStatistics> {
    try {
      // 获取所有订单数据用于计算统计信息
      const historyResponse = await this.getPaymentHistory({ limit: 1000 });
      const orders = historyResponse.orders;
      
      // 计算统计数据
      const completed_orders = orders.filter(order => order.status === 'paid').length;
      const pending_orders = orders.filter(order => order.status === 'pending').length;
      const total_spent = orders
        .filter(order => order.status === 'paid')
        .reduce((sum, order) => sum + order.amount, 0);
      
      // 获取当前套餐（最近的已支付订单）
      const paidOrders = orders
        .filter(order => order.status === 'paid')
        .sort((a, b) => new Date(b.paid_at || b.created_at).getTime() - new Date(a.paid_at || a.created_at).getTime());
      
      const current_plan = paidOrders.length > 0 ? paidOrders[0].plan_id : 'none';
      
      return {
        completed_orders,
        pending_orders,
        total_spent,
        current_plan
      };
    } catch (error) {
      console.error('Error calculating payment statistics:', error);
      // 返回默认统计数据
      return {
        completed_orders: 0,
        pending_orders: 0,
        total_spent: 0,
        current_plan: 'none'
      };
    }
  }

  /**
   * 下载发票
   */
  async downloadInvoice(orderId: string): Promise<Blob> {
    const response = await apiService.get(`/v1/payments/order/${orderId}/invoice`, {
      responseType: 'blob'
    });
    return response;
  }

  /**
   * 发起支付
   */
  async processPayment(orderId: string, paymentMethod: string): Promise<PaymentInfo> {
    return await apiService.post(`/v1/payments/order/${orderId}/pay`, {
      payment_method: paymentMethod
    });
  }
}

// 创建并导出paymentApi实例
export const paymentApi = new PaymentApi(); 