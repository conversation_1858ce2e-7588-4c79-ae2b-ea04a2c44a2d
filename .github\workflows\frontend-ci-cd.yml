name: Frontend CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    paths: ['paper-check-frontend/**']
  pull_request:
    branches: [main]
    paths: ['paper-check-frontend/**']

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: paper-check-frontend

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./paper-check-frontend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: paper-check-frontend/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: TypeScript type check
        run: npm run type-check
      
      - name: ESLint check
        run: npm run lint
      
      - name: Prettier check
        run: npm run format:check

  # 测试阶段
  test:
    runs-on: ubuntu-latest
    needs: quality-check
    defaults:
      run:
        working-directory: ./paper-check-frontend
    
    strategy:
      matrix:
        test-type: [unit, integration]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: paper-check-frontend/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: npm run test:ci
      
      - name: Upload unit test coverage
        if: matrix.test-type == 'unit'
        uses: codecov/codecov-action@v3
        with:
          file: ./paper-check-frontend/coverage/lcov.info
      
      - name: Run integration tests
        if: matrix.test-type == 'integration'
        run: npm run test:integration

  # E2E测试
  e2e-test:
    runs-on: ubuntu-latest
    needs: test
    defaults:
      run:
        working-directory: ./paper-check-frontend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: paper-check-frontend/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      
      - name: Build application
        run: npm run build
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload E2E test results
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: paper-check-frontend/test-results/

  # 构建阶段
  build:
    runs-on: ubuntu-latest
    needs: [quality-check, test]
    defaults:
      run:
        working-directory: ./paper-check-frontend
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: paper-check-frontend/package-lock.json
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
      
      - name: Analyze bundle size
        run: npm run analyze
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist-files
          path: paper-check-frontend/dist/
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Log in to Container Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./paper-check-frontend
          file: ./paper-check-frontend/Dockerfile.prod
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Staging部署
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build, e2e-test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          echo "Image: ${{ needs.build.outputs.image-tag }}"
      
      - name: Run smoke tests
        run: |
          echo "Running smoke tests on staging..."
          curl -f https://staging.papercheck.com/health || exit 1

  # 生产部署
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build, security-scan, e2e-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          echo "Image: ${{ needs.build.outputs.image-tag }}"
      
      - name: Health check
        run: |
          echo "Running health check on production..."
          curl -f https://papercheck.com/health || exit 1
      
      - name: Notify team
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          fields: repo,message,commit,author,action,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

  # 性能测试
  performance-test:
    runs-on: ubuntu-latest
    needs: deploy-production
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://papercheck.com
            https://papercheck.com/login
            https://papercheck.com/dashboard
          configPath: './paper-check-frontend/.lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true 