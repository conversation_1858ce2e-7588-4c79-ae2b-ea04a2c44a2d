<template>
  <div class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- 导航栏 -->
    <AppNavbar :isHomePage="true" />

    <!-- Hero 区域 -->
    <section class="bg-gradient-to-r from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 py-20 transition-colors duration-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div class="space-y-8">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight">
              智能<span class="text-blue-600 dark:text-blue-400">Word文档</span><br>
              检测分析平台
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
              专业的Word文档格式检查、论文合规性验证和结构分析工具。
              支持多种检测模式，快速准确，助您提升文档质量。
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <BaseButton to="/upload" variant="primary" size="lg" class="px-8 py-4 text-lg">
                开始免费检测
              </BaseButton>
              <BaseButton href="#features" variant="secondary" size="lg" class="px-8 py-4 text-lg">
                了解更多
              </BaseButton>
            </div>
            <div class="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                免费试用
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                秒级检测
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                专业报告
              </div>
            </div>
          </div>
          
          <!-- 演示区域 -->
          <div class="relative">
            <BaseCard class="transform rotate-1 hover:rotate-0 transition-all duration-300 p-8">
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <div class="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4">
                  <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">拖拽上传文档</h3>
                <p class="text-gray-600 dark:text-gray-300">支持 .docx 格式的文档</p>
              </div>
              <div class="mt-6 flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <span>📄 research_paper.docx</span>
                <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">已完成</span>
              </div>
            </BaseCard>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section id="features" class="py-20 bg-white dark:bg-gray-900 transition-colors duration-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            强大的文档分析功能
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            我们提供三种专业的文档检测模式，满足不同场景的需求
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 论文检测 -->
          <BaseCard class="text-center hover:shadow-lg transition-shadow duration-300">
            <div class="mx-auto h-16 w-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-6">
              <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">论文检测</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              检测学术论文格式规范、引用格式、参考文献等，确保符合学术标准。
            </p>
            <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-2">
              <li>✓ 格式规范检查</li>
              <li>✓ 引用格式验证</li>
              <li>✓ 参考文献检查</li>
              <li>✓ 章节结构分析</li>
            </ul>
          </BaseCard>
          
          <!-- 格式检查 -->
          <BaseCard class="text-center hover:shadow-lg transition-shadow duration-300">
            <div class="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mb-6">
              <svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">格式检查</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              全面检查文档格式、字体样式、段落间距等，确保文档专业美观。
            </p>
            <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-2">
              <li>✓ 字体样式统一</li>
              <li>✓ 段落格式检查</li>
              <li>✓ 页面布局优化</li>
              <li>✓ 标题层次验证</li>
            </ul>
          </BaseCard>
          
          <!-- 结构分析 -->
          <BaseCard class="text-center hover:shadow-lg transition-shadow duration-300">
            <div class="mx-auto h-16 w-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-6">
              <svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">结构分析</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              深度分析文档结构、逻辑层次和内容组织，提供优化建议。
            </p>
            <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-2">
              <li>✓ 章节结构分析</li>
              <li>✓ 逻辑层次检查</li>
              <li>✓ 内容组织优化</li>
              <li>✓ 目录生成建议</li>
            </ul>
          </BaseCard>
        </div>
      </div>
    </section>

    <!-- 价格方案 -->
    <section id="pricing" class="py-20 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            简单透明的定价
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300">
            按次付费，无隐藏费用，随用随付
          </p>
        </div>
        
        <div class="max-w-md mx-auto">
          <div class="card card-hover text-center relative">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-blue-600 dark:bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                推荐方案
              </span>
            </div>
            <div class="card-body">
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">按次付费</h3>
              <div class="mb-6">
                <span class="text-4xl font-bold text-blue-600 dark:text-blue-400">¥9</span>
                <span class="text-gray-600 dark:text-gray-300">/ 3次检测</span>
              </div>
              <ul class="text-left space-y-3 mb-8">
                <li class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  支持所有检测模式
                </li>
                <li class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  详细分析报告
                </li>
                <li class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  支持多种文件格式
                </li>
                <li class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  7天报告保存
                </li>
                <li class="flex items-center text-gray-700 dark:text-gray-300">
                  <svg class="h-5 w-5 text-green-500 dark:text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  在线客服支持
                </li>
              </ul>
              <router-link to="/pricing" class="btn btn-primary btn-lg w-full">
                立即购买
              </router-link>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-4">
                首次注册免费体验1次检测
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户评价 -->
    <section class="py-20 bg-white dark:bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            用户真实评价
          </h2>
          <p class="text-xl text-gray-600 dark:text-gray-300">
            超过1000+用户的信赖选择
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <BaseCard>
            <div class="flex items-center mb-4">
              <div class="h-12 w-12 bg-blue-500 dark:bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                李
              </div>
              <div class="ml-4">
                <h4 class="font-semibold text-gray-900 dark:text-white">李教授</h4>
                <p class="text-gray-600 dark:text-gray-300 text-sm">高校教师</p>
              </div>
            </div>
            <p class="text-gray-700 dark:text-gray-300">
              "非常专业的文档检测工具，帮助学生规范论文格式，节省了大量审核时间。"
            </p>
            <div class="flex text-yellow-400 dark:text-yellow-500 mt-4">
              ★★★★★
            </div>
          </BaseCard>
          
          <BaseCard>
            <div class="flex items-center mb-4">
              <div class="h-12 w-12 bg-green-500 dark:bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                张
              </div>
              <div class="ml-4">
                <h4 class="font-semibold text-gray-900 dark:text-white">张同学</h4>
                <p class="text-gray-600 dark:text-gray-300 text-sm">硕士研究生</p>
              </div>
            </div>
            <p class="text-gray-700 dark:text-gray-300">
              "毕业论文格式检查太有用了！自动发现了很多我没注意到的细节问题。"
            </p>
            <div class="flex text-yellow-400 dark:text-yellow-500 mt-4">
              ★★★★★
            </div>
          </BaseCard>
          
          <BaseCard>
            <div class="flex items-center mb-4">
              <div class="h-12 w-12 bg-purple-500 dark:bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                王
              </div>
              <div class="ml-4">
                <h4 class="font-semibold text-gray-900 dark:text-white">王经理</h4>
                <p class="text-gray-600 dark:text-gray-300 text-sm">企业文档管理</p>
              </div>
            </div>
            <p class="text-gray-700 dark:text-gray-300">
              "公司文档标准化利器，批量检查格式省时省力，报告详细易懂。"
            </p>
            <div class="flex text-yellow-400 dark:text-yellow-500 mt-4">
              ★★★★★
            </div>
          </BaseCard>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-900 dark:bg-gray-950 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center mb-4">
              <div class="h-8 w-8 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
              <span class="ml-2 text-xl font-semibold">Word分析服务</span>
            </div>
            <p class="text-gray-400 dark:text-gray-300">
              专业的Word文档智能分析平台，助您提升文档质量。
            </p>
          </div>
          
          <div>
            <h3 class="font-semibold mb-4">产品功能</h3>
            <ul class="space-y-2 text-gray-400 dark:text-gray-300">
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">论文检测</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">格式检查</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">结构分析</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">批量处理</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold mb-4">帮助支持</h3>
            <ul class="space-y-2 text-gray-400 dark:text-gray-300">
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">使用指南</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">常见问题</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">联系客服</a></li>
              <li><a href="#" class="hover:text-white dark:hover:text-gray-100 transition-colors">意见反馈</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-semibold mb-4">联系我们</h3>
            <div class="space-y-2 text-gray-400 dark:text-gray-300">
              <p>邮箱：<EMAIL></p>
              <p>QQ群：123456789</p>
              <p>工作时间：9:00-18:00</p>
            </div>
          </div>
        </div>
        
        <div class="border-t border-gray-800 dark:border-gray-700 mt-8 pt-8 text-center text-gray-400 dark:text-gray-300">
          <p>&copy; 2025 Word文档分析服务. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import AppNavbar from '@/components/AppNavbar.vue'

// 状态管理
const userStore = useUserStore()

// 平滑滚动
const smoothScroll = (event: Event) => {
  event.preventDefault()
  const target = event.target as HTMLAnchorElement
  const targetId = target.getAttribute('href')
  
  if (targetId && targetId.startsWith('#')) {
    const element = document.querySelector(targetId)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }
}

// 生命周期
onMounted(() => {
  // 绑定平滑滚动事件
  const anchors = document.querySelectorAll('a[href^="#"]')
  anchors.forEach(anchor => {
    anchor.addEventListener('click', smoothScroll)
  })
  
  // 页面加载动画
  const cards = document.querySelectorAll('.card')
  cards.forEach((card, index) => {
    setTimeout(() => {
      card.classList.add('fade-in')
    }, index * 100)
  })
  
  // 初始化用户状态
  userStore.initializeUser()
})
</script>

<style scoped>
/* 主题切换样式从custom.css全局样式中加载 */

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply text-blue-600 bg-white border-blue-600 hover:bg-blue-50 focus:ring-blue-500 dark:text-blue-400 dark:bg-gray-800 dark:border-blue-400 dark:hover:bg-gray-700;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

/* 卡片样式 */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
}

.card-hover {
  @apply transform transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.card-body {
  @apply p-6;
}

/* 动画 */
.fade-in {
  @apply animate-fade-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端导航优化 */
@media (max-width: 768px) {
  .mobile-nav-item {
    @apply flex items-center space-x-2 py-3 px-2 rounded-lg transition-colors;
  }
  
  .mobile-nav-item:hover {
    @apply bg-gray-50 dark:bg-gray-700;
  }
  
  .mobile-user-info {
    @apply bg-gray-50 dark:bg-gray-700 rounded-lg px-3 py-2 mb-2;
  }
  
  /* 移动端主题切换按钮优化 */
  .mobile-theme-toggle {
    @apply flex items-center justify-between py-3 px-2 rounded-lg;
  }
  
  .mobile-theme-toggle:hover {
    @apply bg-gray-50 dark:bg-gray-700;
  }
  
  /* 移动端菜单按钮触摸优化 */
  .mobile-menu-button {
    @apply p-2 rounded-lg transition-colors;
    min-height: 44px;
    min-width: 44px;
  }
  
  .mobile-menu-button:hover {
    @apply bg-gray-100 dark:bg-gray-700;
  }
}

/* 导航栏阴影优化 */
.navbar-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.dark .navbar-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}
</style> 