"""
文档结构分析器

负责基于预处理的数据进行文档结构分析，不涉及Word COM操作。
"""

import time
from typing import Dict, Any, List
from app.core.logging import logger


class StructureAnalyzer:
    """文档结构分析器"""
    
    def __init__(self):
        """初始化结构分析器"""
        self.analysis_stats = {
            'analyzed_documents': 0,
            'analysis_time': 0.0
        }
    
    def analyze_document_structure(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析文档结构
        
        Args:
            processed_data: 预处理后的文档数据
            
        Returns:
            dict: 结构分析结果
        """
        start_time = time.time()
        logger.info("🏗️ 开始分析文档结构")
        
        try:
            if not processed_data.get('success', False):
                raise ValueError(f"预处理数据无效: {processed_data.get('error_message', '未知错误')}")
            
            # 1. 分析文档层次结构
            hierarchy_analysis = self._analyze_document_hierarchy(processed_data)
            
            # 2. 分析文档完整性
            completeness_analysis = self._analyze_document_completeness(processed_data)
            
            # 3. 分析文档质量
            quality_analysis = self._analyze_document_quality(processed_data)
            
            # 4. 生成结构统计
            structure_statistics = self._generate_structure_statistics(processed_data)
            
            # 5. 识别文档类型
            document_type = self._identify_document_type(processed_data)
            
            # 合并分析结果
            analysis_result = {
                'hierarchy_analysis': hierarchy_analysis,
                'completeness_analysis': completeness_analysis,
                'quality_analysis': quality_analysis,
                'structure_statistics': structure_statistics,
                'document_type': document_type,
                'analysis_time': time.time() - start_time,
                'success': True
            }
            
            self.analysis_stats['analyzed_documents'] += 1
            self.analysis_stats['analysis_time'] += time.time() - start_time
            
            analysis_time = time.time() - start_time
            logger.info(f"✅ 文档结构分析完成，耗时: {analysis_time:.3f}秒")
            
            return analysis_result
            
        except Exception as e:
            analysis_time = time.time() - start_time
            error_msg = f"文档结构分析失败: {str(e)}"
            logger.error(f"❌ {error_msg}, 耗时: {analysis_time:.3f}秒")
            
            return {
                'success': False,
                'error_message': error_msg,
                'analysis_time': analysis_time
            }
    
    def _analyze_document_hierarchy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析文档层次结构"""
        logger.info("📊 分析文档层次结构")
        
        outline = data.get('document_structure', {}).get('outline', [])
        paragraphs = data.get('document_content', {}).get('paragraphs', [])
        
        # 分析标题层级
        heading_levels = {}
        max_level = 0
        min_level = float('inf')
        
        for item in outline:
            level = item.get('level', 0)
            if level > 0:
                heading_levels[level] = heading_levels.get(level, 0) + 1
                max_level = max(max_level, level)
                min_level = min(min_level, level)
        
        # 分析结构完整性
        has_proper_hierarchy = self._check_hierarchy_completeness(heading_levels, min_level, max_level)
        
        # 分析段落分布
        paragraph_distribution = self._analyze_paragraph_distribution(paragraphs)
        
        return {
            'heading_levels': heading_levels,
            'max_level': max_level if max_level > 0 else 0,
            'min_level': min_level if min_level != float('inf') else 0,
            'total_headings': len(outline),
            'has_proper_hierarchy': has_proper_hierarchy,
            'paragraph_distribution': paragraph_distribution
        }
    
    def _analyze_document_completeness(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析文档完整性"""
        logger.info("📋 分析文档完整性")
        
        structures = data.get('document_structure', {}).get('document_structures', [])
        cover_info = data.get('document_structure', {}).get('cover_page_info', {})
        
        # 检查必要结构
        required_structures = ['封面', '摘要', '目录', '正文', '参考文献']
        found_structures = {}
        
        for struct in structures:
            name = struct.get('name', '').lower()
            for req in required_structures:
                if req.lower() in name or self._is_structure_match(name, req):
                    found_structures[req] = struct
                    break
        
        # 计算完整性分数
        completeness_score = len(found_structures) / len(required_structures) * 100
        
        # 检查封面信息
        cover_completeness = self._analyze_cover_completeness(cover_info)
        
        return {
            'required_structures': required_structures,
            'found_structures': list(found_structures.keys()),
            'missing_structures': [req for req in required_structures if req not in found_structures],
            'completeness_score': completeness_score,
            'cover_completeness': cover_completeness,
            'has_cover': cover_info.get('has_cover', False),
            'has_toc': data.get('data_index', {}).get('has_toc', False)
        }
    
    def _analyze_document_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析文档质量"""
        logger.info("⭐ 分析文档质量")
        
        info = data.get('document_info', {})
        content = data.get('document_content', {})
        index = data.get('data_index', {})
        
        # 基本质量指标
        word_count = info.get('words', 0)
        paragraph_count = index.get('paragraph_count', 0)
        table_count = index.get('table_count', 0)
        image_count = index.get('image_count', 0)
        
        # 计算质量分数
        quality_scores = {
            'length_score': self._calculate_length_score(word_count),
            'structure_score': self._calculate_structure_score(paragraph_count, table_count, image_count),
            'formatting_score': self._calculate_formatting_score(content),
            'content_score': self._calculate_content_score(content)
        }
        
        overall_score = sum(quality_scores.values()) / len(quality_scores)
        
        return {
            'quality_scores': quality_scores,
            'overall_score': overall_score,
            'word_count': word_count,
            'paragraph_count': paragraph_count,
            'table_count': table_count,
            'image_count': image_count,
            'avg_words_per_paragraph': word_count / paragraph_count if paragraph_count > 0 else 0
        }
    
    def _generate_structure_statistics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成结构统计"""
        logger.info("📈 生成结构统计")
        
        info = data.get('document_info', {})
        index = data.get('data_index', {})
        structure = data.get('document_structure', {})
        
        return {
            'total_pages': info.get('pages', 0),
            'total_words': info.get('words', 0),
            'total_characters': info.get('characters', 0),
            'total_paragraphs': info.get('paragraphs', 0),
            'total_tables': index.get('table_count', 0),
            'total_images': index.get('image_count', 0),
            'total_headings': index.get('outline_count', 0),
            'total_structures': index.get('structure_count', 0),
            'text_length': index.get('text_length', 0),
            'has_toc': index.get('has_toc', False),
            'has_cover': index.get('has_cover', False),
            'structure_method': structure.get('structure_analysis_method', 'unknown')
        }
    
    def _identify_document_type(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """识别文档类型"""
        logger.info("🔍 识别文档类型")
        
        info = data.get('document_info', {})
        structures = data.get('document_structure', {}).get('document_structures', [])
        
        # 基于结构识别文档类型
        structure_names = [s.get('name', '').lower() for s in structures]
        
        document_types = {
            'thesis': ['学位论文', '毕业论文', '学士', '硕士', '博士'],
            'report': ['报告', '调研', '分析'],
            'paper': ['论文', '期刊', '会议'],
            'proposal': ['开题', '提案', '方案'],
            'manual': ['手册', '说明', '指南']
        }
        
        identified_type = 'unknown'
        confidence = 0.0
        
        for doc_type, keywords in document_types.items():
            matches = sum(1 for keyword in keywords if any(keyword in name for name in structure_names))
            if matches > 0:
                current_confidence = matches / len(keywords)
                if current_confidence > confidence:
                    identified_type = doc_type
                    confidence = current_confidence
        
        return {
            'type': identified_type,
            'confidence': confidence,
            'word_count': info.get('words', 0),
            'page_count': info.get('pages', 0),
            'characteristics': self._get_document_characteristics(data)
        }
    
    def _check_hierarchy_completeness(self, heading_levels: Dict[int, int], min_level: int, max_level: int) -> bool:
        """检查层次结构完整性"""
        if not heading_levels:
            return False
        
        # 检查是否有连续的层级
        for level in range(min_level, max_level):
            if level not in heading_levels:
                return False
        
        return True
    
    def _analyze_paragraph_distribution(self, paragraphs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析段落分布"""
        if not paragraphs:
            return {'total': 0, 'avg_length': 0, 'headings': 0, 'content': 0}
        
        total_paragraphs = len(paragraphs)
        heading_count = sum(1 for p in paragraphs if p.get('is_heading', False))
        content_count = total_paragraphs - heading_count
        avg_length = sum(p.get('word_count', 0) for p in paragraphs) / total_paragraphs
        
        return {
            'total': total_paragraphs,
            'headings': heading_count,
            'content': content_count,
            'avg_length': avg_length
        }
    
    def _analyze_cover_completeness(self, cover_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析封面完整性"""
        required_fields = ['title', 'author', 'institution', 'date']
        found_fields = []
        
        for field in required_fields:
            if cover_info.get(field) and cover_info[field].strip():
                found_fields.append(field)
        
        completeness = len(found_fields) / len(required_fields) * 100
        
        return {
            'required_fields': required_fields,
            'found_fields': found_fields,
            'missing_fields': [f for f in required_fields if f not in found_fields],
            'completeness_percentage': completeness
        }
    
    def _is_structure_match(self, name: str, required: str) -> bool:
        """检查结构名称是否匹配"""
        # 简单的匹配逻辑，可以根据需要扩展
        keywords = {
            '封面': ['cover', 'title', '标题'],
            '摘要': ['abstract', 'summary', '概要'],
            '目录': ['contents', 'toc', 'index'],
            '正文': ['content', 'body', 'main'],
            '参考文献': ['references', 'bibliography', '文献']
        }
        
        if required in keywords:
            return any(keyword in name for keyword in keywords[required])
        
        return False
    
    def _calculate_length_score(self, word_count: int) -> float:
        """计算长度分数"""
        if word_count < 1000:
            return 20.0
        elif word_count < 5000:
            return 60.0
        elif word_count < 10000:
            return 80.0
        else:
            return 100.0
    
    def _calculate_structure_score(self, paragraphs: int, tables: int, images: int) -> float:
        """计算结构分数"""
        score = 0.0
        
        # 段落数量评分
        if paragraphs > 10:
            score += 40.0
        elif paragraphs > 5:
            score += 20.0
        
        # 表格评分
        if tables > 0:
            score += 30.0
        
        # 图片评分
        if images > 0:
            score += 30.0
        
        return min(score, 100.0)
    
    def _calculate_formatting_score(self, content: Dict[str, Any]) -> float:
        """计算格式分数"""
        paragraphs = content.get('paragraphs', [])
        if not paragraphs:
            return 0.0
        
        # 检查格式一致性
        font_consistency = self._check_font_consistency(paragraphs)
        style_consistency = self._check_style_consistency(paragraphs)
        
        return (font_consistency + style_consistency) / 2 * 100
    
    def _calculate_content_score(self, content: Dict[str, Any]) -> float:
        """计算内容分数"""
        paragraphs = content.get('paragraphs', [])
        if not paragraphs:
            return 0.0
        
        # 简单的内容质量评估
        non_empty_paragraphs = sum(1 for p in paragraphs if p.get('text', '').strip())
        content_ratio = non_empty_paragraphs / len(paragraphs)
        
        return content_ratio * 100
    
    def _check_font_consistency(self, paragraphs: List[Dict[str, Any]]) -> float:
        """检查字体一致性"""
        if not paragraphs:
            return 0.0
        
        fonts = [p.get('font_name', '') for p in paragraphs if p.get('font_name')]
        if not fonts:
            return 0.0
        
        most_common_font = max(set(fonts), key=fonts.count)
        consistency = fonts.count(most_common_font) / len(fonts)
        
        return consistency
    
    def _check_style_consistency(self, paragraphs: List[Dict[str, Any]]) -> float:
        """检查样式一致性"""
        if not paragraphs:
            return 0.0
        
        styles = [p.get('style', '') for p in paragraphs if p.get('style')]
        if not styles:
            return 0.0
        
        unique_styles = len(set(styles))
        # 样式种类适中为好
        if unique_styles <= 5:
            return 1.0
        elif unique_styles <= 10:
            return 0.8
        else:
            return 0.6
    
    def _get_document_characteristics(self, data: Dict[str, Any]) -> List[str]:
        """获取文档特征"""
        characteristics = []
        
        info = data.get('document_info', {})
        index = data.get('data_index', {})
        
        if info.get('pages', 0) > 50:
            characteristics.append('长文档')
        elif info.get('pages', 0) < 10:
            characteristics.append('短文档')
        
        if index.get('table_count', 0) > 5:
            characteristics.append('表格丰富')
        
        if index.get('image_count', 0) > 5:
            characteristics.append('图片丰富')
        
        if index.get('has_toc'):
            characteristics.append('有目录')
        
        if index.get('has_cover'):
            characteristics.append('有封面')
        
        return characteristics
