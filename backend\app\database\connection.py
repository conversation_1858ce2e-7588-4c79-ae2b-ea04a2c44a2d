"""
Word文档分析服务 - 数据库连接管理

使用SQLAlchemy异步引擎和PostgreSQL数据库
"""

from typing import Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text

from app.core.config import settings
from app.core.logging import logger

# 全局数据库引擎和会话工厂
async_engine = None
async_session_factory = None


async def init_database():
    """
    初始化数据库连接和会话工厂
    """
    global async_engine, async_session_factory
    
    try:
        logger.info(f"正在连接到PostgreSQL数据库: {settings.database.url}")
        
        # 创建异步引擎
        async_engine = create_async_engine(
            settings.database.url,
            echo=settings.database.echo,
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow,
            pool_timeout=settings.database.pool_timeout,
            pool_recycle=settings.database.pool_recycle,
            pool_pre_ping=settings.database.pool_pre_ping,
            query_cache_size=1200,
        )
        
        # 创建会话工厂
        async_session_factory = async_sessionmaker(
            bind=async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        logger.info("数据库连接成功")
        
        # 测试连接
        await test_connection()
        
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise


async def close_database():
    """关闭数据库连接"""
    global async_engine
    
    if async_engine:
        await async_engine.dispose()
        async_engine = None
        logger.info("数据库连接已关闭")


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话（依赖注入用）
    """
    if async_session_factory is None:
        await init_database()
    
    async with async_session_factory() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {str(e)}")
            raise


async def get_database_session() -> AsyncSession:
    """
    获取一个独立的数据库会话（用于后台任务）
    """
    if async_session_factory is None:
        await init_database()
        if async_session_factory is None:
            raise Exception("数据库会话工厂未能初始化")

    return async_session_factory()


async def test_connection():
    """测试数据库连接"""
    try:
        async with async_session_factory() as session:
            result = await session.execute(text("SELECT 1"))
            result.fetchone()
            logger.info("数据库连接测试成功")
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        raise


async def get_database_status() -> dict:
    """获取数据库状态"""
    try:
        if async_engine is None:
            return {
                "status": "unhealthy",
                "message": "数据库引擎未初始化",
                "error": "Engine not initialized"
            }
        
        # 测试连接
        async with async_session_factory() as session:
            # 测试基本查询
            await session.execute(text("SELECT 1"))
            
            # 检查核心表是否存在
            result = await session.execute(
                text("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'tasks'")
            )
            tasks_table_exists = (result.fetchone())[0] > 0
            
            # 获取连接池状态
            pool_status = async_engine.pool.status()
            
            if tasks_table_exists:
                status = "healthy"
                message = "数据库连接正常，核心表存在"
            else:
                status = "degraded"
                message = "数据库连接正常，但核心表 'tasks' 不存在"
                
            return {
                "status": status,
                "message": message,
                "database_url": settings.database.url.split('@')[1] if '@' in settings.database.url else "Hidden",
                "pool_status": pool_status,
                "pool_size": settings.database.pool_size,
                "max_overflow": settings.database.max_overflow
            }
            
    except Exception as e:
        logger.error(f"数据库状态检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "message": "数据库连接失败",
            "error": str(e)
        } 