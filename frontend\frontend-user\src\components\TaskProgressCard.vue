<template>
  <BaseCard class="task-progress-card" :class="{ 'border-blue-500': isActive }">
    <div class="space-y-4">
      <!-- 任务头部信息 -->
      <div class="flex items-start justify-between">
        <div class="flex-1 min-w-0">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
            {{ task.title || `任务 ${task.id}` }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
            {{ task.description || getTaskTypeDescription(task.type) }}
          </p>
        </div>
        
        <!-- 任务状态徽章 -->
        <div class="flex-shrink-0 ml-4">
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            :class="getStatusBadgeClass(task.status)"
          >
            <div
              v-if="task.status === 'running'"
              class="animate-spin -ml-1 mr-1.5 h-3 w-3 text-current"
            >
              <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <div
              v-else
              class="h-2 w-2 rounded-full mr-1.5"
              :class="getStatusDotClass(task.status)"
            ></div>
            {{ getStatusText(task.status) }}
          </span>
        </div>
      </div>

      <!-- 进度条 -->
      <div v-if="task.status === 'running' || task.status === 'pending'" class="space-y-2">
        <div class="flex justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-300">进度</span>
          <span class="text-gray-900 dark:text-white font-medium">{{ Math.round(task.progress || 0) }}%</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
            :style="{ width: `${task.progress || 0}%` }"
          ></div>
        </div>
      </div>

      <!-- 任务详细信息 -->
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-500 dark:text-gray-400">创建时间</span>
          <p class="text-gray-900 dark:text-white font-medium">
            {{ formatDateTime(task.created_at) }}
          </p>
        </div>
        <div v-if="task.completed_at">
          <span class="text-gray-500 dark:text-gray-400">完成时间</span>
          <p class="text-gray-900 dark:text-white font-medium">
            {{ formatDateTime(task.completed_at) }}
          </p>
        </div>
        <div v-if="task.estimated_duration">
          <span class="text-gray-500 dark:text-gray-400">预计耗时</span>
          <p class="text-gray-900 dark:text-white font-medium">
            {{ formatDuration(task.estimated_duration) }}
          </p>
        </div>
        <div v-if="task.actual_duration">
          <span class="text-gray-500 dark:text-gray-400">实际耗时</span>
          <p class="text-gray-900 dark:text-white font-medium">
            {{ formatDuration(task.actual_duration) }}
          </p>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="task.status === 'failed' && task.error_message" class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-red-800 dark:text-red-200">
              任务执行失败
            </h4>
            <p class="text-sm text-red-700 dark:text-red-300 mt-1">
              {{ task.error_message }}
            </p>
          </div>
        </div>
      </div>

      <!-- 成功结果 -->
      <div v-if="task.status === 'completed' && task.result" class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-green-800 dark:text-green-200">
              任务完成
            </h4>
            <p class="text-sm text-green-700 dark:text-green-300 mt-1">
              {{ getResultSummary(task.result) }}
            </p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3 pt-2 border-t border-gray-200 dark:border-gray-700">
        <BaseButton
          v-if="task.status === 'failed'"
          variant="secondary"
          size="sm"
          @click="$emit('retry', task)"
        >
          重试
        </BaseButton>
        
        <BaseButton
          v-if="task.status === 'running'"
          variant="secondary"
          size="sm"
          @click="$emit('cancel', task)"
        >
          取消
        </BaseButton>
        
        <BaseButton
          v-if="task.status === 'completed' && task.result"
          variant="primary"
          size="sm"
          @click="$emit('view-result', task)"
        >
          查看结果
        </BaseButton>
        
        <BaseButton
          v-if="task.status === 'completed' || task.status === 'failed'"
          variant="secondary"
          size="sm"
          @click="$emit('delete', task)"
        >
          删除
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import BaseCard from './BaseCard.vue'
import BaseButton from './BaseButton.vue'
import websocketService from '@/services/websocketService'

interface Task {
  id: string
  title?: string
  description?: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress?: number
  created_at: string
  completed_at?: string
  estimated_duration?: number
  actual_duration?: number
  error_message?: string
  result?: any
}

interface Props {
  task: Task
  realTimeUpdates?: boolean
}

interface Emits {
  (e: 'retry', task: Task): void
  (e: 'cancel', task: Task): void
  (e: 'delete', task: Task): void
  (e: 'view-result', task: Task): void
  (e: 'task-updated', task: Task): void
}

const props = withDefaults(defineProps<Props>(), {
  realTimeUpdates: true
})

const emit = defineEmits<Emits>()

// 响应式状态
const isActive = ref(false)
const localTask = ref<Task>({ ...props.task })

// WebSocket订阅取消函数
let unsubscribeTaskStatus: (() => void) | null = null

// 计算属性
const taskTypeDescriptions = {
  'document_analysis': '文档分析任务',
  'paper_check': '论文检测任务',
  'format_check': '格式检查任务',
  'content_extraction': '内容提取任务',
  'image_processing': '图片处理任务'
}

// 方法
const getTaskTypeDescription = (type: string) => {
  return taskTypeDescriptions[type as keyof typeof taskTypeDescriptions] || '未知任务类型'
}

const getStatusText = (status: string) => {
  const statusTexts = {
    pending: '等待中',
    running: '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusTexts[status as keyof typeof statusTexts] || status
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200',
    running: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
  }
  return classes[status as keyof typeof classes] || classes.pending
}

const getStatusDotClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-400',
    running: 'bg-blue-400',
    completed: 'bg-green-400',
    failed: 'bg-red-400'
  }
  return classes[status as keyof typeof classes] || classes.pending
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

const getResultSummary = (result: any) => {
  if (!result) return '任务已完成'
  
  if (typeof result === 'string') {
    return result
  }
  
  if (result.summary) {
    return result.summary
  }
  
  if (result.total_problems !== undefined) {
    return `发现 ${result.total_problems} 个问题`
  }
  
  if (result.pages !== undefined) {
    return `处理了 ${result.pages} 页内容`
  }
  
  return '任务已完成'
}

const updateTaskFromWebSocket = (data: any) => {
  localTask.value = {
    ...localTask.value,
    status: data.status,
    progress: data.progress,
    error_message: data.error,
    result: data.result
  }
  
  if (data.status === 'completed') {
    localTask.value.completed_at = new Date().toISOString()
  }
  
  // 触发更新事件
  emit('task-updated', localTask.value)
  
  // 任务状态变化时的视觉反馈
  isActive.value = true
  setTimeout(() => {
    isActive.value = false
  }, 1000)
}

// 生命周期
onMounted(() => {
  if (props.realTimeUpdates && (localTask.value.status === 'pending' || localTask.value.status === 'running')) {
    // 订阅WebSocket任务状态更新
    unsubscribeTaskStatus = websocketService.subscribeToTaskStatus(
      localTask.value.id,
      updateTaskFromWebSocket
    )
  }
})

onUnmounted(() => {
  if (unsubscribeTaskStatus) {
    unsubscribeTaskStatus()
  }
})

// 监听props变化
watch(() => props.task, (newTask) => {
  localTask.value = { ...newTask }
}, { deep: true })

// 暴露本地任务状态
defineExpose({
  task: computed(() => localTask.value)
})
</script>

<style scoped>
.task-progress-card {
  transition: all 0.3s ease;
}

.task-progress-card.border-blue-500 {
  box-shadow: 0 0 0 1px rgb(59 130 246 / 0.5);
}

/* 进度条动画 */
@keyframes progress-animation {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.bg-blue-600 {
  background: linear-gradient(90deg, #2563eb, #3b82f6, #2563eb);
  background-size: 200% 100%;
  animation: progress-animation 2s linear infinite;
}
</style> 