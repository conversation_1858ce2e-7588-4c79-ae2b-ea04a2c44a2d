# ==================================================
# Word文档分析服务 - Prometheus告警规则
# ==================================================

groups:
  # 服务可用性告警
  - name: service_availability
    rules:
      - alert: WordServiceDown
        expr: up{job="word-service"} == 0
        for: 1m
        labels:
          severity: critical
          service: word-service
        annotations:
          summary: "Word服务不可用"
          description: "Word文档分析服务已停止运行超过1分钟"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 2m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis服务不可用"
          description: "Redis缓存服务已停止运行超过2分钟"

      - alert: NginxDown
        expr: up{job="nginx"} == 0
        for: 1m
        labels:
          severity: warning
          service: nginx
        annotations:
          summary: "Nginx服务不可用"
          description: "Nginx反向代理服务已停止运行超过1分钟"

  # 性能告警
  - name: performance_alerts
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: performance
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 90
        for: 10m
        labels:
          severity: critical
          category: performance
        annotations:
          summary: "磁盘使用率过高"
          description: "实例 {{ $labels.instance }} 磁盘使用率超过90%，当前值: {{ $value }}%"

  # 应用性能告警
  - name: application_performance
    rules:
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过2秒，当前值: {{ $value }}秒"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 3m
        labels:
          severity: critical
          category: application
        annotations:
          summary: "错误率过高"
          description: "HTTP 5xx错误率超过5%，当前值: {{ $value }}%"

      - alert: TooManyFailedTasks
        expr: rate(word_tasks_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          category: application
        annotations:
          summary: "任务失败率过高"
          description: "Word文档处理任务失败率过高，每分钟失败 {{ $value }} 个任务"

  # 资源告警
  - name: resource_alerts
    rules:
      - alert: TooManyOpenFiles
        expr: node_filefd_allocated / node_filefd_maximum * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: resource
        annotations:
          summary: "打开文件数过多"
          description: "实例 {{ $labels.instance }} 打开文件数超过80%，当前值: {{ $value }}%"

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) + rate(node_network_transmit_bytes_total[5m]) > 100000000
        for: 10m
        labels:
          severity: warning
          category: resource
        annotations:
          summary: "网络流量过高"
          description: "实例 {{ $labels.instance }} 网络流量超过100MB/s"

  # 业务告警
  - name: business_alerts
    rules:
      - alert: QueueTooLong
        expr: word_task_queue_length > 100
        for: 5m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "任务队列过长"
          description: "Word文档处理队列长度超过100，当前值: {{ $value }}"

      - alert: ProcessingTimeTooLong
        expr: histogram_quantile(0.95, rate(word_processing_duration_seconds_bucket[10m])) > 300
        for: 10m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "文档处理时间过长"
          description: "95%的文档处理时间超过5分钟，当前值: {{ $value }}秒"

      - alert: LowSuccessRate
        expr: rate(word_tasks_completed_total[10m]) / (rate(word_tasks_completed_total[10m]) + rate(word_tasks_failed_total[10m])) * 100 < 95
        for: 10m
        labels:
          severity: warning
          category: business
        annotations:
          summary: "任务成功率过低"
          description: "Word文档处理成功率低于95%，当前值: {{ $value }}%"

  # 安全告警
  - name: security_alerts
    rules:
      - alert: TooManyFailedLogins
        expr: rate(auth_failed_attempts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "登录失败次数过多"
          description: "每分钟登录失败次数超过10次，可能存在暴力破解攻击"

      - alert: SuspiciousActivity
        expr: rate(http_requests_total{status="403"}[5m]) > 5
        for: 3m
        labels:
          severity: warning
          category: security
        annotations:
          summary: "可疑活动检测"
          description: "403禁止访问请求频率过高，可能存在恶意访问"

  # PostgreSQL数据库告警
  - name: database_alerts
    rules:
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "PostgreSQL连接数过高"
          description: "PostgreSQL数据库活跃连接数超过80%，当前值: {{ $value }}%"

      - alert: SlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "慢查询过多"
          description: "PostgreSQL查询效率低下，当前值: {{ $value }}"

      - alert: PostgreSQLDown
        expr: up{job="postgresql"} == 0
        for: 1m
        labels:
          severity: critical
          category: database
        annotations:
          summary: "PostgreSQL服务不可用"
          description: "PostgreSQL数据库服务已停止运行"

      - alert: DatabaseLockWait
        expr: pg_locks_count{mode="ExclusiveLock"} > 10
        for: 3m
        labels:
          severity: warning
          category: database
        annotations:
          summary: "数据库锁等待过多"
          description: "PostgreSQL存在过多排他锁，可能影响性能"
