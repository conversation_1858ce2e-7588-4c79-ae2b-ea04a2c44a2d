# =============================================================================
# Word文档分析服务 - 项目专用 .gitignore
# 更新时间: 2024-12-19
# 适用于: Python FastAPI + Vue3 TypeScript 全栈项目
# =============================================================================

# ===== Python 后端相关 =====

# Python 字节码和缓存
__pycache__/
*.py[cod]
*$py.class
*.so

# Python 分发和打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 测试和覆盖率
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
test-results/
test-output/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.pipenv/

# Python 开发工具
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/
cython_debug/

# ===== Node.js 前端相关 =====

# 依赖模块
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
dist-ssr/
*.local

# Vite 相关
.vite/
.rollup.cache/

# 前端环境变量
.env.local
.env.development.local
.env.test.local
.env.production.local

# 前端缓存
.cache/
.nuxt/
.next/
.svelte-kit/

# TypeScript 编译
*.tsbuildinfo

# ===== 数据库相关 =====

# SQLite 数据库
*.db
*.sqlite
*.sqlite3
*.sqlite3-journal
*.sqlite3-shm
*.sqlite3-wal

# PostgreSQL 相关
*.sql.bak
*.dump

# 数据库数据目录
data/
db/

# ===== 缓存和存储 =====

# Redis
dump.rdb
*.rdb

# 缓存目录
cache/
.cache/
tmp/
temp/
temporary/

# ===== 日志文件 =====

# 应用日志
logs/
*.log
log/
word_service.log
access.log
error.log
debug.log

# 系统日志
*.pid
*.sock

# ===== 配置和敏感信息 =====

# 环境配置文件
.env.local
.env.production
.env.development
.env.staging
.env.test

# 本地配置
config.local.py
config.local.yaml
config.local.json
settings.local.py
local_settings.py

# 密钥和证书
*.secret
*.key
*.pem
*.crt
*.cert
*.p12
*.pfx
secrets/
private/
keys/

# API密钥
api_keys.txt
.api_keys

# ===== 上传和媒体文件 =====

# 用户上传文件
uploads/
upload/
static/uploads/
media/
files/

# Word文档和Office文件
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx
test_documents/
sample_documents/

# 图片和媒体
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
*.ico
*.webp
*.mp4
*.mp3
*.avi
*.mov

# ===== 开发工具和IDE =====

# VS Code
.vscode/
*.code-workspace

# PyCharm / IntelliJ
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 操作系统文件 =====

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== 备份和临时文件 =====

# 备份文件
*.bak
*.backup
*.old
*.orig
*.save
*~

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2
*.gz
*.bz2

# 临时文件
*.tmp
*.temp
temp_*
tmp_*

# ===== Docker 和部署 =====

# Docker 本地数据
docker-compose.override.yml
.docker/

# 部署相关
.deploy/
deploy.local.*

# ===== 文档和报告 =====

# 文档构建输出
docs/_build/
docs/build/
site/

# 测试报告
test_report.html
coverage_report.html

# ===== 项目特定文件 =====

# 本项目临时验证脚本（已清理完成）
*_verification*.py
test_*.py.tmp
verification_*.json
comprehensive_*.py
extended_*.py
final_*.py

# 项目特定临时文件
check-*.bat
start-*.bat
stop-*.bat

# 调试和分析文件
*.prof
*.profile
profile_*

# 本地开发配置
.local/
local/

# 运行时文件
*.pid
*.lock
.lock

# ===== 大文件和二进制 =====

# 大文件（超过100MB）
*.iso
*.dmg
*.exe
*.msi
*.pdf

# 模型文件和数据集
*.model
*.pkl
*.h5
*.ckpt

# ===== 特殊忽略 =====

# 忽略但保留目录结构的空文件
!.gitkeep
!.keep

# 但是保留某些重要的配置示例文件
!config.example.yaml
!.env.example
!requirements*.txt
!package*.json

# 保留重要的说明文档
!README*.md
!*.md

# 保留Docker相关配置
!Dockerfile*
!docker-compose*.yml
!.dockerignore

# ===== 项目清理记录 =====

# 项目清理过程中的临时文件（如果再次出现）
项目清理临时记录*
清理工作临时*
*清理状态*.json
临时验证*