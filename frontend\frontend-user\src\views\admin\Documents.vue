<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 管理员导航栏 -->
    <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/admin/dashboard" class="flex items-center hover:opacity-80 transition-opacity">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">W</span>
              </div>
            </div>
            <div class="ml-3">
              <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
              <span class="text-xs text-red-600 dark:text-red-400 ml-2 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded">管理员</span>
            </div>
          </router-link>
          
          <!-- 导航菜单 -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link to="/admin/dashboard" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">仪表盘</router-link>
            <router-link to="/admin/users" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">用户管理</router-link>
            <router-link to="/admin/documents" class="text-red-600 dark:text-red-400 font-medium">文档管理</router-link>
            <router-link to="/admin/tasks" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">任务监控</router-link>
            <router-link to="/admin/system" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">系统设置</router-link>
            <router-link to="/admin/reports" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">报告中心</router-link>
          </div>
          
          <!-- 管理员菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 主题切换开关 -->
            <div class="hidden md:block theme-toggle-container">
              <button @click="themeStore.toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
                <div class="theme-toggle-icons">
                  <!-- 太阳图标 (明亮模式) -->
                  <svg class="sun-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- 月亮图标 (暗黑模式) -->
                  <svg class="moon-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
              </button>
            </div>
            <div class="relative">
              <button @click="showAdminMenu = !showAdminMenu" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <div class="h-8 w-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                  <span class="text-red-600 dark:text-red-400 font-medium text-sm">管</span>
                </div>
                <span class="hidden md:block">管理员</span>
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div v-if="showAdminMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                <div class="py-1">
                  <router-link to="/" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">切换到用户界面</router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button @click="handleAdminLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700">退出登录</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 页面标题和操作 -->
      <div class="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            文档管理
          </h1>
          <p class="text-gray-600 dark:text-gray-300">
            管理全平台用户上传的文档和分析结果
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button @click="exportDocuments" class="btn btn-secondary">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            导出数据
          </button>
          <button @click="cleanupDocuments" class="btn btn-warning">
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            清理过期
          </button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ documentStats.total.toLocaleString() }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">总文档数</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ documentStats.storageSize }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">存储占用</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ documentStats.todayUploads }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">今日上传</p>
          </div>
        </div>
        
        <div class="card text-center">
          <div class="card-body">
            <div class="mx-auto h-12 w-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-3">
              <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ documentStats.errorFiles }}</p>
            <p class="text-sm text-gray-600 dark:text-gray-400">异常文件</p>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="card mb-6">
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- 搜索框 -->
            <div>
              <div class="relative">
                <input 
                  v-model="searchQuery"
                  type="text" 
                  placeholder="搜索文档名称、用户..." 
                  class="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  @input="filterDocuments"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                  </svg>
                </div>
              </div>
            </div>
            
            <!-- 状态筛选 -->
            <div>
              <select v-model="statusFilter" @change="filterDocuments" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">所有状态</option>
                <option value="completed">已完成</option>
                <option value="processing">处理中</option>
                <option value="failed">失败</option>
                <option value="pending">等待中</option>
              </select>
            </div>
            
            <!-- 文件类型 -->
            <div>
              <select v-model="typeFilter" @change="filterDocuments" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">所有类型</option>
                <option value="docx">DOCX文档</option>
                <option value="doc">DOC文档</option>
                <option value="pdf">PDF文档</option>
                <option value="rtf">RTF文档</option>
              </select>
            </div>
            
            <!-- 分析类型 -->
            <div>
              <select v-model="analysisFilter" @change="filterDocuments" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">所有分析</option>
                <option value="format">格式检测</option>
                <option value="structure">结构分析</option>
                <option value="citation">引用检查</option>
                <option value="comprehensive">综合分析</option>
              </select>
            </div>
            
            <!-- 上传时间 -->
            <div>
              <select v-model="dateFilter" @change="filterDocuments" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="year">今年</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 文档列表 -->
      <div class="card">
        <div class="card-body p-0">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    <label class="custom-checkbox">
                      <input type="checkbox" @change="selectAllDocuments">
                      <span class="checkbox-visual"></span>
                    </label>
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    文档信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    分析状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    文件大小
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    上传时间
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="document in paginatedDocuments" :key="document.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <label class="custom-checkbox">
                      <input 
                        type="checkbox" 
                        v-model="selectedDocuments"
                        :value="document.id"
                        class="document-checkbox"
                      >
                      <span class="checkbox-visual"></span>
                    </label>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div :class="[
                        'h-10 w-10 rounded flex items-center justify-center',
                        getFileIconColor(document.type, document.status)
                      ]">
                        <svg v-if="document.type === 'pdf'" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                        <svg v-else class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ document.name }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ document.id }}</div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">{{ getAnalysisTypeText(document.analysisType) }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ document.username }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ document.userEmail }}</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500">{{ getUserTypeText(document.userType) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="[
                      'status-badge text-xs',
                      document.status === 'completed' ? 'status-completed' :
                      document.status === 'processing' ? 'status-pending' :
                      document.status === 'failed' ? 'status-failed' :
                      document.status === 'pending' ? 'status-pending' :
                      'status-failed'
                    ]">
                      {{ getStatusText(document.status) }}
                    </span>
                    <div v-if="document.status === 'completed'" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      发现 {{ document.issues || 3 }} 个问题
                    </div>
                    <div v-else-if="document.status === 'processing'" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1">
                        <div class="bg-blue-600 h-1 rounded-full" :style="{ width: document.progress + '%' }"></div>
                      </div>
                      <span class="text-xs">{{ document.progress }}%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    {{ formatFileSize(document.size) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ formatDate(document.uploadTime) }}<br>
                    <span class="text-xs">{{ formatTime(document.uploadTime) }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button @click="viewDocument(document)" class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">查看</button>
                      <button v-if="document.status === 'completed'" @click="downloadDocument(document)" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">下载</button>
                      <button v-else-if="document.status === 'processing'" @click="cancelTask(document)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">取消</button>
                      <button @click="deleteDocument(document)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 批量操作和分页 -->
          <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <!-- 批量操作 -->
            <div class="flex items-center justify-center mb-4">
              <button 
                @click="performBatchAction" 
                :disabled="selectedDocuments.length === 0"
                class="btn btn-secondary btn-sm"
                :class="{ 'opacity-50 cursor-not-allowed': selectedDocuments.length === 0 }"
              >
                {{ selectedDocuments.length > 0 ? `批量操作 (${selectedDocuments.length})` : '批量操作' }}
              </button>
            </div>
            
            <!-- 分页 -->
            <BasePagination 
              :current-page="currentPage"
              :page-size="pageSize"
              :total="filteredDocuments.length"
              @page-change="(page: number) => currentPage = page"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { adminLogout, validateAdminAccess } from '@/utils/adminAuth'
import { $notify } from '@/utils/useNotifications'
import { $confirm } from '@/utils/useConfirm'
import BasePagination from '@/components/BasePagination.vue'

const router = useRouter()
const themeStore = useThemeStore()

// 组件状态
const showAdminMenu = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const analysisFilter = ref('')
const dateFilter = ref('')
const currentPage = ref(1)
const pageSize = 20
const selectedDocuments = ref<string[]>([])

// 文档统计
const documentStats = reactive({
  total: 15847,
  storageSize: '12.8GB',
  todayUploads: 234,
  errorFiles: 45
})

// 模拟文档数据
const allDocuments = ref([
  {
    id: 'DOC20240115001',
    name: '毕业论文_最终版.docx',
    username: '张三',
    userEmail: '<EMAIL>',
    userType: 'professional',
    status: 'completed',
    analysisType: 'format',
    size: 2516582, // 2.4MB
    uploadTime: '2024-01-15 14:30:00',
    type: 'docx',
    issues: 3
  },
  {
    id: 'DOC20240115002',
    name: '学术论文草稿.docx',
    username: '李四',
    userEmail: '<EMAIL>',
    userType: 'standard',
    status: 'processing',
    analysisType: 'comprehensive',
    size: 1887437, // 1.8MB
    uploadTime: '2024-01-15 15:45:00',
    type: 'docx',
    progress: 65
  },
  {
    id: 'DOC20240115003',
    name: '研究报告.pdf',
    username: '王五',
    userEmail: '<EMAIL>',
    userType: 'basic',
    status: 'failed',
    analysisType: 'structure',
    size: 3145728, // 3MB
    uploadTime: '2024-01-14 16:20:00',
    type: 'pdf'
  },
  {
    id: 'DOC20240115004',
    name: '学位论文答辩.docx',
    username: '赵六',
    userEmail: '<EMAIL>',
    userType: 'enterprise',
    status: 'pending',
    analysisType: 'citation',
    size: 4194304, // 4MB
    uploadTime: '2024-01-15 10:15:00',
    type: 'docx'
  },
  {
    id: 'DOC20240115005',
    name: '项目总结报告.rtf',
    username: '钱七',
    userEmail: '<EMAIL>',
    userType: 'free',
    status: 'completed',
    analysisType: 'format',
    size: 1048576, // 1MB
    uploadTime: '2024-01-13 09:30:00',
    type: 'rtf',
    issues: 1
  }
])

// 计算属性
const filteredDocuments = computed(() => {
  let filtered = allDocuments.value.slice()

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(doc => 
      doc.name.toLowerCase().includes(query) ||
      doc.username.toLowerCase().includes(query) ||
      doc.userEmail.toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(doc => doc.status === statusFilter.value)
  }

  if (typeFilter.value) {
    filtered = filtered.filter(doc => doc.type === typeFilter.value)
  }

  if (analysisFilter.value) {
    filtered = filtered.filter(doc => doc.analysisType === analysisFilter.value)
  }

  if (dateFilter.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    filtered = filtered.filter(doc => {
      const docDate = new Date(doc.uploadTime)
      
      switch (dateFilter.value) {
        case 'today':
          return docDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return docDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
          return docDate >= monthAgo
        case 'year':
          const yearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
          return docDate >= yearAgo
        default:
          return true
      }
    })
  }

  return filtered
})



const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredDocuments.value.slice(start, end)
})

// 方法
const filterDocuments = () => {
  currentPage.value = 1
}

const selectAllDocuments = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.checked) {
    selectedDocuments.value = paginatedDocuments.value.map(doc => doc.id)
  } else {
    selectedDocuments.value = []
  }
}

const getFileIconColor = (type: string, status: string) => {
  if (status === 'failed') {
    return 'bg-red-100 text-red-600'
  }
  if (status === 'processing') {
    return 'bg-yellow-100 text-yellow-600'
  }
  
  switch (type) {
    case 'pdf':
      return 'bg-red-100 text-red-600'
    case 'docx':
    case 'doc':
      return 'bg-blue-100 text-blue-600'
    case 'rtf':
      return 'bg-green-100 text-green-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败',
    pending: '等待中'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getAnalysisTypeText = (type: string) => {
  const typeMap = {
    format: '格式检测',
    structure: '结构分析',
    citation: '引用检查',
    comprehensive: '综合分析'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getUserTypeText = (type: string) => {
  const typeMap = {
    free: '免费用户',
    basic: '基础版用户',
    standard: '标准版用户',
    professional: '专业版用户',
    enterprise: '企业版用户'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
}



const performBatchAction = () => {
  if (selectedDocuments.value.length === 0) return
  
  // 显示批量操作菜单的逻辑
  $notify.info(`对 ${selectedDocuments.value.length} 个文档执行批量操作`)
}

const viewDocument = (document: any) => {
  $notify.info(`查看文档: ${document.name}`)
}

const downloadDocument = (document: any) => {
  $notify.info(`下载文档: ${document.name}`)
}

const deleteDocument = async (document: any) => {
  const result = await $confirm.danger(`确定要删除文档 "${document.name}" 吗？此操作无法撤销。`, {
    title: '删除文档',
    confirmText: '确定删除',
    cancelText: '取消'
  })
  if (result) {
    const index = allDocuments.value.findIndex(d => d.id === document.id)
    if (index > -1) {
      allDocuments.value.splice(index, 1)
      $notify.success(`文档 "${document.name}" 已删除`)
    }
  }
}

const cancelTask = async (document: any) => {
  const result = await $confirm.warning(`确定要取消任务 "${document.name}" 吗？`, {
    title: '取消任务',
    confirmText: '确定取消',
    cancelText: '保留任务'
  })
  if (result) {
    document.status = 'failed'
    $notify.warning(`任务 "${document.name}" 已取消`)
  }
}

const exportDocuments = () => {
  $notify.info('正在导出文档数据...')
}

const cleanupDocuments = async () => {
  const result = await $confirm.danger('确定要清理过期文档吗？此操作不可恢复。', {
    title: '清理过期文档',
    confirmText: '确定清理',
    cancelText: '取消'
  })
  if (result) {
    $notify.info('正在清理过期文档...')
  }
}

const handleAdminLogout = () => {
  adminLogout(router)
}

onMounted(async () => {
  // 检查管理员登录状态
  if (!validateAdminAccess(router, '/admin/documents')) {
    return
  }

  // ... existing code ...
})
</script>

<style scoped>
/* 文档管理特有样式 */
</style> 