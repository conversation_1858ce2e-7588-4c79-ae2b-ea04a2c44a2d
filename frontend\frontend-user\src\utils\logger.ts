/**
 * 统一日志系统
 * 提供开发和生产环境的日志管理
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  remoteUrl?: string
}

class Logger {
  private config: LogConfig
  private levels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
  }

  constructor(config: LogConfig) {
    this.config = config
  }

  private shouldLog(level: LogLevel): boolean {
    return this.levels[level] >= this.levels[this.config.level]
  }

  private formatMessage(level: LogLevel, message: string, data?: any): string {
    const timestamp = new Date().toISOString()
    const baseMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`
    
    if (data) {
      return `${baseMessage} ${JSON.stringify(data)}`
    }
    return baseMessage
  }

  private async sendToRemote(level: LogLevel, message: string, data?: any) {
    if (!this.config.enableRemote || !this.config.remoteUrl) {
      return
    }

    try {
      await fetch(this.config.remoteUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level,
          message,
          data,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })
    } catch (error) {
      // 静默处理远程日志发送失败
      console.error('Failed to send log to remote:', error)
    }
  }

  debug(message: string, data?: any) {
    if (!this.shouldLog('debug')) return

    if (this.config.enableConsole) {
      console.log(this.formatMessage('debug', message, data))
    }
    
    this.sendToRemote('debug', message, data)
  }

  info(message: string, data?: any) {
    if (!this.shouldLog('info')) return

    if (this.config.enableConsole) {
      console.info(this.formatMessage('info', message, data))
    }
    
    this.sendToRemote('info', message, data)
  }

  warn(message: string, data?: any) {
    if (!this.shouldLog('warn')) return

    if (this.config.enableConsole) {
      console.warn(this.formatMessage('warn', message, data))
    }
    
    this.sendToRemote('warn', message, data)
  }

  error(message: string, data?: any) {
    if (!this.shouldLog('error')) return

    if (this.config.enableConsole) {
      console.error(this.formatMessage('error', message, data))
    }
    
    this.sendToRemote('error', message, data)
  }

  // 便捷方法
  logApiCall(method: string, url: string, status: number, duration: number) {
    this.info(`API Call: ${method} ${url}`, {
      status,
      duration: `${duration}ms`
    })
  }

  logUserAction(action: string, details?: any) {
    this.info(`User Action: ${action}`, details)
  }

  logError(error: Error, context?: string) {
    this.error(`${context ? `[${context}] ` : ''}${error.message}`, {
      name: error.name,
      stack: error.stack,
      context
    })
  }
}

// 根据环境创建日志实例
const isDevelopment = import.meta.env.MODE === 'development'

export const logger = new Logger({
  level: isDevelopment ? 'debug' : 'info',
  enableConsole: isDevelopment,
  enableRemote: !isDevelopment,
  remoteUrl: import.meta.env.VITE_LOG_ENDPOINT
})

// 导出便捷方法
export const { debug, info, warn, error } = logger
export default logger 