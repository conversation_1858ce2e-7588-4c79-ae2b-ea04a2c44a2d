#!/usr/bin/env python3
"""
测试结构检测功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.services.document_processor import DocumentProcessor
from app.core.logging import setup_logging

def test_structure_detection():
    """测试结构检测功能"""

    # 设置日志
    setup_logging()

    # 创建文档处理器
    processor = DocumentProcessor()

    # 测试文件路径
    test_file = project_root.parent / "docs" / "test.docx"

    if not test_file.exists():
        print(f"测试文件不存在: {test_file}")
        return

    print(f"开始测试文档: {test_file}")

    try:
        print("正在分析文档结构...")
        # 分析文档结构
        result = processor.analyze_document_structure(
            file_path=str(test_file)
        )
        print("文档结构分析完成")

        print("\n=== 处理结果 ===")
        print(f"分析方法: {result.get('structure_analysis_method', 'unknown')}")

        # 显示检测到的结构
        structures = result.get('document_structures', [])
        print(f"\n=== 检测到的结构 ({len(structures)}个) ===")

        # 按类型分组显示
        standard_structures = [s for s in structures if s.get('type') == 'standard']
        non_standard_structures = [s for s in structures if s.get('type') == 'non_standard']

        print(f"\n📋 标准结构 ({len(standard_structures)}个):")
        for structure in standard_structures:
            name = structure.get('name')
            page = structure.get('page')
            status = structure.get('status')
            content = structure.get('content', {})
            text = content.get('text', '')[:50] if content else ''

            print(f"  ✅ {name} (第{page}页) - {status}")
            if text:
                print(f"     内容: {text}...")

            # 特别显示目录信息
            if name == '目录' and 'toc_entries_count' in content:
                print(f"     TOC条目数量: {content.get('toc_entries_count')}")

        print(f"\n📄 非标准结构 ({len(non_standard_structures)}个):")
        for structure in non_standard_structures[:10]:  # 只显示前10个
            name = structure.get('name')
            page = structure.get('page')
            content = structure.get('content', {})
            text = content.get('text', '')[:50] if content else ''

            print(f"  📝 {name} (第{page}页)")
            if text:
                print(f"     内容: {text}...")

        if len(non_standard_structures) > 10:
            print(f"  ... 还有 {len(non_standard_structures) - 10} 个非标准结构")

        # 显示大纲信息
        outline = result.get('outline', [])
        if outline:
            print(f"\n=== 文档大纲 ({len(outline)}个) ===")
            for item in outline[:5]:  # 只显示前5个
                print(f"- {item.get('text', '')[:50]}...")

        print(f"\n=== 总结 ===")
        print(f"总共检测到 {len(structures)} 个结构")
        print(f"  - 标准结构: {len(standard_structures)} 个")
        print(f"  - 非标准结构: {len(non_standard_structures)} 个")
        print(f"大纲条目: {len(outline)} 个")

        # 检查目录和正文的特殊检测
        toc_structures = [s for s in standard_structures if s.get('name') == '目录']
        main_structures = [s for s in standard_structures if s.get('name') == '正文']

        print(f"\n🎯 特殊检测结果:")
        print(f"  目录检测: {'✅ 成功' if toc_structures else '❌ 未检测到'}")
        if toc_structures:
            toc = toc_structures[0]
            content = toc.get('content', {})
            if 'toc_entries_count' in content:
                print(f"    TOC条目数量: {content.get('toc_entries_count')}")

        print(f"  正文检测: {'✅ 成功' if main_structures else '❌ 未检测到'}")
        if main_structures:
            print(f"    正文页面: {[s.get('page') for s in main_structures]}")
        
    except Exception as e:
        print(f"处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_structure_detection()
