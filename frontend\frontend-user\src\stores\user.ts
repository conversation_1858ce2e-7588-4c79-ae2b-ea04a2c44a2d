import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginRequest, RegisterRequest } from '@/types'
import { authApi } from '@/services'
import websocketService from '@/services/websocketService'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const accessToken = ref<string | null>(localStorage.getItem('access_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!accessToken.value)
  const userRole = computed(() => currentUser.value?.subscription_level || 'free')

  // 登录
  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true
      const response = await authApi.login(credentials)

      accessToken.value = response.access_token
      currentUser.value = response.user
      localStorage.setItem('access_token', response.access_token)

      // 登录成功后建立WebSocket连接
      websocketService.connectAfterLogin(response.access_token)

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    try {
      isLoading.value = true
      const response = await authApi.register(userData)

      accessToken.value = response.access_token
      currentUser.value = response.user
      localStorage.setItem('access_token', response.access_token)

      // 注册成功后建立WebSocket连接
      websocketService.connectAfterLogin(response.access_token)

      return response
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!accessToken.value) return null

    try {
      isLoading.value = true
      const user = await authApi.getCurrentUser()
      currentUser.value = user
      return user
    } catch (error) {
      console.error('Failed to fetch user:', error)
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      const updatedUser = await authApi.updateProfile(profileData)
      currentUser.value = updatedUser
      return updatedUser
    } catch (error) {
      console.error('Failed to update profile:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      currentUser.value = null
      accessToken.value = null
      localStorage.removeItem('access_token')

      // 登出时断开WebSocket连接
      websocketService.disconnect()
    }
  }

  // 初始化用户状态
  const initializeUser = async () => {
    if (accessToken.value) {
      try {
        await fetchCurrentUser()
        // 用户状态恢复成功后，建立WebSocket连接
        websocketService.connectAfterLogin(accessToken.value)
      } catch (error) {
        // 如果token无效，清除状态
        logout()
      }
    }
  }

  return {
    // 状态
    currentUser,
    accessToken,
    isLoading,

    // 计算属性
    isAuthenticated,
    userRole,

    // 方法
    login,
    register,
    fetchCurrentUser,
    updateProfile,
    logout,
    initializeUser,
  }
}) 