"""
论文检测结果数据模型

定义论文检测过程中的结果数据结构
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from pydantic import BaseModel, Field


class CheckSeverity(Enum):
    """检查严重程度"""
    CRITICAL = "critical"   # 严重错误 - 可能导致程序中止或严重功能障碍
    ERROR = "error"         # 错误 - 必须修复
    WARNING = "warning"     # 警告 - 建议修复
    INFO = "info"          # 信息 - 提示性
    SUGGESTION = "suggestion"  # 建议 - 优化建议


class IssueType(Enum):
    """问题类型"""
    FORMAT_ERROR = "format_error"       # 格式错误
    STRUCTURE_ERROR = "structure_error" # 结构错误
    CONTENT_ERROR = "content_error"     # 内容错误
    STYLE_ERROR = "style_error"         # 样式错误
    REFERENCE_ERROR = "reference_error" # 引用错误
    LAYOUT_ERROR = "layout_error"       # 布局错误


@dataclass
class CheckIssue:
    """检查问题数据类"""
    issue_id: str                          # 问题唯一标识
    issue_type: IssueType                  # 问题类型
    severity: CheckSeverity                # 严重程度
    title: str                             # 问题标题
    description: str                       # 问题描述
    location: Optional[str] = None         # 问题位置
    line_number: Optional[int] = None      # 行号
    column_number: Optional[int] = None    # 列号
    element_id: Optional[str] = None       # 元素ID
    suggestions: List[str] = field(default_factory=list)  # 修复建议
    context: Dict[str, Any] = field(default_factory=dict) # 上下文信息
    metadata: Dict[str, Any] = field(default_factory=dict) # 元数据
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "issue_id": self.issue_id,
            "issue_type": self.issue_type.value,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "location": self.location,
            "line_number": self.line_number,
            "column_number": self.column_number,
            "element_id": self.element_id,
            "suggestions": self.suggestions,
            "context": self.context,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat()
        }


@dataclass
class CheckResult:
    """检查结果数据类"""
    rule_id: str                           # 规则ID
    rule_name: str                         # 规则名称
    passed: bool                           # 是否通过
    severity: CheckSeverity                # 严重程度
    message: str                           # 结果消息
    details: Dict[str, Any] = field(default_factory=dict)  # 详细信息
    position: Optional[int] = None         # 问题位置
    suggestions: List[str] = field(default_factory=list)   # 修复建议
    issues: List[CheckIssue] = field(default_factory=list) # 相关问题
    metadata: Dict[str, Any] = field(default_factory=dict) # 元数据
    execution_time: float = 0.0            # 执行时间
    timestamp: datetime = field(default_factory=datetime.now)  # 时间戳
    
    def add_issue(self, issue: CheckIssue):
        """添加问题"""
        self.issues.append(issue)
        if not self.passed and issue.severity in [CheckSeverity.ERROR, CheckSeverity.WARNING]:
            self.passed = False
    
    def get_issues_by_severity(self, severity: CheckSeverity) -> List[CheckIssue]:
        """根据严重程度获取问题"""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_error_count(self) -> int:
        """获取错误数量"""
        return len(self.get_issues_by_severity(CheckSeverity.ERROR))
    
    def get_warning_count(self) -> int:
        """获取警告数量"""
        return len(self.get_issues_by_severity(CheckSeverity.WARNING))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "passed": self.passed,
            "severity": self.severity.value,
            "message": self.message,
            "details": self.details,
            "position": self.position,
            "suggestions": self.suggestions,
            "issues": [issue.to_dict() for issue in self.issues],
            "metadata": self.metadata,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp.isoformat(),
            "error_count": self.get_error_count(),
            "warning_count": self.get_warning_count()
        }


# Pydantic 模型用于API交互
class CheckIssueModel(BaseModel):
    """检查问题Pydantic模型"""
    issue_id: str = Field(..., description="问题唯一标识")
    issue_type: str = Field(..., description="问题类型")
    severity: str = Field(..., description="严重程度")
    title: str = Field(..., description="问题标题")
    description: str = Field(..., description="问题描述")
    location: Optional[str] = Field(None, description="问题位置")
    line_number: Optional[int] = Field(None, description="行号")
    column_number: Optional[int] = Field(None, description="列号")
    element_id: Optional[str] = Field(None, description="元素ID")
    suggestions: List[str] = Field(default_factory=list, description="修复建议")
    context: Dict[str, Any] = Field(default_factory=dict, description="上下文信息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class CheckResultModel(BaseModel):
    """检查结果Pydantic模型"""
    rule_id: str = Field(..., description="规则ID")
    rule_name: str = Field(..., description="规则名称")
    passed: bool = Field(..., description="是否通过")
    severity: str = Field(..., description="严重程度")
    message: str = Field(..., description="结果消息")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")
    position: Optional[int] = Field(None, description="问题位置")
    suggestions: List[str] = Field(default_factory=list, description="修复建议")
    issues: List[CheckIssueModel] = Field(default_factory=list, description="相关问题")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    execution_time: float = Field(0.0, description="执行时间")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    error_count: int = Field(0, description="错误数量")
    warning_count: int = Field(0, description="警告数量")


class PaperCheckReport(BaseModel):
    """论文检测报告"""
    document_id: str = Field(..., description="文档ID")
    document_title: str = Field(..., description="文档标题")
    check_results: List[CheckResultModel] = Field(..., description="检查结果列表")
    overall_score: float = Field(..., description="总体得分")
    total_issues: int = Field(..., description="问题总数")
    error_count: int = Field(..., description="错误数量")
    warning_count: int = Field(..., description="警告数量")
    info_count: int = Field(..., description="信息数量")
    suggestion_count: int = Field(..., description="建议数量")
    check_duration: float = Field(..., description="检查耗时")
    check_timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    recommendations: List[str] = Field(default_factory=list, description="改进建议")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# 辅助函数
def create_check_issue(
    issue_id: str,
    issue_type: IssueType,
    severity: CheckSeverity,
    title: str,
    description: str,
    **kwargs
) -> CheckIssue:
    """创建检查问题实例"""
    return CheckIssue(
        issue_id=issue_id,
        issue_type=issue_type,
        severity=severity,
        title=title,
        description=description,
        **kwargs
    )


def create_check_result(
    rule_id: str,
    rule_name: str,
    passed: bool,
    severity: CheckSeverity,
    message: str,
    **kwargs
) -> CheckResult:
    """创建检查结果实例"""
    return CheckResult(
        rule_id=rule_id,
        rule_name=rule_name,
        passed=passed,
        severity=severity,
        message=message,
        **kwargs
    ) 