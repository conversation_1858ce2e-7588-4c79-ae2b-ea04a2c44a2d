"""
Word文档分析服务 - 任务管理器
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from app.core.logging import logger
from app.database import crud
from app.database.session import session_manager
from app.models import TaskUpdate, TaskStatus, TaskType
from app.core.exceptions import TaskException
from app.tasks.queue import get_task_queue, TaskPriority
from app.tasks.worker import get_worker_pool
from app.tasks.progress import get_progress_tracker
# 移除无用的concurrency导入


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.is_initialized = False
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.max_concurrent_tasks = 5
        self.worker_tasks: List[asyncio.Task] = []
        self._shutdown_event = asyncio.Event()
        self.queue_lock = asyncio.Lock()  # Lock for safe queue manipulation
    
    async def initialize(self):
        """初始化任务管理器"""
        logger.info("正在初始化任务管理器...")
        
        # 启动工作线程
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)
        
        # 恢复未完成的任务
        await self._recover_pending_tasks()
        
        self.is_initialized = True
        logger.info(f"任务管理器初始化完成，启动了 {len(self.worker_tasks)} 个工作线程")
    
    async def shutdown(self):
        """关闭任务管理器"""
        logger.info("正在关闭任务管理器...")
        
        # 设置关闭标志
        self._shutdown_event.set()
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                logger.info(f"取消运行中的任务: {task_id}")
        
        # 等待所有工作线程完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        self.is_initialized = False
        logger.info("任务管理器已关闭")
    
    async def create_task(self, task_type: str, file_path: str, **kwargs):
        """创建新任务"""
        try:
            from app.models.task import TaskCreate, TaskType
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建任务数据
            task_data = TaskCreate(
                task_id=task_id,
                task_type=TaskType(task_type),
                file_path=file_path,
                status=TaskStatus.PENDING,
                created_at=datetime.now(),
                **kwargs
            )
            
            # 保存到数据库
            task = await session_manager.execute_crud_operation(crud.create_task, task_data)
            
            # 添加到处理队列
            await self.process_task(task_id)
            
            logger.info(f"任务创建成功: {task_id}")
            return task
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            raise TaskException(f"创建任务失败: {str(e)}")

    async def process_task(self, task_id: str):
        """处理任务（添加到队列）"""
        try:
            # 验证任务是否存在
            task = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 添加到队列
            await self.task_queue.put(task_id)
            logger.info(f"任务已添加到队列: {task_id}")
            
        except Exception as e:
            logger.error(f"添加任务到队列失败: {task_id} - {str(e)}")
    
    async def cancel_task(self, task_id: str):
        """
        取消任务

        - 如果任务正在运行，则取消其asyncio.Task。
        - 如果任务处于PENDING状态，则从队列中安全地移除。
        - 最后将数据库中的状态更新为CANCELLED。
        """
        try:
            # 检查任务是否在运行中
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                if not task.done():
                    task.cancel()
                    logger.info(f"已取消运行中的任务: {task_id}")
            
            # 检查数据库中的任务状态
            task_in_db = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task_in_db:
                logger.warning(f"尝试取消一个不存在于数据库的任务: {task_id}")
                return

            # 如果任务是PENDING，则尝试从队列中移除
            if task_in_db.status == TaskStatus.PENDING:
                async with self.queue_lock:
                    # 从队列中安全地移除任务
                    items_in_queue = []
                    while not self.task_queue.empty():
                        items_in_queue.append(self.task_queue.get_nowait())

                    task_removed = False
                    new_queue_items = []
                    for item in items_in_queue:
                        if item == task_id:
                            task_removed = True
                        else:
                            new_queue_items.append(item)
                    
                    for item in new_queue_items:
                        await self.task_queue.put(item)

                    if task_removed:
                        logger.info(f"已从队列中移除 PENDING 任务: {task_id}")
                    else:
                        logger.warning(f"任务 {task_id} 状态为 PENDING 但不在队列中，可能已被领取")

            # 统一更新数据库状态
            task_update = TaskUpdate(
                status=TaskStatus.CANCELLED,
                completed_at=datetime.now(),
                updated_at=datetime.now(),
                error_message="任务被用户手动取消"
            )
            await session_manager.execute_crud_operation(crud.update_task, task_id, task_update)
            logger.info(f"任务 {task_id} 状态已更新为 CANCELLED")

        except Exception as e:
            logger.error(f"取消任务失败: {task_id} - {str(e)}")
            raise TaskException(f"取消任务时发生严重错误: {task_id}") from e
    
    async def get_queue_status(self):
        """获取队列状态"""
        return {
            "queue_size": self.task_queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "worker_count": len(self.worker_tasks),
            "max_concurrent": self.max_concurrent_tasks
        }
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程启动: {worker_name}")
        
        while not self._shutdown_event.is_set():
            try:
                # 从队列获取任务（带超时）
                task_id = await asyncio.wait_for(
                    self.task_queue.get(),
                    timeout=1.0
                )
                
                # 处理任务
                await self._execute_task(task_id, worker_name)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except asyncio.CancelledError:
                # 工作线程被取消
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 发生错误: {str(e)}")
        
        logger.info(f"工作线程停止: {worker_name}")
    
    async def _execute_task(self, task_id: str, worker_name: str):
        """执行具体任务"""
        try:
            # 获取任务信息
            task = await session_manager.execute_crud_operation(crud.get_task, task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 检查任务状态
            if task.status != TaskStatus.PENDING:
                logger.warning(f"任务状态不是 pending: {task_id} - {task.status}")
                return
            
            logger.info(f"开始执行任务: {task_id} (工作线程: {worker_name})")
            
            # 更新任务状态为处理中
            await session_manager.execute_crud_operation(crud.update_task, task_id, TaskUpdate(
                status=TaskStatus.PROCESSING,
                updated_at=datetime.now()
            ))
            
            # 创建任务协程
            task_coroutine = self._process_task_by_type(task)
            task_future = asyncio.create_task(task_coroutine)
            
            # 记录运行中的任务
            self.running_tasks[task_id] = task_future
            
            try:
                # 执行任务
                result = await task_future

                # 注意：任务状态的更新现在由progress_tracker.complete_task()负责
                # 这样可以确保进度跟踪和数据库状态的一致性

                # 创建文档记录
                await self._create_document_record(task, result)
                
                logger.info(f"任务执行成功: {task_id}")
                
            except asyncio.CancelledError:
                # 任务被取消
                await session_manager.execute_crud_operation(crud.update_task, task_id, TaskUpdate(
                    status=TaskStatus.CANCELLED,
                    completed_at=datetime.now(),
                    updated_at=datetime.now()
                ))
                logger.info(f"任务被取消: {task_id}")
                
            except Exception as e:
                # 任务执行失败
                # 注意：任务失败状态的更新现在由progress_tracker.fail_task()负责
                # 这样可以确保进度跟踪和数据库状态的一致性
                logger.error(f"任务执行失败: {task_id} - {str(e)}")

                # 确保进度跟踪器也知道任务失败了
                progress_tracker = get_progress_tracker()
                await progress_tracker.fail_task(task_id, str(e))
            
            finally:
                # 从运行中任务列表移除
                self.running_tasks.pop(task_id, None)
                
        except Exception as e:
            logger.error(f"执行任务时发生错误: {task_id} - {str(e)}")
    
    async def _create_document_record(self, task, result):
        """创建文档记录"""
        try:
            from app.models.document import DocumentCreate
            
            # 从任务结果中提取文档信息
            analysis_result = result.get('analysis_result', {}) if isinstance(result, dict) else {}
            content_stats = analysis_result.get('content_stats', {}) if isinstance(analysis_result, dict) else {}
            
            # 创建文档记录
            document_data = DocumentCreate(
                document_id=task.task_id,  # 使用任务ID作为文档ID
                task_id=task.task_id,
                filename=task.filename,
                file_size=task.file_size,
                created_date=task.created_at,
                # 从分析结果中提取统计信息
                page_count=content_stats.get('page_count', 0),
                word_count=content_stats.get('word_count', 0),
                paragraph_count=content_stats.get('paragraph_count', 0),
                image_count=content_stats.get('image_count', 0),
                table_count=content_stats.get('table_count', 0)
            )
            
            # 保存文档记录到数据库
            document = await session_manager.execute_crud_operation(crud.create_document, document_data)
            logger.info(f"文档记录创建成功: {task.task_id} - {task.filename}")
            
        except Exception as e:
            logger.error(f"创建文档记录失败: {task.task_id} - {str(e)}")
            # 不要抛出异常，因为任务已经成功完成，只是文档记录创建失败

    def _convert_to_check_results(self, check_result):
        """将检测结果转换为CheckResult对象列表"""
        from app.models.check_result import CheckResult, CheckIssue, CheckSeverity

        check_results = []

        # 🔥 修复：处理列表格式的检测结果
        if isinstance(check_result, list):
            # 直接处理列表中的每个结果
            for result_item in check_result:
                if isinstance(result_item, dict):
                    # 创建CheckResult对象
                    severity = self._map_problem_severity(result_item.get("severity", "warning"))

                    check_result_obj = CheckResult(
                        rule_id=result_item.get("rule_id", "unknown"),
                        rule_name=result_item.get("rule_name", "未知规则"),
                        passed=result_item.get("passed", False),
                        severity=severity,
                        message=result_item.get("message", ""),
                        details=result_item.get("details", {}),
                        position=result_item.get("position"),
                        metadata=result_item.get("metadata", {})
                    )

                    # 如果有具体的issues，添加到CheckResult中
                    if "issues" in result_item:
                        for issue_data in result_item["issues"]:
                            issue = CheckIssue(
                                issue_type=issue_data.get("type", "format"),
                                severity=self._map_problem_severity(issue_data.get("severity", "warning")),
                                title=issue_data.get("title", ""),
                                description=issue_data.get("description", ""),
                                location=issue_data.get("location", ""),
                                line_number=issue_data.get("line_number"),
                                element_id=issue_data.get("element_id"),
                                suggestions=issue_data.get("suggestions", []),
                                metadata=issue_data.get("metadata", {})
                            )
                            check_result_obj.issues.append(issue)

                    check_results.append(check_result_obj)

        elif isinstance(check_result, dict):
            # 处理字典格式（原有逻辑）
            problems = check_result.get("problems", [])

            for problem in problems:
                # 创建CheckResult对象
                severity = self._map_problem_severity(problem.get("severity", "warning"))

                check_result_obj = CheckResult(
                    rule_id=problem.get("rule_id", "unknown"),
                    rule_name=problem.get("rule_name", "未知规则"),
                    passed=False,
                    severity=severity,
                    message=problem.get("message", ""),
                    details=problem.get("details", {}),
                    position=problem.get("position"),
                    metadata=problem.get("metadata", {})
                )

                # 如果有具体的issues，添加到CheckResult中
                if "issues" in problem:
                    for issue_data in problem["issues"]:
                        issue = CheckIssue(
                            issue_type=issue_data.get("type", "format"),
                            severity=self._map_problem_severity(issue_data.get("severity", "warning")),
                            title=issue_data.get("title", ""),
                            description=issue_data.get("description", ""),
                            location=issue_data.get("location", ""),
                            line_number=issue_data.get("line_number"),
                            element_id=issue_data.get("element_id"),
                            suggestions=issue_data.get("suggestions", []),
                            metadata=issue_data.get("metadata", {})
                        )
                        check_result_obj.issues.append(issue)

                check_results.append(check_result_obj)

        logger.info(f"🔍 转换了 {len(check_results)} 个检测结果为CheckResult对象")
        return check_results

    def _map_problem_severity(self, severity_str: str):
        """映射问题严重程度"""
        from app.models.check_result import CheckSeverity

        severity_mapping = {
            "error": CheckSeverity.ERROR,
            "critical": CheckSeverity.CRITICAL,
            "warning": CheckSeverity.WARNING,
            "info": CheckSeverity.INFO,
            "severe": CheckSeverity.ERROR,
            "general": CheckSeverity.WARNING,
            "suggestion": CheckSeverity.INFO
        }

        return severity_mapping.get(severity_str.lower(), CheckSeverity.WARNING)
    
    async def _process_task_by_type(self, task):
        """根据任务类型处理任务"""
        try:
            if task.task_type == TaskType.PAPER_CHECK:
                return await self._process_paper_check_task(task)
            elif task.task_type == TaskType.CONTENT_ANALYSIS:
                return await self._process_content_analysis_task(task)
            elif task.task_type == TaskType.FORMAT_CHECK:
                return await self._process_format_check_task(task)
            else:
                raise TaskException(f"不支持的任务类型: {task.task_type}")
                
        except Exception as e:
            logger.error(f"处理任务失败: {task.task_id} - {str(e)}")
            raise
    
    async def _process_paper_check_task(self, task):
        """处理论文检测任务"""
        logger.info(f"开始论文检测: {task.task_id}")
        
        # 获取进度跟踪器
        progress_tracker = await get_progress_tracker()
        
        # 定义任务步骤（6步骤架构）
        steps = [
            "文档验证",
            "原始数据提取",
            "数据预处理",
            "结构分析",
            "格式规则检测",
            "生成分析报告"
        ]
        
        # 初始化进度跟踪
        await progress_tracker.start_task(
            task.task_id, 
            "paper_check", 
            steps
        )
        
        # 实现实际的论文检测逻辑
        import time
        task_start_time = time.time()
        try:
            # 🔥 新增：解析检测标准选项
            detection_standard = None
            if hasattr(task, 'analysis_options') and task.analysis_options:
                detection_standard = task.analysis_options.get('detection_standard')
                logger.info(f"检测标准: {detection_standard}")
            
            # ========== 步骤1: 文档上传验证 ==========
            await progress_tracker.update_progress(task.task_id, 10, "step_1", "验证文档格式和完整性")
            
            # 验证文件是否存在和可读
            from pathlib import Path
            if not Path(task.file_path).exists():
                raise TaskException(f"文档文件不存在: {task.file_path}")
            
            await asyncio.sleep(0.5)  # 模拟文件验证时间
            await progress_tracker.complete_step(task.task_id, "step_1", "文档验证完成")
            
            # ========== 步骤2: 原始数据提取（仅使用Word COM） ==========
            await progress_tracker.update_progress(task.task_id, 15, "step_2", "正在提取文档原始数据")

            from app.services.document_processor import DocumentProcessor
            processor = DocumentProcessor()

            # 🔥 优化：纯粹的数据提取，不进行任何分析
            # 注意：不传递进度回调，因为线程池中无法调用异步函数
            raw_data = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: processor.extract_raw_document_data(task.file_path, None)
            )

            # 检查提取是否成功
            if not raw_data or not raw_data.get('success', False):
                error_msg = raw_data.get('error_message', '未知错误') if raw_data else '数据提取失败'
                await progress_tracker.fail_task(task.task_id, f"原始数据提取失败: {error_msg}", "step_2")
                raise TaskException(f"原始数据提取失败: {error_msg}")

            await progress_tracker.update_progress(task.task_id, 35, "step_2", "原始数据提取完成")
            await progress_tracker.complete_step(task.task_id, "step_2", "数据提取成功")

            # ========== 步骤3: 数据预处理 ==========
            await progress_tracker.update_progress(task.task_id, 40, "step_3", "正在预处理文档数据")

            from app.services.data_processor import DataProcessor
            data_processor = DataProcessor()

            # 数据预处理（不使用Word COM）
            processed_data = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: data_processor.preprocess_document_data(raw_data)
            )

            await progress_tracker.update_progress(task.task_id, 50, "step_3", "数据预处理完成")
            await progress_tracker.complete_step(task.task_id, "step_3", "数据预处理成功")

            # ========== 步骤4: 结构分析 ==========
            await progress_tracker.update_progress(task.task_id, 55, "step_4", "正在分析文档结构")

            from app.services.structure_analyzer import StructureAnalyzer
            structure_analyzer = StructureAnalyzer()

            # 结构分析（基于预处理数据）
            structure_result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: structure_analyzer.analyze_document_structure(processed_data)
            )

            await progress_tracker.update_progress(task.task_id, 70, "step_4", "文档结构分析完成")
            await progress_tracker.complete_step(task.task_id, "step_4", "结构分析成功")
            
            # ========== 步骤5: 格式规则检测 ==========
            await progress_tracker.update_progress(task.task_id, 75, "step_5", "正在执行格式规则检测")
            
            # 根据检测标准选择规则文件
            rule_file_path = "config/rules/hbkj_bachelor_2024.json"  # 默认规则文件
            if detection_standard:
                # 如果指定了检测标准，使用特定的规则文件
                if detection_standard == 'hbkj_bachelor_2024':
                    rule_file_path = "config/rules/hbkj_bachelor_2024.json"
                elif detection_standard == 'gbt_7713_1_2006':
                    # GB/T 7713.1-2006: 学位论文编写规则
                    rule_file_path = "config/rules/gbt_7713_1_2006.json"
                elif detection_standard == 'gbt_7714_2015':
                    # GB/T 7714-2015: 参考文献著录规则
                    rule_file_path = "config/rules/gbt_7714_2015.json"
                else:
                    logger.warning(f"未知的检测标准: {detection_standard}，使用默认规则")

            await progress_tracker.update_progress(task.task_id, 80, "step_5", "加载检测规则")

            # 创建规则引擎并加载规则
            from app.checkers.rule_engine import RuleEngine
            from app.services.document_analyzer import CHECK_FUNCTIONS
            rule_engine = RuleEngine(CHECK_FUNCTIONS)

            # 加载规则文件
            rule_engine.load_rules_from_file(rule_file_path)

            await progress_tracker.update_progress(task.task_id, 85, "step_5", "执行格式检测规则")

            # 创建DocumentData对象传递给规则引擎（基于预处理数据）
            from app.services.document_processor import DocumentData

            # 🔥 修复：正确构建elements数据，确保包含style字段
            paragraphs = processed_data.get('document_content', {}).get('paragraphs', [])
            elements = []

            for para in paragraphs:
                # 构建符合检查函数期望的element结构
                element = {
                    "type": "paragraph",
                    "text": para.get('text', ''),
                    "style": {
                        "alignment": para.get('alignment', 'left'),
                        "font_size": para.get('font_size'),
                        "font_family": para.get('font_name', ''),
                        "bold": para.get('is_bold', False)
                    },
                    "alignment": para.get('alignment', 'left'),  # 兼容性字段
                    "font_size": para.get('font_size'),
                    "is_bold": para.get('is_bold', False)
                }
                elements.append(element)

            document_data = DocumentData(
                file_path=task.file_path,
                doc_info=processed_data.get('document_info', {}),
                content_stats=structure_result,
                elements=elements,
                paragraphs=paragraphs,
                tables=processed_data.get('document_content', {}).get('tables', []),
                images=processed_data.get('document_content', {}).get('images', [])
            )

            # 执行论文格式检查
            check_result = await rule_engine.execute_check(document_data)

            # 🔥 调试：检查检测结果
            logger.info(f"🔍 检测结果类型: {type(check_result)}")
            if isinstance(check_result, dict):
                logger.info(f"🔍 检测结果键: {list(check_result.keys())}")
                if "problems" in check_result:
                    problems = check_result["problems"]
                    logger.info(f"🔍 发现问题数量: {len(problems)}")
                    for i, problem in enumerate(problems[:3]):
                        logger.info(f"🔍 问题 {i+1}: {problem}")
                else:
                    logger.info("🔍 检测结果中没有 'problems' 键")
            else:
                logger.info(f"🔍 检测结果内容: {check_result}")

            await progress_tracker.complete_step(task.task_id, "step_5", "格式检测完成")

            # ========== 步骤6: 生成分析报告 ==========
            await progress_tracker.update_progress(task.task_id, 90, "step_6", "正在生成分析报告")

            # 🔥 新架构：基于所有分析结果生成最终报告
            final_analysis = self._build_final_analysis_result(
                raw_data, processed_data, structure_result, check_result
            )

            # 🔥 新增：生成问题片段
            await progress_tracker.update_progress(task.task_id, 95, "step_6", "正在生成问题片段")

            # 生成问题片段
            from app.services.problem_fragment_service import ProblemFragmentService
            fragment_service = ProblemFragmentService()

            # 将检测结果转换为CheckResult对象列表
            check_results = self._convert_to_check_results(check_result)

            # 生成问题片段（暂时不存储到数据库，在API调用时再存储）
            try:
                logger.info(f"🔍 开始生成问题片段，检测结果数量: {len(check_results)}")
                logger.info(f"🔍 文档数据元素数量: {len(document_data.elements)}")
                logger.info(f"🔍 文档数据段落数量: {len(document_data.paragraphs)}")

                fragments = fragment_service.fragment_generator.generate_fragments_from_results(
                    check_results, document_data
                )
                fragment_dicts = [fragment.to_dict() for fragment in fragments]

                logger.info(f"🔍 生成的问题片段数量: {len(fragment_dicts)}")

                # 将问题片段添加到最终结果中
                final_analysis['problem_fragments'] = fragment_dicts
                final_analysis['fragment_count'] = len(fragment_dicts)
                final_analysis['severe_count'] = len([f for f in fragment_dicts if f.get("severity") == "severe"])

                logger.info(f"✅ 生成了 {len(fragment_dicts)} 个问题片段，其中严重问题 {final_analysis['severe_count']} 个")

                # 🔍 调试：显示前几个问题片段
                for i, fragment in enumerate(fragment_dicts[:3]):
                    logger.info(f"🔍 问题片段 {i+1}: {fragment.get('fragment_id')} - {fragment.get('structure')} - {fragment.get('severity')}")

            except Exception as e:
                logger.error(f"❌ 生成问题片段失败: {str(e)}")
                import traceback
                logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
                # 不影响主流程，继续执行
                final_analysis['problem_fragments'] = []
                final_analysis['fragment_count'] = 0
                final_analysis['severe_count'] = 0

            # 🔥 关键修复：将封面页信息添加到document_info中（从原始数据中提取）
            cover_page_info = self._extract_cover_page_info_from_raw_data(raw_data)
            if 'document_info' not in final_analysis:
                final_analysis['document_info'] = {}
            final_analysis['document_info']['cover_page_info'] = cover_page_info
            
            await asyncio.sleep(0.3)  # 模拟报告生成时间
            
            await progress_tracker.update_progress(task.task_id, 100, "step_4", "报告生成完成")
            await progress_tracker.complete_step(task.task_id, "step_4", "分析报告已生成")
            
            # 🔥 修复：根据detection_standard从config.py获取正确的标准名称
            from app.core.config import DETECTION_STANDARD_NAMES
            standard_name = DETECTION_STANDARD_NAMES.get(detection_standard, '论文检测')

            # 完成任务
            # 🔥 新架构：从最终分析结果中提取结构数据
            document_structures = final_analysis.get('document_structures', [])
            outline = final_analysis.get('outline', [])

            logger.info(f"🔥 最终结构数据: document_structures={len(document_structures)}个, outline={len(outline)}个")

            # 🔥 进一步优化：扁平化API结构，减少嵌套和重复
            result = {
                "task_type": "paper_check",
                "status": "completed",
                "compliance_score": check_result.get("compliance_score", 85.0) if isinstance(check_result, dict) else 85.0,
                "problems_found": len(check_result.get("problems", [])) if isinstance(check_result, dict) else 0,
                "processing_time": time.time() - task_start_time,

                # 🔥 优化：将content_stats提升到顶级，与document_structures同级
                "content_stats": final_analysis.get('statistics', {}),

                # 🔥 关键：直接在任务结果中包含结构数据
                "document_structures": document_structures,
                "outline": outline,

                # 🔥 优化：精简的检测结果摘要
                "check_summary": {
                    "compliance_score": check_result.get("compliance_score", 85.0) if isinstance(check_result, dict) else 85.0,
                    "total_problems": len(check_result.get("problems", [])) if isinstance(check_result, dict) else 0,
                    "major_problems": len([p for p in check_result.get("problems", []) if p.get("severity") == "error"]) if isinstance(check_result, dict) else 0,
                    "minor_problems": len([p for p in check_result.get("problems", []) if p.get("severity") == "warning"]) if isinstance(check_result, dict) else 0
                },

                # 🔥 优化：只保留一个standard_name，移除重复
                "detection_standard": detection_standard,
                "standard_name": standard_name,

                # 🔥 优化：精简的文档基本信息
                "document_info": {
                    "pages": final_analysis.get('statistics', {}).get('page_count', 0),
                    "words": final_analysis.get('statistics', {}).get('word_count', 0),
                    "title": final_analysis.get('document_info', {}).get('cover_page_info', {}).get('title', ''),
                    "author": final_analysis.get('document_info', {}).get('cover_page_info', {}).get('author', ''),
                    "cover_page_info": final_analysis.get('document_info', {}).get('cover_page_info', {})
                },

                # 🔥 优化：精简的分析摘要（移除analysis_result包装）
                "analysis_summary": final_analysis.get('analysis_summary', {}),

                # 🔥 优化：处理元信息
                "processing_meta": final_analysis.get('meta', {}),

                # 🔥 新增：包含检测结果和问题片段
                "check_result": final_analysis.get('check_result', {}),
                "problem_fragments": final_analysis.get('problem_fragments', []),
                "fragment_count": final_analysis.get('fragment_count', 0),
                "severe_count": final_analysis.get('severe_count', 0)
            }
            
            # 🔥 调试：添加详细的日志记录
            logger.info(f"准备完成任务 {task.task_id}")
            logger.info(f"结果类型: {type(result)}")
            logger.info(f"结果大小: {len(str(result))} 字符")
            logger.info(f"document_info存在: {'document_info' in result}")
            logger.info(f"document_info内容: {result.get('document_info', {})}")

            # 检查封面页信息
            cover_info = result.get('document_info', {}).get('cover_page_info', {})
            if cover_info:
                logger.info(f"封面页信息已提取: {list(cover_info.keys())}")
                for key, value in cover_info.items():
                    if key != 'raw_text':  # 跳过原始文本，太长了
                        logger.info(f"  {key}: {value}")
            else:
                logger.warning("封面页信息为空或不存在")

            # 🔥 新增：保存结构统计和内容缓存
            await self._save_document_structure_stats(task, result, raw_data)

            await progress_tracker.complete_task(task.task_id, "论文检测任务完成", result)
            
        except Exception as e:
            logger.error(f"论文检测执行失败: {str(e)}")
            
            # 标记任务失败
            await progress_tracker.fail_task(task.task_id, str(e))
            
            result = {
                "task_type": "paper_check",
                "status": "failed",
                "error": str(e),
                "processing_time": 2.0,
                "detection_standard": detection_standard,
                # 🔥 修复：失败情况下也使用扁平化结构
                "content_stats": {
                    "page_count": 0,
                    "word_count": 0,
                    "paragraph_count": 0,
                    "image_count": 0,
                    "table_count": 0,
                    "formula_count": 0,
                    "reference_count": 0,
                    "footnote_count": 0
                },
                "document_structures": [],
                # 🔥 优化：移除outline，统一使用document_structures
                "document_info": {},
                "check_summary": {},
                "analysis_summary": {},
                "processing_meta": {}
            }
        
        logger.info(f"论文检测完成: {task.task_id}")
        return result
    
    async def _process_content_analysis_task(self, task):
        """处理内容分析任务"""
        logger.info(f"开始内容分析: {task.task_id}")
        
        # 模拟处理过程
        await asyncio.sleep(1.5)
        
        # 实现实际的内容分析逻辑
        try:
            from app.services.document_analyzer import DocumentAnalyzer
            from app.services.image_processor import get_image_processor
            
            # 1. 解析文档结构和内容
            analyzer = DocumentAnalyzer()
            analysis_result = await analyzer.analyze_document(task.file_path)

            # 🔥 关键修复：检查分析是否成功
            if not analysis_result.success:
                raise TaskException(f"文档分析核心模块失败: {analysis_result.error_message}")
            
            # 2. 提取图片信息
            image_processor = get_image_processor()
            image_result = image_processor.extract_images_from_document(task.file_path, task.task_id)
            
            # 3. 转换分析结果并生成内容摘要
            converted_analysis = self._convert_analysis_result_to_dict(analysis_result)
            content_stats = converted_analysis.get("analysis_result", {}).get("content_stats", {})
            
            result = {
                "task_type": "content_analysis",
                "status": "completed",
                "word_count": content_stats.get("word_count", 0),
                "paragraph_count": content_stats.get("paragraph_count", 0),
                "image_count": len(image_result.get("images", [])) if isinstance(image_result, dict) else 0,
                "table_count": content_stats.get("table_count", 0),
                "processing_time": 1.5,
                "analysis_result": converted_analysis,
                "image_result": image_result
            }
            
        except Exception as e:
            logger.error(f"内容分析执行失败: {str(e)}")
            result = {
                "task_type": "content_analysis",
                "status": "failed",
                "error": str(e),
                "processing_time": 1.5,
                "analysis_result": {
                    "content_stats": {
                        "page_count": 0,
                        "word_count": 0,
                        "paragraph_count": 0,
                        "image_count": 0,
                        "table_count": 0
                    }
                }
            }
        
        logger.info(f"内容分析完成: {task.task_id}")
        return result
    
    async def _process_format_check_task(self, task):
        """处理格式检查任务"""
        logger.info(f"开始格式检查: {task.task_id}")
        
        # 模拟处理过程
        await asyncio.sleep(1.0)
        
        # 实现实际的格式检查逻辑
        try:
            from app.services.document_analyzer import DocumentAnalyzer
            from app.checkers.format_checker import get_format_checker
            
            # 1. 解析文档格式信息
            analyzer = DocumentAnalyzer()
            analysis_result = await analyzer.analyze_document(task.file_path)

            # 🔥 关键修复：检查分析是否成功
            if not analysis_result.success:
                raise TaskException(f"文档分析核心模块失败: {analysis_result.error_message}")
            
            # 2. 执行格式检查
            format_checker = get_format_checker()
            format_result = await format_checker.check_document_format(analysis_result)
            
            # 3. 转换分析结果为前端期望的格式
            converted_analysis = self._convert_analysis_result_to_dict(analysis_result)
            
            result = {
                "task_type": "format_check",
                "status": "completed",
                "format_score": format_result.get("format_score", 90.0) if isinstance(format_result, dict) else 90.0,
                "format_issues": len(format_result.get("issues", [])) if isinstance(format_result, dict) else 0,
                "processing_time": 1.0,
                "analysis_result": converted_analysis,
                "format_result": format_result
            }
            
        except Exception as e:
            logger.error(f"格式检查执行失败: {str(e)}")
            result = {
                "task_type": "format_check",
                "status": "failed", 
                "error": str(e),
                "processing_time": 1.0,
                "analysis_result": {
                    "content_stats": {
                        "page_count": 0,
                        "word_count": 0,
                        "paragraph_count": 0,
                        "image_count": 0,
                        "table_count": 0
                    }
                }
            }
        
        logger.info(f"格式检查完成: {task.task_id}")
        return result
    
    async def _recover_pending_tasks(self):
        """恢复未完成的任务"""
        try:
            # 获取所有待处理的任务
            pending_tasks = await session_manager.execute_crud_operation(crud.get_tasks, 0, 100, TaskStatus.PENDING)
            
            if pending_tasks:
                logger.info(f"发现 {len(pending_tasks)} 个待处理任务，正在恢复...")
                
                for task in pending_tasks:
                    await self.process_task(task.task_id)
                    
                logger.info(f"已恢复 {len(pending_tasks)} 个待处理任务")
            else:
                logger.info("没有待处理的任务需要恢复")
                
        except Exception as e:
            logger.error(f"恢复待处理任务失败: {str(e)}")

    def _convert_analysis_result_to_dict(self, analysis_result) -> Dict[str, Any]:
        """
        将AnalysisResult dataclass转换为前端期望的字典格式
        
        Args:
            analysis_result: AnalysisResult对象或数据库中存储的字典
            
        Returns:
            Dict[str, Any]: 转换后的字典格式，包含前端需要的路径结构
        """
        if analysis_result is None:
            return {}
        
        # 🔥 关键修复：处理数据库中存储的字典格式
        if isinstance(analysis_result, dict):
            # 检查是否已经是前端期望的格式
            if 'analysis_result' in analysis_result and 'content_stats' in analysis_result.get('analysis_result', {}):
                logger.info("数据已经是前端期望的格式，直接返回")
                return analysis_result
            
            # 如果不是，尝试从字典中构建前端期望的格式
            return self._convert_dict_to_frontend_format(analysis_result)
        
        # 如果是AnalysisResult dataclass
        if hasattr(analysis_result, 'success'):
            result_dict = {
                "success": analysis_result.success,
                "processing_time": getattr(analysis_result, 'processing_time', 0),
                "error_message": getattr(analysis_result, 'error_message', None),
                "warnings": getattr(analysis_result, 'warnings', [])
            }
            
            # 添加document_info（确保可序列化）
            if hasattr(analysis_result, 'document_info') and analysis_result.document_info:
                result_dict["document_info"] = self._serialize_document_info(analysis_result.document_info)
            
            # 处理content_analysis，提取统计信息和结构数据
            content_stats = {}
            document_structures = []
            outline = []
            structure_analysis = {}

            if hasattr(analysis_result, 'content_analysis') and analysis_result.content_analysis:
                content_analysis = analysis_result.content_analysis
                logger.info(f"🔍 content_analysis 类型: {type(content_analysis)}")
                logger.info(f"🔍 content_analysis 属性: {dir(content_analysis)}")

                # 尝试提取统计信息的多种方式
                if hasattr(content_analysis, 'content_stats'):
                    content_stats = content_analysis.content_stats
                    logger.info(f"🔍 从content_stats属性提取到 {len(content_stats)} 个统计字段")
                elif hasattr(content_analysis, 'to_dict'):
                    content_data = content_analysis.to_dict()
                    content_stats = content_data.get("content_stats", {})
                    logger.info(f"🔍 从to_dict()方法提取到 {len(content_stats)} 个统计字段")
                elif isinstance(content_analysis, dict):
                    content_stats = content_analysis.get("content_stats", {})
                    logger.info(f"🔍 从字典格式提取到 {len(content_stats)} 个统计字段")
                else:
                    # 🔥 修复：提取完整的35个统计字段
                    all_stats_fields = [
                        # 学术论文必需统计
                        'page_count', 'word_count', 'character_count', 'characters_with_spaces',
                        'paragraph_count', 'table_count', 'image_count', 'line_count',

                        # 结构分析统计
                        'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                        'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',

                        # 格式规范统计
                        'font_count', 'style_count', 'fonts_used', 'styles_used',
                        'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',

                        # 质量检查统计
                        'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                        'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                        'chart_count', 'drawing_count'
                    ]

                    for attr in all_stats_fields:
                        if hasattr(content_analysis, attr):
                            content_stats[attr] = getattr(content_analysis, attr)

                # 🔥 新增：提取文档结构数据
                logger.info(f"🔍 检查 document_structures 属性: {hasattr(content_analysis, 'document_structures')}")
                logger.info(f"🔍 检查 outline 属性: {hasattr(content_analysis, 'outline')}")

                if hasattr(content_analysis, 'document_structures'):
                    document_structures = content_analysis.document_structures or []
                    logger.info(f"🔍 提取到 document_structures: {len(document_structures)} 个")
                if hasattr(content_analysis, 'outline'):
                    outline = content_analysis.outline or []
                    logger.info(f"🔍 提取到 outline: {len(outline)} 个")
                if hasattr(content_analysis, 'structure_analysis'):
                    structure_analysis = content_analysis.structure_analysis or {}

                logger.info(f"提取到结构数据: {len(document_structures)} 个结构, {len(outline)} 个大纲条目")

                # 不直接存储content_analysis对象，避免序列化问题
                # result_dict["content_analysis"] = content_analysis
            
            # 🔥 关键修复：如果content_analysis没有提供数据，从formatted_json中提取
            if not content_stats and hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                try:
                    formatted_data = json.loads(analysis_result.formatted_json)
                    if 'content_analysis' in formatted_data:
                        json_content_stats = formatted_data['content_analysis']
                        content_stats = {
                            'page_count': json_content_stats.get('page_count', 0),
                            'word_count': json_content_stats.get('word_count', 0),
                            'paragraph_count': json_content_stats.get('paragraph_count', 0),
                            'image_count': json_content_stats.get('image_count', 0),
                            'table_count': json_content_stats.get('table_count', 0),
                            'formula_count': json_content_stats.get('formula_count', 0),
                            'reference_count': json_content_stats.get('reference_count', 0),
                            'footnote_count': json_content_stats.get('footnote_count', 0)
                        }
                except Exception as e:
                    logger.warning(f"从formatted_json提取content_stats失败: {e}")
            
            # 确保content_stats格式正确
            if not isinstance(content_stats, dict):
                content_stats = {}
            
            # 🔥 修复：设置完整的35个统计字段默认值
            default_stats = {
                # 学术论文必需统计
                "page_count": 0,
                "word_count": 0,
                "character_count": 0,
                "characters_with_spaces": 0,
                "paragraph_count": 0,
                "table_count": 0,
                "image_count": 0,
                "line_count": 0,

                # 结构分析统计
                "heading_count": 0,
                "section_count": 0,
                "footnote_count": 0,
                "endnote_count": 0,
                "reference_count": 0,
                "hyperlink_count": 0,
                "bookmark_count": 0,
                "comment_count": 0,
                "field_count": 0,

                # 格式规范统计
                "font_count": 0,
                "style_count": 0,
                "fonts_used": [],
                "styles_used": [],
                "page_orientation": "unknown",
                "page_size": "unknown",
                "margin_info": {},
                "line_spacing_info": {},

                # 质量检查统计
                "spelling_errors": 0,
                "grammar_errors": 0,
                "revision_count": 0,
                "version_count": 0,
                "track_changes_count": 0,
                "formula_count": 0,
                "equation_count": 0,
                "textbox_count": 0,
                "chart_count": 0,
                "drawing_count": 0
            }

            # 合并统计数据（只设置缺失的字段）
            for key, default_value in default_stats.items():
                if key not in content_stats:
                    content_stats[key] = default_value

            # 🔥 调试日志：最终的统计信息
            logger.info(f"🔍 最终content_stats包含 {len(content_stats)} 个字段")
            logger.info(f"🔍 最终content_stats字段列表: {list(content_stats.keys())}")
            logger.info(f"🔍 最终content_stats内容: {content_stats}")
            
            # 创建前端期望的analysis_result结构
            result_dict["analysis_result"] = {
                "content_stats": content_stats,
                "structure_analysis": structure_analysis
            }

            # 🔥 新增：在顶级添加文档结构数据（前端期望的格式）
            result_dict["document_structures"] = document_structures
            result_dict["outline"] = outline

            # 🔥 新增：调试日志 - 检查添加到result_dict中的结构数据
            logger.info(f"添加到result_dict的结构数据: document_structures={len(document_structures)}个, outline={len(outline)}个")
            
            # 添加其他分析结果（确保可序列化）
            if hasattr(analysis_result, 'format_analysis') and analysis_result.format_analysis:
                try:
                    if hasattr(analysis_result.format_analysis, 'to_dict'):
                        result_dict["analysis_result"]["format_analysis"] = analysis_result.format_analysis.to_dict()
                    elif isinstance(analysis_result.format_analysis, dict):
                        result_dict["analysis_result"]["format_analysis"] = analysis_result.format_analysis
                    else:
                        result_dict["analysis_result"]["format_analysis"] = str(analysis_result.format_analysis)
                except Exception:
                    pass
            
            if hasattr(analysis_result, 'structure_analysis') and analysis_result.structure_analysis:
                try:
                    if hasattr(analysis_result.structure_analysis, 'to_dict'):
                        result_dict["analysis_result"]["structure_analysis"] = analysis_result.structure_analysis.to_dict()
                    elif isinstance(analysis_result.structure_analysis, dict):
                        result_dict["analysis_result"]["structure_analysis"] = analysis_result.structure_analysis
                    else:
                        result_dict["analysis_result"]["structure_analysis"] = str(analysis_result.structure_analysis)
                except Exception:
                    pass
            
            if hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                result_dict["formatted_json"] = analysis_result.formatted_json
            
            return result_dict
        
        # 其他类型，尝试转换为字典
        try:
            if hasattr(analysis_result, '__dict__'):
                return analysis_result.__dict__
            else:
                return {"raw_result": str(analysis_result)}
        except Exception as e:
            logger.warning(f"无法转换分析结果: {e}")
            return {"error": f"转换失败: {str(e)}"}

    def _convert_dict_to_frontend_format(self, result_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        将数据库中存储的字典格式转换为前端期望的格式
        
        Args:
            result_dict: 数据库中存储的结果字典
            
        Returns:
            Dict[str, Any]: 前端期望的格式
        """
        logger.info("开始转换字典格式到前端期望格式")

        # 🔥 调试：显示result_dict的顶级键
        logger.info(f"🔍 result_dict顶级键: {list(result_dict.keys())}")

        # 设置默认的统计数据
        default_stats = {
            "page_count": 0,
            "word_count": 0,
            "paragraph_count": 0,
            "image_count": 0,
            "table_count": 0,
            "formula_count": 0,
            "reference_count": 0,
            "footnote_count": 0
        }

        content_stats = {}

        # 🔥 新架构修复：优先从新的6步骤架构数据结构中提取统计信息
        if 'content_stats' in result_dict and isinstance(result_dict['content_stats'], dict):
            # 直接使用新架构的content_stats字段
            content_stats = result_dict['content_stats'].copy()
            logger.info(f"✅ 从新架构content_stats字段提取统计信息: {content_stats}")
        elif 'statistics' in result_dict and isinstance(result_dict['statistics'], dict):
            # 使用新架构的statistics字段
            stats = result_dict['statistics']
            content_stats = {
                'page_count': stats.get('page_count', 0),
                'word_count': stats.get('word_count', 0),
                'paragraph_count': stats.get('paragraph_count', 0),
                'character_count': stats.get('character_count', 0),
                'image_count': stats.get('image_count', 0),
                'table_count': stats.get('table_count', 0),
                'formula_count': stats.get('formula_count', 0),
                'reference_count': stats.get('reference_count', 0),
                'footnote_count': stats.get('footnote_count', 0),
            }
            logger.info(f"✅ 从新架构statistics字段提取统计信息: {content_stats}")
        elif 'analysis_result' in result_dict and isinstance(result_dict['analysis_result'], dict):
            # 🔥 关键修复：从analysis_result中提取统计信息
            analysis_result = result_dict['analysis_result']
            logger.info(f"🔍 analysis_result键: {list(analysis_result.keys())}")

            # 检查analysis_result中的content_stats
            if 'content_stats' in analysis_result and isinstance(analysis_result['content_stats'], dict):
                content_stats = analysis_result['content_stats'].copy()
                logger.info(f"✅ 从analysis_result.content_stats提取统计信息: {content_stats}")
            # 检查analysis_result中的statistics
            elif 'statistics' in analysis_result and isinstance(analysis_result['statistics'], dict):
                stats = analysis_result['statistics']
                content_stats = {
                    'page_count': stats.get('page_count', 0),
                    'word_count': stats.get('word_count', 0),
                    'paragraph_count': stats.get('paragraph_count', 0),
                    'character_count': stats.get('character_count', 0),
                    'image_count': stats.get('image_count', 0),
                    'table_count': stats.get('table_count', 0),
                    'formula_count': stats.get('formula_count', 0),
                    'reference_count': stats.get('reference_count', 0),
                    'footnote_count': stats.get('footnote_count', 0),
                }
                logger.info(f"✅ 从analysis_result.statistics提取统计信息: {content_stats}")
            else:
                # 如果analysis_result中没有统计信息，继续到旧架构
                content_stats = None  # 标记为未找到
                logger.info(f"⚠️ analysis_result中未找到统计信息，analysis_result键: {list(analysis_result.keys())}")
        else:
            content_stats = None  # 标记为未找到

        # 🔥 修复：只有在没有找到统计信息时才使用旧架构
        if content_stats is None:
            content_stats = {}
            # 回退到旧的数据结构提取方式
            # 1. 尝试从顶层document_info中提取统计信息
            if 'document_info' in result_dict and isinstance(result_dict['document_info'], dict):
                doc_info = result_dict['document_info']
                content_stats.update({
                    'page_count': doc_info.get('pages', 0),
                    'word_count': doc_info.get('words', 0),
                    'paragraph_count': doc_info.get('paragraphs', 0),
                    'character_count': doc_info.get('characters', 0),
                    'image_count': doc_info.get('images', 0),
                    'table_count': doc_info.get('tables', 0),
                })
                logger.info(f"⚠️ 从旧架构document_info提取统计信息: {content_stats}")
        
        # 🔥 关键修复：从嵌套的analysis_result.document_info中提取统计信息
        if 'analysis_result' in result_dict and isinstance(result_dict['analysis_result'], dict):
            nested_analysis = result_dict['analysis_result']
            if 'document_info' in nested_analysis and isinstance(nested_analysis['document_info'], dict):
                nested_doc_info = nested_analysis['document_info']
                logger.info(f"发现嵌套document_info: {nested_doc_info}")
                
                # 优先使用嵌套的document_info数据
                content_stats.update({
                    'page_count': nested_doc_info.get('pages', content_stats.get('page_count', 0)),
                    'word_count': nested_doc_info.get('words', content_stats.get('word_count', 0)),
                    'paragraph_count': nested_doc_info.get('paragraphs', content_stats.get('paragraph_count', 0)),
                    'image_count': nested_doc_info.get('images', content_stats.get('image_count', 0)),
                    'table_count': nested_doc_info.get('tables', content_stats.get('table_count', 0)),
                })
            
            # 🔥 新增修复：处理双重嵌套的analysis_result.analysis_result.content_stats
            if 'analysis_result' in nested_analysis and isinstance(nested_analysis['analysis_result'], dict):
                double_nested_analysis = nested_analysis['analysis_result']
                if 'content_stats' in double_nested_analysis and isinstance(double_nested_analysis['content_stats'], dict):
                    double_nested_content_stats = double_nested_analysis['content_stats']
                    logger.info(f"发现双重嵌套content_stats: {double_nested_content_stats}")
                    
                    # 使用双重嵌套的content_stats数据（这是最准确的）
                    content_stats.update({
                        'page_count': double_nested_content_stats.get('page_count', content_stats.get('page_count', 0)),
                        'word_count': double_nested_content_stats.get('word_count', content_stats.get('word_count', 0)),
                        'paragraph_count': double_nested_content_stats.get('paragraph_count', content_stats.get('paragraph_count', 0)),
                        'image_count': double_nested_content_stats.get('image_count', content_stats.get('image_count', 0)),
                        'table_count': double_nested_content_stats.get('table_count', content_stats.get('table_count', 0)),
                        'formula_count': double_nested_content_stats.get('formula_count', content_stats.get('formula_count', 0)),
                        'reference_count': double_nested_content_stats.get('reference_count', content_stats.get('reference_count', 0)),
                        'footnote_count': double_nested_content_stats.get('footnote_count', content_stats.get('footnote_count', 0)),
                    })
        
        # 2. 尝试从formatted_json中提取统计信息
        if 'formatted_json' in result_dict and result_dict['formatted_json']:
            try:
                formatted_data = json.loads(result_dict['formatted_json'])
                if 'content_analysis' in formatted_data:
                    json_content_stats = formatted_data['content_analysis']
                    # 🔥 修复：提取完整的35个统计字段
                    all_stats_fields = [
                        'page_count', 'word_count', 'character_count', 'characters_with_spaces',
                        'paragraph_count', 'table_count', 'image_count', 'line_count',
                        'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                        'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',
                        'font_count', 'style_count', 'fonts_used', 'styles_used',
                        'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',
                        'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                        'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                        'chart_count', 'drawing_count'
                    ]
                    for key in all_stats_fields:
                        if key in json_content_stats:
                            content_stats[key] = json_content_stats[key]
            except Exception as e:
                logger.warning(f"从formatted_json解析统计信息失败: {e}")
        
        # 🔥 关键修复：从嵌套的analysis_result.formatted_json中提取统计信息
        if 'analysis_result' in result_dict and isinstance(result_dict['analysis_result'], dict):
            nested_analysis = result_dict['analysis_result']
            if 'formatted_json' in nested_analysis and nested_analysis['formatted_json']:
                try:
                    formatted_data = json.loads(nested_analysis['formatted_json'])
                    if 'content_analysis' in formatted_data:
                        json_content_stats = formatted_data['content_analysis']
                        logger.info(f"发现嵌套formatted_json中的content_analysis: {json_content_stats}")
                        # 🔥 修复：提取完整的35个统计字段
                        all_stats_fields = [
                            'page_count', 'word_count', 'character_count', 'characters_with_spaces',
                            'paragraph_count', 'table_count', 'image_count', 'line_count',
                            'heading_count', 'section_count', 'footnote_count', 'endnote_count',
                            'reference_count', 'hyperlink_count', 'bookmark_count', 'comment_count', 'field_count',
                            'font_count', 'style_count', 'fonts_used', 'styles_used',
                            'page_orientation', 'page_size', 'margin_info', 'line_spacing_info',
                            'spelling_errors', 'grammar_errors', 'revision_count', 'version_count',
                            'track_changes_count', 'formula_count', 'equation_count', 'textbox_count',
                            'chart_count', 'drawing_count'
                        ]
                        for key in all_stats_fields:
                            if key in json_content_stats:
                                content_stats[key] = json_content_stats[key]
                except Exception as e:
                    logger.warning(f"从嵌套formatted_json解析统计信息失败: {e}")
        
        # 3. 尝试从其他字段中提取统计信息
        possible_fields = ['analysis_result', 'content_analysis', 'result', 'stats', 'statistics']
        for field in possible_fields:
            if field in result_dict and isinstance(result_dict[field], dict):
                field_data = result_dict[field]
                for key in default_stats.keys():
                    if key in field_data:
                        content_stats[key] = field_data[key]
        
        # 4. 确保所有统计字段都有值
        for key, default_value in default_stats.items():
            if key not in content_stats:
                content_stats[key] = default_value
        
        # 🔥 深度优化：构建精简扁平化的前端格式

        # 优化 document_info：移除重复数据，只保留核心信息
        document_info = result_dict.get('document_info', {})
        cover_info = document_info.get('cover_page_info', {})

        optimized_document_info = {
            "title": cover_info.get('title') or document_info.get('title', ''),
            "author": cover_info.get('author') or document_info.get('author', ''),
            "major": cover_info.get('major', ''),
            "department": cover_info.get('department', ''),
            "student_id": cover_info.get('student_id', ''),
            "advisor": cover_info.get('advisor', ''),
            "date": cover_info.get('date', ''),
            "degree_type": cover_info.get('degree_type', ''),
            "school": cover_info.get('school', '')
            # 🔥 移除重复的 pages/words，统一使用 content_stats
        }

        converted_result = {
            "success": result_dict.get('success', True),
            "processing_time": result_dict.get('processing_time', 0),
            "error_message": result_dict.get('error_message', None),
            "warnings": result_dict.get('warnings', []),

            # 🔥 优化：精简的文档信息，移除重复数据
            "document_info": optimized_document_info,

            # 🔥 关键修复：content_stats提升到顶级，与document_structures同级
            "content_stats": content_stats,

            # 🔥 包含文档结构数据（顶级字段）- 移除重复的outline，并转换为新格式
            "document_structures": self._convert_document_structures_to_new_format(result_dict.get('document_structures', [])),
            # 🔥 优化：移除outline，统一使用document_structures

            # 🔥 保留检测标准相关字段
            "detection_standard": result_dict.get('detection_standard'),
            "standard_name": result_dict.get('standard_name'),

            # 🔥 保留其他任务相关字段
            "task_type": result_dict.get('task_type'),
            "status": result_dict.get('status'),
            "compliance_score": result_dict.get('compliance_score'),
            "problems_found": result_dict.get('problems_found'),

            # 🔥 优化：只在有值时包含 check_result
            **({"check_result": result_dict["check_result"]} if result_dict.get("check_result") else {}),

            "check_summary": result_dict.get('check_summary', {}),
            "analysis_summary": result_dict.get('analysis_summary', {}),
            "processing_meta": result_dict.get('processing_meta', {})

            # 🔥 移除冗余字段：formatted_json (通常为null)
        }
        
        logger.info(f"转换完成，content_stats: {content_stats}")

        # 🔥 新增：调试日志 - 检查结构数据
        document_structures = converted_result.get('document_structures', [])
        outline = converted_result.get('outline', [])
        logger.info(f"转换后的结构数据: document_structures={len(document_structures)}个, outline={len(outline)}个")

        return converted_result

    def _convert_document_structures_to_new_format(self, document_structures: list) -> list:
        """
        将旧格式的document_structures转换为新格式（只保留count字段）

        Args:
            document_structures: 旧格式的结构列表

        Returns:
            新格式的结构列表（移除计数字段）
        """
        converted_structures = []

        for structure in document_structures:
            # 复制原有结构
            new_structure = structure.copy()

            # 🔥 优化：生成统一的count字段
            structure_name = structure.get('name', '')

            # 如果已经有count字段，直接使用
            if 'count' in structure:
                count_display = structure['count']
            else:
                # 兼容旧数据：从旧字段生成count
                word_count = structure.get('word_count', 0)
                reference_count = structure.get('reference_count', 0)

                if structure_name == '参考文献':
                    # 参考文献：尝试从内容分析
                    content_text = structure.get('content', {}).get('text', '')
                    if content_text and len(content_text) > 10:
                        # 简单的语言分析
                        chinese_count, foreign_count = self._analyze_reference_languages(content_text)

                        if chinese_count > 0 and foreign_count > 0:
                            count_display = f"中文{chinese_count}条;外文{foreign_count}条"
                        elif chinese_count > 0:
                            count_display = f"中文{chinese_count}条"
                        elif foreign_count > 0:
                            count_display = f"外文{foreign_count}条"
                        else:
                            count_display = f"{chinese_count + foreign_count}条"
                    elif reference_count > 0:
                        count_display = f"{reference_count}条"
                    else:
                        count_display = "0条"
                else:
                    # 其他结构：使用字数
                    count_display = f"{word_count}字"

            # 🔥 优化：只保留必要字段，移除计数字段
            cleaned_structure = {
                'name': new_structure.get('name', ''),
                'status': new_structure.get('status', 'present'),
                'type': new_structure.get('type', 'standard'),
                'page': new_structure.get('page'),
                'content': new_structure.get('content', {}),
                'identifiers_matched': new_structure.get('identifiers_matched', []),
                'required': new_structure.get('required', False),
                'count': count_display  # 只保留count字段
            }

            converted_structures.append(cleaned_structure)

        logger.info(f"转换document_structures格式完成: {len(converted_structures)}个结构（已移除计数字段）")
        return converted_structures

    def _analyze_reference_languages(self, text: str) -> tuple[int, int]:
        """
        简化的参考文献语言分析（仅用于兼容旧数据）

        Args:
            text: 参考文献文本

        Returns:
            (中文参考文献条数, 外文参考文献条数)
        """
        if not text:
            return 0, 0

        import re

        # 简单统计参考文献条数
        total_refs = len(re.findall(r'\[\d+\]', text))
        if total_refs == 0:
            return 0, 0

        # 简单的语言判断：基于中文字符比例
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))

        if chinese_chars > english_chars:
            # 中文为主
            chinese_count = max(1, int(total_refs * 0.8))
            foreign_count = total_refs - chinese_count
        else:
            # 外文为主
            foreign_count = max(1, int(total_refs * 0.8))
            chinese_count = total_refs - foreign_count

        return chinese_count, foreign_count

    def _serialize_document_info(self, document_info) -> Dict[str, Any]:
        """
        序列化document_info，处理datetime对象
        
        Args:
            document_info: 包含可能不可序列化对象的文档信息
            
        Returns:
            Dict[str, Any]: 可序列化的字典
        """
        if not document_info:
            return {}
        
        serialized = {}
        
        for key, value in document_info.items():
            try:
                if value is None:
                    serialized[key] = None
                elif isinstance(value, (str, int, float, bool)):
                    serialized[key] = value
                elif hasattr(value, 'isoformat'):  # datetime对象
                    serialized[key] = value.isoformat()
                elif isinstance(value, dict):
                    serialized[key] = self._serialize_document_info(value)
                elif isinstance(value, list):
                    serialized[key] = [self._serialize_value(item) for item in value]
                else:
                    # 其他类型转换为字符串
                    serialized[key] = str(value)
            except Exception as e:
                logger.warning(f"序列化document_info字段 {key} 失败: {e}")
                serialized[key] = str(value)
        
        return serialized
    
    def _serialize_value(self, value):
        """序列化单个值"""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif hasattr(value, 'isoformat'):  # datetime对象
            return value.isoformat()
        elif isinstance(value, dict):
            return self._serialize_document_info(value)
        elif isinstance(value, list):
            return [self._serialize_value(item) for item in value]
        else:
            return str(value)
    
    def _extract_cover_page_info_from_analysis(self, analysis_result) -> Dict[str, Any]:
        """
        从分析结果中提取封面页信息
        
        Args:
            analysis_result: 文档分析结果
            
        Returns:
            Dict[str, Any]: 封面页信息
        """
        try:
            # 默认封面页信息
            default_cover_info = {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': ''
            }
            
            # 如果分析结果为空，返回默认值
            if not analysis_result or not analysis_result.success:
                logger.warning("分析结果为空或失败，返回默认封面页信息")
                return default_cover_info
            
            # 尝试从document_info中获取封面页信息
            if hasattr(analysis_result, 'document_info') and analysis_result.document_info:
                doc_info = analysis_result.document_info
                
                # 检查是否有封面页信息
                if 'cover_page_info' in doc_info:
                    cover_info = doc_info['cover_page_info']
                    logger.info(f"从document_info中获取封面页信息: {cover_info}")
                    return cover_info
                
                # 尝试从其他字段中获取部分信息
                if 'title' in doc_info or 'author' in doc_info:
                    partial_info = default_cover_info.copy()
                    partial_info['title'] = doc_info.get('title', '')
                    partial_info['author'] = doc_info.get('author', '')
                    return partial_info
            
            # 🔥 新增：从 formatted_json 中提取封面页信息
            if hasattr(analysis_result, 'formatted_json') and analysis_result.formatted_json:
                try:
                    import json
                    formatted_data = json.loads(analysis_result.formatted_json)
                    
                    # 检查document_info中的封面页信息
                    if 'document_info' in formatted_data and isinstance(formatted_data['document_info'], dict):
                        doc_info = formatted_data['document_info']
                        if 'cover_page_info' in doc_info:
                            cover_info = doc_info['cover_page_info']
                            logger.info(f"从formatted_json中获取封面页信息: {cover_info}")
                            return cover_info
                    
                    # 检查是否有直接的封面页信息
                    if 'cover_page_info' in formatted_data:
                        cover_info = formatted_data['cover_page_info']
                        logger.info(f"从formatted_json直接获取封面页信息: {cover_info}")
                        return cover_info
                        
                except Exception as e:
                    logger.warning(f"从formatted_json解析封面页信息失败: {e}")
            
            # 如果所有方法都失败，返回默认值
            logger.warning("无法从分析结果中提取封面页信息，返回默认值")
            return default_cover_info
            
        except Exception as e:
            logger.error(f"提取封面页信息失败: {str(e)}")
            return {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': '',
                'error': str(e)
            }

    def _build_final_analysis_result(self, raw_data: Dict[str, Any], processed_data: Dict[str, Any],
                                   structure_result: Dict[str, Any], check_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 优化：构建最终的分析结果（减少数据重复）

        Args:
            raw_data: 原始数据提取结果
            processed_data: 数据预处理结果
            structure_result: 结构分析结果
            check_result: 格式检测结果

        Returns:
            dict: 最终分析结果
        """
        logger.info("🔧 构建最终分析结果")

        # 提取原始数据
        raw_document_data = raw_data.get('raw_data', {})

        # 🔥 调试：检查原始数据结构
        logger.info(f"🔥 调试：raw_document_data keys: {list(raw_document_data.keys())}")
        document_structure = raw_document_data.get('document_structure', {})
        logger.info(f"🔥 调试：document_structure keys: {list(document_structure.keys())}")
        document_structures = document_structure.get('document_structures', [])
        logger.info(f"🔥 调试：document_structures 数量: {len(document_structures)}")

        # 🔥 修复：从原始数据中提取完整的35个统计字段
        document_info = raw_document_data.get('document_info', {})

        # 基础统计信息（优先使用stat_前缀的字段，这些是Word COM接口的准确数据）
        content_stats = {
            'page_count': document_info.get('stat_pages', 0) or document_info.get('pages', 0),
            'word_count': document_info.get('stat_words', 0) or document_info.get('words', 0),
            'character_count': document_info.get('stat_characters', 0) or document_info.get('characters', 0),
            'characters_with_spaces': document_info.get('characters_with_spaces', 0),
            'paragraph_count': document_info.get('stat_paragraphs', 0) or document_info.get('paragraphs', 0),
            'table_count': document_info.get('stat_tables', 0) or document_info.get('tables', 0),
            'image_count': document_info.get('stat_images', 0) or document_info.get('images', 0),
            'line_count': document_info.get('lines', 0),

            # 结构分析统计
            'heading_count': document_info.get('heading_count', 0),
            'section_count': document_info.get('section_count', 0),
            'footnote_count': document_info.get('footnote_count', 0),
            'endnote_count': document_info.get('endnote_count', 0),
            'reference_count': document_info.get('reference_count', 0),
            'hyperlink_count': document_info.get('hyperlink_count', 0),
            'bookmark_count': document_info.get('bookmark_count', 0),
            'comment_count': document_info.get('comment_count', 0),
            'field_count': document_info.get('field_count', 0),

            # 格式规范统计
            'font_count': document_info.get('font_count', 0),
            'style_count': document_info.get('style_count', 0),
            'fonts_used': document_info.get('fonts_used', []),
            'styles_used': document_info.get('styles_used', []),
            'page_orientation': document_info.get('page_orientation', 'unknown'),
            'page_size': document_info.get('page_size', 'unknown'),
            'margin_info': document_info.get('margin_info', {}),
            'line_spacing_info': document_info.get('line_spacing_info', {}),

            # 质量检查统计
            'spelling_errors': document_info.get('spelling_errors', 0),
            'grammar_errors': document_info.get('grammar_errors', 0),
            'revision_count': document_info.get('revision_count', 0),
            'version_count': document_info.get('version_count', 0),
            'track_changes_count': document_info.get('track_changes_count', 0),
            'formula_count': document_info.get('formula_count', 0),
            'equation_count': document_info.get('equation_count', 0),
            'textbox_count': document_info.get('textbox_count', 0),
            'chart_count': document_info.get('chart_count', 0),
            'drawing_count': document_info.get('drawing_count', 0),
        }

        # 🔥 调试日志：检查提取的统计信息
        logger.info(f"🔍 从document_info提取的统计字段数: {len(content_stats)}")
        logger.info(f"🔍 document_info包含的字段: {list(document_info.keys())}")
        logger.info(f"🔍 提取的content_stats: {content_stats}")

        # 从内容数据中补充统计信息（作为备用或补充）
        document_content = raw_document_data.get('document_content', {})
        if document_content:
            # 如果Word COM没有获取到表格数和图片数，使用内容分析的结果
            if content_stats['table_count'] == 0:
                content_stats['table_count'] = len(document_content.get('tables', []))
            if content_stats['image_count'] == 0:
                content_stats['image_count'] = len(document_content.get('images', []))

        # 从文档结构分析中补充统计信息
        document_structure = raw_document_data.get('document_structure', {})
        if document_structure and 'statistics' in document_structure:
            structure_stats = document_structure['statistics']
            # 优先使用结构分析的统计信息（通常更准确）
            for key in ['pages', 'words', 'characters', 'paragraphs', 'tables', 'images']:
                if key in structure_stats and structure_stats[key] > 0:
                    if key == 'pages':
                        content_stats['page_count'] = structure_stats[key]
                    elif key == 'words':
                        content_stats['word_count'] = structure_stats[key]
                    elif key == 'characters':
                        content_stats['character_count'] = structure_stats[key]
                    elif key == 'paragraphs':
                        content_stats['paragraph_count'] = structure_stats[key]
                    elif key == 'tables':
                        content_stats['table_count'] = structure_stats[key]
                    elif key == 'images':
                        content_stats['image_count'] = structure_stats[key]

        logger.info(f"📊 提取的文档统计信息: {content_stats}")

        # 🔥 优化：构建精简的最终结果，减少数据重复
        final_result = {
            # 文档基本信息（合并处理）
            'document_info': self._merge_document_info(document_info, processed_data.get('document_info', {})),

            # 🔥 优化：精简的结构信息
            'document_structures': self._optimize_document_structures(document_structures),

            # 🔥 优化：只保留必要的outline信息
            'outline': self._optimize_outline(raw_document_data.get('document_structure', {}).get('outline', [])),

            # 🔥 修复：使用从原始数据提取的统计信息
            'statistics': content_stats,

            # 🔥 优化：精简的分析结果
            'analysis_summary': {
                'hierarchy_score': structure_result.get('hierarchy_analysis', {}).get('score', 0),
                'completeness_score': structure_result.get('completeness_analysis', {}).get('score', 0),
                'quality_score': structure_result.get('quality_analysis', {}).get('score', 0),
                'document_type': structure_result.get('document_type', {}).get('type', 'unknown')
            },

            # 🔥 新增：包含检测结果
            'check_result': check_result,

            # 处理元信息
            'meta': {
                'extraction_method': raw_document_data.get('extraction_method', 'unknown'),
                'processing_pipeline': 'optimized_v2',
                'processing_times': {
                    'raw_extraction': raw_data.get('processing_time', 0),
                    'preprocessing': processed_data.get('preprocessing_time', 0),
                    'structure_analysis': structure_result.get('analysis_time', 0)
                }
            }
        }

        logger.info(f"✅ 最终分析结果构建完成，包含 {len(final_result.get('document_structures', []))} 个结构，{len(final_result.get('outline', []))} 个大纲条目")

        return final_result

    def _merge_document_info(self, raw_info: Dict[str, Any], processed_info: Dict[str, Any]) -> Dict[str, Any]:
        """合并文档信息，避免重复"""
        merged = {}

        # 基本信息优先使用原始数据
        for key in ['pages', 'words', 'characters', 'paragraphs', 'tables', 'images']:
            merged[key] = raw_info.get(key) or processed_info.get(key) or 0

        # 元数据信息
        for key in ['title', 'author', 'subject', 'keywords', 'file_name', 'file_size']:
            merged[key] = raw_info.get(key) or processed_info.get(key) or ''

        # 时间信息
        for key in ['creation_time', 'last_save_time']:
            merged[key] = raw_info.get(key) or processed_info.get(key)

        # 封面页信息
        if 'cover_page_info' in raw_info or 'cover_page_info' in processed_info:
            merged['cover_page_info'] = {
                **(processed_info.get('cover_page_info', {})),
                **(raw_info.get('cover_page_info', {}))
            }

        return merged

    def _optimize_document_structures(self, structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化文档结构数据，减少冗余，并生成count字段"""
        optimized = []

        for structure in structures:
            structure_name = structure.get('name', '')

            # 🔥 新增：生成统一的count字段
            # 如果原始数据已经有count字段，直接使用
            if 'count' in structure:
                count_display = structure['count']
            else:
                # 兼容旧数据：从旧字段生成count
                word_count = structure.get('word_count', 0)
                reference_count = structure.get('reference_count', 0)

                if structure_name == '参考文献':
                    # 参考文献：尝试从内容分析
                    content_text = structure.get('content', {}).get('text', '')
                    if content_text and len(content_text) > 10:
                        # 简单的语言分析
                        chinese_count, foreign_count = self._analyze_reference_languages(content_text)

                        if chinese_count > 0 and foreign_count > 0:
                            count_display = f"中文{chinese_count}条;外文{foreign_count}条"
                        elif chinese_count > 0:
                            count_display = f"中文{chinese_count}条"
                        elif foreign_count > 0:
                            count_display = f"外文{foreign_count}条"
                        else:
                            count_display = f"{chinese_count + foreign_count}条"
                    elif reference_count > 0:
                        count_display = f"{reference_count}条"
                    else:
                        count_display = "0条"
                else:
                    # 其他结构：使用字数
                    count_display = f"{word_count}字"

            # 🔥 优化：只保留必要字段，移除旧的计数字段
            optimized_structure = {
                'name': structure_name,
                'type': structure.get('type', 'standard'),
                'status': structure.get('status', 'present'),
                'page': structure.get('page'),
                'count': count_display,  # 🔥 新增：统一的count字段
                'required': structure.get('required', False)
            }

            # 只在需要时包含content信息
            content = structure.get('content', {})
            if content and content.get('text'):
                # 🔥 修复：对于参考文献，保留更多内容用于准确分析
                text_limit = 2000 if structure_name == '参考文献' else 500
                optimized_structure['content'] = {
                    'text': content.get('text', '')[:text_limit],
                    'style': content.get('style', ''),
                    'paragraph_index': content.get('paragraph_index')
                }

            optimized.append(optimized_structure)

        logger.info(f"优化document_structures完成: {len(optimized)}个结构，已生成count字段")
        return optimized

    def _optimize_outline(self, outline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化大纲数据，只保留必要信息"""
        optimized = []

        for item in outline:
            optimized_item = {
                'text': item.get('text', '')[:50],  # 限制文本长度
                'level': item.get('level', 1),
                'page': item.get('page'),
                'type': item.get('type', 'standard'),
                'structure_name': item.get('structure_name', '')
            }
            optimized.append(optimized_item)

        return optimized

    def _extract_cover_page_info_from_raw_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        从原始数据中提取封面页信息

        Args:
            raw_data: 原始数据提取结果

        Returns:
            Dict[str, Any]: 封面页信息
        """
        try:
            logger.info("🔍 从原始数据中提取封面页信息")

            # 默认封面页信息
            default_cover_info = {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': ''
            }

            # 检查原始数据是否成功
            if not raw_data or not raw_data.get('success', False):
                logger.warning("原始数据提取失败，返回默认封面页信息")
                return default_cover_info

            # 从原始数据中获取文档结构信息
            raw_document_data = raw_data.get('raw_data', {})
            document_structure = raw_document_data.get('document_structure', {})
            cover_page_info = document_structure.get('cover_page_info', {})

            if cover_page_info:
                logger.info(f"✅ 从原始数据中获取到封面页信息: {list(cover_page_info.keys())}")
                # 合并默认信息和提取的信息
                result = {**default_cover_info, **cover_page_info}
                return result
            else:
                logger.warning("原始数据中没有封面页信息")
                return default_cover_info

        except Exception as e:
            logger.error(f"从原始数据提取封面页信息失败: {str(e)}")
            return {
                'title': '',
                'author': '',
                'student_id': '',
                'department': '',
                'major': '',
                'advisor': '',
                'school': '',
                'date': '',
                'degree_type': '',
                'raw_text': '',
                'error': str(e)
            }

    async def _save_document_structure_stats(self, task, result: dict, raw_data: dict):
        """
        保存文档结构统计和内容缓存

        Args:
            task: 任务对象
            result: 任务结果
            raw_data: 原始数据
        """
        try:
            from app.database.crud import (
                create_document_structures_batch,
                create_document_content_cache,
                save_document_content_to_file
            )
            from app.models.document_structure import DocumentStructureCreate, DocumentContentCache
            from app.database.connection import get_database_session
            import os
            import uuid
            from datetime import datetime

            # 获取数据库会话
            session = await get_database_session()

            try:
                # 获取文档ID（使用任务ID作为文档ID）
                document_id = task.task_id

                # 1. 保存文档内容缓存
                cache_dir = os.path.join("data", "content_cache")
                os.makedirs(cache_dir, exist_ok=True)

                cache_file_path = os.path.join(cache_dir, f"{document_id}_full_content.json")

                # 保存原始数据到文件
                if save_document_content_to_file(raw_data, cache_file_path):
                    # 创建缓存记录
                    cache_record = DocumentContentCache(
                        cache_id=f"cache_{uuid.uuid4().hex}",
                        document_id=document_id,
                        content_type="full_content",
                        file_path=cache_file_path,
                        file_size=os.path.getsize(cache_file_path),
                        created_at=datetime.now()
                    )

                    await create_document_content_cache(session, cache_record)
                    logger.info(f"文档内容缓存已保存: {cache_file_path}")

                # 2. 保存结构统计
                document_structures = result.get('document_structures', [])
                logger.info(f"🔥 调试：获取到的结构数据数量: {len(document_structures)}")
                logger.info(f"🔥 调试：结构数据内容: {document_structures[:2] if document_structures else '无数据'}")

                if document_structures:
                    structure_records = []

                    for structure in document_structures:
                        # 🔥 优化：简化结构，只保留必要字段
                        structure_name = structure.get('name', '')
                        count_display = structure.get('count', '0字')

                        # 创建结构统计记录（移除计数字段）
                        structure_record = DocumentStructureCreate(
                            structure_id=f"struct_{uuid.uuid4().hex}",
                            document_id=document_id,
                            structure_name=structure_name,
                            structure_type=structure.get('type', 'standard'),
                            status=structure.get('status', 'present'),
                            word_count=0,  # 不再计算，保持数据库兼容性
                            reference_count=0,  # 不再计算，保持数据库兼容性
                            page_number=structure.get('page'),
                            paragraph_index=structure.get('content', {}).get('paragraph_index'),
                            style_info=structure.get('content', {}),
                            created_at=datetime.now()
                        )
                        structure_records.append(structure_record)
                        logger.info(f"🔥 调试：创建结构记录: {structure_name} - {structure.get('type')} - count={count_display}")

                    # 批量保存结构统计
                    await create_document_structures_batch(session, structure_records)
                    logger.info(f"文档结构统计已保存: {len(structure_records)} 个结构")
                else:
                    logger.warning("🔥 调试：没有找到document_structures数据，无法保存结构统计")

                await session.commit()

            except Exception as e:
                await session.rollback()
                logger.error(f"保存文档结构统计失败: {str(e)}")
                raise
            finally:
                await session.close()

        except Exception as e:
            logger.error(f"保存文档结构统计和缓存失败: {str(e)}")
            # 不抛出异常，避免影响主任务流程


# 全局任务管理器实例
task_manager = TaskManager()
