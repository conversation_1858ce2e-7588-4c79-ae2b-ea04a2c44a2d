"""
数据库工具函数

提供数据库操作的通用工具函数，减少代码重复
"""

import json
from typing import Any, Dict, Optional
from app.core.logging import logger


def deserialize_json_field(data: Dict[str, Any], field_name: str, default: Any = None) -> Any:
    """
    统一的JSON字段反序列化函数
    
    Args:
        data: 包含字段的数据字典
        field_name: 要反序列化的字段名
        default: 默认值
        
    Returns:
        反序列化后的值或默认值
    """
    field_value = data.get(field_name)
    if field_value is None:
        return default
        
    # 如果已经是字典类型，直接返回
    if isinstance(field_value, dict):
        return field_value
        
    # 如果是字符串，尝试JSON解析
    if isinstance(field_value, str):
        try:
            return json.loads(field_value)
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"JSON反序列化失败 {field_name}: {e}")
            return default
    
    # 其他类型返回默认值
    return default


def prepare_task_data(row_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    准备任务数据，统一处理JSON字段
    
    Args:
        row_data: 从数据库获取的原始行数据
        
    Returns:
        处理后的任务数据
    """
    # 处理analysis_options字段
    row_data['analysis_options'] = deserialize_json_field(
        row_data, 'analysis_options', {}
    )
    
    # 处理result字段
    row_data['result'] = deserialize_json_field(
        row_data, 'result', {}
    )
    
    return row_data


def prepare_problem_data(row_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    准备问题数据，统一处理JSON字段
    
    Args:
        row_data: 从数据库获取的原始行数据
        
    Returns:
        处理后的问题数据
    """
    # 处理details字段
    row_data['details'] = deserialize_json_field(
        row_data, 'details', {}
    )
    
    return row_data


def prepare_result_data(row_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    准备检测结果数据，统一处理JSON字段
    
    Args:
        row_data: 从数据库获取的原始行数据
        
    Returns:
        处理后的检测结果数据
    """
    # 处理detailed_results字段
    row_data['detailed_results'] = deserialize_json_field(
        row_data, 'detailed_results', {}
    )
    
    return row_data


def serialize_json_field(value: Any) -> Optional[str]:
    """
    序列化JSON字段
    
    Args:
        value: 要序列化的值
        
    Returns:
        JSON字符串或None
    """
    if value is None:
        return None
        
    if isinstance(value, str):
        return value
        
    try:
        return json.dumps(value, ensure_ascii=False, separators=(',', ':'))
    except (TypeError, ValueError) as e:
        logger.warning(f"JSON序列化失败: {e}")
        return None


def build_where_conditions(params: Dict[str, Any], conditions_map: Dict[str, str]) -> tuple[str, Dict[str, Any]]:
    """
    构建WHERE条件子句
    
    Args:
        params: 查询参数
        conditions_map: 条件映射 {param_name: sql_condition}
        
    Returns:
        (where_clause, filtered_params)
    """
    conditions = []
    filtered_params = {}
    
    for param_name, sql_condition in conditions_map.items():
        if param_name in params and params[param_name] is not None:
            conditions.append(sql_condition)
            filtered_params[param_name] = params[param_name]
    
    where_clause = ""
    if conditions:
        where_clause = " WHERE " + " AND ".join(conditions)
    
    return where_clause, filtered_params


def build_pagination_clause(skip: int = 0, limit: int = 20) -> tuple[str, Dict[str, Any]]:
    """
    构建分页子句
    
    Args:
        skip: 跳过的记录数
        limit: 限制的记录数
        
    Returns:
        (pagination_clause, pagination_params)
    """
    pagination_clause = " LIMIT :limit OFFSET :skip"
    pagination_params = {"limit": limit, "skip": skip}
    
    return pagination_clause, pagination_params


def build_order_clause(order_by: Optional[str] = None, order_direction: str = "DESC") -> str:
    """
    构建排序子句
    
    Args:
        order_by: 排序字段
        order_direction: 排序方向 ASC/DESC
        
    Returns:
        排序子句
    """
    if not order_by:
        return ""
    
    # 安全检查：只允许特定字段排序
    allowed_fields = {
        'created_at', 'updated_at', 'completed_at', 'started_at',
        'task_id', 'filename', 'status', 'progress', 'checked_at'
    }
    
    if order_by not in allowed_fields:
        logger.warning(f"不允许的排序字段: {order_by}")
        return ""
    
    direction = "DESC" if order_direction.upper() == "DESC" else "ASC"
    return f" ORDER BY {order_by} {direction}"


def validate_pagination_params(page: int, limit: int) -> tuple[int, int]:
    """
    验证和规范化分页参数
    
    Args:
        page: 页码 (1-based)
        limit: 每页记录数
        
    Returns:
        (skip, validated_limit)
    """
    # 验证页码
    if page < 1:
        page = 1
    
    # 验证每页记录数
    if limit < 1:
        limit = 20
    elif limit > 100:  # 限制最大每页记录数
        limit = 100
    
    skip = (page - 1) * limit
    
    return skip, limit 