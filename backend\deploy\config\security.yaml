# ==================================================
# Word文档分析服务 - 安全加固配置
# ==================================================

# 认证和授权安全
authentication:
  # JWT安全配置
  secret_key: "${SECRET_KEY}"              # 从环境变量获取
  algorithm: "HS256"                       # 安全的签名算法
  access_token_expire_minutes: 480         # 8小时过期
  refresh_token_expire_days: 1             # 1天过期
  
  # 密码安全策略
  password_min_length: 12                  # 最小密码长度
  password_require_uppercase: true         # 要求大写字母
  password_require_lowercase: true         # 要求小写字母
  password_require_numbers: true           # 要求数字
  password_require_special: true           # 要求特殊字符
  password_history_count: 5                # 密码历史记录
  
  # 账户安全
  max_login_attempts: 5                    # 最大登录尝试次数
  lockout_duration_minutes: 30             # 锁定持续时间
  session_timeout_minutes: 480             # 会话超时时间
  max_concurrent_sessions: 3               # 最大并发会话数
  
  # 多因素认证
  mfa_enabled: true                        # 启用MFA
  mfa_methods: ["totp", "sms"]             # 支持的MFA方法
  mfa_backup_codes: 10                     # 备用代码数量

# 数据加密安全
encryption:
  # 数据库加密
  database_encryption: true                # 启用数据库加密
  encryption_algorithm: "AES-256-GCM"      # 加密算法
  key_rotation_days: 90                    # 密钥轮换周期
  
  # 文件加密
  file_encryption: true                    # 启用文件加密
  encrypt_at_rest: true                    # 静态数据加密
  encrypt_in_transit: true                 # 传输数据加密
  
  # 敏感数据处理
  mask_sensitive_data: true                # 敏感数据脱敏
  data_classification: true                # 数据分类
  pii_detection: true                      # PII检测

# 网络安全
network:
  # HTTPS配置
  force_https: true                        # 强制HTTPS
  hsts_enabled: true                       # 启用HSTS
  hsts_max_age: 31536000                   # HSTS最大年龄(1年)
  hsts_include_subdomains: true            # 包含子域名
  
  # TLS配置
  tls_version_min: "1.2"                   # 最小TLS版本
  tls_ciphers: [                           # 允许的加密套件
    "ECDHE-ECDSA-AES128-GCM-SHA256",
    "ECDHE-RSA-AES128-GCM-SHA256",
    "ECDHE-ECDSA-AES256-GCM-SHA384",
    "ECDHE-RSA-AES256-GCM-SHA384"
  ]
  
  # 网络访问控制
  allowed_origins: [                       # 允许的来源
    "https://your-domain.com",
    "https://api.your-domain.com"
  ]
  trusted_proxies: ["127.0.0.1"]          # 信任的代理
  
  # DDoS防护
  rate_limiting: true                      # 启用限流
  connection_limit: 1000                   # 连接限制
  request_timeout: 30                      # 请求超时

# 输入验证和清理
input_validation:
  # 文件上传安全
  max_file_size: 52428800                  # 50MB限制
  allowed_file_types: [".docx", ".doc"]    # 允许的文件类型
  file_content_validation: true            # 文件内容验证
  virus_scanning: true                     # 病毒扫描
  
  # 输入清理
  sanitize_input: true                     # 输入清理
  xss_protection: true                     # XSS防护
  sql_injection_protection: true          # SQL注入防护
  
  # 内容安全策略
  csp_enabled: true                        # 启用CSP
  csp_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# 访问控制
access_control:
  # 基于角色的访问控制
  rbac_enabled: true                       # 启用RBAC
  default_role: "user"                     # 默认角色
  admin_approval_required: true            # 管理员审批
  
  # 权限管理
  permission_inheritance: true             # 权限继承
  resource_level_permissions: true         # 资源级权限
  
  # IP访问控制
  ip_whitelist_enabled: false              # IP白名单(可选)
  ip_blacklist_enabled: true               # IP黑名单
  geo_blocking_enabled: false              # 地理位置阻断(可选)

# 审计和日志
audit:
  # 审计日志
  audit_enabled: true                      # 启用审计
  audit_all_requests: true                 # 审计所有请求
  audit_sensitive_operations: true         # 审计敏感操作
  
  # 日志安全
  log_encryption: true                     # 日志加密
  log_integrity_check: true                # 日志完整性检查
  log_retention_days: 365                  # 日志保留期
  
  # 监控告警
  security_monitoring: true                # 安全监控
  anomaly_detection: true                  # 异常检测
  real_time_alerts: true                   # 实时告警

# 数据保护
data_protection:
  # 数据备份
  backup_encryption: true                  # 备份加密
  backup_frequency: "daily"                # 备份频率
  backup_retention_days: 30                # 备份保留期
  
  # 数据清理
  data_purging: true                       # 数据清理
  purge_schedule: "weekly"                 # 清理计划
  secure_deletion: true                    # 安全删除
  
  # 隐私保护
  privacy_by_design: true                  # 隐私设计
  data_minimization: true                  # 数据最小化
  consent_management: true                 # 同意管理

# 应用安全
application:
  # 代码安全
  secure_coding_practices: true            # 安全编码实践
  dependency_scanning: true                # 依赖扫描
  vulnerability_scanning: true             # 漏洞扫描
  
  # 运行时保护
  runtime_protection: true                 # 运行时保护
  memory_protection: true                  # 内存保护
  stack_protection: true                   # 栈保护
  
  # 错误处理
  secure_error_handling: true              # 安全错误处理
  error_message_sanitization: true         # 错误消息清理
  debug_info_disabled: true                # 禁用调试信息

# 基础设施安全
infrastructure:
  # 容器安全
  container_security: true                 # 容器安全
  image_scanning: true                     # 镜像扫描
  runtime_security: true                   # 运行时安全
  
  # 主机安全
  host_hardening: true                     # 主机加固
  os_patching: true                        # 操作系统补丁
  antivirus_enabled: true                  # 防病毒
  
  # 网络安全
  firewall_enabled: true                   # 防火墙
  intrusion_detection: true                # 入侵检测
  network_segmentation: true               # 网络分段

# 合规性
compliance:
  # 数据保护法规
  gdpr_compliance: true                    # GDPR合规
  ccpa_compliance: true                    # CCPA合规
  
  # 安全标准
  iso27001_compliance: true                # ISO 27001合规
  soc2_compliance: true                    # SOC 2合规
  
  # 行业标准
  owasp_top10_protection: true             # OWASP Top 10防护
  nist_framework: true                     # NIST框架

# 事件响应
incident_response:
  # 响应计划
  incident_response_plan: true             # 事件响应计划
  automated_response: true                 # 自动响应
  escalation_procedures: true              # 升级程序
  
  # 取证和恢复
  forensic_logging: true                   # 取证日志
  backup_recovery: true                    # 备份恢复
  business_continuity: true                # 业务连续性

# 安全配置验证
security_validation:
  # 配置检查
  security_config_validation: true         # 安全配置验证
  periodic_security_review: true           # 定期安全审查
  penetration_testing: true                # 渗透测试
  
  # 安全指标
  security_metrics: true                   # 安全指标
  risk_assessment: true                    # 风险评估
  threat_modeling: true                    # 威胁建模
