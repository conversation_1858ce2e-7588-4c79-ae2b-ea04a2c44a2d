<template>
  <BaseLayout>
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            🎉 新通知系统测试
          </h1>
          <p class="text-lg text-gray-600 dark:text-gray-300">
            替代原生alert的现代化Toast通知系统
          </p>
        </div>

        <!-- 基础通知测试 -->
        <BaseCard class="mb-8">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              🚀 基础通知类型
            </h2>
          </template>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <BaseButton 
              @click="showSuccess" 
              variant="success" 
              class="w-full"
            >
              ✅ 成功通知
            </BaseButton>
            
            <BaseButton 
              @click="showError" 
              variant="danger" 
              class="w-full"
            >
              ❌ 错误通知
            </BaseButton>
            
            <BaseButton 
              @click="showWarning" 
              variant="warning" 
              class="w-full"
            >
              ⚠️ 警告通知
            </BaseButton>
            
            <BaseButton 
              @click="showInfo" 
              variant="info" 
              class="w-full"
            >
              ℹ️ 信息通知
            </BaseButton>
          </div>
        </BaseCard>

        <!-- 高级功能测试 -->
        <BaseCard class="mb-8">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              ⭐ 高级功能测试
            </h2>
          </template>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <BaseButton 
              @click="showWithTitle" 
              variant="primary" 
              class="w-full"
            >
              📝 带标题通知
            </BaseButton>
            
            <BaseButton 
              @click="showLongDuration" 
              variant="secondary" 
              class="w-full"
            >
              ⏱️ 长时间显示
            </BaseButton>
            
            <BaseButton 
              @click="showPersistent" 
              variant="outline-primary" 
              class="w-full"
            >
              📌 永久通知
            </BaseButton>
            
            <BaseButton 
              @click="showBatch" 
              variant="outline-secondary" 
              class="w-full"
            >
              🔄 批量通知
            </BaseButton>
            
            <BaseButton 
              @click="clearAll" 
              variant="light" 
              class="w-full"
            >
              🗑️ 清除所有
            </BaseButton>
            
            <BaseButton 
              @click="showCustom" 
              variant="dark" 
              class="w-full"
            >
              🎨 自定义样式
            </BaseButton>
          </div>
        </BaseCard>

        <!-- 替换对比演示 -->
        <BaseCard class="mb-8">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              🔄 Alert替换对比
            </h2>
          </template>
          
          <div class="space-y-6">
            <!-- 原生Alert示例 -->
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <h3 class="text-lg font-medium text-red-900 dark:text-red-200 mb-2">
                ❌ 原生Alert (已淘汰)
              </h3>
              <div class="flex items-center justify-between">
                <code class="text-sm text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/40 px-2 py-1 rounded">
                  alert('请先登录后再进行购买。')
                </code>
                <BaseButton 
                  @click="showOldAlert" 
                  variant="danger" 
                  size="sm"
                >
                  演示原生Alert
                </BaseButton>
              </div>
              <p class="text-sm text-red-600 dark:text-red-400 mt-2">
                ⚠️ 阻塞界面、样式陈旧、无法自定义
              </p>
            </div>

            <!-- 新通知系统示例 -->
            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <h3 class="text-lg font-medium text-green-900 dark:text-green-200 mb-2">
                ✅ 新通知系统 (推荐)
              </h3>
              <div class="flex items-center justify-between">
                <code class="text-sm text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/40 px-2 py-1 rounded">
                  $notify.warning('请先登录后再进行购买。')
                </code>
                <BaseButton 
                  @click="showNewNotification" 
                  variant="success" 
                  size="sm"
                >
                  演示新通知
                </BaseButton>
              </div>
              <p class="text-sm text-green-600 dark:text-green-400 mt-2">
                ✨ 现代设计、支持暗黑模式、非阻塞、可定制
              </p>
            </div>
          </div>
        </BaseCard>

        <!-- 实际场景模拟 -->
        <BaseCard>
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              🎭 实际场景模拟
            </h2>
          </template>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 表单验证场景 -->
            <div class="space-y-4">
              <h3 class="font-medium text-gray-900 dark:text-white">📝 表单验证场景</h3>
              <div class="space-y-2">
                <BaseButton @click="simulateFormValidation" variant="outline-primary" size="sm" class="w-full">
                  模拟表单验证失败
                </BaseButton>
                <BaseButton @click="simulateFormSuccess" variant="outline-primary" size="sm" class="w-full">
                  模拟表单提交成功
                </BaseButton>
              </div>
            </div>

            <!-- 支付流程场景 -->
            <div class="space-y-4">
              <h3 class="font-medium text-gray-900 dark:text-white">💳 支付流程场景</h3>
              <div class="space-y-2">
                <BaseButton @click="simulatePaymentFlow" variant="outline-secondary" size="sm" class="w-full">
                  模拟完整支付流程
                </BaseButton>
                <BaseButton @click="simulatePaymentError" variant="outline-secondary" size="sm" class="w-full">
                  模拟支付失败
                </BaseButton>
              </div>
            </div>

            <!-- 文件操作场景 -->
            <div class="space-y-4">
              <h3 class="font-medium text-gray-900 dark:text-white">📁 文件操作场景</h3>
              <div class="space-y-2">
                <BaseButton @click="simulateFileUpload" variant="outline-success" size="sm" class="w-full">
                  模拟文件上传
                </BaseButton>
                <BaseButton @click="simulateFileError" variant="outline-success" size="sm" class="w-full">
                  模拟文件错误
                </BaseButton>
              </div>
            </div>

            <!-- 系统操作场景 -->
            <div class="space-y-4">
              <h3 class="font-medium text-gray-900 dark:text-white">⚙️ 系统操作场景</h3>
              <div class="space-y-2">
                <BaseButton @click="simulateSystemOperation" variant="outline-warning" size="sm" class="w-full">
                  模拟系统维护
                </BaseButton>
                <BaseButton @click="simulateSystemRestart" variant="outline-warning" size="sm" class="w-full">
                  模拟系统重启
                </BaseButton>
              </div>
            </div>
          </div>
        </BaseCard>

        <!-- 统计信息 -->
        <div class="mt-8 text-center">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            当前通知数量: {{ notificationCount }} | 
            总测试次数: {{ testCount }}
          </p>
        </div>
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseLayout from '@/components/BaseLayout.vue'
import BaseCard from '@/components/BaseCard.vue'
import BaseButton from '@/components/BaseButton.vue'
import { $notify, useNotifications } from '@/utils/useNotifications'

const { getNotificationCount } = useNotifications()
const testCount = ref(0)

const notificationCount = computed(() => getNotificationCount())

// 增加测试计数
const incrementTest = () => {
  testCount.value++
}

// 基础通知测试
const showSuccess = () => {
  $notify.success('操作成功完成！')
  incrementTest()
}

const showError = () => {
  $notify.error('操作失败，请重试')
  incrementTest()
}

const showWarning = () => {
  $notify.warning('请注意检查输入信息')
  incrementTest()
}

const showInfo = () => {
  $notify.info('这是一条信息提示')
  incrementTest()
}

// 高级功能测试
const showWithTitle = () => {
  $notify.success('您的文档已成功保存到服务器', { 
    title: '保存成功',
    duration: 4000 
  })
  incrementTest()
}

const showLongDuration = () => {
  $notify.warning('这条消息将显示8秒钟', { 
    duration: 8000,
    title: '长时间显示'
  })
  incrementTest()
}

const showPersistent = () => {
  $notify.info('这是一条永久通知，点击右上角X关闭', { 
    duration: 0,
    persistent: true,
    title: '永久通知'
  })
  incrementTest()
}

const showBatch = () => {
  $notify.info('开始批量操作...')
  setTimeout(() => $notify.warning('正在处理第1项...'), 500)
  setTimeout(() => $notify.warning('正在处理第2项...'), 1000)
  setTimeout(() => $notify.warning('正在处理第3项...'), 1500)
  setTimeout(() => $notify.success('批量操作完成！'), 2000)
  incrementTest()
}

const clearAll = () => {
  $notify.clear()
  incrementTest()
}

const showCustom = () => {
  $notify.info('这是自定义样式的通知', {
    title: '自定义通知',
    duration: 5000
  })
  incrementTest()
}

// Alert对比演示
const showOldAlert = () => {
  alert('这是原生alert - 阻塞界面，样式陈旧，无法自定义')
  incrementTest()
}

const showNewNotification = () => {
  $notify.warning('请先登录后再进行购买。')
  incrementTest()
}

// 实际场景模拟
const simulateFormValidation = () => {
  $notify.warning('用户名不能为空')
  setTimeout(() => $notify.warning('密码长度至少8位'), 500)
  setTimeout(() => $notify.warning('请输入有效的邮箱地址'), 1000)
  incrementTest()
}

const simulateFormSuccess = () => {
  $notify.info('正在提交表单...')
  setTimeout(() => $notify.success('表单提交成功！'), 1500)
  incrementTest()
}

const simulatePaymentFlow = () => {
  $notify.info('正在创建订单...')
  setTimeout(() => $notify.info('订单创建成功，跳转支付页面'), 1000)
  setTimeout(() => $notify.info('等待支付确认...'), 2000)
  setTimeout(() => $notify.success('支付成功！您的账户余额已更新'), 4000)
  incrementTest()
}

const simulatePaymentError = () => {
  $notify.info('正在处理支付...')
  setTimeout(() => $notify.error('支付失败：余额不足'), 2000)
  incrementTest()
}

const simulateFileUpload = () => {
  $notify.info('正在上传文件... (0%)')
  setTimeout(() => $notify.info('上传进度: 25%'), 500)
  setTimeout(() => $notify.info('上传进度: 50%'), 1000)
  setTimeout(() => $notify.info('上传进度: 75%'), 1500)
  setTimeout(() => $notify.success('文件上传成功！'), 2000)
  incrementTest()
}

const simulateFileError = () => {
  $notify.info('正在检查文件...')
  setTimeout(() => $notify.error('文件格式不支持，请上传Word文档'), 1500)
  incrementTest()
}

const simulateSystemOperation = () => {
  $notify.warning('系统将在5分钟后进行维护', {
    duration: 0,
    persistent: true,
    title: '系统维护通知'
  })
  incrementTest()
}

const simulateSystemRestart = () => {
  $notify.info('正在重启系统服务...')
  setTimeout(() => $notify.warning('服务重启中，请稍候...'), 1000)
  setTimeout(() => $notify.success('系统服务重启完成'), 3000)
  incrementTest()
}
</script>

<style scoped>
/* 额外样式可以在这里添加 */
</style> 