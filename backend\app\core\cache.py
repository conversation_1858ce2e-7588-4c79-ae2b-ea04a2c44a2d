"""
Word文档分析服务 - Redis缓存管理
"""

import redis.asyncio as aioredis
from typing import Optional

from app.core.config import settings
from app.core.logging import logger

# 全局Redis连接池对象
redis_pool: Optional[aioredis.ConnectionPool] = None
redis_connection: Optional[aioredis.Redis] = None
redis_available: bool = False


async def init_redis():
    """初始化Redis连接池（可选）"""
    global redis_pool, redis_connection, redis_available
    
    # 如果Redis URL为空，跳过初始化
    if not settings.redis.url:
        logger.info("Redis URL未配置，跳过Redis初始化")
        redis_available = False
        return
    
    try:
        logger.info(f"正在连接到Redis: {settings.redis.url}")
        redis_pool = aioredis.ConnectionPool.from_url(
            settings.redis.url,
            max_connections=settings.redis.max_connections,
            encoding=settings.redis.encoding,
            decode_responses=settings.redis.decode_responses
        )
        redis_connection = aioredis.Redis(connection_pool=redis_pool)
        
        # 测试连接
        await redis_connection.ping()
        redis_available = True
        logger.info("Redis连接成功")
        
    except Exception as e:
        logger.warning(f"Redis连接失败，将在无缓存模式下运行: {str(e)}")
        redis_available = False
        redis_connection = None
        redis_pool = None
        # 不抛出异常，允许应用继续启动


async def close_redis():
    """关闭Redis连接"""
    global redis_pool, redis_connection, redis_available
    if redis_connection:
        try:
            await redis_connection.close()
        except Exception as e:
            logger.warning(f"关闭Redis连接时出错: {str(e)}")
    if redis_pool:
        try:
            await redis_pool.disconnect()
        except Exception as e:
            logger.warning(f"断开Redis连接池时出错: {str(e)}")
    
    redis_pool = None
    redis_connection = None
    redis_available = False
    logger.info("Redis连接已关闭")


async def get_redis_connection() -> Optional[aioredis.Redis]:
    """
    获取当前的Redis连接。
    如果Redis不可用，返回None。
    """
    if not redis_available:
        return None
    
    if redis_connection is None:
        await init_redis()
    
    return redis_connection if redis_available else None


async def is_redis_available() -> bool:
    """检查Redis是否可用"""
    return redis_available


async def get_redis_status() -> dict:
    """获取Redis状态"""
    if not redis_available:
        return {
            "status": "disabled",
            "message": "Redis未启用或连接失败",
            "details": {}
        }
    
    try:
        redis_conn = await get_redis_connection()
        if redis_conn is None:
            return {
                "status": "unavailable",
                "message": "Redis连接不可用",
                "details": {}
            }
        
        # 测试连接
        ping_success = await redis_conn.ping()
        
        if ping_success:
            # 获取Redis信息
            info = await redis_conn.info()
            status = "healthy"
            message = "Redis连接正常"
            details = {
                "redis_version": info.get("redis_version"),
                "used_memory_human": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "db0_keys": info.get("db0", {}).get("keys", 0)
            }
        else:
            status = "unhealthy"
            message = "Redis ping失败"
            details = {}
        
        return {
            "status": status,
            "message": message,
            "details": details
        }
        
    except Exception as e:
        logger.error(f"Redis状态检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "message": "Redis连接失败",
            "error": str(e)
        }


# 缓存装饰器和辅助函数
async def cache_get(key: str) -> Optional[str]:
    """从缓存获取值"""
    if not redis_available:
        return None
    
    try:
        redis_conn = await get_redis_connection()
        if redis_conn:
            return await redis_conn.get(key)
    except Exception as e:
        logger.warning(f"缓存读取失败: {str(e)}")
    
    return None


async def cache_set(key: str, value: str, expire: int = 3600) -> bool:
    """设置缓存值"""
    if not redis_available:
        return False
    
    try:
        redis_conn = await get_redis_connection()
        if redis_conn:
            await redis_conn.setex(key, expire, value)
            return True
    except Exception as e:
        logger.warning(f"缓存写入失败: {str(e)}")
    
    return False


async def cache_delete(key: str) -> bool:
    """删除缓存值"""
    if not redis_available:
        return False
    
    try:
        redis_conn = await get_redis_connection()
        if redis_conn:
            await redis_conn.delete(key)
            return True
    except Exception as e:
        logger.warning(f"缓存删除失败: {str(e)}")
    
    return False 