<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen" :class="layoutClass">
    <!-- 统一使用AppNavbar组件 - 桌面端和移动端 -->
    <AppNavbar v-if="!hideNavbar" />
    
    <!-- 主要内容区域 -->
    <div 
      v-if="useContainer" 
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" 
      :class="[containerClass, { 'pb-20 md:pb-8': !hideNavbar }]"
    >
      <!-- 页面标题区域 -->
      <PageHeader 
        v-if="showHeader"
        :title="title"
        :description="description"
        :breadcrumbs="breadcrumbs"
        :mobile="isMobile"
      >
        <!-- 标题右侧操作按钮插槽 -->
        <template #actions>
          <slot name="header-actions"></slot>
        </template>
      </PageHeader>
      
      <!-- 页面内容 -->
      <slot></slot>
    </div>
    
    <!-- 不使用容器的直接内容 -->
    <template v-else>
      <div :class="{ 'pb-20 md:pb-8': !hideNavbar }">
        <slot></slot>
      </div>
    </template>
    
    <!-- 移动端底部导航栏 -->
    <MobileTabBar v-if="!hideNavbar" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useUserStore } from '@/stores/user'

interface Breadcrumb {
  text: string
  to?: string
}

interface Props {
  // 页面标题
  title?: string
  // 页面描述
  description?: string
  // 面包屑导航
  breadcrumbs?: Breadcrumb[]
  // 是否显示导航栏
  hideNavbar?: boolean
  // 是否显示页面标题
  showHeader?: boolean
  // 是否使用容器布局
  useContainer?: boolean
  // 容器的额外样式类
  containerClass?: string
  // 布局的额外样式类  
  layoutClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  hideNavbar: false,
  showHeader: true,
  useContainer: true,
  containerClass: 'py-8',
  layoutClass: ''
})

const userStore = useUserStore()

// 移动端检测
const windowWidth = ref(0)
const isMobile = computed(() => windowWidth.value < 768)

// 更新窗口宽度
const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
}

// 生命周期
onMounted(() => {
  updateWindowWidth()
  window.addEventListener('resize', updateWindowWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})
</script>

<script lang="ts">
import AppNavbar from './AppNavbar.vue'
import PageHeader from './PageHeader.vue'
import MobileTabBar from './MobileTabBar.vue'

export default {
  name: 'BaseLayout',
  components: {
    AppNavbar,
    PageHeader,
    MobileTabBar
  }
}
</script>

<style scoped>
/* 内容区域适配 */
.content-area {
  min-height: calc(100vh - theme('spacing.16')); /* 减去顶部导航栏高度 */
}

.content-area.with-mobile-nav {
  min-height: calc(100vh - theme('spacing.14') - theme('spacing.16')); /* 减去顶部和底部导航栏高度 */
}

/* 平滑的响应式过渡 */
nav {
  transition: all 0.2s ease-in-out;
}

/* 移动端优化的触摸目标 */
a,
button {
  min-height: 44px; /* 符合移动端触摸标准 */
  min-width: 44px;
}
</style> 