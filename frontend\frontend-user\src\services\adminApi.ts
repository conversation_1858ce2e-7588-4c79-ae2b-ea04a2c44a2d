import apiService from './api'
import type { User, Document, Task, PaginationParams, PaginatedResponse } from '@/types'

export interface UserManagementParams extends PaginationParams {
  status?: 'active' | 'inactive' | 'banned'
  search?: string
  registration_date_from?: string
  registration_date_to?: string
  sort_by?: 'created_at' | 'username' | 'email' | 'last_login'
  sort_order?: 'asc' | 'desc'
}

export interface SystemStats {
  users: {
    total: number
    active: number
    new_today: number
    new_this_week: number
  }
  documents: {
    total: number
    processed_today: number
    processing: number
    failed: number
    total_size: number
  }
  tasks: {
    total: number
    pending: number
    running: number
    completed_today: number
    failed_today: number
  }
  system: {
    uptime: number
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    api_response_time: number
  }
}

export interface OrderInfo {
  id: number
  user_id: number
  user: User
  amount: number
  currency: string
  status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method: string
  package_type: string
  package_quantity: number
  created_at: string
  paid_at?: string
  refunded_at?: string
}

export interface SystemConfig {
  app_name: string
  app_version: string
  max_file_size: number
  supported_formats: string[]
  analysis_options: Record<string, any>
  pricing: {
    basic_package: {
      name: string
      price: number
      quantity: number
    }
  }
  email_settings: {
    smtp_server: string
    smtp_port: number
    use_tls: boolean
  }
  rate_limits: {
    uploads_per_hour: number
    api_calls_per_minute: number
  }
}

export class AdminApi {
  /**
   * 管理员登录
   */
  async adminLogin(credentials: { username: string; password: string }): Promise<{
    access_token: string
    admin: User
  }> {
    const response = await apiService.post<{ access_token: string; admin: User }>(
      '/v1/admin/auth/login',
      credentials
    )

    // 保存管理员token
    if (response.access_token) {
      localStorage.setItem('admin_token', response.access_token)
      localStorage.setItem('admin_user', JSON.stringify(response.admin))
    }

    return response
  }

  /**
   * 管理员登出
   */
  async adminLogout(): Promise<void> {
    try {
      await apiService.post('/v1/admin/auth/logout')
    } finally {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }

  /**
   * 获取系统统计信息
   */
  async getSystemStats(): Promise<SystemStats> {
    return apiService.get<SystemStats>('/v1/admin/stats')
  }

  /**
   * 获取用户列表
   */
  async getUsers(params?: UserManagementParams): Promise<PaginatedResponse<User>> {
    const queryParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const url = `/v1/admin/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return apiService.get<PaginatedResponse<User>>(url)
  }

  /**
   * 检查管理员是否已登录
   */
  isAdminLoggedIn(): boolean {
    return !!localStorage.getItem('admin_token')
  }

  /**
   * 获取存储的管理员信息
   */
  getStoredAdmin(): User | null {
    const adminStr = localStorage.getItem('admin_user')
    return adminStr ? JSON.parse(adminStr) : null
  }
}

export const adminApi = new AdminApi()
export default adminApi 