"""
数据预处理器

负责将Word COM提取的原始数据进行预处理，转换为可分析的标准化数据结构。
不涉及Word COM操作，纯粹的数据处理。
"""

import time
from typing import Dict, Any, List
from app.core.logging import logger


class DataProcessor:
    """数据预处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.processing_stats = {
            'processed_documents': 0,
            'processing_time': 0.0
        }
    
    def preprocess_document_data(self, raw_data_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理文档原始数据
        
        Args:
            raw_data_result: Word COM提取的原始数据结果
            
        Returns:
            dict: 预处理后的数据
        """
        start_time = time.time()
        logger.info("🔄 开始预处理文档数据")
        
        try:
            if not raw_data_result.get('success', False):
                raise ValueError(f"原始数据提取失败: {raw_data_result.get('error_message', '未知错误')}")
            
            raw_data = raw_data_result.get('raw_data', {})
            
            # 1. 预处理文档信息
            processed_info = self._preprocess_document_info(raw_data.get('document_info', {}))
            
            # 2. 预处理文档内容
            processed_content = self._preprocess_document_content(raw_data.get('document_content', {}))
            
            # 3. 预处理文档结构
            processed_structure = self._preprocess_document_structure(raw_data.get('document_structure', {}))
            
            # 4. 建立数据索引
            data_index = self._build_data_index(processed_content, processed_structure)
            
            # 合并预处理结果
            processed_data = {
                'document_info': processed_info,
                'document_content': processed_content,
                'document_structure': processed_structure,
                'data_index': data_index,
                'original_file_path': raw_data.get('file_path'),
                'extraction_method': raw_data.get('extraction_method'),
                'preprocessing_time': time.time() - start_time,
                'success': True
            }
            
            self.processing_stats['processed_documents'] += 1
            self.processing_stats['processing_time'] += time.time() - start_time
            
            processing_time = time.time() - start_time
            logger.info(f"✅ 数据预处理完成，耗时: {processing_time:.3f}秒")
            
            return processed_data
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"数据预处理失败: {str(e)}"
            logger.error(f"❌ {error_msg}, 耗时: {processing_time:.3f}秒")
            
            return {
                'success': False,
                'error_message': error_msg,
                'preprocessing_time': processing_time
            }
    
    def _preprocess_document_info(self, document_info: Dict[str, Any]) -> Dict[str, Any]:
        """预处理文档基本信息"""
        logger.info("📋 预处理文档基本信息")
        
        processed_info = {
            'pages': self._safe_get_int(document_info, 'stat_pages', 0),
            'words': self._safe_get_int(document_info, 'stat_words', 0),
            'characters': self._safe_get_int(document_info, 'stat_characters', 0),
            'paragraphs': self._safe_get_int(document_info, 'stat_paragraphs', 0),
            'title': self._safe_get_str(document_info, 'title', ''),
            'author': self._safe_get_str(document_info, 'author', ''),
            'subject': self._safe_get_str(document_info, 'subject', ''),
            'keywords': self._safe_get_str(document_info, 'keywords', ''),
            'comments': self._safe_get_str(document_info, 'comments', ''),
            'creation_time': document_info.get('creation_time'),
            'last_save_time': document_info.get('last_save_time'),
            'file_name': self._safe_get_str(document_info, 'file_name', ''),
            'file_size': document_info.get('file_size', 0),
        }
        
        return processed_info
    
    def _preprocess_document_content(self, document_content: Dict[str, Any]) -> Dict[str, Any]:
        """预处理文档内容"""
        logger.info("📄 预处理文档内容")
        
        processed_content = {
            'paragraphs': self._preprocess_paragraphs(document_content.get('paragraphs', [])),
            'tables': self._preprocess_tables(document_content.get('tables', [])),
            'images': self._preprocess_images(document_content.get('images', [])),
            'headers': self._preprocess_headers(document_content.get('headers', [])),
            'footers': self._preprocess_footers(document_content.get('footers', [])),
            'footnotes': self._preprocess_footnotes(document_content.get('footnotes', [])),
            'hyperlinks': self._preprocess_hyperlinks(document_content.get('hyperlinks', [])),
        }
        
        return processed_content
    
    def _preprocess_document_structure(self, document_structure: Dict[str, Any]) -> Dict[str, Any]:
        """预处理文档结构"""
        logger.info("🏗️ 预处理文档结构")
        
        processed_structure = {
            'outline': self._preprocess_outline(document_structure.get('outline', [])),
            'document_structures': self._preprocess_document_structures(document_structure.get('document_structures', [])),
            'toc_entries': self._preprocess_toc_entries(document_structure.get('toc_entries', [])),
            'styles_used': self._preprocess_styles(document_structure.get('styles_used', [])),
            'cover_page_info': self._preprocess_cover_page(document_structure.get('cover_page_info', {})),
            'statistics': document_structure.get('statistics', {}),
            'structure_analysis_method': document_structure.get('structure_analysis_method', 'unknown')
        }
        
        return processed_structure
    
    def _build_data_index(self, content: Dict[str, Any], structure: Dict[str, Any]) -> Dict[str, Any]:
        """建立数据索引"""
        logger.info("🔍 建立数据索引")
        
        index = {
            'paragraph_count': len(content.get('paragraphs', [])),
            'table_count': len(content.get('tables', [])),
            'image_count': len(content.get('images', [])),
            'outline_count': len(structure.get('outline', [])),
            'structure_count': len(structure.get('document_structures', [])),
            'text_length': sum(len(p.get('text', '')) for p in content.get('paragraphs', [])),
            'has_toc': len(structure.get('toc_entries', [])) > 0,
            'has_cover': bool(structure.get('cover_page_info', {}).get('has_cover', False)),
        }
        
        return index
    
    def _preprocess_paragraphs(self, paragraphs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理段落数据"""
        processed = []
        for i, para in enumerate(paragraphs):
            processed_para = {
                'index': i,
                'text': self._clean_text(para.get('text', '')),
                'style': para.get('style', ''),
                'alignment': para.get('alignment', 'left'),
                'font_name': para.get('font_name', ''),
                'font_size': self._safe_get_float(para, 'font_size', 12.0),
                'is_bold': para.get('is_bold', False),
                'is_italic': para.get('is_italic', False),
                'page_number': para.get('page_number', 1),
                'level': para.get('level', 0),
                'is_heading': para.get('is_heading', False),
                'word_count': len(para.get('text', '').split()),
                'char_count': len(para.get('text', ''))
            }
            processed.append(processed_para)
        return processed
    
    def _preprocess_tables(self, tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理表格数据"""
        processed = []
        for i, table in enumerate(tables):
            processed_table = {
                'index': i,
                'rows': table.get('rows', 0),
                'columns': table.get('columns', 0),
                'page_number': table.get('page_number', 1),
                'content': table.get('content', []),
                'has_header': table.get('has_header', False),
                'cell_count': table.get('rows', 0) * table.get('columns', 0)
            }
            processed.append(processed_table)
        return processed
    
    def _preprocess_images(self, images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理图片数据"""
        processed = []
        for i, image in enumerate(images):
            processed_image = {
                'index': i,
                'width': image.get('width', 0),
                'height': image.get('height', 0),
                'page_number': image.get('page_number', 1),
                'alt_text': image.get('alt_text', ''),
                'caption': image.get('caption', ''),
                'format': image.get('format', 'unknown')
            }
            processed.append(processed_image)
        return processed
    
    def _preprocess_headers(self, headers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理页眉数据"""
        return [{'text': self._clean_text(h.get('text', '')), 'page': h.get('page', 1)} for h in headers]
    
    def _preprocess_footers(self, footers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理页脚数据"""
        return [{'text': self._clean_text(f.get('text', '')), 'page': f.get('page', 1)} for f in footers]
    
    def _preprocess_footnotes(self, footnotes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理脚注数据"""
        return [{'text': self._clean_text(fn.get('text', '')), 'reference': fn.get('reference', '')} for fn in footnotes]
    
    def _preprocess_hyperlinks(self, hyperlinks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理超链接数据"""
        return [{'text': hl.get('text', ''), 'url': hl.get('url', ''), 'page': hl.get('page', 1)} for hl in hyperlinks]
    
    def _preprocess_outline(self, outline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理大纲数据"""
        processed = []
        for item in outline:
            processed_item = {
                'text': self._clean_text(item.get('text', '')),
                'level': item.get('level', 0),
                'page': item.get('page', 1),
                'style': item.get('style', ''),
                'type': item.get('type', 'heading'),
                'paragraph_index': item.get('paragraph_index', 0)
            }
            processed.append(processed_item)
        return processed
    
    def _preprocess_document_structures(self, structures: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理文档结构数据"""
        processed = []
        for struct in structures:
            processed_struct = {
                'name': struct.get('name', ''),
                'type': struct.get('type', 'unknown'),
                'status': struct.get('status', 'unknown'),
                'page': struct.get('page', 1),
                'content': struct.get('content', {}),
                'confidence': struct.get('confidence', 0.0)
            }
            processed.append(processed_struct)
        return processed
    
    def _preprocess_toc_entries(self, toc_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理目录条目数据"""
        return [{'text': self._clean_text(entry.get('text', '')), 'page': entry.get('page', 1), 'level': entry.get('level', 0)} for entry in toc_entries]
    
    def _preprocess_styles(self, styles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理样式数据"""
        return [{'name': style.get('name', ''), 'type': style.get('type', ''), 'usage_count': style.get('usage_count', 0)} for style in styles]
    
    def _preprocess_cover_page(self, cover_info: Dict[str, Any]) -> Dict[str, Any]:
        """预处理封面信息"""
        return {
            'has_cover': cover_info.get('has_cover', False),
            'title': self._clean_text(cover_info.get('title', '')),
            'author': self._clean_text(cover_info.get('author', '')),
            'institution': self._clean_text(cover_info.get('institution', '')),
            'date': cover_info.get('date', ''),
            'cover_elements': cover_info.get('cover_elements', [])
        }
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ''
        # 移除多余的空白字符
        return ' '.join(text.split())
    
    def _safe_get_int(self, data: Dict[str, Any], key: str, default: int) -> int:
        """安全获取整数值"""
        try:
            value = data.get(key, default)
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def _safe_get_float(self, data: Dict[str, Any], key: str, default: float) -> float:
        """安全获取浮点数值"""
        try:
            value = data.get(key, default)
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def _safe_get_str(self, data: Dict[str, Any], key: str, default: str) -> str:
        """安全获取字符串值"""
        try:
            value = data.get(key, default)
            return str(value) if value is not None else default
        except (ValueError, TypeError):
            return default
