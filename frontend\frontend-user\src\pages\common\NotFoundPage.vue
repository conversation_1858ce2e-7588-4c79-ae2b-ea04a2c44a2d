<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4 text-center">
      <!-- 404图标 -->
      <div class="mx-auto h-24 w-24 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-8">
        <svg class="h-12 w-12 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.732 16c-.77.833.192 2.5 1.732 2.5z"/>
        </svg>
      </div>
      
      <!-- 404标题 -->
      <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">
        404
      </h1>
      
      <!-- 描述 -->
      <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-200 mb-4">
        页面未找到
      </h2>
      
      <p class="text-gray-600 dark:text-gray-300 mb-8">
        抱歉，您访问的页面不存在或已被移动。
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-3 sm:space-y-0 sm:space-x-3 sm:flex sm:justify-center">
        <router-link to="/" class="btn btn-primary w-full sm:w-auto">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
          </svg>
          返回首页
        </router-link>
        
        <button @click="goBack" class="btn btn-secondary w-full sm:w-auto">
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
          </svg>
          返回上页
        </button>
      </div>
      
      <!-- 常用链接 -->
      <div class="mt-12">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          或者访问这些页面：
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <router-link to="/auth" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
            用户登录
          </router-link>
          <router-link to="/pricing" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
            价格方案
          </router-link>
          <router-link to="/help" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
            帮助中心
          </router-link>
          <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
            联系支持
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回上一页
const goBack = () => {
  // 如果有历史记录则返回，否则去首页
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
/* 404页面特有样式 */
</style> 