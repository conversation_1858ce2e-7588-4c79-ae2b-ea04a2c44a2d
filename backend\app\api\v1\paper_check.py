"""
论文检测相关API接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.database import crud
from app.security import get_current_user_id
from app.core.logging import logger
from app.core.response import success_response
from app.services.problem_fragment_service import ProblemFragmentService

router = APIRouter(prefix="/paper-check", tags=["论文检测"])

# 初始化问题片段服务
fragment_service = ProblemFragmentService()


@router.get("/problem-fragments/{task_id}")
async def get_problem_fragments(
    task_id: str,
    structure: Optional[str] = Query(None, description="按结构筛选"),
    severity: Optional[str] = Query(None, description="按严重程度筛选"),
    category: Optional[str] = Query(None, description="按问题类别筛选"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取指定任务的问题片段详细信息
    
    - **task_id**: 检测任务ID
    - **structure**: 可选，按结构筛选（title/abstract/introduction等）
    - **severity**: 可选，按严重程度筛选（severe/general/suggestion）
    - **category**: 可选，按问题类别筛选（format/structure/citation等）
    - **page**: 可选，分页页码（默认1）
    - **limit**: 可选，每页数量（默认20，最大100）
    """
    try:
        # 使用问题片段服务获取真实数据
        result = await fragment_service.get_fragments_by_task(
            task_id=task_id,
            session=session,
            structure=structure,
            severity=severity,
            category=category,
            page=page,
            limit=limit
        )

        logger.info(f"获取任务 {task_id} 的问题片段: {len(result['fragments'])}/{result['total_count']}")

        return success_response(
            data=result,
            message="获取问题片段成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取问题片段失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取问题片段失败: {str(e)}")


@router.get("/problem-fragments/{task_id}/{fragment_id}")
async def get_problem_fragment_detail(
    task_id: str,
    fragment_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取单个问题片段的详细信息
    
    - **task_id**: 检测任务ID
    - **fragment_id**: 问题片段ID
    """
    try:
        # 使用问题片段服务获取详情
        fragment = await fragment_service.get_fragment_detail(
            task_id=task_id,
            fragment_id=fragment_id,
            session=session
        )

        if not fragment:
            raise HTTPException(status_code=404, detail="问题片段未找到")

        logger.info(f"获取问题片段详情: {task_id}/{fragment_id}")

        return success_response(
            data=fragment,
            message="获取问题片段详情成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取问题片段详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取问题片段详情失败: {str(e)}")


# 模拟数据生成函数已移除，现在使用真实的问题片段服务
