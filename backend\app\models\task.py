"""
Word文档分析服务 - 任务相关数据模型（优化版本）
"""

import uuid
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime
from app.models.user import UserInDB


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """任务类型枚举"""
    PAPER_CHECK = "paper_check"
    CONTENT_ANALYSIS = "content_analysis"
    FORMAT_CHECK = "format_check"
    DOCUMENT_PARSE = "document_parse"


class TaskBase(BaseModel):
    """任务基础模型"""
    filename: str = Field(..., min_length=1, max_length=255, description="原始文件名")
    file_size: int = Field(..., gt=0, le=52428800, description="文件大小（字节），最大50MB")
    analysis_options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="分析选项")
    user_id: str = Field(..., description="关联用户ID")

    @validator('filename')
    def validate_filename(cls, v):
        """验证文件名"""
        v = v.strip()
        if not v:
            raise ValueError('文件名不能为空')
        
        # 检查文件扩展名
        allowed_extensions = ['.doc', '.docx']
        if not any(v.lower().endswith(ext) for ext in allowed_extensions):
            raise ValueError(f'仅支持Word文档格式：{", ".join(allowed_extensions)}')
        
        # 检查文件名中的非法字符
        illegal_chars = ['<', '>', ':', '"', '|', '?', '*']
        if any(char in v for char in illegal_chars):
            raise ValueError(f'文件名包含非法字符：{", ".join(illegal_chars)}')
        
        return v

    @validator('file_size')
    def validate_file_size(cls, v):
        """验证文件大小"""
        if v <= 0:
            raise ValueError('文件大小必须大于0')
        if v > 52428800:  # 50MB
            raise ValueError('文件大小不能超过50MB')
        return v

    @validator('analysis_options')
    def validate_analysis_options(cls, v):
        """验证分析选项"""
        if v is None:
            return {}
        
        # 确保是字典类型
        if not isinstance(v, dict):
            raise ValueError('分析选项必须是字典类型')
        
        # 验证已知的选项键
        valid_keys = {
            'check_format', 'check_structure', 'check_citation', 
            'check_reference', 'generate_report', 'report_format',
            'detection_standard', 'standard_name'  # 新增：支持检测标准选项
        }
        
        for key in v.keys():
            if key not in valid_keys:
                # 这里可以选择严格模式或宽松模式
                pass  # 当前为宽松模式，允许未知选项
        
        return v

    class Config:
        from_attributes = True


class TaskCreate(TaskBase):
    """任务创建模型"""
    task_id: str = Field(default_factory=lambda: f"task_{uuid.uuid4().hex}", description="唯一任务ID")
    file_path: str = Field(..., min_length=1, max_length=500, description="文件存储路径")
    task_type: TaskType = Field(default=TaskType.PAPER_CHECK, description="任务类型")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    progress: int = Field(default=0, ge=0, le=100, description="任务进度")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    processing_time: Optional[int] = Field(default=None, ge=0, description="处理耗时（秒）")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(default_factory=dict, description="任务结果")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")

    @validator('file_path')
    def validate_file_path(cls, v):
        """验证文件路径"""
        v = v.strip()
        if not v:
            raise ValueError('文件路径不能为空')
        if len(v) > 500:
            raise ValueError('文件路径过长，最大500字符')
        return v

    @validator('progress')
    def validate_progress(cls, v):
        """验证进度值"""
        if not (0 <= v <= 100):
            raise ValueError('进度值必须在0-100之间')
        return v

    @validator('processing_time')
    def validate_processing_time(cls, v):
        """验证处理时间"""
        if v is not None and v < 0:
            raise ValueError('处理时间不能为负数')
        return v

    @validator('completed_at')
    def validate_completion_time(cls, v, values):
        """验证完成时间的逻辑一致性"""
        if v is not None:
            started_at = values.get('started_at')
            created_at = values.get('created_at')
            
            # 完成时间不能早于创建时间
            if created_at and v < created_at:
                raise ValueError('完成时间不能早于创建时间')
            
            # 如果有开始时间，完成时间不能早于开始时间
            if started_at and v < started_at:
                raise ValueError('完成时间不能早于开始时间')
        
        return v

    @validator('started_at')
    def validate_start_time(cls, v, values):
        """验证开始时间的逻辑一致性"""
        if v is not None:
            created_at = values.get('created_at')
            if created_at and v < created_at:
                raise ValueError('开始时间不能早于创建时间')
        return v

    @validator('status')
    def validate_status_logic(cls, v, values):
        """验证状态逻辑一致性"""
        completed_at = values.get('completed_at')
        
        # 如果状态为completed，必须有完成时间
        if v == TaskStatus.COMPLETED and completed_at is None:
            # 这里可以自动设置完成时间或抛出错误
            pass  # 暂时允许，会在数据库层面处理
        
        return v


class TaskUpdate(BaseModel):
    """任务更新模型"""
    status: Optional[TaskStatus] = None
    progress: Optional[int] = Field(default=None, ge=0, le=100)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_time: Optional[int] = Field(default=None, ge=0)
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    updated_at: Optional[datetime] = Field(default_factory=datetime.now, description="更新时间")

    @validator('progress')
    def validate_progress_update(cls, v):
        """验证更新时的进度值"""
        if v is not None and not (0 <= v <= 100):
            raise ValueError('进度值必须在0-100之间')
        return v

    @validator('processing_time')
    def validate_processing_time_update(cls, v):
        """验证更新时的处理时间"""
        if v is not None and v < 0:
            raise ValueError('处理时间不能为负数')
        return v

    @validator('error_message')
    def validate_error_message(cls, v):
        """验证错误信息"""
        if v is not None:
            v = v.strip()
            if len(v) == 0:
                return None
            if len(v) > 1000:  # 限制错误信息长度
                return v[:1000] + "..."
        return v


class TaskInDB(TaskCreate):
    """数据库中的任务模型"""
    progress: int = Field(default=0, ge=0, le=100, description="任务进度")
    started_at: Optional[datetime] = Field(default=None, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    processing_time: Optional[int] = Field(default=None, ge=0, description="处理耗时（秒）")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    result: Optional[Dict[str, Any]] = Field(default_factory=dict, description="任务结果")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")


class Task(TaskInDB):
    """API响应的任务模型"""
    result_url: Optional[str] = Field(default=None, description="分析结果的URL")
    user_id: str

    @validator('result_url', always=True)
    def generate_result_url(cls, v, values):
        """如果任务完成，则生成结果URL"""
        status = values.get('status')
        task_id = values.get('task_id')
        if status == TaskStatus.COMPLETED and task_id:
            return f"/api/v1/documents/{task_id}/result"
        return None


class TaskResponse(BaseModel):
    """返回给客户端的任务模型"""
    task_id: str
    user_id: str
    filename: str
    status: TaskStatus
    progress: int
    created_at: datetime
    completed_at: Optional[datetime] = None
    result_summary: Optional[str] = None  # 例如 "发现3个严重问题"


class TaskStatistics(BaseModel):
    """任务统计信息模型"""
    total_tasks: int = Field(..., ge=0, description="总任务数")
    pending_tasks: int = Field(..., ge=0, description="待处理任务数")
    processing_tasks: int = Field(..., ge=0, description="处理中任务数")
    completed_tasks: int = Field(..., ge=0, description="已完成任务数")
    failed_tasks: int = Field(..., ge=0, description="失败任务数")
    cancelled_tasks: int = Field(..., ge=0, description="已取消任务数")
    average_processing_time: Optional[float] = Field(None, ge=0, description="平均处理时间（秒）")
    success_rate: float = Field(..., ge=0, le=1, description="成功率")
    
    @validator('success_rate')
    def calculate_success_rate(cls, v, values):
        """计算成功率"""
        total = values.get('total_tasks', 0)
        completed = values.get('completed_tasks', 0)
        if total > 0:
            return completed / total
        return 0.0


class TaskFilter(BaseModel):
    """任务过滤器模型"""
    status: Optional[TaskStatus] = None
    task_type: Optional[TaskType] = None
    user_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    filename_pattern: Optional[str] = None
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        start_date = values.get('start_date')
        if v is not None and start_date is not None and v < start_date:
            raise ValueError('结束日期不能早于开始日期')
        return v


class TaskBatch(BaseModel):
    """批量任务操作模型"""
    task_ids: List[str] = Field(..., min_items=1, max_items=100, description="任务ID列表")
    action: str = Field(..., description="操作类型：cancel, retry, delete")
    
    @validator('task_ids')
    def validate_task_ids(cls, v):
        """验证任务ID列表"""
        if not v:
            raise ValueError('任务ID列表不能为空')
        
        # 去重
        unique_ids = list(set(v))
        if len(unique_ids) != len(v):
            v = unique_ids
        
        # 验证ID格式
        for task_id in v:
            if not task_id.startswith('task_'):
                raise ValueError(f'无效的任务ID格式: {task_id}')
        
        return v

    @validator('action')
    def validate_action(cls, v):
        """验证操作类型"""
        valid_actions = ['cancel', 'retry', 'delete']
        if v not in valid_actions:
            raise ValueError(f'无效的操作类型，支持的操作：{", ".join(valid_actions)}')
        return v 