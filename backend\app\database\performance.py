"""
Word文档分析服务 - 数据库性能优化

实现数据库层性能优化：
- 查询优化和批量操作
- 连接池智能管理
- 索引策略优化
- 事务优化
- 缓存集成
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics
from contextlib import asynccontextmanager

from sqlalchemy import text, inspect, MetaData
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session
from sqlalchemy.pool import QueuePool

from app.core.logging import logger
from app.core.config import get_settings
from app.database.connection import get_database, get_async_session
from app.core.cache import cache_get, cache_set


class QueryOptimizer:
    """查询优化器 - 智能SQL优化"""
    
    def __init__(self):
        self.settings = get_settings()
        self.slow_queries = deque(maxlen=100)
        self.query_stats = defaultdict(list)
        
    async def record_query(self, query: str, duration: float, result_count: int = 0):
        """记录查询性能"""
        query_info = {
            'query': query[:200],  # 截断长查询
            'duration': duration,
            'result_count': result_count,
            'timestamp': datetime.utcnow()
        }
        
        # 记录慢查询
        if duration > 1.0:  # 超过1秒的查询
            self.slow_queries.append(query_info)
            logger.warning(f"慢查询检测: {duration:.2f}s - {query[:100]}")
        
        # 统计查询模式
        query_pattern = self._extract_query_pattern(query)
        self.query_stats[query_pattern].append(duration)
    
    def _extract_query_pattern(self, query: str) -> str:
        """提取查询模式"""
        query_lower = query.lower().strip()
        
        if query_lower.startswith('select'):
            try:
                parts = query_lower.split()
                from_idx = parts.index('from')
                table_name = parts[from_idx + 1].split()[0]
                return f"select_from_{table_name}"
            except:
                return "select_unknown"
        elif query_lower.startswith('insert'):
            return "insert"
        elif query_lower.startswith('update'):
            return "update"
        elif query_lower.startswith('delete'):
            return "delete"
        else:
            return "other"
    
    def get_query_stats(self) -> Dict[str, Any]:
        """获取查询统计信息"""
        return {
            'slow_queries_count': len(self.slow_queries),
            'query_patterns': len(self.query_stats),
            'recent_slow_queries': [
                {
                    'query': q['query'],
                    'duration': q['duration'],
                    'timestamp': q['timestamp'].isoformat()
                }
                for q in list(self.slow_queries)[-5:]
            ]
        }


class BatchOperationOptimizer:
    """批量操作优化器"""
    
    def __init__(self):
        self.batch_size = 100
        self.batch_stats = defaultdict(int)
        
    async def batch_insert(self, table_name: str, records: List[Dict[str, Any]]) -> bool:
        """批量插入优化"""
        try:
            if not records:
                return True
            
            start_time = time.time()
            
            # 分批处理
            batch_count = 0
            for i in range(0, len(records), self.batch_size):
                batch = records[i:i + self.batch_size]
                await self._execute_batch_insert(table_name, batch)
                batch_count += 1
            
            duration = time.time() - start_time
            self.batch_stats[f'{table_name}_batch_inserts'] += batch_count
            
            logger.info(f"批量插入完成: {table_name}, {len(records)}条记录, {duration:.2f}s")
            return True
            
        except Exception as e:
            logger.error(f"批量插入失败: {table_name} - {str(e)}")
            return False
    
    async def _execute_batch_insert(self, table_name: str, records: List[Dict[str, Any]]):
        """执行批量插入"""
        async with get_async_session() as session:
            try:
                if records:
                    columns = list(records[0].keys())
                    placeholders = ', '.join([f':{col}' for col in columns])
                    query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                    
                    await session.execute(text(query), records)
                    await session.commit()
                    
            except Exception as e:
                await session.rollback()
                raise e
    
    def get_batch_stats(self) -> Dict[str, Any]:
        """获取批量操作统计"""
        return dict(self.batch_stats)


class DatabasePerformanceOptimizer:
    """数据库性能优化器 - 统一管理"""
    
    def __init__(self):
        self.query_optimizer = QueryOptimizer()
        self.batch_optimizer = BatchOperationOptimizer()
        self.monitoring_active = False
        
    async def start_monitoring(self):
        """启动数据库性能监控"""
        self.monitoring_active = True
        logger.info("数据库性能监控器已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        logger.info("数据库性能监控器已停止")
    
    @asynccontextmanager
    async def optimized_query(self, query: str):
        """优化查询上下文管理器"""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            await self.query_optimizer.record_query(query, duration)
    
    async def batch_insert_optimized(self, table_name: str, records: List[Dict[str, Any]]) -> bool:
        """优化的批量插入"""
        return await self.batch_optimizer.batch_insert(table_name, records)
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合性能统计"""
        return {
            'monitoring_active': self.monitoring_active,
            'timestamp': datetime.utcnow().isoformat(),
            'query_stats': self.query_optimizer.get_query_stats(),
            'batch_stats': self.batch_optimizer.get_batch_stats()
        }


# 全局数据库性能优化器实例
_db_optimizer: Optional[DatabasePerformanceOptimizer] = None


async def get_db_performance_optimizer() -> DatabasePerformanceOptimizer:
    """获取数据库性能优化器实例"""
    global _db_optimizer
    
    if _db_optimizer is None:
        _db_optimizer = DatabasePerformanceOptimizer()
        await _db_optimizer.start_monitoring()
    
    return _db_optimizer 