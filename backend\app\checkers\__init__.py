"""
论文检测系统模块

该模块提供完整的论文标准检测功能，包括：
- 检测规则引擎 (RuleEngine)
- 格式检测器 (FormatChecker)  
- 结构检测器 (StructureChecker)
- 问题报告生成器 (ReportGenerator)

主要功能：
- 论文格式规范检查
- 文档结构合规检测
- 引用和参考文献验证
- 格式问题识别和报告
"""

from .rule_engine import RuleEngine
from ..models.rule import CheckRule, RuleType, CheckSeverity
from ..models.check_result import CheckResult
from .format_checker import FormatChecker, FormatCheckResult, FormatIssue, FormatCheckType
from .structure_checker import StructureChecker, StructureCheckResult, StructureIssue, StructureCheckType
from .report_generator import ReportGenerator, CheckReport, IssueSeverity

__all__ = [
    # 规则引擎
    "RuleEngine",
    "CheckRule", 
    "CheckResult",
    "RuleType",
    "CheckSeverity",
    
    # 格式检测器
    "FormatChecker",
    "FormatCheckResult",
    "FormatIssue",
    "FormatCheckType",
    
    # 结构检测器
    "StructureChecker",
    "StructureCheckResult",
    "StructureIssue",
    "StructureCheckType",
    
    # 报告生成器
    "ReportGenerator",
    "CheckReport",
    "IssueSeverity",
]

__version__ = "1.0.0"
__author__ = "Word Document Analysis Service Team"