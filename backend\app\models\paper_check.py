"""
Word文档分析服务 - 论文检测相关数据模型（优化版本）
"""

import uuid
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class ComplianceStatus(str, Enum):
    """合规状态枚举"""
    COMPLIANT = "compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    NON_COMPLIANT = "non_compliant"
    UNKNOWN = "unknown"


class ProblemSeverity(str, Enum):
    """问题严重程度枚举"""
    SEVERE = "severe"
    GENERAL = "general"
    SUGGESTION = "suggestion"


class ProblemCategory(str, Enum):
    """问题类别枚举"""
    FORMAT = "format"
    STRUCTURE = "structure"
    CITATION = "citation"
    REFERENCE = "reference"
    STYLE = "style"
    CONTENT = "content"
    LAYOUT = "layout"
    TYPOGRAPHY = "typography"


class PaperCheckResultBase(BaseModel):
    """论文检测结果基础模型 - 完全匹配数据库结构"""
    paper_standard: str = Field(..., description="检测标准")
    overall_score: float = Field(..., ge=0, le=100, description="总体评分")
    compliance_status: ComplianceStatus = Field(..., description="合规状态")
    total_problems: int = Field(default=0, ge=0, description="问题总数")
    major_problems: int = Field(default=0, ge=0, description="严重问题数")
    minor_problems: int = Field(default=0, ge=0, description="一般问题数")
    detailed_results: Optional[Dict[str, Any]] = Field(default_factory=dict, description="详细检测结果")
    checked_at: datetime = Field(default_factory=datetime.now, description="检测时间")

    @validator('total_problems', always=True)
    def validate_problem_counts(cls, v, values):
        """验证问题数量的逻辑一致性"""
        major = values.get('major_problems', 0)
        minor = values.get('minor_problems', 0)
        if v != major + minor:
            raise ValueError(f"总问题数({v})必须等于严重问题数({major}) + 一般问题数({minor})")
        return v

    class Config:
        from_attributes = True


class PaperCheckResultCreate(PaperCheckResultBase):
    """论文检测结果创建模型"""
    result_id: str = Field(default_factory=lambda: f"result_{uuid.uuid4().hex}", description="结果ID")
    document_id: str = Field(..., description="关联的文档ID")


class PaperCheckResultInDB(PaperCheckResultCreate):
    """数据库中的论文检测结果模型"""
    pass


class PaperCheckResult(PaperCheckResultInDB):
    """API响应的论文检测结果模型"""
    problems: Optional[List['Problem']] = Field(default=None, description="问题列表")


class ProblemBase(BaseModel):
    """问题基础模型 - 完全匹配数据库结构"""
    category: ProblemCategory = Field(..., description="问题类别")
    problem_type: str = Field(..., description="具体问题类型")
    severity: ProblemSeverity = Field(..., description="严重程度")
    title: str = Field(..., max_length=200, description="问题标题")
    description: str = Field(..., description="问题描述")
    position: Optional[int] = Field(default=None, ge=0, description="在文档中的位置")
    range_start: Optional[int] = Field(default=None, ge=0, description="问题范围开始位置")
    range_end: Optional[int] = Field(default=None, ge=0, description="问题范围结束位置")
    page_number: Optional[int] = Field(default=None, gt=0, description="页码")
    suggestion: Optional[str] = Field(default=None, description="修复建议")
    auto_fixable: bool = Field(default=False, description="是否可自动修复")

    @validator('range_end')
    def validate_range(cls, v, values):
        """验证范围的逻辑一致性"""
        range_start = values.get('range_start')
        if v is not None and range_start is not None and v < range_start:
            raise ValueError("range_end不能小于range_start")
        return v

    class Config:
        from_attributes = True


class ProblemCreate(ProblemBase):
    """问题创建模型"""
    problem_id: str = Field(default_factory=lambda: f"prob_{uuid.uuid4().hex}", description="问题ID")
    result_id: str = Field(..., description="关联的检测结果ID")
    document_id: str = Field(..., description="关联的文档ID")  # 新增字段，与数据库匹配


class ProblemInDB(ProblemCreate):
    """数据库中的问题模型"""
    pass


class Problem(ProblemInDB):
    """API响应的问题模型"""
    pass


class ProblemFragment(BaseModel):
    """问题片段模型（用于详细问题分析）"""
    fragment_id: str = Field(..., description="片段ID")
    structure: str = Field(..., description="文档结构位置")
    category: ProblemCategory = Field(..., description="问题类别")
    severity: ProblemSeverity = Field(..., description="严重程度")
    position: int = Field(..., ge=0, description="位置")
    original_text: str = Field(..., description="原始文本")
    range_start: int = Field(..., ge=0, description="范围开始")
    range_end: int = Field(..., ge=0, description="范围结束")
    context_before: Optional[str] = Field(default=None, description="前文上下文")
    context_after: Optional[str] = Field(default=None, description="后文上下文")
    extended_context: Optional[Dict[str, str]] = Field(default=None, description="扩展上下文")
    standard_format: Optional[str] = Field(default=None, description="标准格式")
    problem_description: str = Field(..., description="问题描述")
    standard_reference: Optional[str] = Field(default=None, description="标准参考")
    standard_link: Optional[str] = Field(default=None, description="标准链接")
    problem_details: List[Dict[str, Any]] = Field(default_factory=list, description="问题详情")
    correction_preview: Optional[str] = Field(default=None, description="修正预览")
    correction_steps: List[str] = Field(default_factory=list, description="修正步骤")
    auto_fixable: bool = Field(default=False, description="是否可自动修复")
    fix_confidence: float = Field(default=0.0, ge=0, le=1, description="修复置信度")
    manual_review_required: bool = Field(default=True, description="是否需要人工审核")
    related_fragments: List[str] = Field(default_factory=list, description="相关片段ID")
    occurrence_frequency: Optional[Dict[str, int]] = Field(default=None, description="出现频率统计")

    @validator('range_end')
    def validate_fragment_range(cls, v, values):
        """验证片段范围的逻辑一致性"""
        range_start = values.get('range_start')
        if v < range_start:
            raise ValueError("range_end不能小于range_start")
        return v


class PaperStandard(BaseModel):
    """论文标准模型"""
    standard_id: str = Field(..., description="标准ID")
    name: str = Field(..., description="标准名称")
    description: str = Field(..., description="标准描述")
    version: str = Field(..., description="版本")
    rules: Dict[str, Any] = Field(..., description="规则配置")
    enabled: bool = Field(default=True, description="是否启用")


class CheckOptions(BaseModel):
    """检测选项模型"""
    standard: str = Field(default="undergraduate", description="检测标准")
    check_format: bool = Field(default=True, description="检查格式")
    check_structure: bool = Field(default=True, description="检查结构")
    check_citation: bool = Field(default=True, description="检查引用")
    check_reference: bool = Field(default=True, description="检查参考文献")
    auto_fix_suggestions: bool = Field(default=True, description="生成自动修复建议")
    generate_report: bool = Field(default=True, description="生成检测报告")
    report_format: str = Field(default="html", description="报告格式")


class ProblemStatistics(BaseModel):
    """问题统计模型"""
    total_problems: int = Field(..., ge=0, description="问题总数")
    severe_problems: int = Field(..., ge=0, description="严重问题数")
    general_problems: int = Field(..., ge=0, description="一般问题数")
    suggestion_problems: int = Field(..., ge=0, description="建议问题数")
    auto_fixable_problems: int = Field(..., ge=0, description="可自动修复问题数")
    problems_by_category: Dict[str, int] = Field(default_factory=dict, description="按类别分组的问题数")
    problems_by_page: Dict[int, int] = Field(default_factory=dict, description="按页面分组的问题数")


class CheckReport(BaseModel):
    """检测报告模型"""
    report_id: str = Field(..., description="报告ID")
    document_id: str = Field(..., description="文档ID")
    document_title: str = Field(..., description="文档标题")
    check_standard: str = Field(..., description="检测标准")
    overall_score: float = Field(..., ge=0, le=100, description="总体得分")
    compliance_status: ComplianceStatus = Field(..., description="合规状态")
    statistics: ProblemStatistics = Field(..., description="问题统计")
    problems: List[Problem] = Field(..., description="问题列表")
    recommendations: List[str] = Field(default_factory=list, description="改进建议")
    check_duration: float = Field(..., ge=0, description="检查耗时（秒）")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# 更新前向引用
PaperCheckResult.model_rebuild() 