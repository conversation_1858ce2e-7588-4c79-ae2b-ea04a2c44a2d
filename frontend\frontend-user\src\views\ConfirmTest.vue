<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          确认对话框系统测试
        </h1>
        <p class="text-gray-600 dark:text-gray-300">
          测试现代化的确认对话框组件，替代原生confirm函数
        </p>
      </div>

      <!-- 对比演示 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- 原生confirm -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            ❌ 原生 confirm
          </h2>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            原生confirm的问题：样式陈旧、阻塞界面、无法自定义
          </p>
          <button
            @click="showNativeConfirm"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            测试原生confirm
          </button>
        </div>

        <!-- 新confirm系统 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            ✅ 新确认对话框
          </h2>
          <p class="text-gray-600 dark:text-gray-300 mb-4">
            现代化设计、非阻塞、完全可定制、支持暗黑模式
          </p>
          <button
            @click="showNewConfirm"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            测试新确认对话框
          </button>
        </div>
      </div>

      <!-- 功能测试 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          功能测试
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 信息确认 -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="text-center mb-3">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white">信息确认</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">一般操作确认</p>
            </div>
            <button
              @click="showInfoConfirm"
              class="w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              测试
            </button>
          </div>

          <!-- 警告确认 -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="text-center mb-3">
              <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"/>
                </svg>
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white">警告确认</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">需要注意的操作</p>
            </div>
            <button
              @click="showWarningConfirm"
              class="w-full px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-sm"
            >
              测试
            </button>
          </div>

          <!-- 危险确认 -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="text-center mb-3">
              <div class="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
                <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"/>
                </svg>
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white">危险操作</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">不可逆删除操作</p>
            </div>
            <button
              @click="showDangerConfirm"
              class="w-full px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
            >
              测试
            </button>
          </div>

          <!-- 成功确认 -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="text-center mb-3">
              <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-2">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white">成功确认</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">积极操作确认</p>
            </div>
            <button
              @click="showSuccessConfirm"
              class="w-full px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
            >
              测试
            </button>
          </div>
        </div>
      </div>

      <!-- 使用示例 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          使用示例
        </h2>
        
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">基础用法</h3>
            <pre class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-sm overflow-x-auto"><code>import { $confirm } from '@/utils/useConfirm'

// 基础确认
const result = await $confirm.confirm('确定要删除这个文件吗？')
if (result) {
  console.log('用户确认删除')}</code></pre>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">危险操作</h3>
            <pre class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-sm overflow-x-auto"><code>// 危险操作确认
const result = await $confirm.danger('此操作将永久删除数据，无法恢复！')
if (result) {
  // 执行删除操作
}</code></pre>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">自定义选项</h3>
            <pre class="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 text-sm overflow-x-auto"><code>// 自定义标题和按钮文本
const result = await $confirm.warning('系统将重启，可能影响在线用户', {
  title: '系统维护',
  confirmText: '立即重启',
  cancelText: '稍后重启'
})</code></pre>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="lastResult !== null" class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          测试结果
        </h3>
        <p class="text-gray-600 dark:text-gray-300">
          用户选择: 
          <span :class="lastResult ? 'text-green-600' : 'text-red-600'" class="font-medium">
            {{ lastResult ? '确认' : '取消' }}
          </span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { $confirm } from '@/utils/useConfirm'
import { $notify } from '@/utils/useNotifications'

const lastResult = ref<boolean | null>(null)

// 原生confirm测试
const showNativeConfirm = () => {
  const result = confirm('这是原生confirm - 阻塞界面，样式陈旧，无法自定义')
  lastResult.value = result
  $notify.info(`原生confirm结果: ${result ? '确认' : '取消'}`)
}

// 新confirm系统测试
const showNewConfirm = async () => {
  const result = await $confirm.confirm('这是新的确认对话框 - 现代化设计，非阻塞，完全可定制！')
  lastResult.value = result
  $notify.success(`新确认对话框结果: ${result ? '确认' : '取消'}`)
}

// 信息确认测试
const showInfoConfirm = async () => {
  const result = await $confirm.info('这是一个信息确认对话框，用于一般操作确认。')
  lastResult.value = result
  $notify.info(`信息确认结果: ${result ? '确认' : '取消'}`)
}

// 警告确认测试
const showWarningConfirm = async () => {
  const result = await $confirm.warning('这是一个警告确认对话框，用于需要用户特别注意的操作。')
  lastResult.value = result
  $notify.warning(`警告确认结果: ${result ? '确认' : '取消'}`)
}

// 危险确认测试
const showDangerConfirm = async () => {
  const result = await $confirm.danger('这是一个危险操作确认对话框，用于不可逆的删除操作。')
  lastResult.value = result
  $notify.error(`危险确认结果: ${result ? '确认' : '取消'}`)
}

// 成功确认测试
const showSuccessConfirm = async () => {
  const result = await $confirm.success('这是一个成功确认对话框，用于积极的操作确认。')
  lastResult.value = result
  $notify.success(`成功确认结果: ${result ? '确认' : '取消'}`)
}
</script> 