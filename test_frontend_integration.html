<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>前端整合规则结构测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>🧪 前端整合规则结构测试</h1>
    
    <div class="test-section">
        <h2>字数要求提取测试</h2>
        <div id="requirements-test">正在测试...</div>
    </div>

    <script>
        // 模拟API服务
        class ApiService {
            async get(url) {
                const response = await fetch(`http://localhost:8000/api${url}`)
                const data = await response.json()
                return data.data
            }
        }

        const apiService = new ApiService()

        // 修改后的前端函数
        const getWordRequirementsFromStandard = (detectionStandard) => {
            const requirements = {}

            // 新架构：从document_structure中获取内容要求
            if (detectionStandard?.definitions?.document_structure) {
                const documentStructure = detectionStandard.definitions.document_structure
                
                documentStructure.forEach((section) => {
                    if (section.content_requirements) {
                        const contentReq = section.content_requirements
                        requirements[section.name] = {
                            min: contentReq.min,
                            max: contentReq.max,
                            unit: contentReq.unit
                        }
                    }
                })
                return requirements
            }

            // 向后兼容：从旧的rules.content中获取
            if (detectionStandard?.rules?.content) {
                const contentRules = detectionStandard.rules.content
                Object.entries(contentRules).forEach(([ruleKey, rule]) => {
                    const params = rule.parameters
                    if (params && params.unit && params.target_structure) {
                        requirements[params.target_structure] = {
                            min: params.min,
                            max: params.max,
                            unit: params.unit
                        }
                    }
                })
                return requirements
            }

            return requirements
        }

        // 执行测试
        async function runTest() {
            try {
                const detectionStandard = await apiService.get("/v1/system/detection-standards/hbkj_bachelor_2024")

                // 测试新的整合结构
                console.log("🔍 检测标准数据结构:", detectionStandard)

                const requirements = getWordRequirementsFromStandard(detectionStandard)

                let html = "<div class=\"success\">✅ 字数要求提取成功！<br><br>"

                // 显示提取到的要求
                if (Object.keys(requirements).length > 0) {
                    html += "📊 从新整合结构提取的要求：<br>"
                    Object.entries(requirements).forEach(([name, req]) => {
                        html += `📋 ${name}: ${req.min || ""}-${req.max || ""} ${req.unit}<br>`
                    })
                } else {
                    html += "⚠️ 未提取到任何字数要求<br>"
                }

                // 显示结构信息
                const documentStructure = detectionStandard?.definitions?.document_structure
                if (documentStructure) {
                    const sectionsWithContent = documentStructure.filter(s => s.content_requirements)
                    html += `<br>📋 总章节数: ${documentStructure.length}<br>`
                    html += `📊 有内容要求的章节: ${sectionsWithContent.length}<br>`
                }

                // 检查旧结构
                const oldContent = detectionStandard?.rules?.content
                html += `📋 旧content规则数量: ${oldContent ? Object.keys(oldContent).length : 0}<br>`

                html += "</div>"

                document.getElementById("requirements-test").innerHTML = html

            } catch (error) {
                console.error("测试错误:", error)
                document.getElementById("requirements-test").innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`
            }
        }

        window.addEventListener("load", runTest)
    </script>
</body>
</html>
