"""
手动测试问题片段API，看看是否能触发问题片段生成
"""

import requests
import json

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heibailan5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def test_fragment_api_with_force_generation(token, task_id):
    """测试问题片段API，强制生成问题片段"""
    try:
        # 先尝试正常获取
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        print(f"🔍 测试问题片段API: {url}")
        
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 20,
            "force_regenerate": True  # 尝试强制重新生成
        })
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 问题片段API调用成功!")
            print(f"📋 问题片段总数: {data['data']['total_count']}")
            
            fragments = data['data']['fragments']
            print(f"🔍 当前页片段数量: {len(fragments)}")
            
            if fragments:
                # 显示前几个问题片段的详细信息
                print("\n📌 问题片段详情:")
                for i, fragment in enumerate(fragments[:5]):
                    print(f"\n   片段 {i+1}:")
                    print(f"     ID: {fragment.get('fragment_id', 'N/A')}")
                    print(f"     结构: {fragment.get('structure', 'N/A')}")
                    print(f"     类别: {fragment.get('category', 'N/A')}")
                    print(f"     严重程度: {fragment.get('severity', 'N/A')}")
                    print(f"     位置: {fragment.get('position', 'N/A')}")
                    print(f"     原文: {fragment.get('original_text', 'N/A')[:50]}...")
                    print(f"     问题描述: {fragment.get('problem_description', 'N/A')}")
                    print(f"     标准参考: {fragment.get('standard_reference', 'N/A')}")
                    print(f"     可自动修复: {fragment.get('auto_fixable', False)}")
                    print(f"     规则ID: {fragment.get('rule_id', 'N/A')}")
                
                # 查找中文关键词相关的问题
                keywords_fragments = [f for f in fragments if "关键词" in f.get('original_text', '') or "keywords" in f.get('rule_id', '').lower()]
                
                if keywords_fragments:
                    print(f"\n🎯 中文关键词相关问题片段: {len(keywords_fragments)} 个")
                    for fragment in keywords_fragments:
                        print(f"   - {fragment.get('fragment_id')}: {fragment.get('problem_description')}")
                        if "alignment" in fragment.get('problem_description', '').lower():
                            print(f"     ✅ 发现对齐问题!")
                else:
                    print(f"\n⚠️ 未找到中文关键词相关的问题片段")
            else:
                print("\n❌ 没有问题片段")
            
            return True
        else:
            print(f"❌ 问题片段API调用失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试问题片段API失败: {str(e)}")
        return False

def test_task_status(token, task_id):
    """检查任务状态"""
    try:
        task_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(task_url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            task_data = result.get("data", {})
            
            print(f"📊 任务状态:")
            print(f"   ID: {task_data.get('task_id')}")
            print(f"   状态: {task_data.get('status')}")
            print(f"   进度: {task_data.get('progress')}%")
            print(f"   文件名: {task_data.get('filename')}")
            
            # 检查任务结果中是否有问题片段相关信息
            task_result = task_data.get("result", {})
            
            if "problem_fragments" in task_result:
                fragments = task_result["problem_fragments"]
                print(f"   问题片段数量: {len(fragments)}")
            else:
                print(f"   ❌ 任务结果中没有问题片段字段")
            
            if "fragment_count" in task_result:
                print(f"   片段计数: {task_result['fragment_count']}")
            
            if "severe_count" in task_result:
                print(f"   严重问题计数: {task_result['severe_count']}")
            
            return True
        else:
            print(f"❌ 获取任务状态失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查任务状态失败: {str(e)}")
        return False

def main():
    """主测试流程"""
    print("🚀 手动测试问题片段API")
    print("=" * 60)
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌")
        return
    
    # 使用最新的任务ID
    task_id = "task_c3345acc3e4647b1b7586acbdb10a587"
    
    print(f"🔍 测试任务: {task_id}")
    
    # 1. 检查任务状态
    print(f"\n📊 步骤1: 检查任务状态...")
    test_task_status(token, task_id)
    
    # 2. 测试问题片段API
    print(f"\n🔍 步骤2: 测试问题片段API...")
    if test_fragment_api_with_force_generation(token, task_id):
        print(f"\n🌐 前端查看: http://localhost:3000/document/{task_id}/statistics")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
