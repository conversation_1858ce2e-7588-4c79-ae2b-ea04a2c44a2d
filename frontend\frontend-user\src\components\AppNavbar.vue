<template>
  <!-- 导航栏 -->
  <nav class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <router-link to="/" class="flex items-center hover:opacity-80 transition-opacity">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-sm">W</span>
            </div>
          </div>
          <div class="ml-3">
            <span class="text-xl font-semibold text-gray-900 dark:text-white">Word分析服务</span>
          </div>
        </router-link>
        
        <!-- 导航菜单 -->
        <div class="hidden md:flex items-center space-x-8">
          <!-- 首页模式的导航菜单 -->
          <template v-if="isHomePage">
            <a href="#features" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">功能特性</a>
            <a href="#pricing" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">价格方案</a>
            <a href="#about" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">关于我们</a>
          </template>
          
          <!-- 认证页面模式不显示导航菜单 -->
          <template v-else-if="!isAuthPage">
            <router-link 
              to="/dashboard" 
              class="font-medium transition-colors"
              :class="isActiveRoute('/dashboard') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'"
            >
              仪表盘
            </router-link>
            <router-link 
              to="/upload" 
              class="transition-colors"
              :class="isActiveRoute('/upload') ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'"
            >
              上传文档
            </router-link>
            <router-link 
              to="/documents" 
              class="transition-colors"
              :class="isActiveRoute('/documents') ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'"
            >
              我的文档
            </router-link>
            <router-link 
              to="/tasks" 
              class="transition-colors"
              :class="isActiveRoute('/tasks') ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'"
            >
              任务中心
            </router-link>
          </template>
        </div>
        
        <!-- 右侧操作区域 -->
        <div class="flex items-center space-x-1 md:space-x-4 mobile-right-actions">
          <!-- 通知中心 - 仅在已登录且非首页且非认证页面时显示 -->
          <NotificationCenter v-if="userStore.isAuthenticated && !isHomePage && !isAuthPage" />
          
          <!-- 主题切换开关 -->
          <div class="hidden md:block mobile-hide theme-toggle-container">
            <button 
              @click="toggleTheme" 
              class="theme-toggle mt-1" 
              title="切换主题" 
              aria-label="切换明暗主题"
            >
            </button>
          </div>
          
          <!-- 认证页面模式的返回首页链接 -->
          <template v-if="isAuthPage">
            <router-link to="/" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">
              返回首页
            </router-link>
          </template>
          
          <!-- 未登录状态的按钮 -->
          <template v-else-if="!userStore.isAuthenticated">
            <router-link to="/auth" class="hidden md:block text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors">登录</router-link>
            <router-link to="/auth?mode=register" class="hidden md:block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">免费注册</router-link>
          </template>
          
          <!-- 已登录状态的用户菜单 -->
          <template v-else>
            <!-- 用户下拉菜单 -->
            <div class="relative" ref="userMenuRef">
              <button 
                @click="toggleUserMenu" 
                class="flex items-center space-x-1 md:space-x-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              >
                <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 dark:text-blue-400 font-medium text-sm">{{ userStore.currentUser?.username?.charAt(0) || '用' }}</span>
                </div>
                <span class="hidden md:block">{{ userStore.currentUser?.username || '用户' }}</span>
                <svg class="h-4 w-4 hidden md:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
              
              <!-- 下拉菜单 -->
              <div 
                v-show="showUserMenu" 
                class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
              >
                <div class="py-1">
                  <router-link 
                    to="/profile" 
                    @click="closeUserMenu"
                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    个人中心
                  </router-link>
                  <router-link 
                    to="/orders" 
                    @click="closeUserMenu"
                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    订单管理
                  </router-link>
                  <router-link 
                    to="/documents" 
                    @click="closeUserMenu"
                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    我的文档
                  </router-link>
                  <div class="border-t border-gray-100 dark:border-gray-600"></div>
                  <button 
                    @click="handleLogout"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    退出登录
                  </button>
                </div>
              </div>
            </div>
          </template>
          
          <!-- 移动端菜单按钮 -->
          <button 
            @click="toggleMobileMenu"
            class="md:hidden w-8 h-8 flex items-center justify-center text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 移动端菜单 -->
      <div v-show="showMobileMenu" class="md:hidden border-t border-gray-200 dark:border-gray-700">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <!-- 主题切换 - 移动端 -->
          <div class="flex items-center justify-between px-3 py-2">
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
              </svg>
              <span class="text-gray-600 dark:text-gray-300 text-base font-medium">主题模式</span>
            </div>
            <button @click="toggleTheme" class="theme-toggle" title="切换主题" aria-label="切换明暗主题">
            </button>
          </div>
          <div class="border-t border-gray-200 dark:border-gray-700 pt-1"></div>
          
          <!-- 认证页面模式的移动端菜单 -->
          <template v-if="isAuthPage">
            <router-link to="/" @click="closeMobileMenu" class="block px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">返回首页</router-link>
          </template>
          
          <!-- 首页模式的移动端菜单 -->
          <template v-else-if="isHomePage">
            <a href="#features" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
              <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
              功能特性
            </a>
            <a href="#pricing" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
              <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              价格方案
            </a>
            <a href="#about" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
              <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              关于我们
            </a>
            
            <!-- 未登录移动端菜单 -->
            <template v-if="!userStore.isAuthenticated">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-1 mt-2"></div>
              <router-link to="/auth" @click="closeMobileMenu" class="block px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">登录</router-link>
              <router-link to="/auth?mode=register" @click="closeMobileMenu" class="block px-3 py-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">免费注册</router-link>
            </template>
            
            <!-- 已登录移动端菜单 -->
            <template v-else>
              <div class="border-t border-gray-200 dark:border-gray-700 pt-1 mt-2"></div>
              <div class="flex items-center py-2 px-3 text-gray-700 dark:text-gray-200">
                <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
                  <span class="text-blue-600 dark:text-blue-400 font-medium text-sm">{{ userStore.currentUser?.username?.charAt(0) || '用' }}</span>
                </div>
                <span class="font-medium">{{ userStore.currentUser?.username || '用户' }}</span>
              </div>
              <router-link to="/dashboard" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                仪表盘
              </router-link>
              <router-link to="/upload" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                上传文档
              </router-link>
              <router-link to="/documents" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                我的文档
              </router-link>
              <router-link to="/profile" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                个人中心
              </router-link>
              <router-link to="/orders" @click="closeMobileMenu" class="flex items-center px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                <svg class="h-5 w-5 mr-3 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13a2 2 0 11-4 0 2 2 0 014 0zM9 19a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                订单管理
              </router-link>
              <div class="border-t border-gray-200 dark:border-gray-700 pt-1 mt-2"></div>
              <button @click="handleLogout(); closeMobileMenu()" class="flex items-center w-full text-left px-3 py-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300">
                <svg class="h-5 w-5 mr-3 text-red-500 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                退出登录
              </button>
            </template>
          </template>
          
          <!-- 应用内页面的移动端菜单 -->
          <template v-else>
            <router-link 
              to="/dashboard" 
              @click="closeMobileMenu"
              class="block px-3 py-2 rounded-md text-base font-medium"
              :class="isActiveRoute('/dashboard') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'"
            >
              仪表盘
            </router-link>
            <router-link 
              to="/upload" 
              @click="closeMobileMenu"
              class="block px-3 py-2 rounded-md text-base font-medium"
              :class="isActiveRoute('/upload') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'"
            >
              上传文档
            </router-link>
            <router-link 
              to="/documents" 
              @click="closeMobileMenu"
              class="block px-3 py-2 rounded-md text-base font-medium"
              :class="isActiveRoute('/documents') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'"
            >
              我的文档
            </router-link>
            <router-link 
              to="/tasks" 
              @click="closeMobileMenu"
              class="block px-3 py-2 rounded-md text-base font-medium"
              :class="isActiveRoute('/tasks') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'"
            >
              任务中心
            </router-link>
          </template>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NotificationCenter from './NotificationCenter.vue'

// Props
interface Props {
  isHomePage?: boolean
  isAuthPage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isHomePage: false,
  isAuthPage: false
})

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 状态管理
const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const userMenuRef = ref<HTMLElement>()

// 检查当前路由是否激活
const isActiveRoute = (path: string) => {
  return route.path === path || route.path.startsWith(path + '/')
}

// 主题切换
const toggleTheme = () => {
  const isDark = document.documentElement.classList.contains('dark')
  if (isDark) {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('theme', 'light')
  } else {
    document.documentElement.classList.add('dark')
    localStorage.setItem('theme', 'dark')
  }
}

// 用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
  showMobileMenu.value = false
}

const closeUserMenu = () => {
  showUserMenu.value = false
}

// 移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  showUserMenu.value = false
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 登出处理
const handleLogout = async () => {
  try {
    await userStore.logout()
    closeUserMenu()
    router.push('/auth')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

// 初始化主题
const initializeTheme = () => {
  const savedTheme = localStorage.getItem('theme')
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  
  if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
  document.addEventListener('click', handleClickOutside)
}

onMounted(() => {
  initializeTheme()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
/* 主题切换样式将使用全局样式 */

/* 移动端右侧操作区域优化 */
@media (max-width: 768px) {
  .mobile-right-actions > * + * {
    margin-left: 2px; /* 移动端间距最小化 */
  }
}
</style> 