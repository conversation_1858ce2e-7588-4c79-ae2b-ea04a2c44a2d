<template>
  <div class="space-y-6">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总文档数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalDocuments }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">检测完成</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.completedTasks }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-yellow-100 dark:bg-yellow-900/50 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">发现问题</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalIssues }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-purple-100 dark:bg-purple-900/50 rounded-full flex items-center justify-center">
              <svg class="h-4 w-4 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">剩余次数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.remainingChecks }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用趋势图表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">使用趋势</h3>
      <div class="h-64">
        <canvas ref="trendChart"></canvas>
      </div>
    </div>

    <!-- 检测类型分布 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">检测类型分布</h3>
        <div class="h-48">
          <canvas ref="typeChart"></canvas>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">问题类型统计</h3>
        <div class="space-y-3">
          <div v-for="(issue, index) in stats.issueTypes" :key="index" 
               class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div :class="[
                'h-3 w-3 rounded-full',
                issue.color
              ]"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400">{{ issue.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ issue.count }}</span>
              <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div :class="[
                  'h-2 rounded-full',
                  issue.color
                ]" :style="{ width: issue.percentage + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'

interface UserStats {
  totalDocuments: number
  completedTasks: number
  totalIssues: number
  remainingChecks: number
  issueTypes: {
    name: string
    count: number
    percentage: number
    color: string
  }[]
  monthlyUsage: {
    month: string
    documents: number
    checks: number
  }[]
}

const userStore = useUserStore()
const trendChart = ref<HTMLCanvasElement>()
const typeChart = ref<HTMLCanvasElement>()

const stats = ref<UserStats>({
  totalDocuments: 0,
  completedTasks: 0,
  totalIssues: 0,
  remainingChecks: 0,
  issueTypes: [],
  monthlyUsage: []
})

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    // 这里应该调用实际的API
    // const response = await userApi.getUserStats()
    // stats.value = response.data
    
    // 模拟数据
    stats.value = {
      totalDocuments: 24,
      completedTasks: 20,
      totalIssues: 45,
      remainingChecks: userStore.currentUser?.check_balance || 0,
      issueTypes: [
        { name: '格式问题', count: 18, percentage: 40, color: 'bg-red-500' },
        { name: '引用错误', count: 12, percentage: 27, color: 'bg-yellow-500' },
        { name: '结构问题', count: 8, percentage: 18, color: 'bg-blue-500' },
        { name: '其他问题', count: 7, percentage: 15, color: 'bg-gray-500' }
      ],
      monthlyUsage: [
        { month: '8月', documents: 5, checks: 8 },
        { month: '9月', documents: 12, checks: 15 },
        { month: '10月', documents: 8, checks: 12 },
        { month: '11月', documents: 15, checks: 18 },
        { month: '12月', documents: 24, checks: 20 }
      ]
    }
  } catch (error) {
    console.error('Failed to fetch user stats:', error)
  }
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  // 使用趋势图表
  if (trendChart.value) {
    const ctx = trendChart.value.getContext('2d')
    if (ctx) {
      // 这里可以使用 Chart.js 或其他图表库
      drawTrendChart(ctx)
    }
  }
  
  // 检测类型分布图表
  if (typeChart.value) {
    const ctx = typeChart.value.getContext('2d')
    if (ctx) {
      drawTypeChart(ctx)
    }
  }
}

// 绘制趋势图表（简单实现）
const drawTrendChart = (ctx: CanvasRenderingContext2D) => {
  const canvas = ctx.canvas
  const width = canvas.width
  const height = canvas.height
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  // 绘制简单的折线图
  ctx.strokeStyle = '#3B82F6'
  ctx.lineWidth = 2
  ctx.beginPath()
  
  const data = stats.value.monthlyUsage
  const pointWidth = width / (data.length - 1)
  
  data.forEach((item, index) => {
    const x = index * pointWidth
    const y = height - (item.documents / 30) * height
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  
  ctx.stroke()
}

// 绘制类型分布图表（简单实现）
const drawTypeChart = (ctx: CanvasRenderingContext2D) => {
  const canvas = ctx.canvas
  const width = canvas.width
  const height = canvas.height
  const centerX = width / 2
  const centerY = height / 2
  const radius = Math.min(width, height) / 2 - 20
  
  // 清空画布
  ctx.clearRect(0, 0, width, height)
  
  const colors = ['#EF4444', '#F59E0B', '#3B82F6', '#6B7280']
  let currentAngle = 0
  
  stats.value.issueTypes.forEach((issue, index) => {
    const sliceAngle = (issue.percentage / 100) * 2 * Math.PI
    
    ctx.fillStyle = colors[index]
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.closePath()
    ctx.fill()
    
    currentAngle += sliceAngle
  })
}

onMounted(async () => {
  await fetchUserStats()
  await initCharts()
})
</script>

<style scoped>
canvas {
  width: 100%;
  height: 100%;
}
</style> 