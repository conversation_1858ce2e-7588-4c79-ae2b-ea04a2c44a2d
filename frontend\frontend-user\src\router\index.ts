import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 导入布局 - 暂时不使用统一布局

// 🔒 管理员登录安全配置 - 只对登录入口进行特殊保护
const ADMIN_LOGIN_PATH = '/secure-mgmt-x7k9p2w/auth'
const ADMIN_ACCESS_TOKEN = 'wms_secure_2024'

// 验证管理员访问令牌（仅用于登录页面）
const verifyAdminAccess = (query: any) => {
  return query.token === ADMIN_ACCESS_TOKEN
}

// 用户端路由配置
const userRoutes: RouteRecordRaw[] = [
  // 首页独立路由（不使用UserLayout）
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/Home.vue'),
    meta: { title: 'Word文档分析服务 - 智能文档检测平台' },
  },
  // 用户功能页面（直接路由）
      {
        path: '/dashboard',
        name: 'dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { requiresAuth: true, title: '仪表盘 - Word文档分析服务' },
      },
      {
        path: '/documents',
        name: 'documents',
        component: () => import('@/views/Documents.vue'),
        meta: { requiresAuth: true, title: '我的文档 - Word文档分析服务' },
      },
      {
        path: '/document/:id',
        name: 'document-detail',
        component: () => import('@/views/DocumentDetail.vue'),
        meta: { requiresAuth: true, title: '文档详情 - Word文档分析服务' },
      },
      {
        path: '/document/:id/statistics',
        name: 'StatisticsReport',
        component: () => import('@/views/StatisticsReport.vue'),
        meta: { requiresAuth: true, title: '统计报告 - Word文档分析服务' },
      },
      {
        path: '/upload',
        name: 'upload',
        component: () => import('@/views/Upload.vue'),
        meta: { requiresAuth: true, title: '上传文档 - Word文档分析服务' },
      },
      {
        path: '/tasks',
        name: 'tasks',
        component: () => import('@/views/Tasks.vue'),
        meta: { requiresAuth: true, title: '任务中心 - Word文档分析服务' },
      },
      {
        path: '/pricing',
        name: 'pricing',
        component: () => import('@/views/Pricing.vue'),
        meta: { title: '价格方案 - Word文档分析服务' },
      },
      {
        path: '/orders',
        name: 'orders',
        component: () => import('@/views/Orders.vue'),
        meta: { requiresAuth: true, title: '订单管理 - Word文档分析服务' },
      },
      {
        path: '/profile',
        name: 'profile',
        component: () => import('@/views/Profile.vue'),
        meta: { requiresAuth: true, title: '个人中心 - Word文档分析服务' },
  },
  // 🔒 管理员登录入口 - 特殊安全保护
  {
    path: ADMIN_LOGIN_PATH,
    name: 'admin-login',
    component: () => import('@/views/admin/Login.vue'),
    meta: { requiresAdminToken: true, title: '管理员登录 - Word文档分析服务' },
  },
  // 📋 管理员功能页面 - 保持原有路径，但增强安全验证
  {
    path: '/admin/dashboard',
    name: 'admin-dashboard',
    component: () => import('@/views/admin/Dashboard.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '管理员仪表盘 - Word文档分析服务' },
  },
  {
    path: '/admin/users',
    name: 'admin-users',
    component: () => import('@/views/admin/Users.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '用户管理 - Word文档分析服务' },
  },
  {
    path: '/admin/documents',
    name: 'admin-documents',
    component: () => import('@/views/admin/Documents.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '文档管理 - Word文档分析服务' },
  },
  {
    path: '/admin/tasks',
    name: 'admin-tasks',
    component: () => import('@/views/admin/Tasks.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '任务监控 - Word文档分析服务' },
  },
  {
    path: '/admin/system',
    name: 'admin-system',
    component: () => import('@/views/admin/System.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '系统设置 - Word文档分析服务' },
  },
  {
    path: '/admin/reports',
    name: 'admin-reports',
    component: () => import('@/views/admin/Reports.vue'),
    meta: { requiresAdmin: true, hideIfNotLoggedIn: true, title: '报告中心 - Word文档分析服务' },
  },
  // 认证相关页面（不使用布局）
  {
    path: '/auth',
    name: 'auth',
    component: () => import('@/views/Auth.vue'),
    meta: { title: '用户登录 - Word文档分析服务' },
  },
  {
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/views/ForgotPassword.vue'),
    meta: { title: '忘记密码 - Word文档分析服务' },
  },
  {
    path: '/reset-password',
    name: 'reset-password',
    component: () => import('@/views/ResetPassword.vue'),
    meta: { title: '重置密码 - Word文档分析服务' },
  },
  // 通用页面
  {
    path: '/help',
    name: 'help',
    component: () => import('../pages/common/HelpPage.vue'),
    meta: { title: '帮助中心 - Word文档分析服务' },
  },
  {
    path: '/maintenance',
    name: 'maintenance',
    component: () => import('../pages/common/MaintenancePage.vue'),
    meta: { title: '系统维护 - Word文档分析服务' },
  },
  // 测试页面
  {
    path: '/notification-test',
    name: 'NotificationTest',
    component: () => import('@/views/NotificationTest.vue'),
    meta: { title: '通知系统测试 - Word文档分析服务' },
  },
  {
    path: '/confirm-test',
    name: 'ConfirmTest',
    component: () => import('@/views/ConfirmTest.vue'),
    meta: { title: '确认对话框测试 - Word文档分析服务' },
  },
  {
    path: '/mobile-demo',
    name: 'MobileDemo',
    component: () => import('@/views/MobileDemo.vue'),
    meta: { title: '移动端演示 - Word文档分析服务' },
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../pages/common/NotFoundPage.vue'),
    meta: { title: '页面未找到 - Word文档分析服务' },
  },
  // 重定向未知路由到404
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: userRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 如果用户有token但还没有用户信息，等待初始化完成
  if (userStore.accessToken && !userStore.currentUser && !userStore.isLoading) {
    try {
      await userStore.fetchCurrentUser()
    } catch (error) {
      // token无效，已在fetchCurrentUser中处理清除
    }
  }
  
  // 🔒 检查管理员登录页面的访问令牌（仅登录页面需要令牌）
  if (to.meta.requiresAdminToken) {
    if (!verifyAdminAccess(to.query)) {
      // 访问令牌无效，重定向到404页面（不暴露管理员入口存在）
      next({ name: '404' })
      return
    }
  }
  
  // 🛡️ 检查管理员页面访问权限
  if (to.meta.requiresAdmin) {
    const adminToken = localStorage.getItem('adminToken')
    if (!adminToken) {
      // 如果页面设置了hideIfNotLoggedIn，未登录时显示404
      if (to.meta.hideIfNotLoggedIn) {
        next({ name: '404' })
        return
      }
      // 否则重定向到管理员登录页，并保持访问令牌
      next({ 
        name: 'admin-login', 
        query: { token: ADMIN_ACCESS_TOKEN, redirect: to.fullPath } 
      })
      return
    }
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    // 保存原始目标路由
    next({ name: 'auth', query: { redirect: to.fullPath } })
    return
  }
  
  // 如果已登录用户访问认证页面，重定向到仪表盘
  if (to.name === 'auth' && userStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }
  
  // 如果已登录管理员访问管理员登录页，重定向到管理员仪表盘
  if (to.name === 'admin-login' && localStorage.getItem('adminToken')) {
    next({ name: 'admin-dashboard' })
    return
  }
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  } else {
    document.title = 'Word文档分析服务 - 智能文档检测平台'
  }
  
  next()
})

export default router
