"""
直接测试问题片段生成器
"""

import sys
import os
sys.path.append('backend')

from app.services.problem_fragment_generator import ProblemFragmentGenerator
from app.services.document_processor import DocumentData
from app.models.check_result import CheckResult, CheckSeverity

def test_fragment_generator():
    """测试问题片段生成器"""
    
    print("🔍 开始测试问题片段生成器")
    print("=" * 50)
    
    # 创建问题片段生成器
    generator = ProblemFragmentGenerator()
    
    # 创建模拟的检测结果
    check_results = [
        CheckResult(
            rule_id="structure.section_order",
            rule_name="章节顺序检查",
            passed=False,
            severity=CheckSeverity.ERROR,
            message="发现4个错误。主要问题：缺少必需章节：中文摘要; 缺少必需章节：目录; 缺少必需章节：附录等",
            details={
                "error_count": 4,
                "missing_sections": ["中文摘要", "目录", "附录"],
                "identified_sections": ["封面", "任务书", "开题报告", "正文", "参考文献", "致谢", "诚信声明", "版权声明", "中文关键词", "英文摘要", "英文关键词"]
            },
            position=None,
            metadata={}
        ),
        CheckResult(
            rule_id="format.body_text",
            rule_name="正文格式检查",
            passed=False,
            severity=CheckSeverity.WARNING,
            message="未找到正文段落",
            details={},
            position=None,
            metadata={}
        ),
        CheckResult(
            rule_id="format.headings",
            rule_name="标题格式检查",
            passed=False,
            severity=CheckSeverity.WARNING,
            message="未找到1级标题",
            details={},
            position=None,
            metadata={}
        )
    ]
    
    # 创建模拟的文档数据
    document_data = DocumentData(
        file_path="test.docx",
        doc_info={
            "title": "新媒体技术对舞蹈编导创作手法的影响研究",
            "author": "李岩",
            "pages": 36,
            "words": 18806
        },
        content_stats={
            "page_count": 36,
            "word_count": 18806,
            "paragraph_count": 243
        },
        elements=[
            {"id": "elem_1", "text": "新媒体技术对舞蹈编导创作手法的影响研究", "type": "title"},
            {"id": "elem_2", "text": "关键词：舞蹈创作；新媒体技术；编导手法；影响研究；创新应用", "type": "keywords"},
            {"id": "elem_3", "text": "Key words: dance creation; new media technology; choreographic techniques", "type": "keywords"},
            {"id": "elem_4", "text": "1.2.1 国内发展现状", "type": "heading"},
            {"id": "elem_5", "text": "正文内容段落...", "type": "paragraph"}
        ],
        paragraphs=[],
        tables=[],
        images=[]
    )
    
    try:
        print(f"📊 输入检测结果数量: {len(check_results)}")
        print(f"📊 文档数据元素数量: {len(document_data.elements)}")
        
        # 生成问题片段
        fragments = generator.generate_fragments_from_results(check_results, document_data)
        
        print(f"✅ 生成问题片段数量: {len(fragments)}")
        
        # 显示生成的问题片段
        for i, fragment in enumerate(fragments):
            print(f"\n📌 问题片段 {i+1}:")
            print(f"   ID: {fragment.fragment_id}")
            print(f"   结构: {fragment.structure}")
            print(f"   类别: {fragment.category}")
            print(f"   严重程度: {fragment.severity}")
            print(f"   位置: {fragment.position}")
            print(f"   原文: {fragment.original_text[:50]}...")
            print(f"   问题描述: {fragment.problem_description}")
            print(f"   标准参考: {fragment.standard_reference}")
            print(f"   可自动修复: {fragment.auto_fixable}")
            print(f"   规则ID: {fragment.rule_id}")
        
        # 转换为字典格式
        fragment_dicts = [fragment.to_dict() for fragment in fragments]
        
        print(f"\n📋 转换为字典格式成功，数量: {len(fragment_dicts)}")
        
        # 按结构分组统计
        structure_stats = {}
        severity_stats = {}
        
        for fragment_dict in fragment_dicts:
            structure = fragment_dict.get('structure', '未知')
            severity = fragment_dict.get('severity', '未知')
            
            structure_stats[structure] = structure_stats.get(structure, 0) + 1
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        print(f"\n📊 按结构分组统计:")
        for structure, count in structure_stats.items():
            print(f"   {structure}: {count} 个")
        
        print(f"\n📊 按严重程度统计:")
        for severity, count in severity_stats.items():
            print(f"   {severity}: {count} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"❌ 详细错误信息: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_fragment_generator()
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功!")
    else:
        print("💥 测试失败!")
