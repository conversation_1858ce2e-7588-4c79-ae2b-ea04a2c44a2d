import './assets/css/custom.css'
import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useUserStore } from './stores/user'
import { useThemeStore } from './stores/theme'
import { installConfirm } from './utils/useConfirm'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 安装确认对话框系统
installConfirm(app)

// 初始化应用状态
const userStore = useUserStore()
const themeStore = useThemeStore()

// 初始化主题
themeStore.initializeTheme()

// 初始化用户状态
userStore.initializeUser()

app.mount('#app')
