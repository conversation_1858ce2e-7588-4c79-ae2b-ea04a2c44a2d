<template>
  <div class="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
      <div class="text-center">
        <!-- 维护图标 -->
        <div class="mx-auto h-24 w-24 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-8">
          <svg class="h-12 w-12 text-yellow-600 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        
        <!-- 标题 -->
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          系统维护中
        </h1>
        
        <!-- 描述 -->
        <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
          我们正在进行系统维护升级，以为您提供更好的服务体验。
        </p>
        
        <!-- 预计完成时间 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 mb-8 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">维护信息</h3>
          <div class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
            <div class="flex justify-between">
              <span>开始时间:</span>
              <span>{{ maintenanceInfo.startTime }}</span>
            </div>
            <div class="flex justify-between">
              <span>预计完成:</span>
              <span>{{ maintenanceInfo.endTime }}</span>
            </div>
            <div class="flex justify-between">
              <span>维护类型:</span>
              <span>{{ maintenanceInfo.type }}</span>
            </div>
          </div>
        </div>
        
        <!-- 联系信息 -->
        <div class="space-y-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            如有紧急问题，请联系我们：
          </p>
          <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <a href="mailto:<EMAIL>" class="btn btn-secondary">
              <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              邮件支持
            </a>
            <a href="tel:************" class="btn btn-secondary">
              <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              ************
            </a>
          </div>
        </div>
        
        <!-- 刷新按钮 -->
        <div class="mt-8">
          <button @click="refreshPage" class="btn btn-primary">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            刷新页面
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

// 维护信息
const maintenanceInfo = reactive({
  startTime: '2024-01-20 02:00',
  endTime: '2024-01-20 06:00',
  type: '系统升级'
})

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}
</script>

<style scoped>
/* 维护页面特有样式 */
</style> 