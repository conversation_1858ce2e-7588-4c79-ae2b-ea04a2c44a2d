import requests
import json
import time
import os

def get_auth_token():
    """获取认证令牌"""
    try:
        login_url = "http://localhost:8000/api/v1/auth/login"
        login_data = {
            "username": "8966097",
            "password": "heiba<PERSON>n5112"
        }
        
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get("data", {}).get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取认证令牌失败: {str(e)}")
        return None

def upload_test_document(token):
    """上传测试文档"""
    try:
        upload_url = "http://localhost:8000/api/v1/documents/upload"
        headers = {"Authorization": f"Bearer {token}"}
        
        # 检查测试文档是否存在
        test_doc_path = "docs/test_center_keywords.docx"
        if not os.path.exists(test_doc_path):
            print(f"❌ 测试文档不存在: {test_doc_path}")
            return None

        with open(test_doc_path, 'rb') as f:
            files = {"file": ("test_center_keywords.docx", f, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")}
            data = {"document_type": "thesis"}
            
            response = requests.post(upload_url, headers=headers, files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            task_id = result.get("data", {}).get("task_id")
            print(f"✅ 文档上传成功: {task_id}")
            return task_id
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上传文档失败: {str(e)}")
        return None

def wait_for_task_completion(token, task_id, max_wait_time=120):
    """等待任务完成"""
    try:
        task_url = f"http://localhost:8000/api/v1/tasks/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            response = requests.get(task_url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                task_data = result.get("data", {})
                status = task_data.get("status")
                progress = task_data.get("progress", 0)
                
                print(f"📊 任务状态: {status} ({progress}%)")
                
                if status == "completed":
                    print("✅ 任务完成")
                    return True
                elif status == "failed":
                    print("❌ 任务失败")
                    error_message = task_data.get("error_message", "未知错误")
                    print(f"错误信息: {error_message}")
                    return False
                else:
                    print("⏳ 等待任务完成...")
                    time.sleep(5)
            else:
                print(f"❌ 获取任务状态失败: {response.status_code}")
                return False
        
        print("⏰ 等待超时")
        return False
        
    except Exception as e:
        print(f"❌ 等待任务完成失败: {str(e)}")
        return False

def check_chinese_keywords_rule(token, task_id):
    """检查中文关键词规则"""
    try:
        # 获取问题片段
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        print(f"🔍 检查中文关键词规则: {url}")
        
        response = requests.get(url, headers=headers, params={
            "structure": "中文关键词",  # 筛选中文关键词相关的问题
            "page": 1,
            "limit": 50
        })
        
        if response.status_code == 200:
            data = response.json()
            fragments = data['data']['fragments']
            
            print(f"📋 中文关键词相关问题片段数量: {len(fragments)}")
            
            # 查找中文关键词格式相关的问题
            keywords_format_issues = []
            for fragment in fragments:
                if "关键词" in fragment.get('original_text', '') or \
                   "chinese_keywords" in fragment.get('rule_id', ''):
                    keywords_format_issues.append(fragment)
            
            if keywords_format_issues:
                print(f"🚨 发现 {len(keywords_format_issues)} 个中文关键词格式问题:")
                
                for i, issue in enumerate(keywords_format_issues):
                    print(f"\n📌 问题 {i+1}:")
                    print(f"   片段ID: {issue.get('fragment_id', 'N/A')}")
                    print(f"   规则ID: {issue.get('rule_id', 'N/A')}")
                    print(f"   严重程度: {issue.get('severity', 'N/A')}")
                    print(f"   原文: {issue.get('original_text', 'N/A')}")
                    print(f"   问题描述: {issue.get('problem_description', 'N/A')}")
                    print(f"   标准要求: {issue.get('standard_reference', 'N/A')}")
                    print(f"   可自动修复: {issue.get('auto_fixable', False)}")
                
                # 检查是否有左对齐相关的问题
                alignment_issues = [issue for issue in keywords_format_issues 
                                  if "对齐" in issue.get('problem_description', '') or 
                                     "alignment" in issue.get('rule_id', '')]
                
                if alignment_issues:
                    print(f"\n✅ 成功检测到中文关键词对齐问题: {len(alignment_issues)} 个")
                    return True
                else:
                    print(f"\n⚠️ 未检测到中文关键词对齐问题，可能文档格式已符合要求")
                    return True
            else:
                print("✅ 未发现中文关键词格式问题，文档格式符合要求")
                return True
                
        else:
            print(f"❌ 获取问题片段失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 检查中文关键词规则失败: {str(e)}")
        return False

def get_all_problem_fragments(token, task_id):
    """获取所有问题片段，用于调试"""
    try:
        url = f"http://localhost:8000/api/v1/paper-check/problem-fragments/{task_id}"
        headers = {"Authorization": f"Bearer {token}"}
        
        response = requests.get(url, headers=headers, params={
            "page": 1,
            "limit": 100
        })
        
        if response.status_code == 200:
            data = response.json()
            fragments = data['data']['fragments']
            
            print(f"\n📊 所有问题片段统计:")
            print(f"   总数: {data['data']['total_count']}")
            
            # 按结构分组
            structure_stats = {}
            rule_stats = {}
            
            for fragment in fragments:
                structure = fragment.get('structure', '未知')
                rule_id = fragment.get('rule_id', '未知')
                
                structure_stats[structure] = structure_stats.get(structure, 0) + 1
                rule_stats[rule_id] = rule_stats.get(rule_id, 0) + 1
            
            print(f"\n📊 按结构分组:")
            for structure, count in sorted(structure_stats.items()):
                print(f"   {structure}: {count} 个")
            
            print(f"\n📊 按规则分组:")
            for rule_id, count in sorted(rule_stats.items()):
                print(f"   {rule_id}: {count} 个")
            
            # 显示前几个问题片段
            print(f"\n📌 前5个问题片段:")
            for i, fragment in enumerate(fragments[:5]):
                print(f"\n   片段 {i+1}:")
                print(f"     ID: {fragment.get('fragment_id', 'N/A')}")
                print(f"     结构: {fragment.get('structure', 'N/A')}")
                print(f"     规则: {fragment.get('rule_id', 'N/A')}")
                print(f"     严重程度: {fragment.get('severity', 'N/A')}")
                print(f"     原文: {fragment.get('original_text', 'N/A')[:50]}...")
                print(f"     问题: {fragment.get('problem_description', 'N/A')}")
            
            return True
        else:
            print(f"❌ 获取问题片段失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取问题片段失败: {str(e)}")
        return False

def main():
    """主测试流程"""
    print("🚀 开始测试中文关键词左对齐规则")
    print("=" * 60)
    
    # 1. 获取认证令牌
    print("🔐 步骤1: 获取认证令牌...")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        return
    
    # 2. 上传测试文档
    print("\n📄 步骤2: 上传测试文档...")
    task_id = upload_test_document(token)
    if not task_id:
        print("❌ 无法上传文档，测试终止")
        return
    
    # 3. 等待任务完成
    print("\n⏳ 步骤3: 等待任务完成...")
    if not wait_for_task_completion(token, task_id):
        print("❌ 任务未完成，但继续检查问题片段...")
    
    # 4. 获取所有问题片段（调试用）
    print("\n🔍 步骤4: 获取所有问题片段...")
    get_all_problem_fragments(token, task_id)
    
    # 5. 检查中文关键词规则
    print("\n🎯 步骤5: 检查中文关键词左对齐规则...")
    if check_chinese_keywords_rule(token, task_id):
        print(f"\n🌐 前端查看: http://localhost:3000/document/{task_id}/statistics")
        print("✅ 测试完成！请在前端页面查看问题片段板块中的中文关键词相关问题。")
    else:
        print("\n❌ 中文关键词规则检查失败")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
