# 河北科技学院学士学位论文检测标准说明

## 标准概述

**标准名称**: 河北科技学院学士学位论文检测标准  
**标准代码**: HBKJ-Bachelor-2024  
**版本**: 2024  
**文件路径**: `backend/config/rules/hbkj_bachelor_2024.json`

## 标准特点

### 1. 完整的论文结构要求
河北科技学院的检测标准包含了学士学位论文的完整结构要求，包括：
- 封面
- 毕业设计任务书
- 开题报告
- 诚信声明
- 版权声明
- 中文摘要和关键词
- 英文摘要和关键词
- 目录
- 正文
- 参考文献
- 致谢
- 附录

### 2. 严格的格式要求
该标准对论文格式有严格的要求：
- **页面设置**: A4纸张，上下边距2.5cm，左边距3.0cm，右边距2.0cm
- **正文格式**: 中文宋体，英文Times New Roman，小四号字体，1.5倍行距，首行缩进2字符
- **标题格式**: 
  - 一级标题：黑体，三号，居中，加粗
  - 二级标题：黑体，四号，左对齐，加粗
  - 三级标题：黑体，小四号，左对齐，加粗

### 3. 特色内容要求
- **任务书要求**: 必须包含完整的学生信息、指导教师信息和任务内容
- **开题报告要求**: 必须包含研究背景、文献综述、研究方案和进度安排，字数2000-5000字
- **摘要要求**: 中文摘要300-500字，关键词3-5个

## 重构后的规则结构

### 规则分类

#### 1. 结构检查规则 (hbkj_structure)
- `hbkj_structure_001_cover` - 封面信息检查
- `hbkj_structure_002_task_book` - 毕业设计任务书检查
- `hbkj_structure_003_proposal_report` - 开题报告检查
- `hbkj_structure_004_integrity_statement` - 诚信声明检查
- `hbkj_structure_005_copyright_statement` - 版权声明检查
- `hbkj_structure_006_abstract_complete` - 摘要和关键词完整性检查
- `hbkj_structure_007_table_of_contents` - 目录检查
- `hbkj_structure_008_main_body` - 正文内容检查
- `hbkj_structure_009_references` - 参考文献检查
- `hbkj_structure_010_acknowledgments` - 致谢检查
- `hbkj_structure_011_appendix` - 附录检查

#### 2. 格式检查规则 (hbkj_format)
- `hbkj_format_001_page_setup` - 页面设置检查
- `hbkj_format_002_body_text` - 正文格式检查
- `hbkj_format_003_level_1_title` - 一级标题格式检查
- `hbkj_format_004_level_2_title` - 二级标题格式检查
- `hbkj_format_005_level_3_title` - 三级标题格式检查
- `hbkj_format_006_header_footer` - 页眉页脚格式检查
- `hbkj_format_007_chinese_abstract` - 中文摘要格式检查
- `hbkj_format_008_english_abstract` - 英文摘要格式检查
- `hbkj_format_009_references_format` - 参考文献格式检查

#### 3. 顺序检查规则 (hbkj_order)
- `hbkj_order_001_section_order` - 论文章节顺序检查

### 规则严重等级
- **error**: 必须修复的错误（如缺少封面、任务书、开题报告等）
- **warning**: 需要注意的问题（如页面设置、摘要格式等）
- **info**: 建议性问题（如致谢、附录等）

## 系统集成

### 1. 标准配置
在 `backend/config/paper_standards.json` 中添加了 `hbkj_bachelor` 检查配置：

```json
{
  "name": "河北科技学院本科论文检查",
  "description": "适用于河北科技学院学士学位论文的检查标准",
  "rule_file": "hbkj_bachelor_2024.json",
  "rule_groups": ["hbkj_structure", "hbkj_format", "hbkj_order"],
  "word_count": {
    "min": 8000,
    "max": 15000
  },
  "reference_count": {
    "min": 10,
    "max": 30
  }
}
```

### 2. 规则组配置
添加了三个规则组：
- `hbkj_structure`: 结构检查规则
- `hbkj_format`: 格式检查规则
- `hbkj_order`: 顺序检查规则

### 3. 检查函数映射
每个规则都指定了对应的检查函数名称，如：
- `check_cover_elements` - 封面信息检查
- `check_required_section` - 必需章节检查
- `check_headings_by_level` - 标题级别检查
- `check_text_format` - 文本格式检查
- `check_page_setup` - 页面设置检查

## 使用方法

### 1. 在后端代码中使用
```python
from app.checkers.rule_engine import RuleEngine

# 创建规则引擎实例
rule_engine = RuleEngine()

# 加载河北科技学院标准
rule_engine.load_standard("hbkj_bachelor_2024")

# 执行检查
results = rule_engine.check_document(document)
```

### 2. 在前端选择标准
用户可以在前端界面中选择"河北科技学院本科论文检查"标准进行检测。

## 与其他标准的区别

### 与GB/T 7713.1-2006的区别
- 增加了任务书和开题报告的检查要求
- 增加了诚信声明和版权声明的检查
- 更加严格的格式要求
- 特定的页面设置要求

### 与GB/T 7714-2015的区别
- 不仅检查参考文献格式，还检查整体论文结构
- 包含完整的论文写作规范
- 针对学士学位论文的特殊要求

## 扩展性

该标准设计具有良好的扩展性：
1. 可以轻松添加新的检查规则
2. 支持自定义检查函数
3. 可以调整规则的严重等级
4. 支持规则的启用/禁用

## 注意事项

1. **规则ID命名**: 使用 `hbkj_` 前缀以避免与其他标准冲突
2. **参数设置**: 所有关键参数都在 `parameters` 中定义，便于调整
3. **错误信息**: 每个规则都提供了详细的错误描述信息
4. **标签分类**: 使用标签对规则进行分类，便于管理和查找

## 维护和更新

1. 定期检查标准是否需要更新
2. 根据学校要求调整检查参数
3. 添加新的检查规则时保持命名一致性
4. 更新时注意向后兼容性

---

**创建时间**: 2024-12-19  
**最后更新**: 2024-12-19  
**维护者**: 系统管理员 