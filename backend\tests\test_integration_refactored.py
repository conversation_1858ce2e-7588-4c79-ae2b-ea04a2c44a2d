"""
重构后检测引擎的端到端集成测试

该测试文件验证整个检测流程的集成，包括：
1. 文档处理 -> 规则引擎 -> 检查函数的完整流程
2. 真实文档的检测
3. 性能测试
4. 错误恢复测试
"""

import pytest
import asyncio
import time
from pathlib import Path
from unittest.mock import Mock, patch

from app.checkers.rule_engine import RuleEngine
from app.services.document_analyzer import CHECK_FUNCTIONS
from app.services.document_processor import DocumentProcessor, DocumentData
from app.models.check_result import CheckResult, CheckSeverity


class TestEndToEndIntegration:
    """端到端集成测试"""

    @pytest.mark.asyncio
    async def test_full_detection_pipeline_with_real_rule_file(self, sample_document_data):
        """测试使用真实规则文件的完整检测流程"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        # 初始化规则引擎
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(rule_file)
        
        # 执行完整检测
        results = await rule_engine.execute_check(sample_document_data)
        
        # 验证结果
        assert isinstance(results, list)
        assert len(results) > 0
        assert all(isinstance(r, CheckResult) for r in results)
        
        # 验证每个结果都有必需的字段
        for result in results:
            assert result.rule_id is not None
            assert result.rule_name is not None
            assert isinstance(result.passed, bool)
            assert isinstance(result.severity, CheckSeverity)
            assert isinstance(result.message, str)

    @pytest.mark.asyncio
    async def test_detection_with_real_document(self, test_docx_path):
        """测试使用真实文档的检测"""
        if not test_docx_path or not Path(test_docx_path).exists():
            pytest.skip("测试文档不存在")
        
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        # 初始化组件
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(rule_file)
        
        # 创建模拟文档数据（因为真实的文档处理器可能需要Word服务）
        mock_document_data = DocumentData(
            file_path=test_docx_path,
            content={
                "text": "这是一个测试文档的内容。包含多个段落和标题。",
                "paragraphs": [
                    {"text": "第一章 引言", "style": {"font_family": "黑体", "font_size": 16}},
                    {"text": "这是正文内容。", "style": {"font_family": "宋体", "font_size": 12}},
                    {"text": "摘要", "style": {"font_family": "宋体", "font_size": 12}},
                    {"text": "这是摘要内容，包含了论文的主要观点和结论。", "style": {"font_family": "宋体", "font_size": 12}}
                ],
                "headings": [
                    {"text": "第一章 引言", "level": 1, "style": {"font_family": "黑体", "font_size": 16}},
                    {"text": "1.1 研究背景", "level": 2, "style": {"font_family": "黑体", "font_size": 14}}
                ]
            },
            metadata={
                "page_count": 10,
                "word_count": 5000,
                "creation_time": "2024-01-01T00:00:00"
            }
        )
        
        # 执行检测
        results = await rule_engine.execute_check(mock_document_data)
        
        # 验证结果
        assert isinstance(results, list)
        assert len(results) > 0
        
        # 统计结果
        passed_count = sum(1 for r in results if r.passed)
        failed_count = len(results) - passed_count
        
        print(f"\n检测结果统计:")
        print(f"总规则数: {len(results)}")
        print(f"通过: {passed_count}")
        print(f"失败: {failed_count}")
        
        # 显示失败的规则（如果有）
        failed_rules = [r for r in results if not r.passed]
        if failed_rules:
            print(f"\n失败的规则:")
            for rule in failed_rules:
                print(f"- {rule.rule_name}: {rule.message}")

    @pytest.mark.asyncio
    async def test_execution_plan_phases(self):
        """测试执行计划的阶段性执行"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(rule_file)
        
        # 验证执行计划存在且有多个阶段
        assert len(rule_engine.execution_plan) > 0
        
        # 验证每个阶段的结构
        for phase in rule_engine.execution_plan:
            assert "phase" in phase
            assert "description" in phase
            assert "rules" in phase
            assert isinstance(phase["rules"], list)
            
            # 验证规则引用格式
            for rule_ref in phase["rules"]:
                assert "$ref" in rule_ref
                assert rule_ref["$ref"].startswith("#/rules/")

    @pytest.mark.asyncio
    async def test_error_recovery_during_execution(self, sample_document_data):
        """测试执行过程中的错误恢复"""
        # 创建一个包含有问题规则的配置
        problematic_config = {
            "metadata": {"standard_id": "test", "name": "test", "version": "1.0.0"},
            "definitions": {},
            "rules": {
                "test": {
                    "good_rule": {
                        "name": "正常规则",
                        "check_function": "check_text_format",
                        "parameters": {}
                    },
                    "bad_rule": {
                        "name": "有问题的规则",
                        "check_function": "nonexistent_function",
                        "parameters": {}
                    },
                    "another_good_rule": {
                        "name": "另一个正常规则",
                        "check_function": "check_page_setup",
                        "parameters": {}
                    }
                }
            },
            "execution_plan": [
                {
                    "phase": "test_phase",
                    "description": "测试阶段",
                    "rules": [
                        {"$ref": "#/rules/test/good_rule"},
                        {"$ref": "#/rules/test/bad_rule"},
                        {"$ref": "#/rules/test/another_good_rule"}
                    ]
                }
            ]
        }
        
        import tempfile
        import json
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(problematic_config, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            rule_engine = RuleEngine(CHECK_FUNCTIONS)
            rule_engine.load_rules_from_file(temp_file)
            
            # 执行检测，应该能处理错误并继续执行其他规则
            results = await rule_engine.execute_check(sample_document_data)
            
            # 验证结果
            assert len(results) == 3  # 所有规则都应该有结果
            
            # 验证错误规则返回了错误结果
            bad_rule_result = next((r for r in results if "有问题的规则" in r.rule_name), None)
            assert bad_rule_result is not None
            assert not bad_rule_result.passed
            assert bad_rule_result.severity == CheckSeverity.ERROR
            
            # 验证正常规则仍然执行
            good_rule_results = [r for r in results if r.rule_name in ["正常规则", "另一个正常规则"]]
            assert len(good_rule_results) == 2
            
        finally:
            Path(temp_file).unlink()


class TestPerformance:
    """性能测试"""

    @pytest.mark.asyncio
    async def test_rule_execution_performance(self, sample_document_data):
        """测试规则执行性能"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(rule_file)
        
        # 执行检测并测量时间
        start_time = time.perf_counter()
        results = await rule_engine.execute_check(sample_document_data)
        end_time = time.perf_counter()
        
        execution_time = end_time - start_time
        
        # 验证性能指标
        assert execution_time < 10.0, f"检测执行时间过长: {execution_time:.2f}秒"
        assert len(results) > 0
        
        # 验证每个规则的执行时间
        for result in results:
            if hasattr(result, 'execution_time') and result.execution_time is not None:
                assert result.execution_time < 5.0, f"单个规则执行时间过长: {result.execution_time:.2f}秒"
        
        print(f"\n性能测试结果:")
        print(f"总执行时间: {execution_time:.3f}秒")
        print(f"规则数量: {len(results)}")
        print(f"平均每规则: {execution_time/len(results):.3f}秒")

    @pytest.mark.asyncio
    async def test_ref_resolution_performance(self):
        """测试引用解析性能"""
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        
        # 测量规则加载时间（包含引用解析）
        start_time = time.perf_counter()
        rule_engine.load_rules_from_file(rule_file)
        end_time = time.perf_counter()
        
        loading_time = end_time - start_time
        
        # 验证加载性能
        assert loading_time < 5.0, f"规则加载时间过长: {loading_time:.2f}秒"
        
        print(f"\n引用解析性能测试结果:")
        print(f"规则加载时间: {loading_time:.3f}秒")
        print(f"加载的规则数: {len(rule_engine.rules)}")


class TestRegressionTests:
    """回归测试"""

    @pytest.mark.asyncio
    async def test_backward_compatibility(self, sample_document_data):
        """测试向后兼容性"""
        # 确保重构后的引擎能产生与重构前一致的结果
        rule_file = "config/rules/hbkj_bachelor_2024.json"
        
        if not Path(rule_file).exists():
            pytest.skip(f"规则文件不存在: {rule_file}")
        
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        rule_engine.load_rules_from_file(rule_file)
        
        results = await rule_engine.execute_check(sample_document_data)
        
        # 验证结果结构与预期一致
        assert isinstance(results, list)
        assert all(isinstance(r, CheckResult) for r in results)
        
        # 验证关键规则存在
        rule_names = [r.rule_name for r in results]
        expected_rule_types = ["结构", "格式", "内容"]
        
        # 至少应该有这些类型的规则
        has_structure_rules = any("结构" in name or "章节" in name or "顺序" in name for name in rule_names)
        has_format_rules = any("格式" in name or "标题" in name or "字体" in name for name in rule_names)
        has_content_rules = any("内容" in name or "字数" in name or "数量" in name for name in rule_names)
        
        assert has_structure_rules, "缺少结构检查规则"
        assert has_format_rules, "缺少格式检查规则"
        assert has_content_rules, "缺少内容检查规则"

    def test_rule_engine_statistics(self):
        """测试规则引擎统计功能"""
        rule_engine = RuleEngine(CHECK_FUNCTIONS)
        
        # 验证初始统计
        assert rule_engine.execution_stats["total_rules_executed"] == 0
        assert rule_engine.execution_stats["total_execution_time"] == 0.0
        assert rule_engine.execution_stats["rules_passed"] == 0
        assert rule_engine.execution_stats["rules_failed"] == 0
