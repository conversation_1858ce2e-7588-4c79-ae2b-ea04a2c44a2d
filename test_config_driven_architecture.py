#!/usr/bin/env python3
"""
测试配置驱动架构：验证无需修改代码即可添加新规则
"""

import requests
import json

def test_config_driven_architecture():
    """测试配置驱动架构的可扩展性"""
    
    print("🔍 测试配置驱动架构")
    print("=" * 60)
    
    try:
        # 1. 测试API是否返回新增的版权声明规则
        print("1️⃣ 测试API规则配置...")
        response = requests.get('http://localhost:8000/api/v1/system/detection-standards/hbkj_bachelor_2024')
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            return False
            
        api_data = response.json()['data']
        content_rules = api_data.get('rules', {}).get('content', {})
        
        # 检查是否包含版权声明规则
        copyright_rule = content_rules.get('copyright_statement_word_count')
        
        if not copyright_rule:
            print("❌ 版权声明规则缺失！")
            return False
            
        print("✅ API规则配置正确")
        print(f"   版权声明: {copyright_rule['parameters']['min']}-{copyright_rule['parameters']['max']}{copyright_rule['parameters']['unit']}")
        print(f"   目标结构: {copyright_rule['parameters']['target_structure']}")
        print(f"   统计方法: {copyright_rule['parameters']['count_method']}")
        
        # 2. 验证配置驱动的解析逻辑
        print("\n2️⃣ 验证配置驱动的解析逻辑...")
        
        requirements = {}
        
        # 🔥 新的配置驱动逻辑：无需硬编码映射
        for rule_key, rule_config in content_rules.items():
            params = rule_config.get('parameters', {})
            if params and params.get('unit') and params.get('target_structure'):
                # 直接使用配置中的target_structure
                structure_name = params['target_structure']
                
                requirements[structure_name] = {
                    'min': params.get('min'),
                    'max': params.get('max'),
                    'unit': params.get('unit'),
                    'count_method': params.get('count_method'),
                    'rule_name': rule_config.get('name')
                }
        
        print("✅ 配置驱动解析成功")
        print("📋 解析出的所有规则:")
        for name, req in requirements.items():
            if req['min'] and req['max']:
                requirement_text = f"{req['min']}-{req['max']}{req['unit']}"
            elif req['min']:
                requirement_text = f"≥{req['min']}{req['unit']}"
            else:
                requirement_text = "-"
            print(f"   {name}: {requirement_text} (方法: {req['count_method']})")
        
        # 3. 验证版权声明规则是否正确解析
        print("\n3️⃣ 验证版权声明规则...")
        
        copyright_req = requirements.get('版权声明')
        if not copyright_req:
            print("❌ 版权声明规则解析失败！")
            return False
            
        print("✅ 版权声明规则解析成功")
        print(f"   标准要求: {copyright_req['min']}-{copyright_req['max']}{copyright_req['unit']}")
        print(f"   统计方法: {copyright_req['count_method']}")
        print(f"   规则名称: {copyright_req['rule_name']}")
        
        # 4. 模拟字数分析功能
        print("\n4️⃣ 模拟字数分析功能...")
        
        # 模拟文档结构数据（包含版权声明）
        mock_structures = [
            {'name': '中文摘要', 'content': {'word_count': 355}, 'count': '355字'},
            {'name': '英文摘要', 'content': {'word_count': 194}, 'count': '194词'},
            {'name': '中文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '英文关键词', 'content': {'word_count': 5}, 'count': '5个'},
            {'name': '版权声明', 'content': {'word_count': 120}, 'count': '120字'},  # 新增
            {'name': '参考文献', 'content': {'word_count': 14}, 'count': '中文11条外文3条'}
        ]
        
        analysis_results = []
        
        for structure in mock_structures:
            name = structure['name']
            current_count = structure['content']['word_count']
            count_display = structure['count']
            
            # 获取标准要求
            requirement = requirements.get(name)
            
            if requirement:
                min_val = requirement['min']
                max_val = requirement['max']
                unit = requirement['unit']
                
                # 生成标准要求显示
                if min_val and max_val:
                    standard_req = f"{min_val}-{max_val}{unit}"
                elif min_val:
                    standard_req = f"≥{min_val}{unit}"
                else:
                    standard_req = "-"
                
                # 分析结果
                if min_val and max_val:
                    if min_val <= current_count <= max_val:
                        result = "✅ 达标"
                    elif current_count < min_val:
                        result = "⚠️ 不足"
                    else:
                        result = "⚠️ 过多"
                elif min_val:
                    result = "✅ 达标" if current_count >= min_val else "⚠️ 不足"
                else:
                    result = "无要求"
            else:
                standard_req = "-"
                result = "无要求"
            
            analysis_results.append({
                'structure': name,
                'standard_requirement': standard_req,
                'current_situation': count_display,
                'analysis_result': result
            })
        
        # 5. 显示完整的字数分析结果
        print("\n5️⃣ 字数分析结果表格:")
        print("-" * 80)
        print(f"{'结构名称':<12} {'标准要求':<15} {'当前情况':<15} {'分析结果':<10}")
        print("-" * 80)
        
        for result in analysis_results:
            print(f"{result['structure']:<12} {result['standard_requirement']:<15} {result['current_situation']:<15} {result['analysis_result']:<10}")
        
        # 6. 验证版权声明分析结果
        print("\n6️⃣ 验证版权声明分析结果...")
        
        copyright_result = next((r for r in analysis_results if r['structure'] == '版权声明'), None)
        
        if not copyright_result:
            print("❌ 版权声明分析结果缺失！")
            return False
            
        print("✅ 版权声明分析成功")
        print(f"   标准要求: {copyright_result['standard_requirement']}")
        print(f"   当前情况: {copyright_result['current_situation']}")
        print(f"   分析结果: {copyright_result['analysis_result']}")
        
        # 验证具体数值
        if (copyright_result['standard_requirement'] == '50-200字' and
            copyright_result['current_situation'] == '120字' and
            copyright_result['analysis_result'] == '✅ 达标'):
            print("   ✅ 版权声明分析逻辑正确")
        else:
            print("   ❌ 版权声明分析逻辑错误")
            return False
        
        print("\n🎉 配置驱动架构测试通过！")
        
        # 7. 架构优势总结
        print("\n📋 配置驱动架构优势:")
        print("✅ 无需修改后端代码：新增规则只需修改JSON配置文件")
        print("✅ 无需修改前端代码：自动解析target_structure字段")
        print("✅ 统一数据源：前后端都从同一个规则文件读取")
        print("✅ 灵活扩展：支持多种统计方法和单位")
        print("✅ 类型安全：通过count_method参数指定统计逻辑")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_driven_architecture()
    exit(0 if success else 1)
