from fastapi import APIRouter, Depends, HTTPException, Body
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.payment import Order, PaymentInfo, PaymentMethod
from app.services.payment_service import payment_service
from app.database.session import get_db
from app.security import get_current_user_id
from app.core.response import success_response, error_response
from app.core.logging import logger

router = APIRouter()

class CreateOrderRequest(BaseModel):
    plan_id: str
    payment_method: PaymentMethod

# 模拟套餐价格
PLAN_PRICES = {
    "basic": 9.0,
    "standard": 25.0,
    "professional": 99.0,
}

@router.post("/create-order", response_model=dict)
async def create_payment_order(
    request: CreateOrderRequest,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    为当前登录用户创建一个支付订单
    """
    try:
        amount = PLAN_PRICES.get(request.plan_id)
        if not amount:
            return error_response(code=400, message="无效的套餐ID")

        order = await payment_service.create_order(
            session,
            user_id=user_id,
            plan_id=request.plan_id,
            amount=amount,
            payment_method=request.payment_method
        )

        # 模拟返回一个支付二维码URL
        qr_code_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=order:{order.order_id}"
        
        payment_info = PaymentInfo(
            order_id=order.order_id,
            qr_code_url=qr_code_url,
            message="订单已创建，请扫描二维码完成支付"
        )
        
        logger.info(f"用户 {user_id} 创建订单成功: {order.order_id}")
        return success_response(data=payment_info.model_dump(), message="订单创建成功")
        
    except Exception as e:
        logger.error(f"创建支付订单失败: {str(e)}, 用户: {user_id}")
        return error_response(code=500, message="创建订单失败，请稍后再试")


@router.get("/order/{order_id}", response_model=dict)
async def get_order_status(
    order_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取指定订单的状态
    """
    try:
        order = await payment_service.get_order_status(session, order_id)
        if not order:
            return error_response(code=404, message="订单未找到")

        # 安全检查：确保用户只能查询自己的订单
        if order.user_id != user_id:
            return error_response(code=403, message="无权访问此订单")
        
        return success_response(data=order.model_dump(), message="获取订单状态成功")
        
    except Exception as e:
        logger.error(f"获取订单状态失败: {str(e)}, 订单ID: {order_id}")
        return error_response(code=500, message="获取订单状态失败")


@router.post("/order/{order_id}/cancel", response_model=dict)
async def cancel_order(
    order_id: str,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    取消订单（仅限待支付状态）
    """
    try:
        order = await payment_service.cancel_order(session, order_id, user_id)
        if not order:
            return error_response(code=404, message="订单未找到或无法取消")
        
        return success_response(data=order.model_dump(), message="订单取消成功")
        
    except Exception as e:
        logger.error(f"取消订单失败: {str(e)}, 订单ID: {order_id}")
        return error_response(code=400, message=str(e))


@router.get("/", response_model=dict)
async def get_payment_history(
    skip: int = 0,
    limit: int = 20,
    status: str = None,
    search: str = None,
    date_range: str = None,
    user_id: str = Depends(get_current_user_id),
    session: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的支付历史（支持筛选）
    """
    try:
        orders = await payment_service.get_user_orders(session, user_id, skip, limit, status, search, date_range)
        
        # 转换为字典格式
        orders_data = [order.model_dump() for order in orders]
        
        # 获取总数（用于分页）
        total_count = await payment_service.get_user_orders_count(session, user_id, status, search, date_range)
        
        return success_response(data={
            "orders": orders_data,
            "total": total_count,
            "skip": skip,
            "limit": limit
        }, message="获取支付历史成功")
        
    except Exception as e:
        logger.error(f"获取支付历史失败: {str(e)}, 用户: {user_id}")
        return error_response(code=500, message="获取支付历史失败")


@router.get("/plans", response_model=dict)
async def get_payment_plans():
    """
    获取可用的支付套餐信息
    """
    try:
        plans = []
        for plan_id, price in PLAN_PRICES.items():
            from app.services.payment_service import PLAN_CHECKS
            checks = PLAN_CHECKS.get(plan_id, 0)
            
            plans.append({
                "plan_id": plan_id,
                "name": {
                    "basic": "基础套餐",
                    "standard": "标准套餐", 
                    "professional": "专业套餐"
                }.get(plan_id, plan_id),
                "price": price,
                "checks": checks,
                "description": f"包含 {checks} 次检测"
            })
        
        return success_response(data={"plans": plans}, message="获取套餐信息成功")
        
    except Exception as e:
        logger.error(f"获取套餐信息失败: {str(e)}")
        return error_response(code=500, message="获取套餐信息失败") 