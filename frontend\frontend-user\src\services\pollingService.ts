import { taskApi, type TaskProgress } from './index'

export interface PollingOptions {
  interval?: number
  maxRetries?: number
  backoffFactor?: number
  onError?: (error: any) => void
  stopCondition?: (data: any) => boolean
}

export interface PollingController {
  start: () => void
  stop: () => void
  isRunning: () => boolean
  getStatus: () => 'idle' | 'running' | 'paused' | 'error'
}

export class PollingService {
  private activePollers: Map<string, PollingController> = new Map()

  /**
   * 轮询单个任务状态
   */
  pollTaskStatus(
    taskId: string,
    onUpdate: (status: TaskProgress) => void,
    options: PollingOptions = {}
  ): PollingController {
    const {
      interval = 2000,
      maxRetries = 10,
      backoffFactor = 1.5,
      onError,
      stopCondition = (status) => status.status === 'completed' || status.status === 'failed'
    } = options

    let isRunning = false
    let isPaused = false
    let retryCount = 0
    let timeoutId: NodeJS.Timeout | null = null
    let currentInterval = interval
    let status: 'idle' | 'running' | 'paused' | 'error' = 'idle'

    const poll = async () => {
      if (!isRunning || isPaused) return

      try {
        const taskStatus = await taskApi.getTaskStatus(taskId)
        onUpdate(taskStatus)
        
        // 重置重试计数
        retryCount = 0
        currentInterval = interval
        status = 'running'

        // 检查是否应该停止轮询
        if (stopCondition(taskStatus)) {
          controller.stop()
          return
        }

        // 继续轮询
        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, currentInterval)
        }
      } catch (error) {
        retryCount++
        status = 'error'
        
        if (onError) {
          onError(error)
        }

        // 如果超过最大重试次数，停止轮询
        if (retryCount >= maxRetries) {
          console.error(`Max retries reached for task ${taskId}`)
          controller.stop()
          return
        }

        // 使用指数退避策略
        currentInterval = Math.min(currentInterval * backoffFactor, 30000)
        
        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, currentInterval)
        }
      }
    }

    const controller: PollingController = {
      start: () => {
        if (isRunning) return
        isRunning = true
        isPaused = false
        status = 'running'
        retryCount = 0
        currentInterval = interval
        poll()
      },
      
      stop: () => {
        isRunning = false
        isPaused = false
        status = 'idle'
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
        this.activePollers.delete(taskId)
      },
      
      isRunning: () => isRunning && !isPaused,
      
      getStatus: () => status
    }

    this.activePollers.set(taskId, controller)
    return controller
  }

  /**
   * 轮询多个任务状态
   */
  pollMultipleTasksStatus(
    taskIds: string[],
    onUpdate: (statuses: Record<string, TaskProgress>) => void,
    options: PollingOptions = {}
  ): PollingController {
    const {
      interval = 3000,
      maxRetries = 10,
      onError,
             stopCondition = (statuses: Record<string, TaskProgress>) => {
         return Object.values(statuses).every(
           status => status.status === 'completed' || status.status === 'failed'
         )
       }
    } = options

    let isRunning = false
    let isPaused = false
    let retryCount = 0
    let timeoutId: NodeJS.Timeout | null = null
    let status: 'idle' | 'running' | 'paused' | 'error' = 'idle'

    const poll = async () => {
      if (!isRunning || isPaused) return

      try {
        const statusPromises = taskIds.map(async (taskId) => {
          try {
            const taskStatus = await taskApi.getTaskStatus(taskId)
            return { taskId, status: taskStatus }
          } catch (error) {
            return { taskId, error }
          }
        })

        const results = await Promise.all(statusPromises)
        const statuses: Record<string, TaskProgress> = {}
        
        results.forEach(({ taskId, status: taskStatus, error }) => {
          if (taskStatus && !error) {
            statuses[taskId] = taskStatus
          }
        })

        onUpdate(statuses)
        
        // 重置重试计数
        retryCount = 0
        status = 'running'

        // 检查是否应该停止轮询
        if (stopCondition(statuses)) {
          controller.stop()
          return
        }

        // 继续轮询
        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, interval)
        }
      } catch (error) {
        retryCount++
        status = 'error'
        
        if (onError) {
          onError(error)
        }

        // 如果超过最大重试次数，停止轮询
        if (retryCount >= maxRetries) {
          console.error(`Max retries reached for multiple tasks polling`)
          controller.stop()
          return
        }

        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, interval * Math.pow(1.5, retryCount))
        }
      }
    }

    const pollerId = `multi_${taskIds.join('_')}`
    
    const controller: PollingController = {
      start: () => {
        if (isRunning) return
        isRunning = true
        isPaused = false
        status = 'running'
        retryCount = 0
        poll()
      },
      
      stop: () => {
        isRunning = false
        isPaused = false
        status = 'idle'
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
        this.activePollers.delete(pollerId)
      },
      
      isRunning: () => isRunning && !isPaused,
      
      getStatus: () => status
    }

    this.activePollers.set(pollerId, controller)
    return controller
  }

  /**
   * 通用轮询函数
   */
  poll<T>(
    fetcher: () => Promise<T>,
    onUpdate: (data: T) => void,
    options: PollingOptions = {}
  ): PollingController {
    const {
      interval = 1000,
      maxRetries = 5,
      backoffFactor = 1.5,
      onError,
      stopCondition = () => false
    } = options

    let isRunning = false
    let isPaused = false
    let retryCount = 0
    let timeoutId: NodeJS.Timeout | null = null
    let currentInterval = interval
    let status: 'idle' | 'running' | 'paused' | 'error' = 'idle'

    const poll = async () => {
      if (!isRunning || isPaused) return

      try {
        const data = await fetcher()
        onUpdate(data)
        
        // 重置重试计数
        retryCount = 0
        currentInterval = interval
        status = 'running'

        // 检查是否应该停止轮询
        if (stopCondition(data)) {
          controller.stop()
          return
        }

        // 继续轮询
        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, currentInterval)
        }
      } catch (error) {
        retryCount++
        status = 'error'
        
        if (onError) {
          onError(error)
        }

        // 如果超过最大重试次数，停止轮询
        if (retryCount >= maxRetries) {
          console.error(`Max retries reached for polling`)
          controller.stop()
          return
        }

        // 使用指数退避策略
        currentInterval = Math.min(currentInterval * backoffFactor, 30000)
        
        if (isRunning && !isPaused) {
          timeoutId = setTimeout(poll, currentInterval)
        }
      }
    }

    const controller: PollingController = {
      start: () => {
        if (isRunning) return
        isRunning = true
        isPaused = false
        status = 'running'
        retryCount = 0
        currentInterval = interval
        poll()
      },
      
      stop: () => {
        isRunning = false
        isPaused = false
        status = 'idle'
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
      },
      
      isRunning: () => isRunning && !isPaused,
      
      getStatus: () => status
    }

    return controller
  }

  /**
   * 停止所有活跃的轮询
   */
  stopAll(): void {
    this.activePollers.forEach(controller => {
      controller.stop()
    })
    this.activePollers.clear()
  }

  /**
   * 获取活跃轮询数量
   */
  getActiveCount(): number {
    return this.activePollers.size
  }

  /**
   * 获取特定轮询的状态
   */
  getPollerStatus(id: string): string | null {
    const controller = this.activePollers.get(id)
    return controller ? controller.getStatus() : null
  }

  /**
   * 暂停所有轮询
   */
  pauseAll(): void {
    // 由于当前实现的简单性，暂停功能将通过停止轮询实现
    // 在更复杂的实现中，可以添加真正的暂停/恢复功能
    this.stopAll()
  }

  /**
   * 轮询文档统计信息
   */
  pollDocumentStats(
    onUpdate: (stats: any) => void,
    options: PollingOptions = {}
  ): PollingController {
    return this.poll(
      () => import('./documentApi').then(module => module.documentApi.getDocumentStats()),
      onUpdate,
      { interval: 10000, ...options }
    )
  }

  /**
   * 轮询任务统计信息
   */
  pollTaskStats(
    onUpdate: (stats: any) => void,
    options: PollingOptions = {}
  ): PollingController {
    return this.poll(
      () => import('./taskApi').then(module => module.taskApi.getTaskStats()),
      onUpdate,
      { interval: 5000, ...options }
    )
  }

  /**
   * 轮询系统健康状态
   */
  pollSystemHealth(
    onUpdate: (health: any) => void,
    options: PollingOptions = {}
  ): PollingController {
    return this.poll(
      () => import('./api').then(module => module.default.healthCheck()),
      onUpdate,
      { interval: 30000, ...options }
    )
  }
}

// 创建全局轮询服务实例
export const pollingService = new PollingService()

// 便捷函数：创建任务状态轮询
export function createTaskStatusPoller(
  taskId: string,
  onUpdate: (status: TaskProgress) => void,
  options?: PollingOptions
): PollingController {
  return pollingService.pollTaskStatus(taskId, onUpdate, options)
}

// 便捷函数：创建多任务状态轮询
export function createMultiTaskStatusPoller(
  taskIds: string[],
  onUpdate: (statuses: Record<string, TaskProgress>) => void,
  options?: PollingOptions
): PollingController {
  return pollingService.pollMultipleTasksStatus(taskIds, onUpdate, options)
}

export default pollingService 