"""
直接测试中文关键词规则检查
"""

import sys
import os
sys.path.append('backend')

from app.services.document_analyzer import check_paragraph_format
from app.services.document_processor import DocumentData
from app.models.check_result import CheckResult, CheckSeverity

def test_direct_rule_check():
    """直接测试中文关键词规则"""
    
    print("🔍 直接测试中文关键词规则")
    print("=" * 50)
    
    # 创建模拟的文档数据，包含居中对齐的中文关键词
    elements = [
        {
            "type": "paragraph",
            "text": "关键词：舞蹈创作；虚拟现实（VR）；增强现实（AR）；短视频传播；编导创作手法",
            "style": {
                "alignment": "center",  # 居中对齐（应该被检测为错误）
                "font_size": 12.0,
                "font_family": "宋体",
                "bold": False
            },
            "alignment": "center",
            "font_size": 12.0,
            "is_bold": False
        },
        {
            "type": "paragraph", 
            "text": "关键词：正确的左对齐关键词",
            "style": {
                "alignment": "left",  # 左对齐（正确）
                "font_size": 12.0,
                "font_family": "宋体", 
                "bold": False
            },
            "alignment": "left",
            "font_size": 12.0,
            "is_bold": False
        }
    ]
    
    document_data = DocumentData(
        file_path="test.docx",
        doc_info={},
        content_stats={},
        elements=elements,
        paragraphs=[],
        tables=[],
        images=[]
    )
    
    # 测试规则参数（从规则文件中复制）
    params = {
        "rule_id": "format.chinese_keywords_format",
        "rule_name": "中文关键词格式检查",
        "style": {
            "font_family": {"value": "宋体"},
            "font_size": {"value": "小四", "point_size": 12},
            "alignment": {"value": "left"},
            "bold": {"value": False}
        },
        "pattern": "^关键词[：:].*",
        "errorMessage": "中文关键词应左对齐，格式为：关键词：词1；词2；词3"
    }
    
    print(f"📊 测试数据:")
    print(f"   元素数量: {len(elements)}")
    print(f"   规则模式: {params['pattern']}")
    print(f"   期望对齐: {params['style']['alignment']['value']}")
    
    # 执行检查
    try:
        result = check_paragraph_format(document_data, params)
        
        print(f"\n✅ 检查完成:")
        print(f"   规则ID: {result.rule_id}")
        print(f"   规则名称: {result.rule_name}")
        print(f"   检查通过: {result.passed}")
        print(f"   严重程度: {result.severity}")
        print(f"   消息: {result.message}")
        
        if hasattr(result, 'details') and result.details:
            print(f"   详细信息: {result.details}")
        
        # 分析结果
        if not result.passed:
            print(f"\n🎉 成功检测到问题!")
            if "alignment" in result.message:
                print(f"✅ 对齐问题被正确检测")
            else:
                print(f"⚠️ 检测到其他问题，但不是对齐问题")
        else:
            print(f"\n❌ 未检测到问题，可能规则有误")
        
        return result
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None

def test_alignment_mapping():
    """测试对齐方式映射"""
    print(f"\n🔍 测试对齐方式映射:")
    
    # Word对齐常量映射
    alignment_map = {
        0: 'left',
        1: 'center', 
        2: 'right',
        3: 'justify'
    }
    
    print(f"   Word常量映射: {alignment_map}")
    
    # 测试不同的对齐值
    test_values = [0, 1, 2, 3, "left", "center", "right", "justify"]
    
    for value in test_values:
        if isinstance(value, int):
            mapped = alignment_map.get(value, 'left')
            print(f"   {value} -> {mapped}")
        else:
            print(f"   {value} (字符串)")

if __name__ == "__main__":
    test_alignment_mapping()
    result = test_direct_rule_check()
    
    print("\n" + "=" * 50)
    if result and not result.passed:
        print("🎉 测试成功！规则能够检测到中文关键词对齐问题。")
    else:
        print("💥 测试失败！规则未能检测到问题。")
