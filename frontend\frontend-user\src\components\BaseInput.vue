<template>
  <div class="form-group">
    <!-- 标签 -->
    <label v-if="label" :for="inputId" :class="[
      'form-label',
      error ? 'text-red-600 dark:text-red-400' : 'text-gray-700 dark:text-gray-300'
    ]">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>
    
    <!-- 输入框容器 -->
    <div :class="inputContainerClass">
      <!-- 前置图标或内容 (select 类型不支持) -->
      <div v-if="!isSelect && (slots.prepend || prependIcon)" class="input-addon input-addon-left">
        <slot name="prepend">
          <svg v-if="prependIcon" class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="prependIcon"/>
          </svg>
        </slot>
      </div>
      
      <!-- Select 下拉选择框 -->
      <select
        v-if="isSelect"
        :id="inputId"
        :name="name"
        :disabled="disabled"
        :required="required"
        :class="inputClass"
        :value="modelValue"
        @change="handleSelectChange"
        @blur="handleBlur"
        @focus="handleFocus"
      >
        <option v-for="option in options" :key="option.value" :value="option.value" :disabled="option.disabled">
          {{ option.label }}
        </option>
      </select>
      
      <!-- Textarea 多行输入框 -->
      <textarea
        v-else-if="isTextarea"
        :id="inputId"
        :name="name"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :rows="rows"
        :class="inputClass"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        v-bind="$attrs"
      ></textarea>
      
      <!-- Input 单行输入框 -->
      <input
        v-else
        :id="inputId"
        :name="name"
        :type="actualType"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="inputClass"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        v-bind="$attrs"
      />
      
      <!-- 后置图标或内容 -->
      <div v-if="!isSelect && (slots.append || appendIcon || showPassword || error)" class="input-addon input-addon-right">
        <slot name="append">
          <!-- 错误图标 -->
          <svg 
            v-if="error && !showPassword && !appendIcon" 
            class="h-5 w-5 text-red-400 dark:text-red-500" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          
          <!-- 密码显示/隐藏按钮 -->
          <button 
            v-else-if="showPassword"
            type="button" 
            @click="togglePasswordVisibility"
            class="focus:outline-none"
          >
            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path v-if="isPasswordVisible" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m6.878-6.878L21 3"/>
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
          
          <!-- 其他后置图标 -->
          <svg v-else-if="appendIcon" class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="appendIcon"/>
          </svg>
        </slot>
      </div>
    </div>
    
    <!-- 帮助文本 -->
    <div v-if="help" class="form-help">{{ help }}</div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="form-error">{{ error }}</div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'BaseInput',
  inheritAttrs: false
}
</script>

<script setup lang="ts">
import { computed, ref, useSlots } from 'vue'

interface Option {
  value: string | number
  label: string
  disabled?: boolean
}

interface Props {
  // v-model 值
  modelValue?: string | number
  // 输入框类型
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'textarea' | 'select'
  // 标签文本
  label?: string
  // 占位符
  placeholder?: string
  // 帮助文本
  help?: string
  // 错误信息
  error?: string
  // 名称属性
  name?: string
  // 是否必填
  required?: boolean
  // 是否禁用
  disabled?: boolean
  // 是否只读
  readonly?: boolean
  // 前置图标路径
  prependIcon?: string
  // 后置图标路径
  appendIcon?: string
  // textarea行数
  rows?: number
  // 输入框大小
  size?: 'sm' | 'md' | 'lg'
  // select的选项
  options?: Option[]
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  size: 'md',
  rows: 3,
  options: () => []
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'change': [event: Event]
  'blur': [event: Event]
  'focus': [event: Event]
}>()

// 获取插槽
const slots = useSlots()

// 密码可见性控制
const isPasswordVisible = ref(false)
const showPassword = computed(() => props.type === 'password')

// 判断类型
const isSelect = computed(() => props.type === 'select')
const isTextarea = computed(() => props.type === 'textarea')

// 生成唯一ID
const inputId = computed(() => props.name || `input-${Math.random().toString(36).substr(2, 9)}`)

// 计算实际输入类型
const actualType = computed(() => {
  if (props.type === 'password') {
    return isPasswordVisible.value ? 'text' : 'password'
  }
  return props.type
})

// 输入框容器样式
const inputContainerClass = computed(() => {
  const classes = ['relative']
  if (props.disabled) classes.push('opacity-50')
  return classes.join(' ')
})

// 输入框样式
const inputClass = computed(() => {
  const classes = ['form-input']
  
  if (isSelect.value) {
    classes.push('pr-10'); // 为下拉箭头留出空间
  }
  
  // 大小样式
  if (props.size === 'sm') classes.push('text-sm py-2')
  else if (props.size === 'lg') classes.push('text-lg py-3')
  
  // 前置内容的左边距
  if (!isSelect.value && (props.prependIcon || slots.prepend)) classes.push('pl-10')
  
  // 后置内容的右边距
  if (!isSelect.value && (props.appendIcon || showPassword.value || props.error || slots.append)) classes.push('pr-10')
  
  // 错误状态 - 增强视觉效果
  if (props.error) {
    classes.push('border-red-400 dark:border-red-500 bg-red-50 dark:bg-red-900/20 focus:border-red-500 focus:ring-red-500 focus:ring-2 error-shake')
  }
  
  return classes.join(' ')
})

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleSelectChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  emit('update:modelValue', target.value)
  emit('change', event) // 转发原生的change事件
}

const handleBlur = (event: Event) => {
  emit('blur', event)
}

const handleFocus = (event: Event) => {
  emit('focus', event)
}

const togglePasswordVisibility = () => {
  isPasswordVisible.value = !isPasswordVisible.value
}
</script>

<style scoped>
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium mb-2 transition-colors duration-200;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm 
         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
         dark:focus:ring-blue-400 dark:focus:border-blue-400
         transition-colors duration-200;
}

/* 有前置图标时的左边距 */
.form-input.pl-10 {
  padding-left: 2.5rem !important;
}

select.form-input {
  @apply appearance-none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.dark select.form-input {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d1d5db' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

.form-input:disabled {
  @apply bg-gray-100 dark:bg-gray-800 cursor-not-allowed;
}

.form-help {
  @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
}

.form-error {
  @apply mt-1 text-sm text-red-600 dark:text-red-400;
}

.input-addon {
  @apply absolute top-0 bottom-0 flex items-center z-10;
}

.input-addon-left {
  @apply left-0 pointer-events-none;
  width: 2.5rem;
  display: flex;
  justify-content: center;
  padding-left: 0;
}

.input-addon-right {
  @apply right-0 pr-3;
}

/* 密码切换按钮不是 point-events-none */
.input-addon-right button {
  pointer-events: auto;
}

/* 错误状态动画效果 */
.error-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* 错误状态额外样式优化 */
.form-input.error-shake {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}
</style> 