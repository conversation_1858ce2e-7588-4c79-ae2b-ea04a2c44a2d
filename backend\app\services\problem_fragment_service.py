"""
问题片段服务

提供问题片段的生成、存储、查询和管理功能。
"""

from typing import Dict, Any, List, Optional
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from ..models.check_result import CheckResult
from ..services.document_processor import DocumentData
from ..services.problem_fragment_generator import ProblemFragmentGenerator, ProblemFragment
from ..database import crud

logger = structlog.get_logger(__name__)


class ProblemFragmentService:
    """问题片段服务"""
    
    def __init__(self):
        self.fragment_generator = ProblemFragmentGenerator()
    
    async def generate_and_store_fragments(
        self,
        task_id: str,
        check_results: List[CheckResult],
        document_data: DocumentData,
        session: AsyncSession
    ) -> List[Dict[str, Any]]:
        """
        生成并存储问题片段
        
        Args:
            task_id: 任务ID
            check_results: 检测结果列表
            document_data: 文档数据
            session: 数据库会话
            
        Returns:
            问题片段字典列表
        """
        try:
            # 生成问题片段
            fragments = self.fragment_generator.generate_fragments_from_results(
                check_results, document_data
            )
            
            logger.info(f"为任务 {task_id} 生成了 {len(fragments)} 个问题片段")
            
            # 转换为字典格式
            fragment_dicts = [fragment.to_dict() for fragment in fragments]
            
            # 存储到任务结果中（可以考虑单独的表存储）
            await self._store_fragments_to_task(task_id, fragment_dicts, session)
            
            return fragment_dicts
            
        except Exception as e:
            logger.error(f"生成问题片段失败: {str(e)}", task_id=task_id)
            raise
    
    async def get_fragments_by_task(
        self,
        task_id: str,
        session: AsyncSession,
        structure: Optional[str] = None,
        severity: Optional[str] = None,
        category: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        获取任务的问题片段
        
        Args:
            task_id: 任务ID
            session: 数据库会话
            structure: 结构筛选
            severity: 严重程度筛选
            category: 类别筛选
            page: 页码
            limit: 每页数量
            
        Returns:
            包含问题片段和分页信息的字典
        """
        try:
            # 从数据库获取任务
            task = await crud.get_task(session, task_id)
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            # 从任务结果中获取问题片段
            fragments = await self._get_fragments_from_task(task, session)
            
            # 应用筛选条件
            filtered_fragments = self._apply_filters(
                fragments, structure, severity, category
            )
            
            # 分页处理
            total_count = len(filtered_fragments)
            skip = (page - 1) * limit
            paginated_fragments = filtered_fragments[skip:skip + limit]
            
            logger.info(f"获取任务 {task_id} 的问题片段: {len(paginated_fragments)}/{total_count}")
            
            return {
                "total_count": total_count,
                "page": page,
                "limit": limit,
                "fragments": paginated_fragments
            }
            
        except Exception as e:
            logger.error(f"获取问题片段失败: {str(e)}", task_id=task_id)
            raise
    
    async def get_fragment_detail(
        self,
        task_id: str,
        fragment_id: str,
        session: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        获取问题片段详情
        
        Args:
            task_id: 任务ID
            fragment_id: 片段ID
            session: 数据库会话
            
        Returns:
            问题片段详情字典
        """
        try:
            # 获取所有片段
            result = await self.get_fragments_by_task(task_id, session, limit=1000)
            fragments = result.get("fragments", [])
            
            # 查找指定片段
            for fragment in fragments:
                if fragment.get("fragment_id") == fragment_id:
                    # 添加详细信息
                    enhanced_fragment = self._enhance_fragment_detail(fragment)
                    return enhanced_fragment
            
            return None
            
        except Exception as e:
            logger.error(f"获取问题片段详情失败: {str(e)}", task_id=task_id, fragment_id=fragment_id)
            raise
    
    async def _store_fragments_to_task(
        self,
        task_id: str,
        fragments: List[Dict[str, Any]],
        session: AsyncSession
    ):
        """将问题片段存储到任务结果中"""
        try:
            task = await crud.get_task(session, task_id)
            if not task:
                raise ValueError(f"任务 {task_id} 不存在")
            
            # 更新任务结果，添加问题片段
            if task.result is None:
                task.result = {}
            
            task.result["problem_fragments"] = fragments
            task.result["fragment_count"] = len(fragments)
            task.result["severe_count"] = len([f for f in fragments if f.get("severity") == "severe"])
            
            # 保存到数据库
            await crud.update_task(session, task_id, {"result": task.result})
            
            logger.info(f"已将 {len(fragments)} 个问题片段存储到任务 {task_id}")
            
        except Exception as e:
            logger.error(f"存储问题片段失败: {str(e)}", task_id=task_id)
            raise
    
    async def _get_fragments_from_task(
        self,
        task,
        session: AsyncSession
    ) -> List[Dict[str, Any]]:
        """从任务结果中获取问题片段"""
        if not task.result:
            return []
        
        fragments = task.result.get("problem_fragments", [])
        
        # 如果没有问题片段，尝试从检测结果生成
        if not fragments and task.result.get("check_result"):
            logger.info(f"任务 {task.task_id} 没有问题片段，尝试从检测结果生成")
            # 🔥 实现从已有检测结果重新生成问题片段的逻辑
            fragments = await self._regenerate_fragments_from_results(task, session)
        
        return fragments

    async def _regenerate_fragments_from_results(
        self,
        task,
        session: AsyncSession
    ) -> List[Dict[str, Any]]:
        """从检测结果重新生成问题片段"""
        try:
            from app.models.check_result import CheckResult, CheckSeverity
            from app.services.document_processor import DocumentData

            # 获取检测结果
            check_result = task.result.get("check_result", [])
            if not check_result:
                logger.warning(f"任务 {task.task_id} 没有检测结果")
                return []

            logger.info(f"从 {len(check_result)} 个检测结果重新生成问题片段")

            # 转换为CheckResult对象
            check_results = []

            for result_item in check_result:
                # 只处理失败的检测结果
                if not result_item.get('passed', True):
                    # 🔥 新增：过滤掉文档结构相关的检测结果
                    rule_id = result_item.get('rule_id', '')
                    if self._should_exclude_rule_from_fragments(rule_id):
                        continue

                    try:
                        # 映射严重程度
                        severity_map = {
                            'error': CheckSeverity.ERROR,
                            'warning': CheckSeverity.WARNING,
                            'critical': CheckSeverity.CRITICAL,
                            'info': CheckSeverity.INFO
                        }

                        severity = severity_map.get(result_item.get('severity', 'warning'), CheckSeverity.WARNING)

                        check_result_obj = CheckResult(
                            rule_id=result_item.get('rule_id', 'unknown'),
                            rule_name=result_item.get('rule_name', '未知规则'),
                            passed=result_item.get('passed', False),
                            severity=severity,
                            message=result_item.get('message', ''),
                            details=result_item.get('details', {}),
                            position=result_item.get('position'),
                            metadata=result_item.get('metadata', {})
                        )

                        check_results.append(check_result_obj)

                    except Exception as e:
                        logger.error(f"转换检测结果失败: {str(e)}")
                        continue

            logger.info(f"转换了 {len(check_results)} 个CheckResult对象")

            # 创建模拟的文档数据
            document_data = DocumentData(
                file_path=task.file_path,
                doc_info=task.result.get('document_info', {}),
                content_stats=task.result.get('content_stats', {}),
                elements=[
                    {"id": "elem_1", "text": "文档内容", "type": "paragraph"}
                ],
                paragraphs=[],
                tables=[],
                images=[]
            )

            # 生成问题片段
            fragments = self.fragment_generator.generate_fragments_from_results(
                check_results, document_data
            )

            # 转换为字典格式
            fragment_dicts = [fragment.to_dict() for fragment in fragments]

            logger.info(f"重新生成了 {len(fragment_dicts)} 个问题片段")

            return fragment_dicts

        except Exception as e:
            logger.error(f"重新生成问题片段失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _should_exclude_rule_from_fragments(self, rule_id: str) -> bool:
        """
        判断规则是否应该从问题片段中排除

        Args:
            rule_id: 规则ID

        Returns:
            True表示应该排除，False表示应该包含
        """
        # 🔥 新方案：从规则配置中读取exclude_from_fragments字段
        return self._check_rule_exclude_config(rule_id)

    def _check_rule_exclude_config(self, rule_id: str) -> bool:
        """
        检查规则配置中是否设置了exclude_from_fragments

        Args:
            rule_id: 规则ID，格式如 "structure.section_order"

        Returns:
            True表示应该排除，False表示应该包含
        """
        try:
            # 加载规则配置
            import json
            import os

            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'config', 'rules', 'hbkj_bachelor_2024.json'
            )

            if not os.path.exists(config_path):
                logger.warning(f"规则配置文件不存在: {config_path}")
                return False

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 解析规则ID，如 "structure.section_order" -> ["structure", "section_order"]
            rule_parts = rule_id.split('.')
            if len(rule_parts) < 2:
                return False

            category = rule_parts[0]  # 如 "structure"
            rule_name = rule_parts[1]  # 如 "section_order"

            # 在配置中查找规则
            rules = config.get('rules', {})
            category_rules = rules.get(category, {})
            rule_config = category_rules.get(rule_name, {})

            # 检查是否设置了exclude_from_fragments
            exclude_flag = rule_config.get('exclude_from_fragments', False)

            if exclude_flag:
                logger.info(f"规则 {rule_id} 配置为排除问题片段")

            return exclude_flag

        except Exception as e:
            logger.error(f"检查规则排除配置失败: {str(e)}")
            return False

    def _apply_filters(
        self,
        fragments: List[Dict[str, Any]],
        structure: Optional[str] = None,
        severity: Optional[str] = None,
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """应用筛选条件"""
        filtered = fragments
        
        if structure:
            filtered = [f for f in filtered if f.get("structure") == structure]
        
        if severity:
            filtered = [f for f in filtered if f.get("severity") == severity]
        
        if category:
            filtered = [f for f in filtered if f.get("category") == category]
        
        return filtered
    
    def _enhance_fragment_detail(self, fragment: Dict[str, Any]) -> Dict[str, Any]:
        """增强问题片段详情"""
        enhanced = fragment.copy()
        
        # 添加扩展上下文
        enhanced["extended_context"] = {
            "paragraph_before": self._get_paragraph_context(fragment, "before"),
            "paragraph_after": self._get_paragraph_context(fragment, "after")
        }
        
        # 添加标准格式示例
        enhanced["standard_format"] = self._get_standard_format_example(fragment)
        
        # 添加问题详情
        enhanced["problem_details"] = self._get_problem_details(fragment)
        
        # 添加修复预览
        enhanced["correction_preview"] = self._get_correction_preview(fragment)
        
        # 添加修复步骤
        enhanced["correction_steps"] = self._get_correction_steps(fragment)
        
        # 添加修复置信度
        enhanced["fix_confidence"] = self._calculate_fix_confidence(fragment)
        
        # 添加是否需要人工审核
        enhanced["manual_review_required"] = not fragment.get("auto_fixable", False)
        
        # 添加相关片段
        enhanced["related_fragments"] = self._find_related_fragments(fragment)
        
        # 添加出现频率
        enhanced["occurrence_frequency"] = self._calculate_occurrence_frequency(fragment)
        
        return enhanced
    
    def _get_paragraph_context(self, fragment: Dict[str, Any], direction: str) -> str:
        """获取段落上下文"""
        # 这里可以实现更复杂的上下文提取逻辑
        if direction == "before":
            return fragment.get("context_before", "")
        else:
            return fragment.get("context_after", "")
    
    def _get_standard_format_example(self, fragment: Dict[str, Any]) -> str:
        """获取标准格式示例"""
        structure = fragment.get("structure", "")
        
        examples = {
            "中文关键词": "关键词：人工智能；机器学习；深度学习；神经网络；数据挖掘",
            "英文关键词": "Key Words: artificial intelligence; machine learning; deep learning; neural network; data mining",
            "正文": "正文段落应使用宋体，小四号字，1.5倍行距，首行缩进2个字符。",
            "一级标题": "第一章 绪论",
            "二级标题": "1.1 研究背景",
            "三级标题": "1.1.1 研究现状"
        }
        
        return examples.get(structure, fragment.get("standard_reference", ""))
    
    def _get_problem_details(self, fragment: Dict[str, Any]) -> List[Dict[str, str]]:
        """获取问题详情"""
        category = fragment.get("category", "")
        
        if category == "format":
            return [
                {"type": "font", "description": "字体设置不正确"},
                {"type": "size", "description": "字号不符合要求"},
                {"type": "alignment", "description": "对齐方式错误"}
            ]
        elif category == "structure":
            return [
                {"type": "order", "description": "章节顺序不正确"},
                {"type": "missing", "description": "缺少必要章节"}
            ]
        else:
            return [
                {"type": "general", "description": fragment.get("problem_description", "")}
            ]
    
    def _get_correction_preview(self, fragment: Dict[str, Any]) -> str:
        """获取修复预览"""
        # 这里可以实现智能修复预览生成
        return f"修正后：{fragment.get('standard_reference', '标准格式')}"
    
    def _get_correction_steps(self, fragment: Dict[str, Any]) -> List[str]:
        """获取修复步骤"""
        if fragment.get("auto_fixable", False):
            return [
                "选择问题文本",
                "应用自动修复",
                "检查修复结果"
            ]
        else:
            return [
                "定位问题位置",
                "参考标准要求",
                "手动调整格式",
                "验证修改结果"
            ]
    
    def _calculate_fix_confidence(self, fragment: Dict[str, Any]) -> float:
        """计算修复置信度"""
        if fragment.get("auto_fixable", False):
            return 0.95
        else:
            return 0.7
    
    def _find_related_fragments(self, fragment: Dict[str, Any]) -> List[str]:
        """查找相关片段"""
        # 这里可以实现基于规则或相似性的相关片段查找
        return []
    
    def _calculate_occurrence_frequency(self, fragment: Dict[str, Any]) -> Dict[str, int]:
        """计算出现频率"""
        # 这里可以实现问题出现频率的统计
        return {"total": 1, "similar": 1}
