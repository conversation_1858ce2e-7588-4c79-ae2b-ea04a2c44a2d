"""
Word COM接口封装模块

提供Word应用程序的COM接口操作，包括：
- Word应用程序的启动和关闭
- 文档的打开、关闭和基本操作
- 线程安全的COM接口管理
- 资源清理和错误处理
"""

import os
import time
import threading
from typing import Optional, Dict, Any, List
from datetime import datetime
import structlog
import asyncio

try:
    import win32com.client
    import pythoncom
    from win32com.client import constants as word_constants
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    win32com = None
    pythoncom = None
    word_constants = None

from app.core.config import settings
from app.core.logging import logger


class WordCOMError(Exception):
    """Word COM操作异常"""
    pass


class WordApplication:
    """Word应用程序COM接口封装"""
    
    def __init__(self, visible: bool = False, timeout: int = 30):
        """
        初始化Word应用程序
        
        Args:
            visible: 是否显示Word界面
            timeout: 启动超时时间（秒）
        """
        if not COM_AVAILABLE:
            raise WordCOMError("pywin32库未安装，无法使用COM接口")
        
        self.visible = visible
        self.timeout = timeout
        self.word_app = None
        self.documents = {}  # 打开的文档缓存
        self.lock = threading.Lock()
        self.usage_count = 0
        self.max_usage_count = getattr(settings, 'WORD_MAX_USAGE_COUNT', 100)
        self.created_at = datetime.utcnow()
        
    def start(self) -> bool:
        """
        启动Word应用程序
        
        Returns:
            bool: 启动是否成功
        """
        try:
            with self.lock:
                if self.word_app is not None:
                    logger.warning("Word应用程序已经启动")
                    return True
                
                logger.info("正在启动Word应用程序...")
                start_time = time.time()
                
                # 初始化COM
                pythoncom.CoInitialize()
                
                # 创建Word应用程序实例
                self.word_app = win32com.client.Dispatch("Word.Application")
                
                # 配置Word应用程序
                self.word_app.Visible = self.visible
                self.word_app.DisplayAlerts = False  # 禁用警告对话框
                
                # 检查启动是否成功
                if self.word_app is None:
                    raise WordCOMError("无法创建Word应用程序实例")
                
                elapsed_time = time.time() - start_time
                logger.info(f"Word应用程序启动成功，耗时: {elapsed_time:.2f}秒")
                
                return True
                
        except Exception as e:
            logger.error(f"启动Word应用程序失败: {str(e)}")
            self.word_app = None
            raise WordCOMError(f"启动Word应用程序失败: {str(e)}")
    
    def stop(self) -> bool:
        """
        关闭Word应用程序
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            with self.lock:
                if self.word_app is None:
                    logger.warning("Word应用程序未启动")
                    return True
                
                logger.info("正在关闭Word应用程序...")
                
                # 关闭所有打开的文档
                self._close_all_documents()
                
                # 退出Word应用程序
                self.word_app.Quit()
                self.word_app = None
                
                # 清理COM
                pythoncom.CoUninitialize()
                
                logger.info("Word应用程序已关闭")
                return True
                
        except Exception as e:
            logger.error(f"关闭Word应用程序失败: {str(e)}")
            return False
    
    def is_running(self) -> bool:
        """
        检查Word应用程序是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        try:
            with self.lock:
                if self.word_app is None:
                    return False
                
                # 尝试访问Word应用程序属性
                _ = self.word_app.Name
                return True
                
        except Exception:
            logger.warning("Word应用程序连接已断开")
            self.word_app = None
            return False
    
    def open_document(self, file_path: str, read_only: bool = True) -> Optional[Any]:
        """
        打开Word文档
        
        Args:
            file_path: 文档文件路径
            read_only: 是否以只读模式打开
            
        Returns:
            Word文档对象
        """
        try:
            if not self.is_running():
                if not self.start():
                    raise WordCOMError("无法启动Word应用程序")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise WordCOMError(f"文件不存在: {file_path}")
            
            # 检查文件扩展名
            if not file_path.lower().endswith(('.docx', '.doc')):
                raise WordCOMError(f"不支持的文件格式: {file_path}")
            
            with self.lock:
                logger.info(f"正在打开文档: {file_path}")
                
                # 打开文档
                doc = self.word_app.Documents.Open(
                    FileName=file_path,
                    ReadOnly=read_only,
                    AddToRecentFiles=False,
                    Visible=False
                )
                
                if doc is None:
                    raise WordCOMError(f"无法打开文档: {file_path}")
                
                # 缓存文档对象
                doc_id = id(doc)
                self.documents[doc_id] = {
                    'document': doc,
                    'file_path': file_path,
                    'opened_at': datetime.utcnow(),
                    'read_only': read_only
                }
                
                self.usage_count += 1
                logger.info(f"文档打开成功: {file_path}, 使用次数: {self.usage_count}")
                
                return doc
                
        except Exception as e:
            logger.error(f"打开文档失败: {file_path}, 错误: {str(e)}")
            raise WordCOMError(f"打开文档失败: {str(e)}")
    
    def close_document(self, doc: Any, save_changes: bool = False) -> bool:
        """
        关闭Word文档
        
        Args:
            doc: Word文档对象
            save_changes: 是否保存更改
            
        Returns:
            bool: 关闭是否成功
        """
        try:
            if doc is None:
                return True
            
            with self.lock:
                doc_id = id(doc)
                doc_info = self.documents.get(doc_id)
                
                if doc_info:
                    file_path = doc_info['file_path']
                    logger.info(f"正在关闭文档: {file_path}")
                else:
                    logger.info("正在关闭文档")
                
                # 关闭文档
                doc.Close(SaveChanges=save_changes)
                
                # 从缓存中移除
                if doc_id in self.documents:
                    del self.documents[doc_id]
                
                logger.info("文档已关闭")
                return True
                
        except Exception as e:
            logger.error(f"关闭文档失败: {str(e)}")
            return False
    
    def get_document_info(self, doc: Any) -> Dict[str, Any]:
        """
        获取文档基本信息
        
        Args:
            doc: Word文档对象
            
        Returns:
            dict: 文档信息
        """
        try:
            if doc is None:
                raise WordCOMError("文档对象为空")
            
            # 🔥 修复：强制更新文档统计信息
            try:
                # 使用数字常量而不是word_constants
                # 强制重新计算文档统计信息
                doc.Range().Information[3]  # wdActiveEndPageNumber = 3
                doc.ComputeStatistics(0, True)  # wdStatisticWords = 0，强制重新计算
                logger.info("强制更新文档统计信息完成")
            except Exception as e:
                logger.warning(f"强制更新文档统计信息失败: {str(e)}")

            # 获取文档属性
            built_in_props = doc.BuiltInDocumentProperties

            # 🔥 修复：使用多种方法获取准确的统计信息
            computed_stats = {}

            # 方法1：使用ComputeStatistics（基础统计）
            try:
                computed_stats = {
                    'words': doc.ComputeStatistics(0),      # wdStatisticWords = 0
                    'characters': doc.ComputeStatistics(3), # wdStatisticCharacters = 3
                    'paragraphs': doc.ComputeStatistics(4), # wdStatisticParagraphs = 4
                }
                logger.info(f"ComputeStatistics获取的基础统计信息: {computed_stats}")
            except Exception as e:
                logger.warning(f"ComputeStatistics获取统计信息失败: {str(e)}")

            # 方法2：使用更准确的页数统计方法
            try:
                # 多种页数统计方法，选择最准确的
                page_methods = []

                # 方法2a：使用Range.Information[wdActiveEndPageNumber] - 更安全的方式
                try:
                    doc_range = doc.Range()
                    if hasattr(doc_range, 'Information'):
                        pages_info = doc_range.Information(3)  # wdActiveEndPageNumber = 3
                        if pages_info and pages_info > 0:
                            page_methods.append(('Range.Information', pages_info))
                except Exception as e:
                    logger.debug(f"Range.Information方法失败: {str(e)}")

                # 方法2b：使用ComputeStatistics
                try:
                    pages_compute = doc.ComputeStatistics(2)  # wdStatisticPages = 2
                    if pages_compute and pages_compute > 0:
                        page_methods.append(('ComputeStatistics', pages_compute))
                except Exception as e:
                    logger.debug(f"ComputeStatistics方法失败: {str(e)}")

                # 方法2c：使用文档的Range到最后一页的方法
                try:
                    # 获取文档的最后一个字符的页码
                    last_char = doc.Characters.Count
                    if last_char > 0:
                        last_range = doc.Range(last_char - 1, last_char)
                        if hasattr(last_range, 'Information'):
                            pages_last = last_range.Information(3)  # wdActiveEndPageNumber
                            if pages_last and pages_last > 0:
                                page_methods.append(('LastRange.Information', pages_last))
                except Exception as e:
                    logger.debug(f"LastRange.Information方法失败: {str(e)}")

                # 方法2d：使用BuiltInDocumentProperties
                try:
                    built_in_props = doc.BuiltInDocumentProperties
                    pages_builtin = self._get_property_value(built_in_props, 'Number of Pages')
                    if pages_builtin and pages_builtin > 0:
                        page_methods.append(('BuiltInProperties', pages_builtin))
                except Exception as e:
                    logger.debug(f"BuiltInProperties方法失败: {str(e)}")

                # 方法2e：使用Sections计算（每个Section可能包含多页）
                try:
                    if hasattr(doc, 'Sections') and doc.Sections.Count > 0:
                        # 遍历所有Section，获取最后一个Section的最后一页
                        last_section = doc.Sections(doc.Sections.Count)
                        if hasattr(last_section, 'Range'):
                            section_range = last_section.Range
                            if hasattr(section_range, 'Information'):
                                pages_section = section_range.Information(3)
                                if pages_section and pages_section > 0:
                                    page_methods.append(('Sections.Range', pages_section))
                except Exception as e:
                    logger.debug(f"Sections方法失败: {str(e)}")

                # 选择最大的页数值（通常最准确）
                if page_methods:
                    pages = max(method[1] for method in page_methods if method[1] > 0)
                    computed_stats['pages'] = pages
                    logger.info(f"页数统计方法结果: {page_methods}, 选择: {pages}")
                else:
                    computed_stats['pages'] = 0
                    logger.warning("所有页数统计方法都失败")

            except Exception as e:
                logger.warning(f"页数统计失败: {str(e)}")
                computed_stats['pages'] = 0

            # 方法3：完整的文档统计信息
            try:
                # 基础内容统计
                computed_stats.update({
                    'tables': doc.Tables.Count if hasattr(doc, 'Tables') else 0,
                    'images': len([s for s in doc.InlineShapes if hasattr(s, 'Type') and s.Type == 3]) if hasattr(doc, 'InlineShapes') else 0,
                    'characters_with_spaces': doc.ComputeStatistics(5) if hasattr(doc, 'ComputeStatistics') else 0,  # wdStatisticCharactersWithSpaces = 5
                    'lines': doc.ComputeStatistics(1) if hasattr(doc, 'ComputeStatistics') else 0,  # wdStatisticLines = 1
                })

                # 🔥 新增：结构分析统计
                structure_stats = self._get_structure_statistics(doc)
                computed_stats.update(structure_stats)

                # 🔥 新增：格式规范统计
                format_stats = self._get_format_statistics(doc)
                computed_stats.update(format_stats)

                # 🔥 新增：质量检查统计
                quality_stats = self._get_quality_statistics(doc)
                computed_stats.update(quality_stats)

                logger.info(f"完整统计信息: {computed_stats}")
            except Exception as e:
                logger.warning(f"完整统计信息获取失败: {str(e)}")
                computed_stats.update({'tables': 0, 'images': 0})

            # 备用方法：如果主要方法失败
            if not computed_stats:
                try:
                    computed_stats = {
                        'pages': 1,  # 至少有1页
                        'words': len(doc.Words) if hasattr(doc, 'Words') else 0,
                        'characters': len(doc.Characters) if hasattr(doc, 'Characters') else 0,
                        'paragraphs': len(doc.Paragraphs) if hasattr(doc, 'Paragraphs') else 0,
                        'tables': len(doc.Tables) if hasattr(doc, 'Tables') else 0,
                        'images': 0,
                    }
                    logger.info(f"备用方法获取的统计信息: {computed_stats}")
                except Exception as e2:
                    logger.warning(f"备用方法也失败: {str(e2)}")
                    computed_stats = {}

            info = {
                'title': self._get_property_value(built_in_props, 'Title'),
                'author': self._get_property_value(built_in_props, 'Author'),
                'subject': self._get_property_value(built_in_props, 'Subject'),
                'keywords': self._get_property_value(built_in_props, 'Keywords'),
                'comments': self._get_property_value(built_in_props, 'Comments'),
                'created_date': self._get_property_value(built_in_props, 'Creation Date'),
                'modified_date': self._get_property_value(built_in_props, 'Last Save Time'),
                # 🔥 修复：优先使用ComputeStatistics的结果，回退到BuiltInDocumentProperties
                'pages': computed_stats.get('pages') or self._get_property_value(built_in_props, 'Number of Pages') or 0,
                'words': computed_stats.get('words') or self._get_property_value(built_in_props, 'Number of Words') or 0,
                'characters': computed_stats.get('characters') or self._get_property_value(built_in_props, 'Number of Characters') or 0,
                'paragraphs': computed_stats.get('paragraphs') or self._get_property_value(built_in_props, 'Number of Paragraphs') or 0,
                # 🔥 新增：表格数和图片数
                'tables': computed_stats.get('tables', 0),
                'images': computed_stats.get('images', 0),
                'file_name': doc.Name,
                'full_name': doc.FullName if hasattr(doc, 'FullName') else '',
            }

            # 🔥 新增：完整的统计字段
            info.update({
                # 基础统计（保持兼容性）
                'stat_pages': computed_stats.get('pages', 0),
                'stat_words': computed_stats.get('words', 0),
                'stat_characters': computed_stats.get('characters', 0),
                'stat_paragraphs': computed_stats.get('paragraphs', 0),
                'stat_tables': computed_stats.get('tables', 0),
                'stat_images': computed_stats.get('images', 0),

                # 🔥 新增：扩展统计信息
                'characters_with_spaces': computed_stats.get('characters_with_spaces', 0),
                'lines': computed_stats.get('lines', 0),

                # 结构分析统计
                'heading_count': computed_stats.get('heading_count', 0),
                'section_count': computed_stats.get('section_count', 0),
                'footnote_count': computed_stats.get('footnote_count', 0),
                'endnote_count': computed_stats.get('endnote_count', 0),
                'reference_count': computed_stats.get('reference_count', 0),
                'hyperlink_count': computed_stats.get('hyperlink_count', 0),
                'bookmark_count': computed_stats.get('bookmark_count', 0),
                'comment_count': computed_stats.get('comment_count', 0),
                'field_count': computed_stats.get('field_count', 0),

                # 格式规范统计
                'font_count': computed_stats.get('font_count', 0),
                'style_count': computed_stats.get('style_count', 0),
                'fonts_used': computed_stats.get('fonts_used', []),
                'styles_used': computed_stats.get('styles_used', []),
                'page_orientation': computed_stats.get('page_orientation', 'unknown'),
                'page_size': computed_stats.get('page_size', 'unknown'),
                'margin_info': computed_stats.get('margin_info', {}),
                'line_spacing_info': computed_stats.get('line_spacing_info', {}),

                # 质量检查统计
                'spelling_errors': computed_stats.get('spelling_errors', 0),
                'grammar_errors': computed_stats.get('grammar_errors', 0),
                'revision_count': computed_stats.get('revision_count', 0),
                'version_count': computed_stats.get('version_count', 0),
                'track_changes_count': computed_stats.get('track_changes_count', 0),
                'formula_count': computed_stats.get('formula_count', 0),
                'equation_count': computed_stats.get('equation_count', 0),
                'textbox_count': computed_stats.get('textbox_count', 0),
                'chart_count': computed_stats.get('chart_count', 0),
                'drawing_count': computed_stats.get('drawing_count', 0),
            })
            
            logger.info(f"获取文档信息成功: {info.get('file_name', 'Unknown')}")
            return info
            
        except Exception as e:
            logger.error(f"获取文档信息失败: {str(e)}")
            raise WordCOMError(f"获取文档信息失败: {str(e)}")
    
    def _get_property_value(self, properties, property_name: str) -> Any:
        """
        安全获取文档属性值

        Args:
            properties: 文档属性集合
            property_name: 属性名称

        Returns:
            属性值或None
        """
        try:
            return properties(property_name).Value
        except Exception:
            return None

    def _get_structure_statistics(self, doc) -> Dict[str, int]:
        """
        获取结构分析统计信息

        Args:
            doc: Word文档对象

        Returns:
            dict: 结构统计信息
        """
        structure_stats = {
            'heading_count': 0,
            'section_count': 0,
            'footnote_count': 0,
            'endnote_count': 0,
            'reference_count': 0,
            'hyperlink_count': 0,
            'bookmark_count': 0,
            'comment_count': 0,
            'field_count': 0,
        }

        try:
            # 标题数量统计
            if hasattr(doc, 'Paragraphs'):
                for paragraph in doc.Paragraphs:
                    try:
                        style_name = paragraph.Style.NameLocal if hasattr(paragraph, 'Style') else ''
                        if any(heading in style_name.lower() for heading in ['heading', '标题', 'title']):
                            structure_stats['heading_count'] += 1
                    except:
                        continue

            # 章节数统计
            if hasattr(doc, 'Sections'):
                structure_stats['section_count'] = doc.Sections.Count

            # 脚注和尾注统计
            if hasattr(doc, 'Footnotes'):
                structure_stats['footnote_count'] = doc.Footnotes.Count
            if hasattr(doc, 'Endnotes'):
                structure_stats['endnote_count'] = doc.Endnotes.Count

            # 超链接统计
            if hasattr(doc, 'Hyperlinks'):
                structure_stats['hyperlink_count'] = doc.Hyperlinks.Count

            # 书签统计
            if hasattr(doc, 'Bookmarks'):
                structure_stats['bookmark_count'] = doc.Bookmarks.Count

            # 批注统计
            if hasattr(doc, 'Comments'):
                structure_stats['comment_count'] = doc.Comments.Count

            # 域统计
            if hasattr(doc, 'Fields'):
                structure_stats['field_count'] = doc.Fields.Count

            # 参考文献统计（通过查找常见的参考文献模式）
            if hasattr(doc, 'Range'):
                try:
                    doc_text = doc.Range().Text
                    import re
                    # 查找参考文献模式：[1], [2], 等
                    ref_pattern = r'\[\d+\]'
                    references = re.findall(ref_pattern, doc_text)
                    structure_stats['reference_count'] = len(set(references))  # 去重
                except:
                    pass

        except Exception as e:
            logger.warning(f"结构统计失败: {str(e)}")

        return structure_stats

    def _get_format_statistics(self, doc) -> Dict[str, Any]:
        """
        获取格式规范统计信息

        Args:
            doc: Word文档对象

        Returns:
            dict: 格式统计信息
        """
        format_stats = {
            'font_count': 0,
            'style_count': 0,
            'fonts_used': [],
            'styles_used': [],
            'page_orientation': 'unknown',
            'page_size': 'unknown',
            'margin_info': {},
            'line_spacing_info': {},
        }

        try:
            # 字体使用统计
            fonts_used = set()
            styles_used = {}
            line_spacings = {}

            if hasattr(doc, 'Paragraphs'):
                for paragraph in doc.Paragraphs:
                    try:
                        # 统计样式使用
                        if hasattr(paragraph, 'Style'):
                            style_name = paragraph.Style.NameLocal
                            styles_used[style_name] = styles_used.get(style_name, 0) + 1

                        # 统计字体使用
                        if hasattr(paragraph, 'Range') and hasattr(paragraph.Range, 'Font'):
                            font_name = paragraph.Range.Font.Name
                            if font_name:
                                fonts_used.add(font_name)

                        # 统计行间距
                        if hasattr(paragraph, 'Format') and hasattr(paragraph.Format, 'LineSpacing'):
                            spacing = paragraph.Format.LineSpacing
                            line_spacings[spacing] = line_spacings.get(spacing, 0) + 1

                    except:
                        continue

            format_stats['font_count'] = len(fonts_used)
            format_stats['style_count'] = len(styles_used)
            format_stats['fonts_used'] = list(fonts_used)[:10]  # 限制返回前10个字体
            format_stats['styles_used'] = [{'name': k, 'count': v} for k, v in sorted(styles_used.items(), key=lambda x: x[1], reverse=True)[:10]]
            format_stats['line_spacing_info'] = dict(list(line_spacings.items())[:5])  # 前5种行间距

            # 页面设置信息
            if hasattr(doc, 'Sections') and doc.Sections.Count > 0:
                try:
                    page_setup = doc.Sections(1).PageSetup
                    format_stats['page_orientation'] = 'portrait' if page_setup.Orientation == 0 else 'landscape'
                    format_stats['page_size'] = f"{page_setup.PageWidth:.0f}x{page_setup.PageHeight:.0f}"
                    format_stats['margin_info'] = {
                        'top': page_setup.TopMargin,
                        'bottom': page_setup.BottomMargin,
                        'left': page_setup.LeftMargin,
                        'right': page_setup.RightMargin,
                    }
                except:
                    pass

        except Exception as e:
            logger.warning(f"格式统计失败: {str(e)}")

        return format_stats

    def _get_quality_statistics(self, doc) -> Dict[str, int]:
        """
        获取质量检查统计信息

        Args:
            doc: Word文档对象

        Returns:
            dict: 质量统计信息
        """
        quality_stats = {
            'spelling_errors': 0,
            'grammar_errors': 0,
            'revision_count': 0,
            'version_count': 0,
            'track_changes_count': 0,
            'formula_count': 0,
            'equation_count': 0,
            'textbox_count': 0,
            'chart_count': 0,
            'drawing_count': 0,
        }

        try:
            # 拼写和语法错误统计
            if hasattr(doc, 'SpellingErrors'):
                quality_stats['spelling_errors'] = doc.SpellingErrors.Count
            if hasattr(doc, 'GrammarErrors'):
                quality_stats['grammar_errors'] = doc.GrammarErrors.Count

            # 修订统计
            if hasattr(doc, 'Revisions'):
                quality_stats['revision_count'] = doc.Revisions.Count

            # 版本统计
            if hasattr(doc, 'Versions'):
                quality_stats['version_count'] = doc.Versions.Count

            # 修订跟踪统计
            if hasattr(doc, 'TrackRevisions'):
                try:
                    # 如果启用了修订跟踪，统计修订数量
                    if doc.TrackRevisions and hasattr(doc, 'Revisions'):
                        quality_stats['track_changes_count'] = doc.Revisions.Count
                except:
                    pass

            # 特殊内容统计
            if hasattr(doc, 'InlineShapes'):
                for shape in doc.InlineShapes:
                    try:
                        shape_type = shape.Type
                        if shape_type == 8:  # wdInlineShapeOLEControlObject (可能是公式)
                            quality_stats['formula_count'] += 1
                        elif shape_type == 12:  # wdInlineShapeChart
                            quality_stats['chart_count'] += 1
                        elif shape_type == 13:  # wdInlineShapeEmbeddedOLEObject
                            quality_stats['equation_count'] += 1
                    except:
                        continue

            # 浮动图形统计
            if hasattr(doc, 'Shapes'):
                for shape in doc.Shapes:
                    try:
                        shape_type = shape.Type
                        if shape_type == 17:  # msoTextBox
                            quality_stats['textbox_count'] += 1
                        elif shape_type in [1, 2, 3, 4, 5]:  # 各种绘图对象
                            quality_stats['drawing_count'] += 1
                    except:
                        continue

        except Exception as e:
            logger.warning(f"质量统计失败: {str(e)}")

        return quality_stats
    
    def _close_all_documents(self):
        """关闭所有打开的文档"""
        try:
            if self.word_app is None:
                return
            
            # 关闭缓存中的文档
            for doc_info in list(self.documents.values()):
                try:
                    doc = doc_info['document']
                    doc.Close(SaveChanges=False)
                except Exception as e:
                    logger.warning(f"关闭文档失败: {str(e)}")
            
            self.documents.clear()
            
            # 关闭Word应用程序中的所有文档
            try:
                for doc in self.word_app.Documents:
                    doc.Close(SaveChanges=False)
            except Exception as e:
                logger.warning(f"关闭Word应用程序文档失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"关闭所有文档失败: {str(e)}")
    
    def should_restart(self) -> bool:
        """
        检查是否需要重启Word应用程序
        
        Returns:
            bool: 是否需要重启
        """
        # 检查使用次数
        if self.usage_count >= self.max_usage_count:
            logger.info(f"Word应用程序使用次数达到上限: {self.usage_count}")
            return True
        
        # 检查运行时间（可选）
        uptime = (datetime.utcnow() - self.created_at).total_seconds()
        max_uptime = getattr(settings, 'WORD_MAX_UPTIME', 3600)  # 默认1小时
        if uptime > max_uptime:
            logger.info(f"Word应用程序运行时间过长: {uptime:.0f}秒")
            return True
        
        return False
    
    def restart(self) -> bool:
        """
        重启Word应用程序
        
        Returns:
            bool: 重启是否成功
        """
        try:
            logger.info("正在重启Word应用程序...")
            
            # 关闭当前应用程序
            self.stop()
            
            # 重置状态
            self.usage_count = 0
            self.created_at = datetime.utcnow()
            
            # 启动新的应用程序
            return self.start()
            
        except Exception as e:
            logger.error(f"重启Word应用程序失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Word应用程序状态
        
        Returns:
            dict: 状态信息
        """
        return {
            'running': self.is_running(),
            'visible': self.visible,
            'usage_count': self.usage_count,
            'max_usage_count': self.max_usage_count,
            'open_documents': len(self.documents),
            'created_at': self.created_at.isoformat(),
            'uptime_seconds': (datetime.utcnow() - self.created_at).total_seconds(),
            'should_restart': self.should_restart()
        }


# 全局Word应用程序实例
_word_app_instance = None
_word_app_lock = threading.Lock()


def get_word_application() -> WordApplication:
    """
    获取全局Word应用程序实例（单例模式）
    
    Returns:
        WordApplication: Word应用程序实例
    """
    global _word_app_instance
    
    with _word_app_lock:
        if _word_app_instance is None:
            visible = getattr(settings, 'WORD_VISIBLE', False)
            timeout = getattr(settings, 'WORD_STARTUP_TIMEOUT', 30)
            _word_app_instance = WordApplication(visible=visible, timeout=timeout)
        
        # 检查是否需要重启
        if _word_app_instance.should_restart():
            logger.info("Word应用程序需要重启")
            _word_app_instance.restart()
        
        return _word_app_instance


def cleanup_word_application():
    """清理全局Word应用程序实例"""
    global _word_app_instance
    
    with _word_app_lock:
        if _word_app_instance is not None:
            _word_app_instance.stop()
            _word_app_instance = None
            logger.info("全局Word应用程序实例已清理")


async def get_com_status() -> Dict[str, Any]:
    """获取Word COM接口状态（用于健康检查）"""
    try:
        # 导入Word COM相关模块
        from app.services.word_application import WordApplication
        
        # 测试COM接口可用性
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 创建Word应用实例
            word_app = WordApplication()
            
            # 检查Word应用是否可用
            if hasattr(word_app, 'word_app') and word_app.word_app:
                # Word实例创建成功
                word_version = getattr(word_app.word_app, 'Version', 'Unknown')
                
                # 清理资源
                if hasattr(word_app, '_close_all_documents'):
                    word_app._close_all_documents()
                
                response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                
                return {
                    "status": "healthy",
                    "response_time_ms": round(response_time, 2),
                    "word_version": word_version,
                    "com_interface": "available",
                    "message": "Word COM接口正常"
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Word应用实例创建失败",
                    "message": "Word COM接口不可用"
                }
                
        except ImportError as e:
            return {
                "status": "unhealthy",
                "error": f"Word COM模块导入失败: {str(e)}",
                "message": "Word COM接口模块不可用"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": f"Word COM接口测试失败: {str(e)}",
                "message": "Word COM接口异常"
            }
            
    except Exception as e:
        logger.error(f"Word COM状态检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Word COM接口检查失败"
        }

async def test_word_com_basic() -> bool:
    """基础Word COM测试"""
    try:
        from app.services.word_application import WordApplication
        
        word_app = WordApplication()
        if hasattr(word_app, 'word_app') and word_app.word_app:
            # 清理资源
            if hasattr(word_app, '_close_all_documents'):
                word_app._close_all_documents()
            return True
        return False
        
    except Exception as e:
        logger.error(f"Word COM基础测试失败: {str(e)}")
        return False

async def get_word_com_info() -> Dict[str, Any]:
    """获取Word COM详细信息"""
    try:
        from app.services.word_application import WordApplication
        
        word_app = WordApplication()
        if hasattr(word_app, 'word_app') and word_app.word_app:
            info = {
                "version": getattr(word_app.word_app, 'Version', 'Unknown'),
                "build": getattr(word_app.word_app, 'Build', 'Unknown'),
                "name": getattr(word_app.word_app, 'Name', 'Microsoft Word'),
                "available": True
            }
            
            # 清理资源
            if hasattr(word_app, '_close_all_documents'):
                word_app._close_all_documents()
                
            return info
        else:
            return {"available": False}
            
    except Exception as e:
        logger.error(f"获取Word COM信息失败: {str(e)}")
        return {"available": False, "error": str(e)} 