# Word文档分析服务需求文档

## 1. 项目概述

### 1.1 项目名称
Word文档分析服务 (Word Document Analysis Service)

### 1.2 项目描述
开发一个运行在Windows系统上的服务程序，用于接收、分析和处理Word文档(.docx)，将文档内容提取并转换为结构化的JSON数据。该服务采用COM组件技术通过Microsoft Word应用程序接口获取最精确的文档信息。

### 1.3 项目目标
- 提供高精度的Word文档内容分析和结构化数据提取
- 支持多任务并发处理，保证系统性能和稳定性
- 输出标准化的JSON格式数据，便于后续处理和集成
- 提供专业的论文标准检测和格式规范验证功能
- 实现智能问题识别、标注和修正建议生成
- 确保7x24小时稳定可靠的服务运行

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 文档接收功能
- **文件上传接口**: 支持HTTP/HTTPS接口接收.docx文件
- **文件格式验证**: 验证上传文件是否为有效的Word文档
- **文件大小限制**: 设置合理的文件大小上限（建议50MB）
- **文件安全检查**: 基础的文件安全扫描

#### 2.1.2 文档分析功能
- **基本信息提取**:
  - 文档标题、作者、创建时间、修改时间
  - 页数统计
  - 字数统计（包括字符数、段落数）
  - 文档版本信息

- **内容结构分析**:
  - 文档正文内容
  - 标题层级结构（H1-H6）
  - 段落分析
  - 分节符位置和类型
  - 页眉页脚内容
  - 脚注和尾注

- **格式信息提取**:
  - 字体信息
  - 样式信息
  - 表格结构和内容
  - 图片信息（完整属性、文件引用）
  - 列表信息
  - 页面布局信息（页边距、页面大小、方向）

- **文档高级功能**:
  - 书签和交叉引用
  - 超链接和外部链接
  - 修订记录和批注
  - 文档保护状态
  - 域代码和公式（含公式统计）

- **论文标准检测专项**:
  - 统计数据采集（公式数、空格数、脚注数、尾注数）
  - 标题编号规范检测和层级结构完整性验证
  - 目录结构和层级分析
  - 参考文献格式检测和引用关系验证
  - 字体一致性分析和学术规范检测
  - 段落格式规范检测（首行缩进、行间距、段间距）
  - 页码格式检测和版面质量评估
  - 图表编号连续性和标题规范检测
  - 问题分级评估（严重/一般/建议三级分类）
  - 可配置检测规则系统（支持多种论文标准）
  - 学术写作规范检测（术语一致性、写作风格）
  - 文档结构完整性检测（必需章节验证）
  - **各结构字数统计分析**（按章节/结构类型的精确字数统计和合规性检测）
  - **精准结构边界检测**（基于COM接口Range对象的精确定位）
  - **原始格式100%还原显示**（包含分节符、分页符等特殊元素）
  - **智能问题标注系统**（实时检测、详细说明、修改建议）
  - **标注版Word文档生成**（自动批注、颜色标记、修改指导）

- **内容顺序保持**:
  - 严格按照原始文档中的内容出现顺序
  - 保持文本、图片、表格等元素的相对位置关系
  - 确保输出结果能够用于重建原始文档的页面效果

#### 2.1.3 数据输出功能
- **JSON格式输出**: 将分析结果组织成结构化的JSON数据
- **数据完整性**: 确保输出数据的完整性和准确性
- **错误信息**: 处理失败时提供详细的错误信息

### 2.2 非功能需求

#### 2.2.1 性能要求
- **并发处理**: 支持同时处理多个文档（建议5-10个并发）
- **响应时间**: 单个文档处理时间不超过30秒（含图片提取和存储）
- **内存管理**: 合理控制内存使用，避免内存泄漏，采用文件分离存储减少内存占用
- **资源清理**: 及时释放COM对象和临时文件

#### 2.2.2 可靠性要求
- **异常处理**: 完善的异常处理机制
- **日志记录**: 详细的操作日志和错误日志
- **错误恢复**: 单个任务失败不影响其他任务
- **服务稳定性**: 7x24小时稳定运行

#### 2.2.3 安全要求
- **文件安全**: 上传文件的病毒扫描和格式验证
- **访问控制**: 基于令牌的API访问控制
- **数据隔离**: 不同用户的文档数据完全隔离
- **敏感信息**: 自动检测和处理敏感信息
- **日志安全**: 日志文件加密存储，不记录敏感内容
- **网络安全**: HTTPS强制加密，防止中间人攻击

## 3. 技术需求

### 3.1 技术栈选择

#### 3.1.1 核心技术
- **编程语言**: Python 3.8+
- **COM接口**: pywin32库
- **Web框架**: FastAPI（推荐）或Flask
- **异步处理**: asyncio + threading
- **数据格式**: JSON

#### 3.1.2 依赖组件
- **Microsoft Word**: 需要在服务器上安装Microsoft Word 2016+（支持版本：2016, 2019, 2021, Office365）
- **Python包**:
  - pywin32 (COM接口)
  - fastapi (Web服务)
  - uvicorn (ASGI服务器)
  - pydantic (数据验证)
  - python-multipart (文件上传)
  - aiofiles (异步文件操作)
  - redis (缓存和任务队列)
  - pillow (图片处理)
  - hashlib (哈希计算)
  - reportlab (PDF生成)
  - pypdf2 (PDF处理和保护)
  - weasyprint (HTML转PDF备选方案)

### 3.2 系统架构

#### 3.2.1 服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端请求     │───→│   Web API层      │───→│   任务队列      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   响应处理      │←──→│   文档处理引擎   │
                       └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   JSON输出      │←───│   COM接口层     │
                       └─────────────────┘    └─────────────────┘
```

#### 3.2.2 数据流程
1. 客户端上传Word文档
2. API层接收并验证文件
3. 任务加入处理队列
4. 文档处理引擎通过COM接口分析文档
5. 提取数据并格式化为JSON
6. 返回分析结果给客户端

## 4. 核心技术要求详述

### 4.1 内容顺序保持机制

#### 4.1.1 顺序保持原则
- **位置索引**: 为每个内容元素分配全局位置索引(position)
- **类型标识**: 明确标识每个元素的类型(paragraph, heading, image, table等)
- **相对位置**: 保持所有元素在原文档中的相对位置关系
- **分页信息**: 记录元素所在的页面位置

#### 4.1.2 实现方式
- 通过COM接口的Range对象获取精确位置
- 使用Word的Selection.Start和Selection.End属性
- 按照文档流顺序遍历所有内容元素
- 维护全局位置计数器确保顺序性

### 4.2 图片处理机制

#### 4.2.1 图片存储方案（推荐：文件分离存储）
- **存储方式**: 图片提取为独立文件，JSON中保存引用信息
- **去重优化**: 基于SHA256哈希的图片去重机制
- **格式支持**: 支持PNG, JPEG, GIF, BMP等常见格式
- **质量保持**: 提取原始质量的图片数据，无压缩损失

#### 4.2.2 图片属性完整提取
- **尺寸信息**: 原始宽高、显示宽高
- **位置信息**: 精确的页面位置坐标
- **锚点信息**: 图片锚点类型和位置
- **环绕属性**: 文字环绕方式
- **视觉效果**: 亮度、对比度、旋转、翻转等

#### 4.2.3 实现细节
- 使用COM接口的InlineShapes和Shapes集合
- 通过Range.Export方法导出图片
- 计算图片SHA256哈希实现去重
- 建立图片引用和元数据索引系统

#### 4.2.4 性能考虑
- **内存友好**: JSON不包含图片数据，大幅减少内存占用
- **网络优化**: 图片可独立缓存，支持CDN加速
- **并发处理**: 图片提取和存储支持并发优化
- **缓存机制**: 基于哈希的图片缓存和去重

## 5. JSON输出格式规范

### 5.1 基本结构
```json
{
  "document_info": {
    "title": "文档标题",
    "author": "作者",
    "created_date": "2024-01-01T00:00:00Z",
    "modified_date": "2024-01-01T00:00:00Z",
    "version": "版本信息",
    "pages": 10,
    "words": 1000,
    "characters": 5000,
    "characters_with_spaces": 5200,
    "characters_without_spaces": 4800,
    "spaces": 400,
    "paragraphs": 50,
    "lines": 120
  },
  "content": {
    "body": [
      {
        "type": "paragraph",
        "position": 1,
        "text": "段落内容",
        "style": "Normal",
        "alignment": "left",
        "font": {
          "name": "宋体",
          "size": 12,
          "bold": false,
          "italic": false
        },
        "spacing": {
          "line_spacing": 1.0,
          "space_before": 0,
          "space_after": 0,
          "first_line_indent": 24
        }
      },
      {
        "type": "heading",
        "position": 2,
        "level": 1,
        "text": "章节标题",
        "style": "Heading 1",
        "numbering": {
          "has_numbering": true,
          "number_text": "1.",
          "number_format": "decimal",
          "level": 1
        },
        "font": {
          "name": "宋体",
          "size": 16,
          "bold": true,
          "italic": false
        }
      },
      {
        "type": "image",
        "position": 3,
        "image_id": "img_doc123_001",
        "url": "/api/v1/images/img_doc123_001.png",
        "image_hash": "sha256:a1b2c3d4e5f6789...",
        "original_width": 400,
        "original_height": 300,
        "display_width": 200,
        "display_height": 150,
        "format": "png",
        "file_size": 45672,
        "alt_text": "图片说明",
        "position_info": {
          "left": 72,
          "top": 144,
          "anchor_type": "inline",
          "wrap_type": "none",
          "page_number": 1
        },
        "properties": {
          "brightness": 0,
          "contrast": 0,
          "rotation": 0,
          "flip_horizontal": false,
          "flip_vertical": false
        }
      },
      {
        "type": "equation",
        "position": 4,
        "equation_text": "E = mc²",
        "equation_type": "inline",
        "numbering": {
          "has_numbering": true,
          "number_text": "(1)",
          "number_format": "parentheses"
        }
      },
      {
        "type": "table",
        "position": 5,
        "rows": 3,
        "columns": 2,
        "data": [
          ["单元格1", "单元格2"],
          ["单元格3", "单元格4"]
        ],
        "caption": {
          "text": "表1 实验数据统计",
          "style": "Table Caption",
          "position": "above"
        }
      }
    ],
    "sections": [
      {
        "section_id": 1,
        "section_break_type": "nextPage",
        "start_position": 1,
        "end_position": 10
      }
    ],
    "headers": [
      {
        "section_id": 1,
        "type": "header",
        "text": "页眉内容"
      }
    ],
    "footers": [
      {
        "section_id": 1,
        "type": "footer",
        "text": "页脚内容"
      }
    ],
    "footnotes": [
      {
        "id": 1,
        "reference_position": 5,
        "text": "脚注内容"
      }
    ],
    "endnotes": [
      {
        "id": 1,
        "text": "尾注内容"
      }
    ]
  },
  "page_layout": {
    "page_size": {
      "width": 595.3,
      "height": 841.9,
      "orientation": "portrait"
    },
    "margins": {
      "top": 72,
      "bottom": 72,
      "left": 90,
      "right": 90
    },
    "columns": 1,
    "line_spacing": 1.0
  },
  "advanced_features": {
    "bookmarks": [
      {
        "name": "bookmark1",
        "range_start": 100,
        "range_end": 150,
        "text": "书签内容"
      }
    ],
    "hyperlinks": [
      {
        "text": "链接文字",
        "url": "https://example.com",
        "range_start": 200,
        "range_end": 210
      }
    ],
    "comments": [
      {
        "id": 1,
        "author": "作者",
        "date": "2024-01-01T00:00:00Z",
        "text": "批注内容",
        "range_start": 300,
        "range_end": 320
      }
    ],
    "document_protection": {
      "protected": false,
      "protection_type": "none",
      "password_protected": false
    }
  },
  "formatting": {
    "styles": [
      {
        "name": "Normal",
        "type": "paragraph",
        "font": {
          "name": "宋体",
          "size": 12
        }
      }
    ],
    "fonts": ["宋体", "Arial", "Times New Roman"],
    "colors": ["#000000", "#FF0000", "#0000FF"],
    "font_usage_analysis": [
      {
        "font_name": "宋体",
        "font_size": 12,
        "usage_count": 450,
        "usage_percentage": 75.0,
        "contexts": ["正文", "脚注"]
      },
      {
        "font_name": "宋体",
        "font_size": 16,
        "usage_count": 25,
        "usage_percentage": 4.2,
        "contexts": ["一级标题"]
      }
    ]
  },
  "paper_format_check": {
    "compliance_summary": {
      "total_issues": 25,
      "severe_issues": 5,
      "general_issues": 15,
      "suggestion_issues": 5,
      "overall_score": 78.5,
      "compliance_level": "良好"
    },
    "detection_config": {
      "paper_type": "academic_thesis",
      "standard": "gb_7714_2015",
      "custom_rules": {
        "font_requirements": {
          "body_font": "宋体",
          "body_size": 12,
          "heading_font": "黑体",
          "title_size": 16
        },
        "spacing_requirements": {
          "line_spacing": 1.5,
          "paragraph_spacing": 6,
          "first_line_indent": 24
        },
        "margin_requirements": {
          "top": 25.4,
          "bottom": 25.4,
          "left": 31.7,
          "right": 31.7
        }
      }
    },
    "issue_classification": {
      "severe": [
        {
          "category": "reference_format",
          "description": "参考文献格式严重不符合国标",
          "position": 5680,
          "suggestion": "请参考GB/T 7714-2015标准调整格式",
          "auto_fixable": false,
          "problem_fragment": {
            "original_text": "[1] 张三.人工智能技术研究.计算机科学,2023,50(3):15-20",
            "range_start": 5680,
            "range_end": 5720,
            "context_before": "...的相关研究表明",
            "context_after": "。因此本文认为...",
            "standard_format": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20.",
            "problem_details": [
              {
                "issue_type": "missing_space",
                "description": "作者姓名后缺少空格",
                "position_in_fragment": 4
              },
              {
                "issue_type": "missing_identifier",
                "description": "缺少文献类型标识符[J]",
                "position_in_fragment": 15
              },
              {
                "issue_type": "punctuation_error",
                "description": "页码范围应使用冒号而非连字符",
                "position_in_fragment": 35
              }
            ],
            "correction_preview": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20."
          }
        }
      ],
      "general": [
        {
          "category": "font_inconsistency",
          "description": "正文字体不统一",
          "positions": [120, 340, 560],
          "suggestion": "建议将正文字体统一为宋体12号",
          "auto_fixable": true,
          "problem_fragments": [
            {
              "original_text": "人工智能技术的发展日新月异",
              "range_start": 120,
              "range_end": 135,
              "context_before": "随着科技进步，",
              "context_after": "，为各行各业带来了...",
              "current_format": {
                "font_name": "Arial",
                "font_size": 12,
                "font_style": "normal"
              },
              "standard_format": {
                "font_name": "宋体",
                "font_size": 12,
                "font_style": "normal"
              },
              "problem_details": [
                {
                  "issue_type": "wrong_font",
                  "description": "正文应使用宋体字体",
                  "current_value": "Arial",
                  "expected_value": "宋体"
                }
              ]
            },
            {
              "original_text": "机器学习算法在图像识别领域表现突出",
              "range_start": 340,
              "range_end": 360,
              "context_before": "近年来，",
              "context_after": "，准确率达到了...",
              "current_format": {
                "font_name": "Times New Roman",
                "font_size": 11,
                "font_style": "normal"
              },
              "standard_format": {
                "font_name": "宋体",
                "font_size": 12,
                "font_style": "normal"
              },
              "problem_details": [
                {
                  "issue_type": "wrong_font",
                  "description": "正文应使用宋体字体",
                  "current_value": "Times New Roman",
                  "expected_value": "宋体"
                },
                {
                  "issue_type": "wrong_size",
                  "description": "正文字号应为12号",
                  "current_value": 11,
                  "expected_value": 12
                }
              ]
            }
          ]
        }
      ],
      "suggestions": [
        {
          "category": "writing_style",
          "description": "建议增加被动语态使用比例",
          "suggestion": "学术论文建议多使用被动语态表达",
          "problem_fragments": [
            {
              "original_text": "我们采用了深度学习方法进行实验",
              "range_start": 890,
              "range_end": 905,
              "context_before": "在本研究中，",
              "context_after": "，结果表明...",
              "suggested_revision": "采用深度学习方法进行了实验",
              "problem_details": [
                {
                  "issue_type": "active_voice",
                  "description": "建议改为被动语态表达",
                  "suggestion": "去掉主观性词汇'我们'，使用被动语态"
                }
              ]
            }
          ]
        }
      ]
    },
    "title_structure": {
      "has_main_title": true,
      "has_subtitle": false,
      "title_format_correct": true,
      "title_issues": []
    },
    "document_structure_analysis": {
      "standard_structure": [
        "封面", "摘要", "关键词", "目录", "引言", "文献综述", 
        "研究方法", "实验结果", "讨论", "结论", "参考文献", "致谢"
      ],
      "actual_structure": [
        "封面", "摘要", "关键词", "目录", "引言", "文献综述",
        "实验结果", "讨论", "结论", "参考文献", "致谢", "附录"
      ],
      "structure_comparison": {
        "missing_sections": [
          {
            "section": "研究方法",
            "expected_position": 7,
            "severity": "severe",
            "description": "缺少必需的研究方法章节",
            "problem_fragment": {
              "insertion_point": 3450,
              "context_before": "2.2 国外研究现状\n本章对相关研究进行了全面综述。",
              "context_after": "3. 实验结果\n3.1 数据集描述",
              "missing_content_template": "3. 研究方法\n3.1 研究设计\n3.2 数据收集方法\n3.3 实验设计\n3.4 评估指标",
              "standard_reference": "GB/T 7714-2015 学位论文编写规则第4.2.3条",
              "problem_details": [
                {
                  "issue_type": "missing_required_section",
                  "description": "学位论文必须包含研究方法章节",
                  "impact": "影响论文的学术规范性和可重现性"
                }
              ]
            }
          }
        ],
        "extra_sections": [
          {
            "section": "附录", 
            "actual_position": 12,
            "severity": "suggestion",
            "description": "附录为可选章节，建议移至参考文献后",
            "problem_fragment": {
              "original_text": "附录A 实验数据详表\n附录B 程序代码清单",
              "range_start": 7800,
              "range_end": 7850,
              "context_before": "致谢\n感谢导师的悉心指导...",
              "context_after": "[文档结束]",
              "suggested_position": "参考文献之后",
              "problem_details": [
                {
                  "issue_type": "section_order",
                  "description": "附录应放在参考文献之后",
                  "current_position": 12,
                  "suggested_position": 13
                }
              ]
            }
          }
        ],
        "misplaced_sections": [
          {
            "section": "致谢",
            "expected_position": 12,
            "actual_position": 11,
            "severity": "general",
            "description": "致谢章节位置建议调整",
            "problem_fragment": {
              "original_text": "致谢\n感谢导师XXX教授的悉心指导和帮助...",
              "range_start": 7200,
              "range_end": 7350,
              "context_before": "参考文献\n[1] 张三. 人工智能...",
              "context_after": "附录A 实验数据详表",
              "expected_context_before": "附录A 实验数据详表\n附录B 程序代码清单",
              "expected_context_after": "[文档结束]",
              "problem_details": [
                {
                  "issue_type": "wrong_position",
                  "description": "致谢应在参考文献和附录之后",
                  "current_position": 11,
                  "expected_position": 12
                }
              ]
            }
          }
        ]
      },
      "required_sections": {
        "abstract": {"exists": true, "position": 2, "compliant": true},
        "keywords": {"exists": true, "position": 3, "compliant": true},
        "introduction": {"exists": true, "position": 4, "compliant": true},
        "methodology": {"exists": false, "missing": true, "compliant": false},
        "results": {"exists": true, "position": 8, "compliant": true},
        "discussion": {"exists": true, "position": 9, "compliant": true},
        "conclusion": {"exists": true, "position": 10, "compliant": true},
        "references": {"exists": true, "position": 11, "compliant": true}
      },
      "heading_hierarchy": {
        "consistent": false,
        "issues": [
          {
            "position": 450,
            "issue": "跳级标题",
            "description": "从一级标题直接跳到三级标题",
            "severity": "general",
            "problem_fragment": {
              "original_text": "1. 引言\n1.1.1 研究背景",
              "range_start": 450,
              "range_end": 470,
              "context_before": "关键词：人工智能；机器学习；深度学习",
              "context_after": "人工智能技术的快速发展...",
              "expected_text": "1. 引言\n1.1 研究背景\n1.1.1 具体背景",
              "problem_details": [
                {
                  "issue_type": "missing_heading_level",
                  "description": "缺少二级标题1.1",
                  "missing_level": 2,
                  "current_structure": "1级 -> 3级",
                  "expected_structure": "1级 -> 2级 -> 3级"
                }
              ]
            }
          }
        ]
      },
      "structure_compliance_score": 75.0,
      "structure_recommendations": [
        {
          "type": "add_section",
          "section": "研究方法",
          "suggested_position": 7,
          "priority": "high",
          "description": "建议在文献综述后添加研究方法章节"
        },
        {
          "type": "move_section", 
          "section": "附录",
          "from_position": 12,
          "to_position": 13,
          "priority": "low",
          "description": "建议将附录移至参考文献之后"
        }
      ],
      "precise_boundary_detection": {
        "detection_method": "com_range_based",
        "boundary_markers": [
          {
            "type": "section_break",
            "position": 1250,
            "break_type": "nextPage",
            "before_section": "引言",
            "after_section": "文献综述"
          },
          {
            "type": "style_change",
            "position": 2340,
            "from_style": "Normal",
            "to_style": "Heading 1",
            "section_boundary": true
          }
        ],
        "content_classification": [
          {
            "range_start": 0,
            "range_end": 150,
            "section": "封面",
            "confidence": 0.98,
            "detection_basis": ["title_keywords", "document_start"]
          },
          {
            "range_start": 151,
            "range_end": 450,
            "section": "摘要",
            "confidence": 0.95,
            "detection_basis": ["heading_style", "content_keywords"]
          }
        ]
      },
      "visual_restoration": {
        "format_preservation": {
          "fonts": true,
          "colors": true,
          "spacing": true,
          "alignment": true,
          "indentation": true
        },
        "special_elements": {
          "section_breaks": {
            "display": true,
            "symbol": "--- 分节符(下一页) ---",
            "style": "border-top: 2px dashed #ccc; margin: 10px 0;"
          },
          "page_breaks": {
            "display": true,
            "symbol": "--- 分页符 ---",
            "style": "border-top: 1px dashed #999; margin: 5px 0;"
          },
          "column_breaks": {
            "display": true,
            "symbol": "--- 分栏符 ---"
          }
        },
        "layout_accuracy": {
          "position_precision": "pixel_level",
          "margin_preservation": true,
          "table_structure": true,
          "image_positioning": true
        }
      },
      "annotation_system": {
        "problem_markers": [
          {
            "position": 1250,
            "type": "structure_error",
            "severity": "severe",
            "problem_description": "缺少必需的'研究方法'章节",
            "standard_reference": "GB/T 7714-2015 学位论文编写规则",
            "solution_suggestion": "请在'文献综述'后添加'研究方法'章节，包含研究设计、数据收集方法、分析方法等内容",
            "example_content": "2.3 研究方法\n2.3.1 研究设计\n2.3.2 数据收集\n2.3.3 分析方法",
            "marker_style": {
              "background_color": "#ffebee",
              "border_left": "4px solid #f44336",
              "icon": "error"
            }
          },
          {
            "position": 2340,
            "type": "format_error",
            "severity": "general",
            "problem_description": "标题字体不符合规范",
            "standard_reference": "论文格式要求：一级标题使用黑体16号",
            "solution_suggestion": "将标题字体改为黑体16号，加粗显示",
            "marker_style": {
              "background_color": "#fff3e0",
              "border_left": "4px solid #ff9800",
              "icon": "warning"
            }
          }
        ],
        "interactive_features": {
          "hover_details": true,
          "click_to_expand": true,
          "solution_preview": true,
          "standard_reference_link": true
        }
      }
    },
    "heading_analysis": {
      "heading_numbering_consistent": true,
      "heading_levels_proper": false,
      "missing_levels": ["2.2"],
      "numbering_issues": [
        {
          "level": 2,
          "expected": "1.1",
          "actual": "2.1",
          "position": 150,
          "severity": "general"
        }
      ]
    },
    "table_of_contents": {
      "exists": true,
      "format_correct": true,
      "page_numbers_accurate": true,
      "issues": []
    },
    "figure_table_analysis": {
      "figure_numbering": {
        "total_figures": 8,
        "numbering_consistent": false,
        "missing_numbers": ["图2.3"],
        "duplicate_numbers": ["图3.1"],
        "format_issues": [
          {
            "figure_id": "fig_5",
            "expected_format": "图1.1 图名",
            "actual_format": "图1.1图名",
            "issue": "缺少空格",
            "position": 2340,
            "severity": "general",
            "problem_fragment": {
              "original_text": "图1.1神经网络结构示意图",
              "range_start": 2340,
              "range_end": 2355,
              "context_before": "如下图所示：",
              "context_after": "从图中可以看出...",
              "expected_text": "图1.1 神经网络结构示意图",
              "problem_details": [
                {
                  "issue_type": "missing_space",
                  "description": "图号与图名之间缺少空格",
                  "position_in_fragment": 4,
                  "correction": "在'图1.1'后添加空格"
                }
              ]
            }
          }
        ]
      },
      "table_numbering": {
        "total_tables": 4,
        "caption_position_correct": true,
        "numbering_issues": []
      }
    },
    "citation_analysis": {
      "in_text_citations": [
        {
          "citation_text": "[1]",
          "position": 1250,
          "reference_exists": true,
          "reference_id": "ref_001"
        }
      ],
      "orphaned_references": [2, 5],
      "missing_references": [8],
      "citation_format_issues": [
        {
          "position": 2340,
          "issue": "引用格式不规范",
          "expected": "[1]",
          "actual": "(1)",
          "severity": "general",
          "problem_fragment": {
            "original_text": "相关研究表明(1)，深度学习在图像识别领域具有显著优势。",
            "range_start": 2340,
            "range_end": 2375,
            "context_before": "近年来的",
            "context_after": "因此本文采用",
            "expected_text": "相关研究表明[1]，深度学习在图像识别领域具有显著优势。",
            "problem_details": [
              {
                "issue_type": "wrong_citation_format",
                "description": "应使用方括号而非圆括号",
                "current_format": "(数字)",
                "expected_format": "[数字]",
                "standard_reference": "GB/T 7714-2015 文后参考文献著录规则"
              }
            ]
          }
        }
      ]
    },
    "references": {
      "total_count": 25,
      "format_analysis": {
        "consistent_style": false,
        "standard_compliance": "gb_7714_2015",
        "style_issues": [
          {
            "reference_number": 5,
            "issue": "期刊名称格式不一致",
            "position": 5680,
            "severity": "severe"
          }
        ]
      }
    },
    "academic_writing_check": {
      "terminology_consistency": {
        "inconsistent_terms": [
          {
            "term_variations": ["人工智能", "AI", "artificial intelligence"],
            "suggestion": "建议统一使用'人工智能'",
            "positions": [340, 890, 1250]
          }
        ]
      },
      "writing_style": {
        "passive_voice_ratio": 0.65,
        "sentence_length_avg": 25.3,
        "academic_tone_score": 0.82,
        "readability_score": 0.75
      },
      "formula_formatting": {
        "total_formulas": 12,
        "numbering_consistent": true,
        "formatting_issues": []
      }
    },
    "page_format": {
      "header_footer_consistent": true,
      "page_numbering_correct": true,
      "margin_compliance": true,
      "line_spacing_compliance": false,
      "spacing_issues": [
        {
          "type": "line_spacing",
          "expected": 1.5,
          "actual": 1.0,
          "paragraph_position": 120,
          "severity": "general"
        }
      ]
    },
    "layout_quality": {
      "page_balance": {
        "text_density": 0.75,
        "white_space_ratio": 0.25,
        "figure_text_ratio": 0.15
      },
      "typography_score": {
        "font_consistency": 0.95,
        "spacing_consistency": 0.88,
        "alignment_quality": 0.92,
        "overall_quality": 0.92
      }
    },
            "improvement_suggestions": [
      {
        "category": "formatting",
        "priority": "high",
        "description": "建议将正文字体统一为宋体12号",
        "affected_positions": [120, 340, 560],
        "auto_fixable": true
      },
      {
        "category": "structure",
        "priority": "medium",
        "description": "建议补充研究方法章节",
        "auto_fixable": false
      }
    ]
  },
  "archive_report_config": {
    "pdf_generation": {
      "engine": "reportlab",
      "page_size": "A4",
      "margins": {
        "top": 25.4,
        "bottom": 25.4,
        "left": 31.7,
        "right": 31.7
      },
      "fonts": {
        "title": {"name": "SimHei", "size": 16, "bold": true},
        "heading": {"name": "SimHei", "size": 14, "bold": true},
        "body": {"name": "SimSun", "size": 12, "bold": false},
        "caption": {"name": "SimSun", "size": 10, "bold": false}
      }
    },
    "watermark_settings": {
      "default_text": "格式检测报告 - 仅供存档",
      "font": {"name": "SimSun", "size": 48, "bold": false},
      "color": "#CCCCCC",
      "opacity": 0.3,
      "rotation": -45,
      "position": "diagonal",
      "repeat_pattern": true,
      "spacing": {"horizontal": 200, "vertical": 150}
    },
    "report_structure": {
      "cover_page": {
        "title": "Word文档格式检测存档报告",
        "document_info": ["文档名称", "检测时间", "检测标准", "报告版本"],
        "logo_position": "top_right",
        "footer": "本报告由Word文档分析服务自动生成"
      },
      "summary_section": {
        "compliance_overview": true,
        "statistics_summary": true,
        "problem_distribution": true,
        "score_visualization": true
      },
      "detailed_analysis": {
        "structure_analysis": true,
        "format_analysis": true,
        "content_analysis": true,
        "reference_analysis": true
      },
      "appendix": {
        "original_document_preview": true,
        "detection_rules": true,
        "standard_references": true,
        "technical_details": false
      }
    },
    "security_features": {
      "pdf_protection": {
        "prevent_copy": false,
        "prevent_print": false,
        "prevent_modify": true,
        "password_protection": false
      },
      "digital_signature": {
        "enabled": false,
        "certificate_path": "",
        "signature_reason": "格式检测报告存档"
      },
      "metadata": {
        "author": "Word文档分析服务",
        "creator": "Paper Check System v1.2",
        "subject": "论文格式检测存档报告",
        "keywords": ["格式检测", "论文分析", "存档报告"]
      }
    }
  }
  },
  "paper_statistics": {
    "total_equations": 5,
    "total_footnotes": 12,
    "total_endnotes": 3,
    "total_images": 8,
    "total_tables": 4,
    "total_lists": 6,
    "total_headings": {
      "level_1": 5,
      "level_2": 12,
      "level_3": 8,
      "level_4": 2,
      "level_5": 0,
      "level_6": 0
    },
    "total_references": 25,
    "total_hyperlinks": 15,
    "detailed_statistics": {
      "character_analysis": {
        "chinese_characters": 8500,
        "english_characters": 1200,
        "numbers": 450,
        "punctuation": 890,
        "special_symbols": 120
      },
      "readability_metrics": {
        "avg_sentence_length": 18.5,
        "avg_paragraph_length": 125.3,
        "complexity_score": 0.72,
        "academic_level": "研究生"
      },
      "structure_word_counts": {
        "total_main_text": 7200,
        "by_structure": [
          {
            "structure_name": "封面",
            "structure_type": "cover",
            "word_count": 65,
            "character_count": 180,
            "paragraph_count": 8,
            "range_start": 0,
            "range_end": 150,
            "compliance_status": "no_requirement"
          },
          {
            "structure_name": "摘要",
            "structure_type": "abstract",
            "word_count": 280,
            "character_count": 850,
            "paragraph_count": 3,
            "range_start": 151,
            "range_end": 450,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 200,
              "max_words": 300,
              "target_words": 250
            }
          },
          {
            "structure_name": "关键词",
            "structure_type": "keywords",
            "word_count": 25,
            "character_count": 75,
            "paragraph_count": 1,
            "range_start": 451,
            "range_end": 500,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 15,
              "max_words": 30,
              "target_words": 20
            }
          },
          {
            "structure_name": "引言",
            "structure_type": "introduction",
            "word_count": 800,
            "character_count": 2400,
            "paragraph_count": 8,
            "range_start": 501,
            "range_end": 1200,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 600,
              "max_words": 1000,
              "target_words": 800
            }
          },
          {
            "structure_name": "文献综述",
            "structure_type": "literature_review",
            "word_count": 1500,
            "character_count": 4500,
            "paragraph_count": 15,
            "range_start": 1201,
            "range_end": 2800,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 1200,
              "max_words": 2000,
              "target_words": 1500
            }
          },
          {
            "structure_name": "研究方法",
            "structure_type": "methodology",
            "word_count": 0,
            "character_count": 0,
            "paragraph_count": 0,
            "range_start": null,
            "range_end": null,
            "compliance_status": "missing",
            "requirement": {
              "min_words": 800,
              "max_words": 1500,
              "target_words": 1000
            }
          },
          {
            "structure_name": "实验结果",
            "structure_type": "results",
            "word_count": 1200,
            "character_count": 3600,
            "paragraph_count": 12,
            "range_start": 2801,
            "range_end": 4200,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 1000,
              "max_words": 1800,
              "target_words": 1200
            }
          },
          {
            "structure_name": "讨论",
            "structure_type": "discussion",
            "word_count": 900,
            "character_count": 2700,
            "paragraph_count": 9,
            "range_start": 4201,
            "range_end": 5500,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 800,
              "max_words": 1200,
              "target_words": 1000
            }
          },
          {
            "structure_name": "结论",
            "structure_type": "conclusion",
            "word_count": 400,
            "character_count": 1200,
            "paragraph_count": 4,
            "range_start": 5501,
            "range_end": 6000,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 300,
              "max_words": 500,
              "target_words": 400
            }
          },
          {
            "structure_name": "参考文献",
            "structure_type": "references",
            "word_count": 850,
            "character_count": 2550,
            "paragraph_count": 25,
            "range_start": 6001,
            "range_end": 7200,
            "compliance_status": "compliant",
            "requirement": {
              "min_count": 20,
              "max_count": 50,
              "target_count": 30
            }
          },
          {
            "structure_name": "致谢",
            "structure_type": "acknowledgments",
            "word_count": 180,
            "character_count": 540,
            "paragraph_count": 2,
            "range_start": 7201,
            "range_end": 7500,
            "compliance_status": "compliant",
            "requirement": {
              "min_words": 100,
              "max_words": 300,
              "target_words": 200
            }
          }
        ],
        "word_count_analysis": {
          "total_required_words": 8000,
          "actual_words": 6200,
          "missing_words": 1800,
          "compliance_rate": 0.775,
          "missing_structures": ["研究方法"],
          "over_limit_structures": [],
          "under_limit_structures": [],
          "balanced_distribution": false,
          "recommendations": [
            {
              "type": "add_structure",
              "structure": "研究方法",
              "suggested_words": 1000,
              "priority": "high"
            },
            {
              "type": "balance_check",
              "description": "各章节字数分布较为均衡",
              "priority": "low"
            }
          ]
        }
      }
    }
  },
  "metadata": {
    "processing_time": "2024-01-01T00:00:00Z",
    "processing_duration": 5.2,
    "status": "success",
    "errors": [],
    "content_order_preserved": true
  }
}
```

### 5.2 详细字段说明

#### 5.2.1 body数组说明
- **position**: 全局位置索引，确保内容按原文档顺序排列
- **type**: 内容类型，包括：
  - `paragraph`: 普通段落
  - `heading`: 标题（1-6级）
  - `image`: 图片
  - `table`: 表格
  - `equation`: 公式
  - `list`: 列表
  - `pagebreak`: 分页符
  - `footnote_ref`: 脚注引用
  - `reference`: 参考文献引用

#### 5.2.2 图片对象详细说明
```json
{
  "type": "image",
  "position": 3,
  "image_id": "img_doc123_001",
  "url": "/api/v1/images/img_doc123_001.png",
  "image_hash": "sha256:a1b2c3d4e5f6789...",
  "original_width": 400,
  "original_height": 300,
  "display_width": 200,
  "display_height": 150,
  "format": "png",
  "file_size": 45672,
  "alt_text": "图片说明文字",
  "position_info": {
    "anchor_type": "inline",
    "page_number": 1,
    "paragraph_position": 2,
    "left": 72,
    "top": 144
  }
}
```

#### 5.2.3 表格对象详细说明
```json
{
  "type": "table",
  "position": 4,
  "rows": 3,
  "columns": 2,
  "data": [
    [
      {
        "text": "单元格内容",
        "style": "Normal",
        "alignment": "left",
        "merged": false
      }
    ]
  ],
  "style": "Table Grid"
}
```

## 6. 接口设计

### 6.1 REST API接口

#### 6.1.1 文档上传接口
- **路径**: POST /api/v1/documents/analyze
- **参数**: 
  - file: 上传的Word文档文件
  - options: 分析选项（可选）
    - image_storage: 图片存储方式（"file_reference", "inline_base64", "hybrid"，默认"file_reference"）
    - preserve_order: 是否保持内容顺序（默认true）
    - extract_properties: 是否提取详细图片属性（默认true）
- **返回**: 分析结果JSON或任务ID

#### 6.1.2 任务状态查询接口
- **路径**: GET /api/v1/tasks/{task_id}
- **返回**: 任务状态和结果

#### 6.1.3 健康检查接口
- **路径**: GET /health
- **返回**: 服务状态信息，包括Word应用状态

#### 6.1.4 图片服务接口
- **路径**: GET /api/v1/images/{image_id}
- **功能**: 获取图片文件
- **参数**: 
  - image_id: 图片唯一标识
  - quality: 图片质量（可选，用于动态压缩）
  - format: 输出格式（可选，png/jpg/webp）

#### 6.1.5 配置接口
- **路径**: GET/POST /api/v1/config
- **功能**: 获取/设置服务配置参数
- **参数**: 
  - max_file_size: 最大文件大小限制
  - concurrent_tasks: 并发任务数量
  - image_storage_path: 图片存储路径
  - enable_image_deduplication: 是否启用图片去重

#### 6.1.6 论文检测配置接口
- **路径**: POST /api/v1/paper-check/config
- **功能**: 设置论文检测规则和标准
- **参数**:
  - paper_type: 论文类型（academic_thesis/journal_paper/degree_thesis）
  - standard: 检测标准（gb_7714_2015/apa/mla）
  - custom_rules: 自定义检测规则

#### 6.1.7 检测标准查询接口
- **路径**: GET /api/v1/paper-check/standards
- **功能**: 获取支持的检测标准列表
- **返回**: 可用的检测标准和规则模板

#### 6.1.8 问题片段查询接口
- **路径**: GET /api/v1/paper-check/problem-fragments/{task_id}
- **功能**: 获取指定任务的问题片段详细信息
- **参数**:
  - task_id: 检测任务ID
  - structure: 可选，按结构筛选（title/abstract/introduction等）
  - severity: 可选，按严重程度筛选（severe/general/suggestion）
  - category: 可选，按问题类别筛选（format/structure/citation等）
  - page: 可选，分页页码（默认1）
  - limit: 可选，每页数量（默认20，最大100）
- **响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_count": 25,
    "page": 1,
    "limit": 20,
    "fragments": [
      {
        "fragment_id": "frag_001",
        "structure": "references",
        "category": "reference_format",
        "severity": "severe",
        "position": 5680,
        "original_text": "[1] 张三.人工智能技术研究.计算机科学,2023,50(3):15-20",
        "range_start": 5680,
        "range_end": 5720,
        "context_before": "...的相关研究表明",
        "context_after": "。因此本文认为...",
        "standard_format": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20.",
        "problem_description": "参考文献格式严重不符合国标",
        "standard_reference": "GB/T 7714-2015 文后参考文献著录规则",
        "problem_details": [
          {
            "issue_type": "missing_space",
            "description": "作者姓名后缺少空格",
            "position_in_fragment": 4,
            "correction_suggestion": "在作者姓名后添加空格"
          },
          {
            "issue_type": "missing_identifier",
            "description": "缺少文献类型标识符[J]",
            "position_in_fragment": 15,
            "correction_suggestion": "在期刊名称前添加[J]标识符"
          }
        ],
        "correction_preview": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20.",
        "auto_fixable": false,
        "fix_confidence": 0.95
      },
      {
        "fragment_id": "frag_002",
        "structure": "introduction",
        "category": "font_inconsistency",
        "severity": "general",
        "position": 120,
        "original_text": "人工智能技术的发展日新月异",
        "range_start": 120,
        "range_end": 135,
        "context_before": "随着科技进步，",
        "context_after": "，为各行各业带来了...",
        "problem_description": "正文字体不统一",
        "standard_reference": "学位论文格式规范：正文使用宋体12号",
        "current_format": {
          "font_name": "Arial",
          "font_size": 12,
          "font_style": "normal"
        },
        "standard_format": {
          "font_name": "宋体",
          "font_size": 12,
          "font_style": "normal"
        },
        "problem_details": [
          {
            "issue_type": "wrong_font",
            "description": "正文应使用宋体字体",
            "current_value": "Arial",
            "expected_value": "宋体",
            "correction_suggestion": "将字体改为宋体"
          }
        ],
        "auto_fixable": true,
        "fix_confidence": 1.0
      }
    ],
    "summary": {
      "by_severity": {
        "severe": 5,
        "general": 15,
        "suggestion": 5
      },
      "by_category": {
        "reference_format": 8,
        "font_inconsistency": 6,
        "structure_missing": 3,
        "citation_format": 4,
        "heading_hierarchy": 2,
        "figure_format": 2
      },
      "by_structure": {
        "title": 1,
        "abstract": 2,
        "introduction": 4,
        "methodology": 0,
        "results": 3,
        "discussion": 2,
        "conclusion": 1,
        "references": 8,
        "acknowledgments": 1,
        "appendix": 3
      },
      "auto_fixable_count": 18,
      "manual_fix_required": 7
    }
  }
}
```

#### 6.1.9 单个问题片段详情接口
- **路径**: GET /api/v1/paper-check/problem-fragments/{task_id}/{fragment_id}
- **功能**: 获取单个问题片段的详细信息
- **响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fragment_id": "frag_001",
    "structure": "references",
    "category": "reference_format",
    "severity": "severe",
    "position": 5680,
    "original_text": "[1] 张三.人工智能技术研究.计算机科学,2023,50(3):15-20",
    "range_start": 5680,
    "range_end": 5720,
    "context_before": "...的相关研究表明",
    "context_after": "。因此本文认为...",
    "extended_context": {
      "paragraph_before": "近年来，国内外学者在人工智能领域进行了大量研究，相关研究表明",
      "paragraph_after": "。因此本文认为，深度学习技术在图像识别领域具有广阔的应用前景。"
    },
    "standard_format": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20.",
    "problem_description": "参考文献格式严重不符合国标",
    "standard_reference": "GB/T 7714-2015 文后参考文献著录规则",
    "standard_link": "https://std.samr.gov.cn/gb/search/gbDetailed?id=71F772D8055ED3A7E05397BE0A0AB82A",
    "problem_details": [
      {
        "issue_type": "missing_space",
        "description": "作者姓名后缺少空格",
        "position_in_fragment": 4,
        "highlight_range": [4, 5],
        "correction_suggestion": "在作者姓名后添加空格",
        "example": "张三. 而非 张三."
      },
      {
        "issue_type": "missing_identifier",
        "description": "缺少文献类型标识符[J]",
        "position_in_fragment": 15,
        "highlight_range": [15, 15],
        "correction_suggestion": "在期刊名称前添加[J]标识符",
        "example": "研究[J]. 计算机科学"
      },
      {
        "issue_type": "punctuation_error",
        "description": "页码范围应使用冒号而非连字符",
        "position_in_fragment": 35,
        "highlight_range": [35, 36],
        "correction_suggestion": "将连字符改为冒号",
        "example": "50(3): 15-20 而非 50(3):15-20"
      }
    ],
    "correction_preview": "[1] 张三. 人工智能技术研究[J]. 计算机科学, 2023, 50(3): 15-20.",
    "correction_steps": [
      "在'张三'后添加空格",
      "在'研究'后添加'[J]'",
      "将页码范围的连字符改为冒号"
    ],
    "auto_fixable": false,
    "fix_confidence": 0.95,
    "manual_review_required": true,
    "related_fragments": ["frag_015", "frag_023"],
    "occurrence_frequency": {
      "same_issue_count": 8,
      "same_category_count": 12
    }
  }
}
```

#### 6.1.10 标注版文档生成接口
- **路径**: POST /api/v1/paper-check/{task_id}/annotated-document
- **功能**: 生成带问题标注的Word文档
- **参数**:
  - annotation_level: 标注级别（all/severe_only/custom）
  - include_suggestions: 是否包含修改建议（默认true）
  - color_scheme: 颜色方案（default/colorblind_friendly/high_contrast）
- **返回**: 标注版Word文档下载链接

#### 6.1.11 结构边界检测接口
- **路径**: POST /api/v1/paper-check/structure-boundaries
- **功能**: 精确检测文档结构边界
- **参数**:
  - detection_method: 检测方法（auto/manual_rules/ml_assisted）
  - confidence_threshold: 置信度阈值（默认0.8）
- **返回**: 详细的结构边界信息

#### 6.1.12 可视化还原接口
- **路径**: GET /api/v1/documents/{task_id}/visual-preview
- **功能**: 生成100%还原的可视化预览
- **参数**:
  - show_breaks: 是否显示分节符/分页符（默认true）
  - highlight_problems: 是否高亮问题区域（默认false）
  - zoom_level: 缩放级别（50-200，默认100）

#### 6.1.13 格式存档报告生成接口
- **路径**: POST /api/v1/paper-check/{task_id}/archive-report
- **功能**: 生成带水印的格式存档报告PDF
- **参数**:
  - watermark_text: 水印文字（默认"格式检测报告 - 仅供存档"）
  - watermark_position: 水印位置（center/diagonal/corner，默认diagonal）
  - watermark_opacity: 水印透明度（0.1-0.5，默认0.3）
  - include_original_document: 是否包含原始文档（默认true）
  - report_template: 报告模板（standard/detailed/executive，默认standard）
- **返回**: 带水印的PDF存档报告下载链接

#### 6.1.14 结构字数统计接口
- **路径**: GET /api/v1/paper-check/{task_id}/word-count-analysis
- **功能**: 获取各结构的详细字数统计分析
- **参数**:
  - include_requirements: 是否包含字数要求对比（默认true）
  - structure_types: 指定统计的结构类型（可选，默认全部）
  - analysis_level: 分析级别（basic/detailed/comprehensive，默认detailed）
- **返回**: 详细的结构字数统计和合规性分析

### 6.2 API版本管理

#### 6.2.1 版本策略
- 使用语义化版本号（如v1.0.0）
- 向后兼容性保证
- 废弃版本提前通知机制

#### 6.2.2 API响应格式
```json
{
  "api_version": "v1.0.0",
  "timestamp": "2024-01-01T00:00:00Z",
  "success": true,
  "data": {},
  "error": null,
  "request_id": "req_123456789"
}
```

### 6.3 错误码定义

| 错误码 | 说明 | HTTP状态码 |
|--------|------|-----------|
| 10001 | 文件格式不支持 | 400 |
| 10002 | 文件大小超限 | 413 |
| 10003 | 文件损坏无法读取 | 422 |
| 20001 | COM接口初始化失败 | 500 |
| 20002 | Word应用程序崩溃 | 500 |
| 20003 | 文档解析超时 | 408 |
| 30001 | 系统资源不足 | 503 |
| 30002 | 并发任务数量超限 | 429 |
| 40001 | 图片提取失败 | 500 |
| 40002 | 图片存储失败 | 500 |
| 50001 | 论文检测规则配置错误 | 400 |
| 50002 | 不支持的检测标准 | 400 |
| 50003 | 检测规则验证失败 | 422 |
| 50004 | 报告生成失败 | 500 |
| 60001 | 结构边界检测失败 | 500 |
| 60002 | 标注文档生成失败 | 500 |
| 60003 | 可视化还原失败 | 500 |
| 60004 | 格式保持精度不足 | 422 |
| 70001 | PDF存档报告生成失败 | 500 |
| 70002 | 水印添加失败 | 500 |
| 70003 | PDF保护设置失败 | 422 |
| 80001 | Word文档生成失败 | 500 |
| 80002 | 文档标注失败 | 500 |
| 80003 | 模板文件不存在 | 404 |
| 90001 | 问题片段不存在 | 404 |
| 90002 | 问题片段查询失败 | 500 |
| 90003 | 问题片段上下文获取失败 | 500 |
| 90004 | 问题片段高亮处理失败 | 500 |

## 7. 开发计划

### 7.1 总体时间估算
- **总开发周期**: 30-41个工作日（约6-8周）
- **核心功能完成**: 第1-3阶段，约14-20天
- **论文检测功能**: 第4阶段，约8-10天
- **系统集成优化**: 第5-6阶段，约8-11天

### 7.2 关键里程碑
- **里程碑1**: 基础文档分析功能完成（第2阶段结束）
- **里程碑2**: 论文检测核心功能完成（第4阶段结束）
- **里程碑3**: 系统测试和优化完成（第6阶段结束）

### 7.3 阶段划分

#### 第一阶段：基础架构搭建（预计3-5天）
- [ ] 项目结构搭建
- [ ] 基础Web API框架
- [ ] COM接口封装
- [ ] 基本的文档读取功能

#### 第二阶段：核心功能开发（预计7-10天）
- [ ] 文档基本信息提取
- [ ] 内容结构分析（按顺序遍历）
- [ ] 图片处理系统（文件分离存储）
- [ ] 表格和列表处理
- [ ] 页面布局信息提取
- [ ] JSON格式化输出（保持顺序）
- [ ] 异常处理机制

#### 第三阶段：高级功能开发（预计4-5天）
- [ ] 书签和超链接处理
- [ ] 批注和修订记录
- [ ] 页眉页脚和脚注尾注
- [ ] 文档保护状态检测
- [ ] 样式和格式信息提取

#### 第四阶段：论文标准检测系统（预计8-10天）
- [ ] 可配置检测规则引擎
- [ ] 问题分级评估系统
- [ ] 文档结构完整性检测
- [ ] 引用关系验证算法
- [ ] 图表编号连续性检测
- [ ] 学术写作规范检测
- [ ] 智能建议生成系统
- [ ] 检测报告生成模块
- [ ] **精准结构边界检测算法**
- [ ] **100%格式还原显示系统**
- [ ] **智能问题标注引擎**
- [ ] **标注版Word文档生成器**
- [ ] **PDF存档报告生成系统**
- [ ] **水印和安全保护功能**

#### 第五阶段：多任务支持（预计3-4天）
- [ ] 任务队列实现
- [ ] 并发处理逻辑
- [ ] 资源管理优化
- [ ] 图片去重和缓存

#### 第六阶段：测试和优化（预计5-7天）
- [ ] 单元测试和集成测试
- [ ] 论文检测功能专项测试
- [ ] 性能测试和压力测试
- [ ] 错误处理测试
- [ ] 兼容性测试（不同Word版本）
- [ ] 检测规则准确性验证
- [ ] 文档编写和部署指南

### 7.4 技术风险和解决方案

#### 7.4.1 COM接口稳定性
- **风险**: COM接口可能出现死锁或崩溃
- **解决方案**: 
  - 实现超时机制
  - 进程隔离
  - 定期重启Word应用

#### 7.4.2 内存管理
- **风险**: 处理大文件和大量图片时内存占用过高
- **解决方案**:
  - 流式处理
  - 及时释放COM对象
  - 监控内存使用
  - 图片分批处理和压缩

#### 7.4.3 并发安全
- **风险**: 多个任务同时访问Word应用
- **解决方案**:
  - 线程安全的COM接口使用
  - 任务队列管理
  - 资源锁机制

#### 7.4.4 内容顺序一致性
- **风险**: 内容遍历顺序可能不稳定
- **解决方案**:
  - 使用Range对象的Start属性排序
  - 实现二次验证机制
  - 建立内容位置映射表

#### 7.4.5 图片处理性能
- **风险**: 处理大量图片时可能影响系统性能和存储空间
- **解决方案**:
  - 异步图片处理
  - 图片大小限制和质量优化
  - 基于哈希的去重机制
  - 分批处理和缓存策略

#### 7.4.6 检测规则准确性
- **风险**: 论文检测规则可能产生误报或漏报
- **解决方案**:
  - 建立标准测试用例库
  - 实现规则权重和置信度机制
  - 提供人工审核接口
  - 持续优化检测算法

#### 7.4.7 多标准兼容性
- **风险**: 不同论文标准之间的规则冲突
- **解决方案**:
  - 模块化规则设计
  - 标准间的优先级机制
  - 灵活的配置系统
  - 标准切换的平滑过渡

#### 7.4.8 结构边界检测精度
- **风险**: 复杂文档的结构边界可能检测不准确
- **解决方案**:
  - 多重检测算法结合（样式+内容+分节符）
  - 机器学习辅助的边界识别
  - 人工校验接口
  - 置信度评估机制

#### 7.4.9 格式还原完整性
- **风险**: 复杂格式可能无法100%还原
- **解决方案**:
  - COM接口深度调用
  - 像素级精度控制
  - 分层渲染技术
  - 格式差异检测和补偿

#### 7.4.10 标注文档生成质量
- **风险**: 标注可能影响原文档结构
- **解决方案**:
  - 非侵入式标注技术
  - 批注和修订记录分离
  - 标注版本控制
  - 原文档备份机制

#### 7.4.11 PDF生成和水印技术
- **风险**: PDF生成可能出现格式丢失或水印失效
- **解决方案**:
  - 多PDF引擎支持（ReportLab + WeasyPrint备选）
  - 水印层级分离技术
  - PDF完整性验证
  - 批量处理优化

#### 7.4.12 存档报告安全性
- **风险**: 存档报告可能被恶意修改或泄露
- **解决方案**:
  - PDF保护和权限控制
  - 数字签名验证
  - 访问日志记录
  - 敏感信息脱敏处理

## 8. 核心技术实现

### 8.1 Word文档解析引擎
- **COM接口调用**: 使用win32com.client直接操作Word应用程序
- **文档对象模型**: 完整访问Document、Range、Paragraph、Style等对象
- **内容提取**: 精确提取文本、格式、位置、样式等信息
- **结构识别**: 基于样式、大纲级别、分节符等识别文档结构

### 8.2 问题片段数据提取系统
- **精确定位技术**:
  - 使用Word Range对象获取精确的字符位置
  - 记录每个问题的Start和End位置
  - 提取问题前后的上下文内容
  - 支持跨段落、跨页面的问题定位

- **上下文提取算法**:
  ```python
  def extract_problem_context(doc, range_start, range_end, context_length=50):
      """提取问题片段的上下文"""
      # 获取问题范围对象
      problem_range = doc.Range(range_start, range_end)
      
      # 提取前后文本
      before_range = doc.Range(max(0, range_start - context_length), range_start)
      after_range = doc.Range(range_end, min(doc.Characters.Count, range_end + context_length))
      
      return {
          'original_text': problem_range.Text,
          'context_before': before_range.Text,
          'context_after': after_range.Text,
          'paragraph_before': get_paragraph_text(problem_range.Paragraphs.First.Previous),
          'paragraph_after': get_paragraph_text(problem_range.Paragraphs.Last.Next)
      }
  ```

- **问题详情分析**:
  - 格式问题：字体、字号、颜色、样式分析
  - 结构问题：标题层级、章节顺序、必需元素检查
  - 内容问题：引用格式、图表编号、术语一致性
  - 位置问题：相对位置、绝对位置、页面位置

- **标准对比引擎**:
  ```python
  def analyze_format_compliance(text_range, standard_config):
      """分析格式合规性"""
      current_format = extract_format_info(text_range)
      expected_format = get_standard_format(standard_config)
      
      issues = []
      if current_format.font_name != expected_format.font_name:
          issues.append({
              'issue_type': 'wrong_font',
              'current_value': current_format.font_name,
              'expected_value': expected_format.font_name,
              'description': f'字体应为{expected_format.font_name}'
          })
      
      return issues
  ```

### 8.3 问题片段存储系统
- **数据库设计**:
  ```sql
  -- 问题片段主表
  CREATE TABLE problem_fragments (
      fragment_id VARCHAR(50) PRIMARY KEY,
      task_id VARCHAR(50) NOT NULL,
      structure VARCHAR(50),
      category VARCHAR(50),
      severity ENUM('severe', 'general', 'suggestion'),
      position INT,
      range_start INT,
      range_end INT,
      original_text TEXT,
      context_before TEXT,
      context_after TEXT,
      problem_description TEXT,
      standard_reference TEXT,
      auto_fixable BOOLEAN,
      fix_confidence DECIMAL(3,2),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_task_structure (task_id, structure),
      INDEX idx_severity_category (severity, category)
  );
  
  -- 问题详情表
  CREATE TABLE problem_details (
      detail_id VARCHAR(50) PRIMARY KEY,
      fragment_id VARCHAR(50),
      issue_type VARCHAR(50),
      description TEXT,
      position_in_fragment INT,
      current_value TEXT,
      expected_value TEXT,
      correction_suggestion TEXT,
      FOREIGN KEY (fragment_id) REFERENCES problem_fragments(fragment_id)
  );
  ```

- **缓存策略**:
  - Redis缓存热点问题片段数据
  - 按任务ID、结构类型、问题类别建立缓存索引
  - 支持分页查询结果缓存

### 8.4 问题片段查询优化
- **索引优化**:
  - 复合索引：(task_id, structure, severity)
  - 全文索引：original_text, problem_description
  - 位置索引：range_start, range_end

- **查询性能优化**:
  ```python
  def query_problem_fragments(task_id, filters, pagination):
      """优化的问题片段查询"""
      # 构建查询条件
      query = ProblemFragment.query.filter_by(task_id=task_id)
      
      # 应用筛选条件
      if filters.get('structure'):
          query = query.filter(ProblemFragment.structure == filters['structure'])
      if filters.get('severity'):
          query = query.filter(ProblemFragment.severity == filters['severity'])
      
      # 分页查询
      return query.paginate(
          page=pagination['page'],
          per_page=pagination['limit'],
          error_out=False
      )
  ```

### 8.5 问题片段高亮显示
- **HTML渲染引擎**:
  - 将Word格式转换为HTML
  - 保持原始格式和布局
  - 添加问题标记和高亮效果

- **交互功能实现**:
  ```javascript
  // 问题片段交互功能
  class ProblemFragmentViewer {
      constructor(containerId) {
          this.container = document.getElementById(containerId);
          this.fragments = [];
      }
      
      // 高亮问题片段
      highlightFragment(fragmentId) {
          const element = this.container.querySelector(`[data-fragment="${fragmentId}"]`);
          element.classList.add('highlight-problem');
          this.showProblemDetails(fragmentId);
      }
      
      // 显示问题详情
      showProblemDetails(fragmentId) {
          const fragment = this.fragments.find(f => f.fragment_id === fragmentId);
          this.renderProblemPanel(fragment);
      }
  }
  ```

## 9. 部署和运维

### 9.1 部署要求
- Windows Server 2016+
- Python 3.8+
- Microsoft Word 2016+
- 至少8GB RAM，推荐16GB+（考虑图片处理内存需求）
- 充足的磁盘空间用于临时文件
- 高性能CPU（多核心，用于并发处理）

### 9.2 运维监控

#### 9.2.1 监控指标
- **服务状态**: 服务可用性、响应时间、吞吐量
- **系统资源**: CPU使用率、内存使用率、磁盘使用率
- **业务指标**: 文档处理成功率、平均处理时间、队列长度
- **错误监控**: 错误率、错误类型分布、异常堆栈
- **Word应用**: Word进程状态、COM接口响应时间
- **图片处理**: 图片提取成功率、存储空间使用情况
- **论文检测**: 检测准确率、规则覆盖率、误报率、漏报率
- **结构检测**: 边界检测精度、结构识别准确率、置信度分布
- **格式还原**: 还原完整度、像素级精度、渲染性能
- **标注质量**: 标注准确性、用户满意度、修改建议采纳率
- **PDF生成**: 生成成功率、水印完整性、文件大小优化
- **存档服务**: 报告下载量、用户满意度、安全事件数量

#### 9.2.2 告警规则
- CPU使用率 > 80% 持续5分钟
- 内存使用率 > 85% 持续3分钟
- 错误率 > 5% 持续2分钟
- 平均响应时间 > 30秒 持续5分钟
- Word应用程序无响应 > 1分钟
- 磁盘空间 < 20% 可用空间
- 论文检测误报率 > 10% 持续1小时
- 检测规则执行失败率 > 5% 持续30分钟
- 结构边界检测精度 < 90% 持续1小时
- 格式还原完整度 < 95% 持续30分钟
- 标注文档生成失败率 > 3% 持续15分钟
- PDF存档报告生成失败率 > 2% 持续10分钟
- 水印完整性检查失败 > 1% 持续5分钟

#### 9.2.3 日志管理
- **日志等级**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志轮转**: 按日期和大小自动轮转
- **日志保留**: 错误日志保留30天，信息日志保留7天
- **日志分析**: 集成ELK stack或类似日志分析工具

---

**文档版本**: v1.3  
**创建日期**: 2024年  
**更新日期**: 2024年

## 变更记录

### v1.3 (2024年) - 文档优化版
- 🔧 修复图片处理方案矛盾，统一为文件分离存储
- 🔧 统一错误码定义格式，采用数字编码
- 🔧 重新组织章节结构，独立核心技术实现章节
- 🔧 删除重复和冗余内容，提升文档可读性
- 🔧 完善API接口编号和描述

### v1.2 (2024年) - 论文标准检测专版
- ✅ 新增论文标准检测专项功能和问题片段分析
- ✅ 增加精准结构边界检测和格式还原显示
- ✅ 新增智能问题标注和标注版Word生成
- ✅ 增加PDF存档报告生成和水印保护

### v1.1 (2024年)
- ✅ 优化图片处理方案和API接口设计
- ✅ 完善监控告警和安全要求

### v1.0 (2024年)
- 初始版本，包含基础功能需求和技术架构 

## 📋 文档质量检查总结

### ✅ 已修复的问题

#### 1. **图片处理方案矛盾**
- **问题**: 性能要求中提到"base64编码"，但推荐方案是"文件分离存储"
- **修复**: 统一为文件分离存储方案，移除所有base64相关的矛盾表述

#### 2. **错误码定义不一致**
- **问题**: 混合使用数字格式（10001）和字符串格式（WORD_GENERATION_FAILED）
- **修复**: 统一采用数字编码格式，补充完整的HTTP状态码

#### 3. **章节结构混乱**
- **问题**: 核心技术实现章节错误放置在运维监控下
- **修复**: 独立为第8章"核心技术实现"，重新调整章节编号

#### 4. **项目目标不完整**
- **问题**: 项目目标未涵盖论文检测功能
- **修复**: 补充论文标准检测和智能问题识别相关目标

#### 5. **开发计划缺少总览**
- **问题**: 缺少总体时间估算和关键里程碑
- **修复**: 添加总体时间估算（30-41天）和3个关键里程碑

#### 6. **重复内容冗余**
- **问题**: 存在重复的技术实现描述和过于详细的变更记录
- **修复**: 删除重复内容，简化变更记录，提升可读性

### 📊 文档质量指标

- **逻辑一致性**: ✅ 已确保功能需求与技术实现一致
- **内容完整性**: ✅ 涵盖从需求到部署的完整流程
- **结构清晰性**: ✅ 章节编号连续，层级结构合理
- **技术可行性**: ✅ 技术方案具体可行，风险识别充分
- **可操作性**: ✅ 开发计划详细，时间估算合理

### 🎯 文档特色

1. **功能全面**: 涵盖基础文档分析和专业论文检测双重功能
2. **技术先进**: 采用COM接口精确提取，文件分离存储优化性能
3. **架构合理**: 模块化设计，支持并发处理和水平扩展
4. **质量保证**: 完善的错误处理、监控告警和测试计划
5. **标准规范**: 支持多种论文标准，可配置检测规则

**文档版本**: v1.3  
**创建日期**: 2024年  
**更新日期**: 2024年