<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
    <div class="max-w-7xl mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-16">
        <svg class="animate-spin h-8 w-8 text-gray-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="mt-4 text-gray-600 dark:text-gray-300">正在加载统计报告...</p>
      </div>
      <!-- 错误提示 -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-xl p-6 mb-6">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-red-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-1.414 1.414A9 9 0 105.636 18.364l1.414-1.414A7 7 0 1116.95 7.05z"/>
          </svg>
          <div>
            <h3 class="text-red-800 font-medium">加载统计报告失败</h3>
            <p class="text-red-700 text-sm mt-1">{{ error }}</p>
          </div>
        </div>
      </div>
      <!-- 主体内容 -->
      <div v-else>
        <!-- 页面标题栏 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">统计报告</h1>
                <p class="text-gray-600 dark:text-gray-300 text-sm">论文格式检测 · 大学生版</p>
              </div>
            </div>
            <div class="text-right">
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs font-medium rounded-md">NO.</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ mixedData.document_id }}</span>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ getCheckedTime() }}</div>
            </div>
          </div>

        </div>
        
          <!-- 文档基本信息 -->
          <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600 p-5 mb-6 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-blue-100 dark:bg-blue-800 opacity-20 rounded-full -mr-16 -mt-16"></div>
            
            <div class="relative z-10">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">文档信息</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Document Information</p>
                  </div>
                </div>
                
                <div class="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-full px-3 py-1 shadow-sm border border-gray-200 dark:border-gray-600">
                  <div class="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-600 dark:text-gray-300">检测完成</span>
                </div>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
                <div class="lg:col-span-4 space-y-3">
                  <!-- 论文标题 -->
                  <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                    <div class="flex items-start space-x-3">
                      <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                        <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                      </div>
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2 mb-1">
                          <span class="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase">论文题目</span>
                          <span class="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs rounded font-medium">{{ mixedData.document_info.degree_type }}</span>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 dark:text-white leading-snug">{{ mixedData.document_info.title }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- 作者、专业、标准 -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-green-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-green-600 dark:text-green-400 uppercase">作者</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.author }}</p>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-purple-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase">专业</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.major }}</p>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600">
                      <div class="flex items-center space-x-2 mb-1">
                        <div class="w-5 h-5 bg-orange-500 rounded flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                          </svg>
                        </div>
                        <span class="text-xs font-medium text-orange-600 dark:text-orange-400 uppercase">检测标准</span>
                      </div>
                      <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ mixedData.document_info.standard }}</p>
                    </div>
                  </div>
                </div>

                <!-- 右侧检测信息 -->
                <div class="lg:col-span-1">
                  <div class="bg-white/80 dark:bg-gray-800/80 rounded-lg p-3 border border-gray-100 dark:border-gray-600 text-center h-full flex flex-col justify-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-3">
                      <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                      </svg>
                    </div>

                    <div class="space-y-2">
                      <div class="text-center">
                        <div class="text-xs font-bold text-gray-900 dark:text-white">{{ getCheckedTime() }}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">检测时间</div>
                      </div>

                      <div class="flex justify-center">
                        <div :class="['inline-flex items-center px-2 py-1 rounded text-xs font-medium', getQualityLevelClass(mixedData.quality_level)]">
                          <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                          </svg>
                          {{ mixedData.quality_level }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文档统计 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">文档统计</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Document Statistics</p>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
              <div class="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ mixedData.statistics.pages }}</div>
                <div class="text-xs text-blue-700 dark:text-blue-300 mt-1">页数</div>
              </div>
              <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ mixedData.statistics.words.toLocaleString() }}</div>
                <div class="text-xs text-green-700 dark:text-green-300 mt-1">字数</div>
              </div>
              <div class="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ mixedData.statistics.tables }}</div>
                <div class="text-xs text-purple-700 dark:text-purple-300 mt-1">表格数</div>
              </div>
              <div class="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ mixedData.statistics.images }}</div>
                <div class="text-xs text-orange-700 dark:text-orange-300 mt-1">图片数</div>
              </div>
              <div class="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ mixedData.statistics.formulas }}</div>
                <div class="text-xs text-red-700 dark:text-red-300 mt-1">公式数</div>
              </div>
              <div class="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ mixedData.statistics.line_count }}</div>
                <div class="text-xs text-yellow-700 dark:text-yellow-300 mt-1">行数</div>
              </div>
              <div class="text-center p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                <div class="text-lg font-bold text-indigo-600 dark:text-indigo-400">{{ mixedData.statistics.footnotes }}</div>
                <div class="text-xs text-indigo-700 dark:text-indigo-300 mt-1">脚注数</div>
              </div>
              <div class="text-center p-3 bg-pink-50 dark:bg-pink-900/20 rounded-lg">
                <div class="text-lg font-bold text-pink-600 dark:text-pink-400">{{ mixedData.statistics.endnotes }}</div>
                <div class="text-xs text-pink-700 dark:text-pink-300 mt-1">尾注数</div>
              </div>
            </div>
          </div>

          <!-- 检测结果核心指标 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- 问题总数 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-500 dark:text-red-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">问题总数</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-red-600 dark:text-red-400 mb-1">{{ mixedData.total_errors }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">检测发现的所有问题</div>
              </div>
            </div>

            <!-- 严重错误 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-red-200 dark:bg-red-800/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600 dark:text-red-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">严重错误</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-red-700 dark:text-red-400 mb-1">{{ mixedData.severity.critical }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">必须修改的错误</div>
              </div>
            </div>

            <!-- 一般错误 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-orange-100 dark:bg-orange-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-500 dark:text-orange-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">一般错误</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-1">{{ mixedData.severity.warning }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">建议修改的问题</div>
              </div>
            </div>

            <!-- 提醒事项 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 relative overflow-hidden">
              <div class="absolute top-0 right-0 w-20 h-20 bg-blue-100 dark:bg-blue-900/20 rounded-full -mr-10 -mt-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-500 dark:text-blue-400" style="margin-top: 30px; margin-left: -30px;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="relative z-10">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">提醒</span>
                    <div class="relative ml-1 group">
                      <svg class="w-3 h-3 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">{{ mixedData.severity.suggestion }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">可选择性修改</div>
              </div>
            </div>
          </div>

          <!-- 问题详情和其他指标 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 问题详情 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <div class="mb-4">
                <div class="flex items-center mb-1">
                  <div class="w-9 h-9 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">问题详情</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Problem Details</p>
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div v-for="(category, index) in mixedData.categories" :key="index" 
                     class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div class="flex items-center">
                    <div :class="['w-6 h-6 rounded flex items-center justify-center mr-3', category.color]">
                      <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ category.name }}</span>
                  </div>
                  <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ category.count }} 种</span>
                </div>
              </div>
            </div>

            <!-- 其他指标 -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
              <div class="mb-4">
                <div class="flex items-center mb-1">
                  <div class="w-9 h-9 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white">其他指标</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Other Metrics</p>
                  </div>
                </div>
              </div>
              <div class="space-y-4">
                <!-- 差错率 -->
                <div :class="['flex items-center justify-between p-4 rounded-lg', getErrorRateBgClass()]">
                  <div>
                    <div :class="['text-md font-medium', getErrorRateTextClass()]">差错率</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">问题密度×10000/总字符数（每万字差错率）</div>
                  </div>
                  <div class="text-right">
                    <div :class="['text-2xl font-bold', getErrorRateTextClass()]">{{ mixedData.error_rate }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">/10000</div>
                  </div>
                </div>
                
                <!-- 质量等级 -->
                <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">质量等级</span>
                  <div :class="['px-3 py-1 rounded-full text-sm font-medium', getQualityLevelClass(mixedData.quality_level)]">
                    {{ mixedData.quality_level }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 结构完整性分析 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">结构完整性分析</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Structure Integrity Analysis</p>
                </div>
              </div>
            </div>
            
            <!-- 结构准确性总览 -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">结构是否准确:</span>
                <span :class="['font-bold', getStructureAccuracy() ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400']">
                  {{ getStructureAccuracy() ? '是' : '否' }}
                </span>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 dark:text-red-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">严重错误:</span>
                  <span class="font-semibold text-red-600 dark:text-red-400 ml-1">{{ getStructureCriticalCount() }} 种</span>
                </div>
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-blue-500 dark:text-blue-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">提醒:</span>
                  <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1">{{ getStructureSuggestionCount() }} 种</span>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <!-- 左侧：标准结构 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-center bg-teal-500 text-white py-2 rounded-t-lg mb-4">
                  <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                  <span class="font-medium">标准结构</span>
                </div>
                <div class="space-y-2">
                  <!-- 当有结构数据时显示对应的标准结构 -->
                  <div v-if="mixedData.current_sections.length > 0 || mixedData.standard_sections.length > 0"
                       v-for="(section, index) in getAlignedStandardSections()" :key="`standard-${index}`"
                       :class="[
                         'flex items-center justify-between p-3 rounded',
                         section.isPlaceholder
                           ? 'bg-gray-100 dark:bg-gray-700/50 border border-dashed border-gray-300 dark:border-gray-600'
                           : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600'
                       ]">
                    <div class="flex items-center space-x-3 flex-1">
                      <!-- 占位符图标 -->
                      <svg v-if="section.isPlaceholder" class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                      </svg>

                      <div class="flex-1">
                        <span :class="[
                          'text-sm font-medium',
                          section.isPlaceholder
                            ? 'text-gray-500 dark:text-gray-400 italic'
                            : 'text-gray-900 dark:text-white'
                        ]">{{ section.name }}</span>
                      </div>
                    </div>

                    <!-- 标签 -->
                    <div v-if="section.isPlaceholder" class="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600/30 text-gray-500 dark:text-gray-400 rounded">
                      多余
                    </div>
                    <div v-else>
                      <span v-if="section.required" class="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded">必需</span>
                      <span v-else class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded">可选</span>
                    </div>
                  </div>

                  <!-- 当无法获取标准结构数据时显示提示 -->
                  <div v-else class="flex items-center justify-center p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div class="text-center">
                      <svg class="w-8 h-8 text-yellow-500 dark:text-yellow-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                      </svg>
                      <p class="text-sm text-yellow-700 dark:text-yellow-300 font-medium">无法获取检测标准配置</p>
                      <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">请检查网络连接或联系管理员</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧：当前论文结构 -->
              <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                <div class="flex items-center justify-center bg-purple-500 text-white py-2 rounded-t-lg mb-4">
                  <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                  <span class="font-medium">当前论文结构</span>
                </div>
                <div class="space-y-2">
                  <!-- 当有当前结构数据时显示 -->
                  <div v-if="mixedData.current_sections.length > 0" v-for="section in mixedData.current_sections" :key="`${section.name}-${section.paragraph_index || 'default'}`"
                       :class="[
                         'flex items-center justify-between p-3 rounded border',
                         section.type === 'non-standard'
                           ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                           : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600'
                       ]">
                    <div class="flex items-center space-x-3 flex-1">
                      <!-- 标题文本 -->
                      <div class="flex-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ section.name }}</span>


                      </div>
                    </div>

                    <!-- 状态指示器 -->
                    <div class="flex items-center space-x-2">
                      <svg v-if="section.type === 'non-standard'" class="w-4 h-4 text-yellow-500 dark:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                      </svg>
                      <svg v-else-if="section.status === 'present'" class="w-4 h-4 text-green-500 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <svg v-else-if="section.status === 'missing'" class="w-4 h-4 text-red-500 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      <svg v-else-if="section.status === 'extra'" class="w-4 h-4 text-blue-500 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>

                      <span v-if="section.type === 'non-standard'" class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded">
                        多余
                      </span>
                      <span v-else-if="section.status === 'present'" class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded">
                        存在
                      </span>
                      <span v-else-if="section.status === 'missing'" class="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded">
                        缺失
                      </span>
                      <span v-else-if="section.status === 'extra'" class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded">
                        多余
                      </span>
                    </div>
                  </div>

                  <!-- 当无法获取当前结构数据时显示提示 -->
                  <div v-else class="flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div class="text-center">
                      <svg class="w-8 h-8 text-gray-400 dark:text-gray-500 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                      <p class="text-sm text-gray-600 dark:text-gray-400 font-medium">无法获取文档结构</p>
                      <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">文档可能未包含标题样式或分析失败</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 字数分析 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">字数分析</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Word Count Analysis</p>
                </div>
              </div>
            </div>
            
            <!-- 字数达标性总览 -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">字数是否达标:</span>
                <span :class="['font-bold', getWordCountCompliant() ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400']">
                  {{ getWordCountCompliant() ? '是' : '否' }}
                </span>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 dark:text-red-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">严重错误:</span>
                  <span class="font-semibold text-red-600 dark:text-red-400 ml-1">{{ getWordCountErrors() }} 种</span>
                </div>
              </div>
            </div>
            
            <!-- 字数分析表格 -->
            <div class="overflow-x-auto">
              <table class="min-w-full">
                <thead class="bg-gray-50 dark:bg-gray-700/50">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">章节结构</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">标准要求</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">当前情况</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">分析结果</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                  <tr v-for="section in generateWordAnalysisData()" :key="section.name" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td class="px-4 py-3 whitespace-nowrap">
                      <div class="flex items-center">
                        <div :class="['w-3 h-3 rounded-full mr-2', getSectionStatusColor(section.status)]"></div>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ section.name }}</span>
                      </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span class="text-sm text-gray-700 dark:text-gray-300">{{ section.standard }}</span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span class="text-sm text-gray-900 dark:text-white">{{ section.current }}</span>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-center">
                      <span :class="['inline-flex items-center px-2 py-1 text-xs font-medium rounded-full', getAnalysisResultClass(section.result)]">
                        <svg v-if="section.result === '达标'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        <svg v-else-if="section.result === '不足'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        <svg v-else-if="section.result === '超标'" class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                        </svg>
                        <svg v-else class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                        </svg>
                        {{ section.result }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 问题片段 -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6">
            <div class="mb-4">
              <div class="flex items-center mb-1">
                <div class="w-9 h-9 bg-gradient-to-br from-red-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                  </svg>
                </div>
                <div>
                  <h2 class="text-lg font-bold text-gray-900 dark:text-white">问题片段</h2>
                  <p class="text-xs text-gray-500 dark:text-gray-400 tracking-wide">Problem Fragments</p>
                </div>
              </div>
            </div>

            <!-- 问题片段总览 -->
            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">问题片段总数:</span>
                <span class="font-bold text-red-600 dark:text-red-400">{{ problemFragments.length }}</span>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center text-sm">
                  <svg class="w-4 h-4 text-red-500 dark:text-red-400 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span class="text-gray-600 dark:text-gray-300">严重错误:</span>
                  <span class="font-semibold text-red-600 dark:text-red-400 ml-1">{{ getSevereProblemsCount() }} 个</span>
                </div>
              </div>
            </div>

            <!-- 问题片段分类显示 -->
            <div v-if="problemFragments.length > 0" class="space-y-6">
              <!-- 按结构分组显示问题片段 -->
              <div v-for="(group, structure) in groupedProblemFragments" :key="structure" class="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                <!-- 分组标题 -->
                <div class="bg-gray-50 dark:bg-gray-700/50 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                  <div class="flex items-center justify-between">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white">{{ getStructureDisplayName(structure) }}</h3>
                    <div class="flex items-center space-x-2">
                      <span class="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 text-xs font-medium rounded">
                        错误: {{ group.length }}种
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 问题片段表格 -->
                <div class="overflow-x-auto">
                  <table class="min-w-full">
                    <thead class="bg-gray-50 dark:bg-gray-700/50">
                      <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16">序号</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">原文片段</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">问题详情</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">标准</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">问题描述</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                      <tr v-for="(fragment, index) in group" :key="fragment.fragment_id" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <!-- 序号 -->
                        <td class="px-4 py-3 whitespace-nowrap text-center">
                          <span class="text-sm font-medium text-gray-900 dark:text-white">{{ index + 1 }}</span>
                        </td>

                        <!-- 原文片段 -->
                        <td class="px-4 py-3">
                          <div class="max-w-xs">
                            <p class="text-sm text-gray-900 dark:text-white line-clamp-3 break-words">{{ fragment.original_text }}</p>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                              位置: {{ fragment.position }}
                            </div>
                          </div>
                        </td>

                        <!-- 问题详情 -->
                        <td class="px-4 py-3">
                          <div class="flex items-center space-x-2">
                            <span :class="getSeverityBadgeClass(fragment.severity)" class="px-2 py-1 text-xs font-medium rounded">
                              {{ getSeverityDisplayName(fragment.severity) }}
                            </span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">{{ fragment.category }}</span>
                          </div>
                        </td>

                        <!-- 标准 -->
                        <td class="px-4 py-3">
                          <div class="max-w-xs">
                            <p v-if="fragment.standard_reference" class="text-sm text-blue-600 dark:text-blue-400 break-words">
                              {{ fragment.standard_reference }}
                            </p>
                            <p v-else class="text-sm text-gray-500 dark:text-gray-400">—</p>
                          </div>
                        </td>

                        <!-- 问题描述 -->
                        <td class="px-4 py-3">
                          <div class="max-w-sm">
                            <p class="text-sm text-gray-900 dark:text-white line-clamp-2 break-words">{{ fragment.problem_description }}</p>
                            <div v-if="fragment.auto_fixable" class="mt-1 flex items-center text-xs text-green-600 dark:text-green-400">
                              <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                              </svg>
                              可自动修复
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 无问题片段时的提示 -->
            <div v-else class="text-center py-8">
              <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <p class="text-gray-500 dark:text-gray-400">暂无问题片段</p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-between items-center">
            <button @click="goBack" 
                    class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
              </svg>
              返回基础报告
            </button>
            
            <div class="flex space-x-3">
              <button @click="exportReport" 
                      class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                导出PDF报告
              </button>
              <button @click="printReport" 
                      class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-1a2 2 0 00-2-2H9a2 2 0 00-2 2v1a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                </svg>
                打印报告
              </button>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { authApi, taskApi } from '@/services'
import { systemApi } from '@/services/systemApi'
import type { SystemStats, ProblemStats, TaskDetail, ProblemFragment } from '@/types'

// 结构分析相关类型定义
interface StandardSection {
  name: string
  required: boolean
}

interface AlignedStandardSection extends StandardSection {
  isPlaceholder: boolean
  placeholderFor?: string
}

interface CurrentSection {
  name: string
  status: 'present' | 'missing' | 'extra'
  type: 'standard' | 'non-standard'
  level?: number
  style?: string
  paragraph_index?: number
  // 🔥 优化：使用统一的count字段，移除旧的计数字段
  count?: string  // 统一的计数字段，包含单位信息，如 "360字" 或 "中文11条;外文3条"
  content?: {
    text?: string
    style?: string
    paragraph_index?: number
    alignment?: string
    font_size?: number
    is_bold?: boolean
  }
}

interface WordAnalysis {
  name: string
  standard: string
  current: string
  unit: string
  result: string
  status: 'present' | 'missing'
}

const router = useRouter()
const route = useRoute()

// 数据状态
const loading = ref(true)
const error = ref('')

// API数据
const systemStats = ref<SystemStats | null | undefined>(undefined)
const problemStats = ref<ProblemStats[]>([])
const userProfile = ref<any>(null)
const taskDetail = ref<TaskDetail | null>(null)
const problemFragments = ref<ProblemFragment[]>([])
const detailedWordStats = ref<Record<string, { count: number; unit: string; displayText?: string }>>({})
const groupedProblemFragments = ref<Record<string, ProblemFragment[]>>({})

// 静态数据（无法从API获取的数据）
const staticData = ref({
  document_info: {
    title: '关于...',
    author: '...',
    major: '...',
    standard: '...',
    checked_at: ''
  },
  quality_level: '良好',
  error_rate: '0.68',
  structure_accurate: false,
  structure_critical: 2,
  structure_suggestion: 3,
  // 🔥 移除硬编码数据，将在fetchData中从API获取
  standard_sections: [] as StandardSection[],
  current_sections: [] as CurrentSection[],
  word_count_compliant: false,
  word_count_errors: 2,
  word_analysis: [] as WordAnalysis[]
})

// 混合数据计算属性
const mixedData = ref({
  document_id: route.params.id as string || 'c4a6c6fb-2b01-4381-8b79-fcbd616eea38',
  document_info: {
    title: '—',
    author: '—',
    major: '—',
    standard: '—',
    checked_at: '',
    degree_type: '学位论文' // 新增学位类型
  },
  quality_level: staticData.value.quality_level,
  error_rate: staticData.value.error_rate,
  total_errors: 0,
  statistics: {
    pages: 0,
    words: 0,
    tables: 0,
    images: 0,
    formulas: 0,
    line_count: 0,
    footnotes: 0,
    endnotes: 0
  },
  severity: {
    critical: 0,
    warning: 0,
    suggestion: 0
  },
  categories: [
    { name: '结构问题', count: 0, color: 'bg-red-500' },
    { name: '页面设置问题', count: 0, color: 'bg-orange-500' },
    { name: '字数问题', count: 0, color: 'bg-yellow-500' },
    { name: '段落问题', count: 0, color: 'bg-green-500' },
    { name: '内容问题', count: 0, color: 'bg-blue-500' },
    { name: '字体问题', count: 0, color: 'bg-purple-500' }
  ],
  structure_accurate: staticData.value.structure_accurate,
  structure_critical: staticData.value.structure_critical,
  structure_suggestion: staticData.value.structure_suggestion,
  standard_sections: staticData.value.standard_sections,
  current_sections: staticData.value.current_sections,
  word_count_compliant: staticData.value.word_count_compliant,
  word_count_errors: staticData.value.word_count_errors,
  word_analysis: staticData.value.word_analysis
})

// 计算结构完整性统计信息
const getStructureAccuracy = () => {
  const currentSections = mixedData.value.current_sections
  if (!currentSections || currentSections.length === 0) {
    return true // 如果没有当前结构数据，默认为准确
  }

  // 检查是否存在缺失或多余的结构
  const hasMissing = currentSections.some(section => section.status === 'missing')
  const hasExtra = currentSections.some(section => section.status === 'extra' || section.type === 'non-standard')

  return !hasMissing && !hasExtra
}

const getStructureCriticalCount = () => {
  const currentSections = mixedData.value.current_sections
  if (!currentSections || currentSections.length === 0) {
    return 0
  }

  // 统计缺失的结构数量（严重错误）
  return currentSections.filter(section => section.status === 'missing').length
}

const getStructureSuggestionCount = () => {
  const currentSections = mixedData.value.current_sections
  if (!currentSections || currentSections.length === 0) {
    return 0
  }

  // 统计多余的结构数量（提醒）
  return currentSections.filter(section =>
    section.status === 'extra' || section.type === 'non-standard'
  ).length
}

// 存储从API获取的检测标准配置
const detectionStandard = ref<any>(null)

// 从检测标准API中获取字数要求配置 - 完全配置驱动
const getWordRequirementsFromStandard = () => {
  const requirements: Record<string, { min?: number; max?: number; unit: string }> = {}

  // 如果还没有加载检测标准配置，返回空对象
  if (!detectionStandard.value?.rules?.content) {
    console.warn('检测标准配置未加载，无法获取字数要求')
    return requirements
  }

  const contentRules = detectionStandard.value.rules.content

  // 🔥 配置驱动：从API获取的规则中解析字数要求，无需硬编码映射
  Object.entries(contentRules).forEach(([ruleKey, rule]: [string, any]) => {
    const params = rule.parameters
    if (params && params.unit && params.target_structure) {
      // 🔥 直接使用配置中的target_structure，无需硬编码映射
      const structureName = params.target_structure

      requirements[structureName] = {
        min: params.min,
        max: params.max,
        unit: params.unit
      }
      console.log(`✅ 从API加载规则: ${structureName} -> ${params.min || ''}-${params.max || ''}${params.unit}`)
    }
  })

  console.log('📋 最终字数要求配置:', requirements)
  return requirements
}

// 获取问题片段数据
const fetchProblemFragments = async () => {
  try {
    const taskId = route.params.id as string
    if (!taskId) return

    console.log('📊 开始获取问题片段数据')
    const result = await taskApi.getProblemFragments(taskId)
    problemFragments.value = result.fragments || []

    // 按结构分组
    const grouped: Record<string, ProblemFragment[]> = {}
    problemFragments.value.forEach(fragment => {
      if (!grouped[fragment.structure]) {
        grouped[fragment.structure] = []
      }
      grouped[fragment.structure].push(fragment)
    })
    groupedProblemFragments.value = grouped

    console.log('📊 问题片段数据获取完成:', problemFragments.value.length)
  } catch (error) {
    console.error('获取问题片段失败:', error)
    // 如果API不存在，使用模拟数据
    problemFragments.value = generateMockProblemFragments()

    // 按结构分组
    const grouped: Record<string, ProblemFragment[]> = {}
    problemFragments.value.forEach(fragment => {
      if (!grouped[fragment.structure]) {
        grouped[fragment.structure] = []
      }
      grouped[fragment.structure].push(fragment)
    })
    groupedProblemFragments.value = grouped
  }
}

// 生成模拟问题片段数据
const generateMockProblemFragments = (): ProblemFragment[] => {
  return [
    {
      fragment_id: 'frag_001',
      structure: '中文关键词',
      category: 'format',
      severity: 'severe',
      position: 1250,
      original_text: '关键词：舞蹈创作；...',
      range_start: 1250,
      range_end: 1280,
      context_before: '对关于式问题',
      context_after: '',
      problem_description: '两词对齐',
      standard_reference: '左对齐',
      auto_fixable: false
    },
    {
      fragment_id: 'frag_002',
      structure: '英文关键词',
      category: 'format',
      severity: 'general',
      position: 1350,
      original_text: 'Key words:dance c...',
      range_start: 1350,
      range_end: 1380,
      context_before: '标题错误',
      context_after: '',
      problem_description: 'Key（1个半角空格）Wor...',
      standard_reference: 'Key（1个半角空格）Wor...',
      auto_fixable: true
    },
    {
      fragment_id: 'frag_003',
      structure: '正文',
      category: 'format',
      severity: 'suggestion',
      position: 2100,
      original_text: '1.2.1 国内发展现状',
      range_start: 2100,
      range_end: 2120,
      context_before: '字号问题',
      context_after: '',
      problem_description: '小四',
      standard_reference: '13',
      auto_fixable: true
    }
  ]
}

// 获取严重问题数量
const getSevereProblemsCount = () => {
  return problemFragments.value.filter(fragment => fragment.severity === 'severe').length
}

// 获取结构显示名称
const getStructureDisplayName = (structure: string) => {
  const nameMap: Record<string, string> = {
    'title': '标题',
    'abstract': '摘要',
    'keywords': '关键词',
    'introduction': '引言',
    'body': '正文',
    'conclusion': '结论',
    'references': '参考文献',
    'appendix': '附录',
    'acknowledgment': '致谢',
    '中文关键词': '中文关键词',
    '英文关键词': '英文关键词',
    '正文': '正文'
  }
  return nameMap[structure] || structure
}

// 获取严重程度显示名称
const getSeverityDisplayName = (severity: string) => {
  const nameMap: Record<string, string> = {
    'severe': '严重',
    'general': '一般',
    'suggestion': '建议'
  }
  return nameMap[severity] || severity
}

// 获取严重程度样式类
const getSeverityBadgeClass = (severity: string) => {
  const classMap: Record<string, string> = {
    'severe': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'general': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
    'suggestion': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
  }
  return classMap[severity] || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
}

// 计算文本中的字数
const countWords = (text: string): number => {
  if (!text) return 0

  // 去除HTML标签和多余空白
  const cleanText = text.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()

  // 分别计算中文字符和英文单词
  const chineseChars = (cleanText.match(/[\u4e00-\u9fff]/g) || []).length
  const englishWords = (cleanText.match(/[a-zA-Z]+/g) || []).length

  return chineseChars + englishWords
}

// 计算参考文献条数
const countReferences = (text: string): number => {
  if (!text) return 0

  // 简单的参考文献条数统计：按行分割，过滤空行
  const lines = text.split('\n').filter(line => line.trim().length > 0)

  // 更精确的统计：查找参考文献格式的行
  const referencePattern = /^\s*\[\d+\]|\[\d+\]\s*[^\n]+|^\s*\d+\.\s*[^\n]+/
  const references = lines.filter(line => referencePattern.test(line))

  return Math.max(references.length, Math.ceil(lines.length / 2)) // 取较大值作为估算
}

// 🔥 优化：移除复杂的前端分析逻辑，直接使用后端提供的count字段



// 获取详细的字数统计数据
const fetchDetailedWordStats = async () => {
  try {
    console.log('📊 开始从document_structures获取字数统计数据')

    // 🔥 修改：直接从document_structures中获取字数统计
    const currentSections = mixedData.value.current_sections

    if (currentSections && currentSections.length > 0) {
      const stats: Record<string, { count: number; unit: string; displayText?: string }> = {}

      for (const section of currentSections) {
        const sectionName = section.name

        if (sectionName === '参考文献') {
          // 🔥 优化：直接使用后端的count字段
          const displayText = section.count || '0条'

          // 提取数字用于排序等
          const countMatch = displayText.match(/(\d+)/g)
          const totalCount = countMatch ? countMatch.reduce((sum, num) => sum + parseInt(num), 0) : 0

          stats[sectionName] = { count: totalCount, unit: '条', displayText }
          console.log(`📊 ${sectionName}: ${displayText}`)
        } else {
          // 🔥 优化：直接使用后端的count字段
          const displayText = section.count || '0字'

          // 提取数字用于排序等
          const countMatch = displayText.match(/(\d+)/)
          const count = countMatch ? parseInt(countMatch[1]) : 0

          stats[sectionName] = { count, unit: '字', displayText }
          console.log(`📊 ${sectionName}: ${displayText}`)
        }
      }

      detailedWordStats.value = stats
      console.log('📊 从document_structures获取的详细字数统计:', stats)
    } else {
      console.warn('document_structures数据为空')
      // 降级到从当前结构数据中获取
      await fetchDetailedWordStatsFromCurrentSections()
    }

  } catch (error) {
    console.error('从document_structures获取字数统计失败:', error)
    // 降级到从当前结构数据中获取
    await fetchDetailedWordStatsFromCurrentSections()
  }
}

// 降级方案：从当前结构数据中获取字数统计
const fetchDetailedWordStatsFromCurrentSections = async () => {
  try {
    const currentSections = mixedData.value.current_sections
    const stats: Record<string, { count: number; unit: string; displayText?: string }> = {}

    console.log('📊 降级方案：从当前结构数据计算字数统计')

    if (currentSections && currentSections.length > 0) {
      for (const section of currentSections) {
        const sectionName = section.name

        // 🔥 修复：优先使用新的count字段
        if (section.count) {
          // 从count字段解析数据
          if (sectionName === '参考文献') {
            // 参考文献：提取总数
            const countMatch = section.count.match(/(\d+)/g)
            const totalCount = countMatch ? countMatch.reduce((sum, num) => sum + parseInt(num), 0) : 0
            stats[sectionName] = { count: totalCount, unit: '条', displayText: section.count }
          } else {
            // 其他结构：提取字数
            const countMatch = section.count.match(/(\d+)/)
            const count = countMatch ? parseInt(countMatch[1]) : 0
            stats[sectionName] = { count, unit: '字', displayText: section.count }
          }
        } else {
          // 如果没有count字段，使用文本长度估算
          const content = section.content || {}
          const text = content.text || ''

          if (sectionName === '参考文献') {
            const count = countReferences(text)
            stats[sectionName] = { count, unit: '条' }
          } else {
            const count = countWords(text)
            stats[sectionName] = { count, unit: '字' }
          }
        }
      }
    }

    detailedWordStats.value = stats
    console.log('📊 降级方案字数统计结果:', stats)

  } catch (error) {
    console.error('降级方案获取字数统计失败:', error)
  }
}

// 获取实际的字数/条数统计
const getActualWordCount = (sectionName: string, sectionData: any) => {
  // 从详细统计数据中获取
  const stats = detailedWordStats.value[sectionName]

  if (stats) {
    // 🔥 新增：优先使用displayText（用于参考文献的详细显示）
    let displayText = stats.displayText
    if (!displayText) {
      displayText = stats.count > 0 ? `${stats.count}${stats.unit}` : '—'
    }

    return {
      count: stats.count,
      unit: stats.unit,
      text: displayText
    }
  }

  // 如果没有详细数据，返回默认值
  const defaultUnit = sectionName === '参考文献' ? '条' : '字'
  return {
    count: 0,
    unit: defaultUnit,
    text: '—'
  }
}

// 生成字数分析数据
const generateWordAnalysisData = () => {
  const currentSections = mixedData.value.current_sections
  if (!currentSections || currentSections.length === 0) {
    return []
  }

  const wordRequirements = getWordRequirementsFromStandard()
  const result = []

  // 遍历当前结构，生成字数分析数据（保持与当前论文结构完全一致）
  for (const section of currentSections) {
    const requirement = wordRequirements[section.name]
    let standardText = '-'
    let analysisResult = '无要求'

    // 获取实际字数/条数
    const actualData = getActualWordCount(section.name, section)

    // 如果有标准要求，计算标准要求文本和分析结果
    if (requirement) {
      if (requirement.min && requirement.max) {
        standardText = `${requirement.min}-${requirement.max}${requirement.unit}`
        if (actualData.count < requirement.min) {
          analysisResult = '不足'
        } else if (actualData.count > requirement.max) {
          analysisResult = '超标'
        } else {
          analysisResult = '达标'
        }
      } else if (requirement.min) {
        standardText = `≥${requirement.min}${requirement.unit}`
        if (actualData.count < requirement.min) {
          analysisResult = '不足'
        } else {
          analysisResult = '达标'
        }
      } else if (requirement.max) {
        standardText = `≤${requirement.max}${requirement.unit}`
        if (actualData.count > requirement.max) {
          analysisResult = '超标'
        } else {
          analysisResult = '达标'
        }
      }
    }

    result.push({
      name: section.name,
      standard: standardText,
      current: actualData.text,
      unit: actualData.unit,
      result: analysisResult,
      status: section.status || 'present',
      type: section.type,
      actualCount: actualData.count
    })
  }

  return result
}

// 计算字数达标性
const getWordCountCompliant = () => {
  const wordAnalysis = generateWordAnalysisData()
  // 只考虑有要求的结构，如果所有有要求的结构都达标，则为达标
  const withRequirements = wordAnalysis.filter(item => item.result !== '无要求')
  if (withRequirements.length === 0) return true
  return !withRequirements.some(item => item.result === '不足' || item.result === '超标')
}

// 计算字数错误数量
const getWordCountErrors = () => {
  const wordAnalysis = generateWordAnalysisData()
  return wordAnalysis.filter(item => item.result === '不足').length
}

// 计算对应的标准结构列表（包含占位符）
const getAlignedStandardSections = () => {
  const result = []
  const standardSections = mixedData.value.standard_sections
  const currentSections = mixedData.value.current_sections

  // 如果没有当前结构数据，直接返回标准结构
  if (!currentSections || currentSections.length === 0) {
    return standardSections.map(section => ({
      ...section,
      isPlaceholder: false
    }))
  }

  // 遍历当前结构，为每个项目在标准结构中找到对应位置
  for (const currentSection of currentSections) {
    if (currentSection.type === 'non-standard') {
      // 对于非标准结构，插入占位符
      result.push({
        name: '（多余结构位置）',
        required: false,
        isPlaceholder: true,
        placeholderFor: currentSection.name
      })
    } else {
      // 对于标准结构，找到对应的标准项目
      const standardSection = standardSections.find(s => s.name === currentSection.name)
      if (standardSection) {
        result.push({
          ...standardSection,
          isPlaceholder: false
        })
      } else {
        // 如果在标准结构中找不到，可能是实际标题，跳过
        continue
      }
    }
  }

  // 添加缺失的标准结构项目
  for (const standardSection of standardSections) {
    const existsInResult = result.some(r => !r.isPlaceholder && r.name === standardSection.name)
    const existsInCurrent = currentSections.some(c => c.name === standardSection.name)

    if (!existsInResult && !existsInCurrent) {
      result.push({
        ...standardSection,
        isPlaceholder: false
      })
    }
  }

  return result
}

// 获取真实数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = ''

    // 并行获取各种数据
    const [
      systemStatsData,
      problemStatsData,
      userProfileData,
      taskDetailData,
      detectionStandardData
    ] = await Promise.allSettled([
      systemApi.getStats(),
      systemApi.getProblemStats(),
      authApi.getUserProfile(),
      route.params.id ? taskApi.getTask(route.params.id as string) : null,
      systemApi.getDetectionStandard('hbkj_bachelor_2024')
    ])
    
    // 处理系统统计数据
    if (systemStatsData.status === 'fulfilled' && systemStatsData.value) {
      systemStats.value = systemStatsData.value
      // 🔥 修复类型: 使用空值合并操作符提供默认值
      mixedData.value.total_errors = systemStatsData.value.total_problems ?? 0
    }
    
    // 处理问题统计数据
    if (problemStatsData.status === 'fulfilled') {
      problemStats.value = problemStatsData.value
      // 更新严重程度统计
      problemStatsData.value.forEach(stat => {
        if (stat.severity === 'critical') {
          mixedData.value.severity.critical = stat.count
        } else if (stat.severity === 'warning') {
          mixedData.value.severity.warning = stat.count
        } else if (stat.severity === 'info') {
          mixedData.value.severity.suggestion = stat.count
        }
      })
    }
    
    // 处理用户资料数据
    if (userProfileData.status === 'fulfilled') {
      userProfile.value = userProfileData.value
      // 更新用户相关信息
      if (userProfileData.value.full_name) {
        mixedData.value.document_info.author = userProfileData.value.full_name
      }
    }
    
    // 处理检测标准数据
    if (detectionStandardData.status === 'fulfilled' && detectionStandardData.value) {
      detectionStandard.value = detectionStandardData.value
      console.log('🔍 检测标准配置加载成功:', detectionStandard.value)
    } else {
      console.warn('检测标准配置加载失败，将使用默认配置')
    }

    // 处理任务详情数据
    if (taskDetailData.status === 'fulfilled' && taskDetailData.value) {
      taskDetail.value = taskDetailData.value as any;

      // 🔥 深度优化：使用新的扁平化API结构提取数据
      if (taskDetail.value) {
        const taskResult = taskDetail.value.result || {};
        const documentInfo = taskResult.document_info || {};
        const contentStats = taskResult.content_stats || {};

        console.log('🔍 调试：taskResult结构:', Object.keys(taskResult));
        console.log('🔍 调试：document_info数据:', documentInfo);
        console.log('🔍 调试：content_stats数据:', contentStats);

        // 🔥 优化：使用精简的document_info，移除重复数据
        mixedData.value.document_info.title = documentInfo.title || '—';
        mixedData.value.document_info.author = documentInfo.author || '—';
        mixedData.value.document_info.major = documentInfo.major || '—';
        mixedData.value.document_info.degree_type = documentInfo.degree_type || '学位论文';

        // 🔥 最终修复：只从result中获取唯一的standard_name
        mixedData.value.document_info.standard = taskResult.standard_name || '标准检测';

        // 检测时间
        mixedData.value.document_info.checked_at = taskDetail.value.created_at || '';
        // 文档编号
        mixedData.value.document_id = taskDetail.value.task_id || '—';

        // 🔥 关键修复: 统一使用content_stats获取所有统计信息
        mixedData.value.statistics = {
          pages: contentStats.page_count || 0,
          words: contentStats.word_count || 0,
          tables: contentStats.table_count || 0,
          images: contentStats.image_count || 0,
          formulas: contentStats.formula_count || 0,
          line_count: contentStats.line_count || 0,
          footnotes: contentStats.footnote_count || 0,
          endnotes: contentStats.endnote_count || 0,
        };

        console.log('🔍 调试：最终统计数据:', mixedData.value.statistics);

        // 🔥 新增：获取结构分析数据
        await fetchStructureAnalysis(taskResult);
      }
    }
    
    // 如果有些数据获取失败，使用默认值
    if (!systemStats.value) {
      mixedData.value.total_errors = 15
      mixedData.value.severity = { critical: 3, warning: 8, suggestion: 4 }
    }
    
    // 获取详细的字数统计数据
    await fetchDetailedWordStats()

    // 获取问题片段数据
    await fetchProblemFragments()

    console.log('统计报告数据加载完成:', {
      systemStats: systemStats.value,
      problemStats: problemStats.value,
      userProfile: userProfile.value,
      taskDetail: taskDetail.value,
      mixedData: mixedData.value,
      detailedWordStats: detailedWordStats.value,
      problemFragments: problemFragments.value
    })

  } catch (err) {
    console.error('获取统计数据失败:', err)
    error.value = '获取统计数据失败，请稍后重试。'
  } finally {
    loading.value = false
  }
}

// 🔥 新增：从 DocumentDetail.vue 移植过来的健壮的取值函数
const getValueFromResult = (result: any, keys: string[], defaultValue: any = 0): any => {
  if (!result) return defaultValue;
  
  const sources = [
    result,
    result.analysis_result,
    result.analysis_result?.content_stats,
    result.content_stats,
    result.document_info,
  ];

  for (const source of sources) {
    if (source) {
      for (const key of keys) {
        if (source[key] !== undefined && source[key] !== null) {
          return source[key];
        }
      }
    }
  }
  
  return defaultValue;
}

// 获取结构分析数据
const fetchStructureAnalysis = async (taskResult: any) => {
  try {
    // 1. 获取检测标准ID
    const detectionStandard = taskResult.detection_standard || 'hbkj_bachelor_2024'

    // 2. 从检测标准配置获取标准结构
    const standardSections = await getStandardSections(detectionStandard)
    mixedData.value.standard_sections = [...standardSections]

    // 3. 从任务结果获取当前文档结构
    const currentSections = getCurrentDocumentSections(taskResult)
    mixedData.value.current_sections = [...currentSections]

  } catch (error) {
    console.error('获取结构分析数据失败:', error)
    // 如果获取失败，设置为空数组
    mixedData.value.standard_sections = []
    mixedData.value.current_sections = getDefaultCurrentSections()
  }
}

// 🔥 新增：获取标准结构定义
const getStandardSections = async (detectionStandard: string): Promise<StandardSection[]> => {
  try {
    // 从API获取检测标准配置
    try {
      const response = await systemApi.getDetectionStandard(detectionStandard)

      // document_structure在definitions对象内部
      const documentStructure = (response as any)?.definitions?.document_structure

      if (documentStructure && Array.isArray(documentStructure)) {
        const sections = documentStructure.map((section: any) => ({
          name: section.name || section.title,
          required: section.required === true
        }))

        return sections
      }
    } catch (apiError: any) {
      console.error('从API获取检测标准失败，使用本地配置:', apiError)
    }

    // 如果API调用失败，返回空数组，让用户知道需要检查网络连接
    console.warn('无法从API获取检测标准配置，请检查网络连接')
    return []
  } catch (error) {
    console.error('获取标准结构失败:', error)
    return []
  }
}

// 🔥 新增：从任务结果获取当前文档结构
const getCurrentDocumentSections = (taskResult: any): CurrentSection[] => {
  try {
    console.log('开始解析文档结构，任务结果:', taskResult)

    // 🔥 优先从 document_structures 获取真实检测到的结构
    const documentStructures = taskResult.document_structures
    if (documentStructures && Array.isArray(documentStructures)) {
      console.log('使用document_structures数据:', documentStructures)

      const sections = documentStructures.map((structure: any) => {
        const status = structure.status || 'present'
        // 🔥 修复：兼容下划线和连字符格式的非标准结构类型
        let type: 'standard' | 'non-standard' = 'standard'
        if (structure.type === 'standard') {
          type = 'standard'
        } else if (structure.type === 'non-standard' || structure.type === 'non_standard') {
          type = 'non-standard'  // 统一映射为连字符格式
        } else {
          type = 'standard'  // 默认显示为标准结构样式
        }

        // 调试：输出结构信息
        if (type === 'non-standard') {
          console.log(`📊 非标准结构: ${structure.structure_name || structure.name} - 类型: ${type}`)
        }

        return {
          name: structure.structure_name || structure.name || '未知结构',
          status: status as 'present' | 'missing' | 'extra',
          type: type,
          level: structure.level || 1,
          style: structure.content?.style || structure.style || 'Normal',
          paragraph_index: structure.content?.paragraph_index || structure.paragraph_index,
          // 🔥 修复：使用新的count字段，移除旧的计数字段
          count: structure.count || '0字',  // 新的统一count字段
          content: structure.content || {
            text: structure.text || '',
            style: structure.style || 'Normal',
            paragraph_index: structure.paragraph_index,
            alignment: structure.alignment || 'left',
            font_size: structure.font_size,
            is_bold: structure.is_bold || false
          }
        }
      })

      if (sections.length > 0) {
        console.log('从document_structures提取到', sections.length, '个结构')
        console.log('📊 最终结构数据:', sections)

        // 🔥 调试：统计各类型结构数量
        const typeStats = sections.reduce((acc, section) => {
          acc[section.type] = (acc[section.type] || 0) + 1
          return acc
        }, {} as Record<string, number>)
        console.log('📊 结构类型统计:', typeStats)

        return sections
      }
    }

    // 如果没有结构分析数据，使用默认数据
    console.log('没有找到结构分析数据，使用默认数据')
    return getDefaultCurrentSections()
  } catch (error) {
    console.error('解析文档结构失败:', error)
    return getDefaultCurrentSections()
  }
}



// 默认当前结构（当无法从后端获取真实结构时使用）
const getDefaultCurrentSections = (): CurrentSection[] => {
  return []
}





// 页面展示时格式化检测时间
const getCheckedTime = () => {
  const t = mixedData.value.document_info.checked_at
  return t ? new Date(t).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }) : '—'
}

const getQualityLevelClass = (level: string) => {
  const classes: Record<string, string> = {
    '优秀': 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
    '良好': 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
    '一般': 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400',
    '较差': 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
  }
  return classes[level] || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
}

const getErrorRateBgClass = () => {
  const rate = parseFloat(mixedData.value.error_rate)
  if (rate <= 0.25) return 'bg-gradient-to-r from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20'
  if (rate <= 0.5) return 'bg-gradient-to-r from-lime-50 to-green-100 dark:from-lime-900/20 dark:to-green-900/20'
  if (rate <= 1.0) return 'bg-gradient-to-r from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20'
  return 'bg-gradient-to-r from-red-50 to-pink-100 dark:from-red-900/20 dark:to-pink-900/20'
}

const getErrorRateTextClass = () => {
  const rate = parseFloat(mixedData.value.error_rate)
  if (rate <= 0.25) return 'text-green-700 dark:text-green-400'
  if (rate <= 0.5) return 'text-green-600 dark:text-green-400'
  if (rate <= 1.0) return 'text-orange-600 dark:text-orange-400'
  return 'text-red-600 dark:text-red-400'
}

const getSectionStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'present': 'bg-green-500',
    'missing': 'bg-red-500',
    'extra': 'bg-blue-500'
  }
  return colors[status] || 'bg-gray-500'
}

const getAnalysisResultClass = (result: string) => {
  const classes: Record<string, string> = {
    '达标': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    '不足': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    '超标': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400',
    '无要求': 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
  }
  return classes[result] || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
}

// 操作函数
const goBack = () => {
  router.go(-1)
}

const exportReport = () => {
  alert('导出PDF报告功能开发中...')
}

const printReport = () => {
  window.print()
}

onMounted(() => {
  // 页面加载时获取数据
  fetchData()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

<style scoped>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}

/* 深色模式下的渐变背景 */
@media (prefers-color-scheme: dark) {
  .bg-gradient-to-br {
    --tw-gradient-from: #111827;
    --tw-gradient-to: #1f2937;
  }
}
</style> 